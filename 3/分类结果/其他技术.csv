﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
EP4482087A1,EP24182514.0,"本发明涉及一种方法，用于确定汽车（5）通信网络（6）的通信节点（10）的防火墙规则，其中，所述机动车辆（5）的控制单元（20，30）经由所述通信节点（10）彼此连接，并且提供并配置用于传输消息（N20，N30）的通信节点（十），基于所述通信点，所述控制单元（20,30）的功能单元（QF，ZF）彼此传输信息，其中，功能单元（QF、ZF）之间的通信关系（KD1，KD2），存储在通信关系数据（KDB）中的单独且每个都连接到通信节点（10）的ECU（20，30）的组件，包括发送源功能单元（QF）的地址数据和/或接收目标功能单元（ZF）的目的地地址数据，其中通信节点（1）是用于检查消息（N20，N30）的防火墙设备（FW），通信节点（10）打算使用防火墙规则（FR）在连接到它的ECU（20，30）之间传输，该过程提供：-基于通信关系数据（KDB），通过生成器设备（GV）的分析工具（AM）确定防火墙规则（FR），其中分析器（AM）使用基于源功能单元（QF）的源地址数据的源防火墙规则（QR），根据相应的通信关系（KD1，KD2）发送消息（N20，N30）和/或基于目标功能单元（ZF）的目标地址数据发送目标防火墙规则（ZR），根据相应的通信关系接收消息（N20，N30），其中源防火墙规则（QR）具有过滤标准，并且意图是防火墙设备（FW）仅在消息（N20,N30）中包含的源功能单元（QF）的源地址数据满足源防火墙规则，N30）只有在消息（N20，N30）中包含的目标功能单元（ZF）的目标地址数据符合目标防火墙规则（ZR）的标准时才会转发给目标功能单元（ZF）。
",H04L9/40,Vector Informatik GmbH,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 具有, 方法, 生成, 设备, 控制, 工具, 配置","汽车, 控制, 机动车, 通信, 网络"
EP4482088A1,EP24182515.7,"本发明涉及将防火墙规则分配给汽车通信网络 （6） 通信节点 （10） 的防火墙设备 （FW） 的方法，车辆（5）的控制装置（20，30）通过通信节点（10）相互连接，用于传输消息（N20，N30），该通信节点通过控制装置（20，30）的功能单元（QF，ZF）相互传输信息，其中消息检查防火墙装置（FW）（N20，N30），所述通信节点（10）将在其相关联的控制单元（20，30）之间传输的信息是基于防火墙规则（FR）提供和配置的，其中，用于将防火墙规则（SR）的过滤标准应用于功能单元（QF，ZF）的消息（N20，N30）的防火墙设备（FW）具有可由通信节点（1）的处理器（11）执行的用于执行软件防火墙规则（BSR，OSR）的过滤软件和可配置有硬件防火墙规则（HR）的过滤硬件具有预定范围，其中功能单元（QF、ZF）在真实或模拟数据业务中经由通信节点（10）彼此发送消息（N20、N30）。该程序规定：-通过生成器设备（GV）的分配装置，分配与特定防火墙规则（FR）或其用于检查消息的组件（N20，N30）应用于相应防火墙规则（FRs）或其组件的频率有关的频率值（H31，H13），-生成器设备（GV）在可用硬件防火墙规则（HR）的范围内，根据这些防火墙规则（FR）或其具有最高频率值（H31、H13）的组件创建硬件防火墙规则；以及-生成器设备（GV）为防火墙规则（FR）或硬件防火墙规则（HR）中未显示的组件创建软件防火墙规则（BSR、OSR）。
",H04L9/40,Vector Informatik GmbH,其他技术,0.0,未匹配到特定技术领域,"提供, 模拟, 装置, 具有, 方法, 生成, 处理, 设备, 控制, 配置","汽车, 控制, 车辆, 通信, 网络"
EP4109789A1,EP22177890.5,"本发明涉及一种用于系统中的时间同步的方法，特别是机动车辆的控制，包括计时器（M1、M2）和至少一个计时器（S1、S2），其中定时器（M1、M2）具有定时器时钟（CM1、CM2），其中定时器（M1，M2）向定时器（S1，S2）发送具有定时器时间指示（TM1）的时间同步消息（SY1），该定时器时间指示从定时器时钟（CM1，CM2）生成，其中计时器（S1，S2）基于时间同步消息（SY1）的计时器时间指示（TM1）设置其计时器时钟（CS1，CS2）。假设计时器（S1、S2）连接到计时器（M1、M2），M2）发送具有至少一个计时器时间指示（TM3）的时间反向同步消息（SY3），并且计时器（M1、M2）基于计时器时间指示（TM3）与计时器时钟（CM1、CM2）的时间信息的比较产生至少一个后续动作。
",H04J3/06,Vector Informatik GmbH,其他技术,0.0,未匹配到特定技术领域,"包括, 具有, 方法, 设置, 生成, 控制, 系统","机动车, 控制"
EP3402035B1,EP18170916.3,"本发明涉及一种充电电池（24），特别是一个汽车电池（24）电力在一个至少有一个ladeanschluss（41）的电池（24）和固定（40），ladeeinrichtung其中ladeeinrichtung（40）和电池（26），在ladesteuerungseinrichtung电池（24）安装与电池（24），形成一个系统，在一个通信线路（46）是相互联系的，全面的：anforderungsnachricht（60）发送一个带有电ladebedarfsangabe（62）和（63）的ladezeitangabe电池ladesteuerungseinrichtung（26）固定在固定ladeeinrichtunG（40），其中一个ladebedarfsangabe（62）充电电池（24）必要的电气ladebedarf，特别是，一个要求的能量和\/或充电电池（24）所需的电气性能ladezeitangabe（63）和一个充电电池（24）可用时间说明发送一个ladeplans（72）通过固定ladeeinrichtung（40）到电池（26），ladesteuerungseinrichtung其中一个最大ladeplan（72）中的充电电池（24）在一个内部的电气性能bereitstellbarer ladezeitangabe（63）的ladezeitraums包含确定在充电电池（24）的电性能为bereitstellbaren（72），这样，ladeplan在第一ladezeitabschnitt（TA1）ladezeitraums第一的最大电功率（PA1010）中的至少一个第一和第二ladezeitabsc ladezeitabschnitt（TA1）hnitt（TA2）的第二ladezeitraums最大电功率（PA2）充电电池（24）给其中第一的电气性能（PA1010）最大的一个因素大于第二最大电功率（PA2），-充电电池（24）根据ladeplan。
",B60L53/14,Vector Informatik GmbH,其他技术,0.0,未匹配到特定技术领域,系统,"通信, 汽车"
EP3379750B1,EP18160248.3,"（vi）本发明涉及一种方法，用于处理一制造信息通过一serveranordnung （10）包含一公路车辆（90）的一控制面板（95）的anfragenachricht （rq），经由一通信网络（nw），特别地互联网或一移动式网络，公路车辆（90）用步骤连接：-通过控制单元（95）经由通信网络（nw）发送anfragenachricht （rq）到serveranordnung （10）的一分发服务器（20）。发送一第一制造信息（vi）包含冗余anfragenachricht （rq1）至第一verarbeitungsserver （30），的第二种制造信息（v （i）包含冗余anfragenachricht （rq2）到第二verarbeitungsserver （50）通过发行服务器（20）.with （vi）（vi）用于被处理的信息的一个相似的或相同工艺的verarbeitungsserver 彼此是redundant.-以第一冗余anfragenachricht （rq1）信息为基础创建第一冗余应答信息（1 '），通过第一verarbeitungsserver （30）发送应答信息到第一redundancya 分发服务器（20）。创建第二个冗余应答信息（'1 '）以第二条冗余anfragenachricht （rq2）信息为基础（vi）和发送应答信息，通过第二verarbeitungsserver （50）的第二redundancya 分发服务器（20），生成并发送一应答信息（一）以为基础第一冗余应答信息（1 '）和第二冗余应答信息（'1 '）从分发服务器到控制单元。
",H04L1/08,Vector Informatik GmbH,其他技术,0.0,未匹配到特定技术领域,"生成, 方法, 处理, 控制","车辆, 通信, 网络, 控制"
EP3402035A1,EP18170916.3,"本发明涉及一种充电电池（24），特别是一个汽车电池（24）电力在一个至少有一个ladeanschluss（41）的电池（24）和固定（40），ladeeinrichtung其中ladeeinrichtung（40）和电池（26），在ladesteuerungseinrichtung电池（24）安装与电池（24），形成一个系统，在一个通信线路（46）是相互联系的，全面的：anforderungsnachricht（60）发送一个带有电ladebedarfsangabe（62）和（63）的ladezeitangabe电池ladesteuerungseinrichtung（26）固定在固定ladeeinrichtunG（40），其中一个ladebedarfsangabe（62）充电电池（24）必要的电气ladebedarf，特别是，一个要求的能量和\/或充电电池（24）所需的电气性能ladezeitangabe（63）和一个充电电池（24）可用时间说明发送一个ladeplans（72）通过固定ladeeinrichtung（40）到电池（26），ladesteuerungseinrichtung其中一个最大ladeplan（72）中的充电电池（24）在一个内部的电气性能bereitstellbarer ladezeitangabe（63）的ladezeitraums包含确定在充电电池（24）的电性能为bereitstellbaren（72），这样，ladeplan在第一ladezeitabschnitt（TA1）ladezeitraums第一的最大电功率（PA1010）中的至少一个第一和第二ladezeitabsc ladezeitabschnitt（TA1）hnitt（TA2）的第二ladezeitraums最大电功率（PA2）充电电池（24）给其中第一的电气性能（PA1010）最大的一个因素大于第二最大电功率（PA2），-充电电池（24）根据ladeplan。
",H02J7/00,Vector Informatik GmbH,其他技术,0.0,未匹配到特定技术领域,系统,"通信, 汽车"
EP3379750A1,EP18160248.3,"（vi）本发明涉及一种方法，用于处理一制造信息通过一serveranordnung （10）包含一公路车辆（90）的一控制面板（95）的anfragenachricht （rq），经由一通信网络（nw），特别地互联网或一移动式网络，公路车辆（90）用步骤连接：-通过控制单元（95）经由通信网络（nw）发送anfragenachricht （rq）到serveranordnung （10）的一分发服务器（20）。发送一第一制造信息（vi）包含冗余anfragenachricht （rq1）至第一verarbeitungsserver （30），的第二种制造信息（v （i）包含冗余anfragenachricht （rq2）到第二verarbeitungsserver （50）通过发行服务器（20）.with （vi）（vi）用于被处理的信息的一个相似的或相同工艺的verarbeitungsserver 彼此是redundant.-以第一冗余anfragenachricht （rq1）信息为基础创建第一冗余应答信息（1 '），通过第一verarbeitungsserver （30）发送应答信息到第一redundancya 分发服务器（20）。创建第二个冗余应答信息（'1 '）以第二条冗余anfragenachricht （rq2）信息为基础（vi）和发送应答信息，通过第二verarbeitungsserver （50）的第二redundancya 分发服务器（20），生成并发送一应答信息（一）以为基础第一冗余应答信息（1 '）和第二冗余应答信息（'1 '）从分发服务器到控制单元。
",H04L1/08,Vector Informatik GmbH,其他技术,0.0,未匹配到特定技术领域,"生成, 方法, 处理, 控制","车辆, 通信, 网络, 控制"
EP1941330B1,EP06828806.7,"本发明涉及一种用于检测控制设备程序(13)的至少一个控制设备变量(12、41；62)的测量装置(11；111)，所述控制设备程序在控制设备(14)中由控制设备处理器(15)执行，其中，所述控制单元处理器(15)使用存储器数据连接(18)将所述至少一个控制单元变量(12，41；62)写入所述控制单元的控制单元存储器(19)，具有一侦测装置(31)以侦测在记忆体资料连接(18)上所传输之资料(30)。测量装置(11；111)具有缓冲存储器，检测装置(31)将通过存储器数据连接(18)传输的数据(30)写入该缓冲存储器，以及一映射记忆体(34)，其中，它使用缓冲存储在缓冲存储器(42；142)中的数据(29')来产生控制单元存储器的至少一部分的存储器映射(33)。当读取装置(35)读取至少一个控制单位变量(12，41；62)时，测量装置(11；111)至少在映射存储器的该部分中阻止数据从缓冲存储器(42，142)写入映射存储器。
",G05B19/042,VECTOR INFORMATIK GMBH,其他技术,0.0,未匹配到特定技术领域,"装置, 具有, 处理, 设备, 控制, 检测","检测, 控制"
EP2061215A1,EP07022333.4,"方法涉及通过使用在一个标识符上的相应散列-功能确定参考值-表检索引（H1 、H2），基于相应索引确定参考值（V1 、V2）。用于相关值-表的相关性-表检索引（R1）通过连接基于连接的功能的参考值形成。在相关值-表中的一相关值（IDtest）测定基于指数。用于一个通信设备的标识符的相关性通过与相关值（IDtest）的标识符的比较测试。一独立权利要求还包括一处理器的一测试模块。
",H04L29/12,Vector Informatik GmbH,其他技术,0.0,未匹配到特定技术领域,"包括, 测试, 方法, 处理, 设备","通信, 测试"
EP1941330A1,EP06828806.7,"本发明涉及一种测量装置( 11；111 )，用于检测至少一个控制单元变量( 12、41；控制单元62 )的执行的程序( 13 )在控制单元( 14 )的处理器( 15 )通过控制单元，其中所述控制单元处理器( 15 )写入至少一个控制单元的可变( 12、41；62 )连接到控制单元( 19 )的存储器控制单元使用内存连接( 18 )，检测装置( 31 )检测数据( 30 )进行传输的存储器连接( 18 ) 。该测量装置( 11；111 )具有一缓冲存储器，将检测装置( 31 )写入该数据( 30 )，这些已传送使用该存贮器数据连接( 18 )和( 34 )的映射存储器，它采用数据' )缓冲存储( 29缓存( 42；142 )产生映射( 33 )，至少部分的控制单元的存储器中。该测量装置( 11；111 )块写入的数据的缓冲存储器( 42、142 )与映射存储器至少在部分映射存储器同时读取装置( 35 )读取至少一个控制单元变量( 12、41；62 ) 。
",G05B19/042,Vector Informatik GmbH,其他技术,0.0,未匹配到特定技术领域,"装置, 具有, 处理, 控制, 检测","检测, 控制"
EP1248430B1,EP01108787.1,"该过滤遮罩决定方法决定该通讯装置(11，12，13)之相关及不相关字元，为每个相关字符提供至少一个输出过滤掩码，组合以提供组合过滤掩码，通过检查所有不相关字符被消除来验证该组合过滤掩码。还包括的是用于以下的独立权利要求：(a)通信设备使用组合过滤掩码用于传输字符的相关性测试；(b)生成模块，用于确定用于所发送的字符的相关性测试的过滤掩码；(c)开发工具、编程工具或通信设备，具有用于确定用于所发送字符的相关性测试的过滤掩码的生成模块
",H04L29/06,VECTOR INFORMATIK GMBH,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 测试, 装置, 具有, 方法, 生成, 设备, 工具","通信, 测试, 验证"
EP1349348B1,EP02400016.8,"基于分配给相应通信设备(K1-K4)的起始标识符(SI1-SI3)来确定通信设备的判决值。将所确定的判定值与通过网络(NET)接收的判定值进行比较。根据比较结果选择适当的通信标识符(CI1-CI4)，并通过网络发送该标识符。还包括以下独立权利要求：(1)通信设备；(2)通信模块；存储通信模块运行程序的存储装置。
",H04L29/12,VECTOR INFORMATIK GMBH,其他技术,0.0,未匹配到特定技术领域,"包括, 设备, 装置","通信, 网络"
EP1223725B1,EP01100723.4,"该方法涉及通过计算规则确定第一相关性表位置，在发生冲突的情况下通过增量规则确定至少一个备选位置，直到找到与另一标识符不关联的空位，如果在第一位或第二位输入标识符或相关值，则将其分类为相关标识符；如果在第一位或第二位输入非使用项，则将其分类为无关标识符。该方法涉及使用计算规则确定标识符在相关表中的第一位，在发生冲突的情况下，使用增量规则确定至少一个替代位置，其中第一个位置不可用，直到找到与另一标识符不关联的空位，如果标识符或关联值输入到第一位或第二位，则将标识符分类为相关，如果未使用输入到第一位或第二位，则将其分类为无关。独立权利要求还包括以下内容：为通信设备生成相关性表的方法、用于相关性检查的测试模块、为通信设备生成相关性表的生成模块、通信设备、，机动车和/或开发设备以及用于测试模块和/或生成模块的存储介质。
",H04L29/12,VECTOR INFORMATIK GMBH,其他技术,0.0,未匹配到特定技术领域,"包括, 测试, 生成, 方法, 设备, 计算","机动车, 通信, 测试"
US06697756B2,US10117052,"一种确定过滤面罩的相关性检测的标识符，其能够被传送给通信设备经由通信网络中的消息，具有如下步骤：确定相关性相关通信设备的标识符，并且标识符不切题的偏离度的通信设备中，确定至少一个初始过滤面罩在所有情况下分别相关性标识符，可以把该马马虎虎各自的相关性标识符并且不马马虎虎偏离度的标识符，组合至少两个初始过滤器掩码作为组合过滤面罩，这种相关性马马虎虎相应标识符与相应的初始过滤面罩和测试各个组合过滤面罩，它为保留器代替其相应的基本初始过滤面罩，如果所有标识符能够偏离度进行掩蔽组合过滤面罩或为拒绝，此仇不报非君子偏离度的至少一个标识符。
",G06F19/00,VECTOR INFORMATIK GMBH,其他技术,0.0,未匹配到特定技术领域,"具有, 检测, 测试, 设备","检测, 测试, 通信, 网络"
EP1349348A1,EP02400016.8,"通信设备的决策值测定基于启动分配给相应通信设备（K1-K4）的标识符（SI1-SI3）。确定的决策值与从网络（NET）接收的决策值相比。选择一适当的通信标识符（CI1-CI4）取决于比较结果，标识符在网络上传送。独立权利要求还包括下文：（1）通信设备；（2）通信模块；和（3）存储器存储通信组件操作程序。
",H04L29/12,Vector Informatik GmbH,其他技术,0.0,未匹配到特定技术领域,"包括, 设备","通信, 网络"
US20020147562A1,US10117052,"一种确定过滤面罩的相关性检测的标识符，其能够经由通信网络发送给通信设备的消息中，具有以下步骤：确定相关性标识符相关的通信，确定标识符不切题的偏离度的通信手段，确定至少一个初始过滤面罩在所有情况下分别相关性标识符，可以把该马马虎虎各自的相关性标识符并且不马马虎虎偏离度的标识符，组合至少两个初始过滤器掩码作为组合过滤面罩，这种相关性马马虎虎相应标识符与相应的初始过滤器掩码，并测定各组合过滤面罩，它为保留器代替其相应的基本初始过滤面罩，如果所有标识符能够偏离度进行掩蔽组合过滤面罩或为拒绝，此仇不报非君子偏离度的至少一个标识符。
",G06F19/00,VECTOR INFORMATIK GMBH,其他技术,0.0,未匹配到特定技术领域,"具有, 检测, 设备","检测, 通信, 网络"
EP1248430A1,EP01108787.1,"过滤面罩测定方法对于通信设备（11，12，13）确定有关的和不相关的字符，具有用于每一并联字符的至少一个输出滤波器掩模的规定，结合提供得到所有的不相关的字符被消除的检查的证实的一个组合过滤器掩模。此外包括用于下文的独立权利要求：（a）使用组合过滤器的一通信设备为传输的字符的相关性测试掩盖；（b）用于对用于传输的字符的相关性测试的过滤面罩的测定的一发电模块；（c）具有用于对用于传输的字符的相关性测试的过滤面罩的测定的一发电模块的一开发工具，一件程序设计工具，或一通信设备
",H04L29/06,Vector Informatik GmbH,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 测试, 具有, 方法, 设备, 工具","通信, 测试"
EP1223725A1,EP01100723.4,"通过一计算规则，方法涉及确定第一相关性搁置放置，在碰撞的情形下通过一增量规则确定至少一个替换地方直到发现不与另一标识符相关联的采空区，分类标识符同样有关的如果它或一结合值在被输入到第一或第二地方和分类它如同不相关的如果进行一不使用条目到第一或第二地方。方法涉及使用一计算规则确定用于标识符的在一张相关性表中的一第一名，确定至少一个替换地方，在碰撞的情形下使用一增量规则，因此直到发现不与另一标识符相关联的一采空区，第一名不可用，分类标识符同样有关的如果标识符或一结合值进入第一或第二地方和分类它作为不相关的如果进行一不使用条目到第一或第二地方。独立权利要求还包括下文：一种用于产生用于一通信设备的一张相关性表，用于相关性检查，一发电模块的一测试模块的方法，其用于产生一张相关性表，其用于测试模块和/或一发电模块的一通信设备、一通信设备、一辆机动车和/或开发设备和一储存介质。
",H04L29/12,Vector Informatik GmbH,其他技术,0.0,未匹配到特定技术领域,"包括, 测试, 方法, 设备, 计算","机动车, 通信, 测试"
