﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
EP2682865A1,EP12005020.8,"方法涉及通过一源-应用程序模块（30）以一使用说明文件（70）为基础创建用于在具有一控制单元（90）的数据通信中的一目标应用组件（50）的一配置文件（72）。配置文件被转移从一线源装置组件到目标应用组件，其中客观的应用程序模块与通过一校准协议的控制单元连通。一与控制信息相关联的参数是从一存储单元（94）至基于配置文件的控制单元传送。独立权利要求还包括下文：（1）一线源装置组件（2）一目标应用组件。
",G06F9/45,Vector Informatik GmbH,多实时机级联技术,2.0,IPC分类号匹配: G06F9/45,"包括, 装置, 具有, 方法, 控制, 配置","通信, 控制"
EP2061215B1,EP07022333.4,"该方法涉及通过对标识符使用相应散列函数来确定参考值表索引(H1、H2)，以及基于相应索引来确定参考值(V1、V2)。通过基于级联函数级联参考值来形成用于相关值表的相关表索引(R1)。基于索引确定相关值表中的相关性值(IDtest)。通过将标识符与相关性值(IDtest)进行比较来测试通信设备的标识符的相关性。本发明还涉及一种包括处理器的测试模块。
",H04L29/12,VECTOR INFORMATIK GMBH,多实时机级联技术,1.0,关键词匹配: 级联,"包括, 测试, 方法, 处理, 设备","通信, 测试"
US20030182407A1,US10391615,"本发明涉及用于确定用于通信设备（K1-K4）连接的在一个网络（NET）上的通信标识符（CI1-CI4）的方法，其具有分配给它们的启动标识符（SI1-SI3），以及为此用一通信设备（K1-K4）和一通信模块（KM）。在过程中，每一通信设备（K1-K4）执行下列步骤与各自的其他通信设备（K1-K4）同步：
（a）确定被分配给通信设备（K1-K4）在所有情况下的以启动标识符（SI1-SI3）为基础的第一本身的决策值，
（b）在网络（NET）上发送本身的决策值，
（c）与本身的决策值与被接收的决策值比较相比
（d）要求至少一个通信标识符（CI1-CI4）并在网络（NET）上传输这至少一个通信标识符（CI1-CI4），如果没有被接收的决策值在步骤（c）中可用，或如果它在步骤（c）中确定只有一个决策值被接收，各自的本身的决策值比所述被接收的决策值的具有一高次。
",G06F15/177,VECTOR INFORMATIK GMBH,多实时机级联技术,2.0,IPC分类号匹配: G06F15/177,"具有, 方法, 设备","通信, 网络"
US20020095520A1,US10036456,"测试方法相关性测试标识符能够经由通信网络发送给通信设备的消息中，测试模块，因此生成模块用于相关性表为性急的方法。在第一位置，该试验方法的相关性表是追求标识符基于计算尺应用于标识符或基于第二备选的位置到后者使用装上规则应用于该标识符的情况中，冲突，其中第一位置不可用直到可用位置被找到。该标识符被指定为挨边，当在第一或第二位置标识符或相关联的值，它是输入也可以为是不相关的，当在第一或第二位置时不使用标记被输入。
",G06F15/16,PROF. VECTOR INFORMATIK GMBH,多实时机级联技术,2.0,IPC分类号匹配: G06F15/16,"测试, 生成, 方法, 设备, 计算","通信, 测试, 网络"
