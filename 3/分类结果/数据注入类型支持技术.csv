﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
US11163543B2,US16837582,"本发明提供了一种设备和方法，用于在数据库中管理软件模块和对象，尤其是机动车的控制单元，在数据库中存储对象，并且其中用于提供对象的至少一个功能的至少一个软件模块与相应的对象相关联。该方法包括获取在操作接口处用编程语言为至少一个软件模块开发的可编译源代码，通过对源代码的语法分析生成软件模块的抽象语法结构，特别是抽象语法树，将抽象语法结构存储在数据库中，以及在所述对象和所述软件模块的抽象语法结构之间建立至少一个逻辑连接。
",G06F8/41,Vector Informatik GmbH,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/41,"提供, 包括, 方法, 生成, 设备, 控制","机动车, 控制"
US20200319862A1,US16837582,"一种用于管理数据库(50)中的软件模块(99)和对象(M95)、尤其是机动车辆(90)的控制单元(94-96)的设备和方法，对象(M95)存储在所述数据库中，并且用于提供对象(M95)的至少一个功能的至少一个软件模块(99)与相应对象(M95)相关联。该方法包括：
在用于所述至少一个软件模块(99)的操作接口处获取以编程语言开发的可编译源代码(Q99)，通过对所述源代码(Q99)的语法分析来生成所述软件模块(99)的抽象语法结构(A99)，特别是抽象语法树，将抽象语法结构(A99)存储在数据库(50)中，以及在对象(M95)和软件模块(99)的抽象语法结构(A99)之间建立至少一个逻辑连接。
",G06F8/41,Vector Informatik GmbH,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/41,"提供, 包括, 方法, 生成, 设备, 控制","机动车, 控制"
EP3719632A1,EP20162486.3,"本发明涉及一种用于在数据库(50)中管理软件模块(99)和对象(M95)、尤其是机动车(90)的控制装置(94-96)的装置和方法，在所述数据库中存储对象(M95)并且在所述数据库中为相应的对象(M95)分配至少一个用于提供对象(M95)的至少一个功能的软件模块(99) 。所述方法包括：-在操作者界面处获取用于至少一个软件模块(99)的可编译的源代码(Q99)，所述源代码以编程语言创建，-通过在语法上分析源代码(Q99)来产生软件模块(99)的abicite语法结构(A99)、尤其是abicite语法树，-在数据库(50)中存储abicite语法结构(A99)以及-建立对象(M95)和软件模块(99)的abicite语法结构(A99)之间的至少一个逻辑连接。
",G06F8/36,Vector Informatik GmbH,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/36,"提供, 包括, 装置, 方法, 控制","机动车, 控制"
EP2682865B1,EP12005020.8,"方法涉及通过一源-应用程序模块（30）以一使用说明文件（70）为基础创建用于在具有一控制单元（90）的数据通信中的一目标应用组件（50）的一配置文件（72）。配置文件被转移从一线源装置组件到目标应用组件，其中客观的应用程序模块与通过一校准协议的控制单元连通。一与控制信息相关联的参数是从一存储单元（94）至基于配置文件的控制单元传送。独立权利要求还包括下文：（1）一线源装置组件（2）一目标应用组件。
",G06F8/51,Vector Informatik GmbH,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/51,"包括, 装置, 具有, 方法, 控制, 配置","通信, 控制"
US20130311695A1,US13894977,"一Flexray 网关包括用于连接第一和第二Flexray 总线的第一和第二总线接口，其中Flexray 网关包括用于连接第一和第二Flexray 总线和用于在第一和第二Flexray 总线之间传输总线信息的耦合装置，其中Flexray 网关包括一台Flexray 控制器，其具有用于传输并接收一条Flexray 总线的第一和第二通道类型的总线信息的第一和第二通道接口。
",G06F13/42,Vector Informatik GmbH,数据注入类型支持技术,2.0,IPC分类号匹配: G06F13/42,"具有, 包括, 装置, 控制",控制
