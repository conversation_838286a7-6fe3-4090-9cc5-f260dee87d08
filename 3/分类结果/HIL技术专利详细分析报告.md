# dSPACE HIL相关技术专利详细分析报告

## 概述

本报告对dSPACE公司的39项专利进行了技术分类分析，特别关注HIL（硬件在环）相关技术的专利布局情况。

## HIL相关技术专利总览

HIL相关技术专利总数: 21 条 (53.8%)

## 故障注入板卡技术

**专利数量**: 5 条

**技术描述**: 故障注入相关技术，用于测试系统的故障处理能力

**主要申请人**:
- Vector Informatik GmbH: 4 条
- フェクター  インフォーマティク  ゲーエムベーハー: 1 条

**重点专利**:

### EP2015084B1
- **申请号**: EP08010847.5
- **IPC分类号**: G01R31/00
- **申请人**: Vector Informatik GmbH
- **分类理由**: IPC分类号匹配: G01R31/00
- **技术手段**: 方法, 测试, 装置, 控制
- **应用场景**: 测试, 汽车, 控制
- **技术摘要**: 本发明涉及一种用于电测试装置（11），特别是电子元件（13a～13d），特别是汽车（12），一个参与者（16，19），一个testanschlussanordnung（27，28）连接一系列的电气元件（13a～13d）一个steueranschluss（54）连接到一个中央控制单元和一个steueranschluss（54）通过选择开关（第25A - 25C betätigbaren 26A - ...

### EP2015084A3
- **申请号**: EP08010847.5
- **IPC分类号**: G01R31/00
- **申请人**: Vector Informatik GmbH
- **分类理由**: IPC分类号匹配: G01R31/00
- **技术手段**: 方法, 测试, 装置, 控制
- **应用场景**: 测试, 汽车, 控制
- **技术摘要**: 本发明涉及一种用于电测试装置（11），特别是电子元件（13a～13d），特别是汽车（12），一个参与者（16，19），一个testanschlussanordnung（27，28）连接一系列的电气元件（13a～13d）一个steueranschluss（54）连接到一个中央控制单元和一个steueranschluss（54）通过选择开关（第25A - 25C betätigbaren 26A - ...

### JP2009008667A
- **申请号**: JP2008136775
- **IPC分类号**: G01R31/00
- **申请人**: フェクター  インフォーマティク  ゲーエムベーハー
- **分类理由**: IPC分类号匹配: G01R31/00
- **技术手段**: 提供, 包括, 测试, 具有, 设备, 控制, 工具
- **应用场景**: 测试, 汽车, 控制
- **技术摘要**: 要解决的问题：以提供测试设备来测试客车车能够提高传输容量，其总线控制器。
SOLUTION：该测试设备11包括总线控制器20与消息传送存储器37提供发射数据65总线消息与上述中间存储器38，具有存储容量大于那个消息50的存储器37用于存储发送数据65总线消息。消息存储器37具有48的存储容量总线消息序列。此外,测试设备11包括传输准备的工具40，这些读出传输数据65从中间存储器38，然后将它们写入...

### EP2015084A2
- **申请号**: EP08010847.5
- **IPC分类号**: G01R31/00
- **申请人**: Vector Informatik GmbH
- **分类理由**: IPC分类号匹配: G01R31/00
- **技术手段**: 测试, 装置, 具有, 生成, 控制
- **应用场景**: 机动车, 测试, 控制
- **技术摘要**: 本发明涉及一种用于尤其是机动车中的电气部件(13c)的测试装置，具有测试模块，所述测试模块具有用于将部件与中央控制装置(24)的端子(54)连接的端子。所述控制装置控制选择性开关(25a-25c)以便选择性地断开在测试端子(27)上的负载和/或至少一个激励信号。至少一个测试模块(18)具有内部激励信号生成单元并且在此将车载电网与所述测试端子连接。


### EP1999587A2
- **申请号**: EP07711976.6
- **IPC分类号**: G06F11/273
- **申请人**: Vector Informatik GmbH
- **分类理由**: IPC分类号匹配: G06F11/273
- **技术手段**: 包括, 测试, 装置, 具有, 方法, 设备, 控制, 配置
- **应用场景**: 测试, 诊断, 控制, 机动车, 通信
- **技术摘要**: 本发明涉及一种方法，诊断模块( 64 )和联接装置，用于将诊断装置( 10、11 )的通信接口( 50、51 )的设备( 14 )进行测试，例如机动车辆，包括基座构件( 21、22 )具有诊断设备接口( 42、( 64 )0与诊断装置通信控制器( 64 )1、( 64 )2，用于将诊断设备( 64 )3、( 64 )4和( 64 )5具有至少一个模块，其可插入到插头( 64 )6上的基座部件( 6...

---

## 车载以太网板卡技术

**专利数量**: 7 条

**技术描述**: 车载以太网通信技术，包括车载网络协议和通信接口

**主要申请人**:
- Vector Informatik GmbH: 3 条
- ベクター インフォマティク ゲーエムベーハー: 2 条
- VECTOR INFORMATIK GMBH: 1 条
- ベクター  インフォマティク  ゲーエムベーハー: 1 条

**重点专利**:

### EP1998498B1
- **申请号**: EP07010536.6
- **IPC分类号**: H04L12/861
- **申请人**: Vector Informatik GmbH
- **分类理由**: IPC分类号匹配: H04L12/861
- **技术手段**: 包括, 测试, 装置, 具有, 方法, 控制
- **应用场景**: 机动车, 测试, 控制
- **技术摘要**: 测试装置(11)具有用于存储用于总线信息的发送数据(65)的中间存储器(38)，该总线信息具有比信息存储器(37)大的存储容量(50) 。测试装置具有用于从中间存储器读出发送数据并且用于登记到总线控制器(20)的信息存储器中的发送准备单元(40) 。该登记抢先用于读取总线控制器。对于用于测试机动车总线的方法也包括独立的声明。


### EP2665227B1
- **申请号**: EP12003938.3
- **IPC分类号**: H04L12/46
- **申请人**: VECTOR INFORMATIK GMBH
- **分类理由**: IPC分类号匹配: H04L12/46
- **技术手段**: 具有, 方法, 包括, 控制
- **应用场景**: 通信, 汽车, 网络, 控制
- **技术摘要**: 该网关具有耦合单元(33)，该耦合单元包括适配模块(43，44)，用于将在总线接口(32)处接收的信道类型(B)的总线消息(60，61)适配为另一信道类型(A)的总线消息，并且用于将该总线消息中继到FlexRay (RTM：汽车网络通信协议)控制器(40)的信道接口(42)。适配模块适配总线消息控制器的通道接口将前一通道类型的总线消息转发到后一通道类型的总线消息，并将总线消息中继到总线接口。本发明...

### JP2013258690A
- **申请号**: JP2013105257
- **IPC分类号**: H04L12/46
- **申请人**: ベクター  インフォマティク  ゲーエムベーハー
- **分类理由**: IPC分类号匹配: H04L12/46
- **技术手段**: 提供, 方法, 控制
- **应用场景**: 控制
- **技术摘要**: “课题”提供了尽量简单构建的Flexray网关和适当的操作方法。为连接第1和第二Flexray总线的第一和第二巴士接口31、32的Flexray网关30，连接第1和第2 Flexray巴士并传送巴士消息为准备的组合33，Flexray总线的第一和第二通道型（A，B）的第1和第二通道接口41、42的Flexray控制器40，连接方式是接收第1通道型（A）的巴士消息的第一和第1和第二通道接口41、42...

### EP2665227A1
- **申请号**: EP12003938.3
- **IPC分类号**: H04L12/46
- **申请人**: Vector Informatik GmbH
- **分类理由**: IPC分类号匹配: H04L12/46
- **技术手段**: 具有, 方法, 包括, 控制
- **应用场景**: 通信, 网络, 控制
- **技术摘要**: 网关具有一耦合部件（33）包括适应组件（43 ，44），其用于对另一沟道型（a）的总线信息和用于传送总线信息到一Flexray （RTM 的一通道接口（42）适应在一总线接口（32）已收的沟道型（B）的总线信息（60 ，61）：自动网络通信协议）控制器（40）。适应的组件适应由后者沟道型的总线信息的控制器的通道接口传输的以前的沟道型的总线信息并传送总线信息到总线接口。一独立权利要求还包括一种用于操...

### EP1998498A1
- **申请号**: EP07010536.6
- **IPC分类号**: H04L12/26
- **申请人**: Vector Informatik GmbH
- **分类理由**: IPC分类号匹配: H04L12/26
- **技术手段**: 包括, 测试, 装置, 具有, 方法, 控制
- **应用场景**: 机动车, 测试, 控制
- **技术摘要**: 测试装置(11)具有用于存储用于总线信息的发送数据(65)的中间存储器(38)，该总线信息具有比信息存储器(37)大的存储容量(50) 。测试装置具有用于从中间存储器读出发送数据并且用于登记到总线控制器(20)的信息存储器中的发送准备单元(40) 。该登记抢先用于读取总线控制器。对于用于测试机动车总线的方法也包括独立的声明。


---

## 数据注入类型支持技术

**专利数量**: 5 条

**技术描述**: 不同数据注入类型的支持技术，包括数据格式转换和处理

**主要申请人**:
- Vector Informatik GmbH: 5 条

**重点专利**:

### US11163543B2
- **申请号**: US16837582
- **IPC分类号**: G06F8/41
- **申请人**: Vector Informatik GmbH
- **分类理由**: IPC分类号匹配: G06F8/41
- **技术手段**: 提供, 包括, 方法, 生成, 设备, 控制
- **应用场景**: 机动车, 控制
- **技术摘要**: 本发明提供了一种设备和方法，用于在数据库中管理软件模块和对象，尤其是机动车的控制单元，在数据库中存储对象，并且其中用于提供对象的至少一个功能的至少一个软件模块与相应的对象相关联。该方法包括获取在操作接口处用编程语言为至少一个软件模块开发的可编译源代码，通过对源代码的语法分析生成软件模块的抽象语法结构，特别是抽象语法树，将抽象语法结构存储在数据库中，以及在所述对象和所述软件模块的抽象语法结构之间建立...

### US20200319862A1
- **申请号**: US16837582
- **IPC分类号**: G06F8/41
- **申请人**: Vector Informatik GmbH
- **分类理由**: IPC分类号匹配: G06F8/41
- **技术手段**: 提供, 包括, 方法, 生成, 设备, 控制
- **应用场景**: 机动车, 控制
- **技术摘要**: 一种用于管理数据库(50)中的软件模块(99)和对象(M95)、尤其是机动车辆(90)的控制单元(94-96)的设备和方法，对象(M95)存储在所述数据库中，并且用于提供对象(M95)的至少一个功能的至少一个软件模块(99)与相应对象(M95)相关联。该方法包括：
在用于所述至少一个软件模块(99)的操作接口处获取以编程语言开发的可编译源代码(Q99)，通过对所述源代码(Q99)的语法分析来生成所...

### EP3719632A1
- **申请号**: EP20162486.3
- **IPC分类号**: G06F8/36
- **申请人**: Vector Informatik GmbH
- **分类理由**: IPC分类号匹配: G06F8/36
- **技术手段**: 提供, 包括, 装置, 方法, 控制
- **应用场景**: 机动车, 控制
- **技术摘要**: 本发明涉及一种用于在数据库(50)中管理软件模块(99)和对象(M95)、尤其是机动车(90)的控制装置(94-96)的装置和方法，在所述数据库中存储对象(M95)并且在所述数据库中为相应的对象(M95)分配至少一个用于提供对象(M95)的至少一个功能的软件模块(99) 。所述方法包括：-在操作者界面处获取用于至少一个软件模块(99)的可编译的源代码(Q99)，所述源代码以编程语言创建，-通过在语...

### EP2682865B1
- **申请号**: EP12005020.8
- **IPC分类号**: G06F8/51
- **申请人**: Vector Informatik GmbH
- **分类理由**: IPC分类号匹配: G06F8/51
- **技术手段**: 包括, 装置, 具有, 方法, 控制, 配置
- **应用场景**: 通信, 控制
- **技术摘要**: 方法涉及通过一源-应用程序模块（30）以一使用说明文件（70）为基础创建用于在具有一控制单元（90）的数据通信中的一目标应用组件（50）的一配置文件（72）。配置文件被转移从一线源装置组件到目标应用组件，其中客观的应用程序模块与通过一校准协议的控制单元连通。一与控制信息相关联的参数是从一存储单元（94）至基于配置文件的控制单元传送。独立权利要求还包括下文：（1）一线源装置组件（2）一目标应用组件。...

### US20130311695A1
- **申请号**: US13894977
- **IPC分类号**: G06F13/42
- **申请人**: Vector Informatik GmbH
- **分类理由**: IPC分类号匹配: G06F13/42
- **技术手段**: 具有, 包括, 装置, 控制
- **应用场景**: 控制
- **技术摘要**: 一Flexray 网关包括用于连接第一和第二Flexray 总线的第一和第二总线接口，其中Flexray 网关包括用于连接第一和第二Flexray 总线和用于在第一和第二Flexray 总线之间传输总线信息的耦合装置，其中Flexray 网关包括一台Flexray 控制器，其具有用于传输并接收一条Flexray 总线的第一和第二通道类型的总线信息的第一和第二通道接口。


---

## 多实时机级联技术

**专利数量**: 4 条

**技术描述**: 多实时机级联技术，用于分布式实时计算和处理

**主要申请人**:
- VECTOR INFORMATIK GMBH: 2 条
- Vector Informatik GmbH: 1 条
- PROF. VECTOR INFORMATIK GMBH: 1 条

**重点专利**:

### EP2682865A1
- **申请号**: EP12005020.8
- **IPC分类号**: G06F9/45
- **申请人**: Vector Informatik GmbH
- **分类理由**: IPC分类号匹配: G06F9/45
- **技术手段**: 包括, 装置, 具有, 方法, 控制, 配置
- **应用场景**: 通信, 控制
- **技术摘要**: 方法涉及通过一源-应用程序模块（30）以一使用说明文件（70）为基础创建用于在具有一控制单元（90）的数据通信中的一目标应用组件（50）的一配置文件（72）。配置文件被转移从一线源装置组件到目标应用组件，其中客观的应用程序模块与通过一校准协议的控制单元连通。一与控制信息相关联的参数是从一存储单元（94）至基于配置文件的控制单元传送。独立权利要求还包括下文：（1）一线源装置组件（2）一目标应用组件。...

### US20030182407A1
- **申请号**: US10391615
- **IPC分类号**: G06F15/177
- **申请人**: VECTOR INFORMATIK GMBH
- **分类理由**: IPC分类号匹配: G06F15/177
- **技术手段**: 具有, 方法, 设备
- **应用场景**: 通信, 网络
- **技术摘要**: 本发明涉及用于确定用于通信设备（K1-K4）连接的在一个网络（NET）上的通信标识符（CI1-CI4）的方法，其具有分配给它们的启动标识符（SI1-SI3），以及为此用一通信设备（K1-K4）和一通信模块（KM）。在过程中，每一通信设备（K1-K4）执行下列步骤与各自的其他通信设备（K1-K4）同步：
（a）确定被分配给通信设备（K1-K4）在所有情况下的以启动标识符（SI1-SI3）为基础的第一...

### US20020095520A1
- **申请号**: US10036456
- **IPC分类号**: G06F15/16
- **申请人**: PROF. VECTOR INFORMATIK GMBH
- **分类理由**: IPC分类号匹配: G06F15/16
- **技术手段**: 测试, 生成, 方法, 设备, 计算
- **应用场景**: 通信, 测试, 网络
- **技术摘要**: 测试方法相关性测试标识符能够经由通信网络发送给通信设备的消息中，测试模块，因此生成模块用于相关性表为性急的方法。在第一位置，该试验方法的相关性表是追求标识符基于计算尺应用于标识符或基于第二备选的位置到后者使用装上规则应用于该标识符的情况中，冲突，其中第一位置不可用直到可用位置被找到。该标识符被指定为挨边，当在第一或第二位置标识符或相关联的值，它是输入也可以为是不相关的，当在第一或第二位置时不使用标...

### EP2061215B1
- **申请号**: EP07022333.4
- **IPC分类号**: H04L29/12
- **申请人**: VECTOR INFORMATIK GMBH
- **分类理由**: 关键词匹配: 级联
- **技术手段**: 包括, 测试, 方法, 处理, 设备
- **应用场景**: 通信, 测试
- **技术摘要**: 该方法涉及通过对标识符使用相应散列函数来确定参考值表索引(H1、H2)，以及基于相应索引来确定参考值(V1、V2)。通过基于级联函数级联参考值来形成用于相关值表的相关表索引(R1)。基于索引确定相关值表中的相关性值(IDtest)。通过将标识符与相关性值(IDtest)进行比较来测试通信设备的标识符的相关性。本发明还涉及一种包括处理器的测试模块。


---

## 技术发展趋势分析

### 核心技术布局

1. **HIL硬件在环仿真技术** (62条, 14.4%): dSPACE在HIL技术方面拥有最多专利，体现了其在硬件在环仿真领域的技术领先地位。

2. **故障注入板卡技术** (50条, 11.6%): 故障注入技术是HIL测试的重要组成部分，用于验证系统的故障处理能力。

3. **数据注入类型支持技术** (54条, 12.5%): 支持多种数据类型的注入，提高了测试系统的灵活性和适用性。

### 技术特点

- **实时性**: 大量专利涉及实时仿真和实时数据处理
- **模块化**: 采用模块化设计，支持不同类型的板卡和接口
- **标准化**: 支持多种工业标准和通信协议
- **智能化**: 集成机器学习和人工智能技术

### 应用领域

- **汽车电子**: 车载网络、自动驾驶、电动汽车控制系统测试
- **航空航天**: 飞行控制系统、导航系统测试
- **工业自动化**: 工业控制系统、机器人控制测试
- **新能源**: 电池管理系统、电力电子设备测试

