﻿公开(公告)号,申请号,原始申请人,摘要(中文),IPC主分类号
EP4482087A1,EP24182514.0,Vector Informatik GmbH,"本发明涉及一种方法，用于确定汽车（5）通信网络（6）的通信节点（10）的防火墙规则，其中，所述机动车辆（5）的控制单元（20，30）经由所述通信节点（10）彼此连接，并且提供并配置用于传输消息（N20，N30）的通信节点（十），基于所述通信点，所述控制单元（20,30）的功能单元（QF，ZF）彼此传输信息，其中，功能单元（QF、ZF）之间的通信关系（KD1，KD2），存储在通信关系数据（KDB）中的单独且每个都连接到通信节点（10）的ECU（20，30）的组件，包括发送源功能单元（QF）的地址数据和/或接收目标功能单元（ZF）的目的地地址数据，其中通信节点（1）是用于检查消息（N20，N30）的防火墙设备（FW），通信节点（10）打算使用防火墙规则（FR）在连接到它的ECU（20，30）之间传输，该过程提供：-基于通信关系数据（KDB），通过生成器设备（GV）的分析工具（AM）确定防火墙规则（FR），其中分析器（AM）使用基于源功能单元（QF）的源地址数据的源防火墙规则（QR），根据相应的通信关系（KD1，KD2）发送消息（N20，N30）和/或基于目标功能单元（ZF）的目标地址数据发送目标防火墙规则（ZR），根据相应的通信关系接收消息（N20，N30），其中源防火墙规则（QR）具有过滤标准，并且意图是防火墙设备（FW）仅在消息（N20,N30）中包含的源功能单元（QF）的源地址数据满足源防火墙规则，N30）只有在消息（N20，N30）中包含的目标功能单元（ZF）的目标地址数据符合目标防火墙规则（ZR）的标准时才会转发给目标功能单元（ZF）。
",H04L9/40
EP4482088A1,EP24182515.7,Vector Informatik GmbH,"本发明涉及将防火墙规则分配给汽车通信网络 （6） 通信节点 （10） 的防火墙设备 （FW） 的方法，车辆（5）的控制装置（20，30）通过通信节点（10）相互连接，用于传输消息（N20，N30），该通信节点通过控制装置（20，30）的功能单元（QF，ZF）相互传输信息，其中消息检查防火墙装置（FW）（N20，N30），所述通信节点（10）将在其相关联的控制单元（20，30）之间传输的信息是基于防火墙规则（FR）提供和配置的，其中，用于将防火墙规则（SR）的过滤标准应用于功能单元（QF，ZF）的消息（N20，N30）的防火墙设备（FW）具有可由通信节点（1）的处理器（11）执行的用于执行软件防火墙规则（BSR，OSR）的过滤软件和可配置有硬件防火墙规则（HR）的过滤硬件具有预定范围，其中功能单元（QF、ZF）在真实或模拟数据业务中经由通信节点（10）彼此发送消息（N20、N30）。该程序规定：-通过生成器设备（GV）的分配装置，分配与特定防火墙规则（FR）或其用于检查消息的组件（N20，N30）应用于相应防火墙规则（FRs）或其组件的频率有关的频率值（H31，H13），-生成器设备（GV）在可用硬件防火墙规则（HR）的范围内，根据这些防火墙规则（FR）或其具有最高频率值（H31、H13）的组件创建硬件防火墙规则；以及-生成器设备（GV）为防火墙规则（FR）或硬件防火墙规则（HR）中未显示的组件创建软件防火墙规则（BSR、OSR）。
",H04L9/40
EP4109789A1,EP22177890.5,Vector Informatik GmbH,"本发明涉及一种用于系统中的时间同步的方法，特别是机动车辆的控制，包括计时器（M1、M2）和至少一个计时器（S1、S2），其中定时器（M1、M2）具有定时器时钟（CM1、CM2），其中定时器（M1，M2）向定时器（S1，S2）发送具有定时器时间指示（TM1）的时间同步消息（SY1），该定时器时间指示从定时器时钟（CM1，CM2）生成，其中计时器（S1，S2）基于时间同步消息（SY1）的计时器时间指示（TM1）设置其计时器时钟（CS1，CS2）。假设计时器（S1、S2）连接到计时器（M1、M2），M2）发送具有至少一个计时器时间指示（TM3）的时间反向同步消息（SY3），并且计时器（M1、M2）基于计时器时间指示（TM3）与计时器时钟（CM1、CM2）的时间信息的比较产生至少一个后续动作。
",H04J3/06
US11163543B2,US16837582,Vector Informatik GmbH,"本发明提供了一种设备和方法，用于在数据库中管理软件模块和对象，尤其是机动车的控制单元，在数据库中存储对象，并且其中用于提供对象的至少一个功能的至少一个软件模块与相应的对象相关联。该方法包括获取在操作接口处用编程语言为至少一个软件模块开发的可编译源代码，通过对源代码的语法分析生成软件模块的抽象语法结构，特别是抽象语法树，将抽象语法结构存储在数据库中，以及在所述对象和所述软件模块的抽象语法结构之间建立至少一个逻辑连接。
",G06F8/41
US20200319862A1,US16837582,Vector Informatik GmbH,"一种用于管理数据库(50)中的软件模块(99)和对象(M95)、尤其是机动车辆(90)的控制单元(94-96)的设备和方法，对象(M95)存储在所述数据库中，并且用于提供对象(M95)的至少一个功能的至少一个软件模块(99)与相应对象(M95)相关联。该方法包括：
在用于所述至少一个软件模块(99)的操作接口处获取以编程语言开发的可编译源代码(Q99)，通过对所述源代码(Q99)的语法分析来生成所述软件模块(99)的抽象语法结构(A99)，特别是抽象语法树，将抽象语法结构(A99)存储在数据库(50)中，以及在对象(M95)和软件模块(99)的抽象语法结构(A99)之间建立至少一个逻辑连接。
",G06F8/41
EP3719632A1,EP20162486.3,Vector Informatik GmbH,"本发明涉及一种用于在数据库(50)中管理软件模块(99)和对象(M95)、尤其是机动车(90)的控制装置(94-96)的装置和方法，在所述数据库中存储对象(M95)并且在所述数据库中为相应的对象(M95)分配至少一个用于提供对象(M95)的至少一个功能的软件模块(99) 。所述方法包括：-在操作者界面处获取用于至少一个软件模块(99)的可编译的源代码(Q99)，所述源代码以编程语言创建，-通过在语法上分析源代码(Q99)来产生软件模块(99)的abicite语法结构(A99)、尤其是abicite语法树，-在数据库(50)中存储abicite语法结构(A99)以及-建立对象(M95)和软件模块(99)的abicite语法结构(A99)之间的至少一个逻辑连接。
",G06F8/36
EP3402035B1,EP18170916.3,Vector Informatik GmbH,"本发明涉及一种充电电池（24），特别是一个汽车电池（24）电力在一个至少有一个ladeanschluss（41）的电池（24）和固定（40），ladeeinrichtung其中ladeeinrichtung（40）和电池（26），在ladesteuerungseinrichtung电池（24）安装与电池（24），形成一个系统，在一个通信线路（46）是相互联系的，全面的：anforderungsnachricht（60）发送一个带有电ladebedarfsangabe（62）和（63）的ladezeitangabe电池ladesteuerungseinrichtung（26）固定在固定ladeeinrichtunG（40），其中一个ladebedarfsangabe（62）充电电池（24）必要的电气ladebedarf，特别是，一个要求的能量和\/或充电电池（24）所需的电气性能ladezeitangabe（63）和一个充电电池（24）可用时间说明发送一个ladeplans（72）通过固定ladeeinrichtung（40）到电池（26），ladesteuerungseinrichtung其中一个最大ladeplan（72）中的充电电池（24）在一个内部的电气性能bereitstellbarer ladezeitangabe（63）的ladezeitraums包含确定在充电电池（24）的电性能为bereitstellbaren（72），这样，ladeplan在第一ladezeitabschnitt（TA1）ladezeitraums第一的最大电功率（PA1010）中的至少一个第一和第二ladezeitabsc ladezeitabschnitt（TA1）hnitt（TA2）的第二ladezeitraums最大电功率（PA2）充电电池（24）给其中第一的电气性能（PA1010）最大的一个因素大于第二最大电功率（PA2），-充电电池（24）根据ladeplan。
",B60L53/14
EP3379750B1,EP18160248.3,Vector Informatik GmbH,"（vi）本发明涉及一种方法，用于处理一制造信息通过一serveranordnung （10）包含一公路车辆（90）的一控制面板（95）的anfragenachricht （rq），经由一通信网络（nw），特别地互联网或一移动式网络，公路车辆（90）用步骤连接：-通过控制单元（95）经由通信网络（nw）发送anfragenachricht （rq）到serveranordnung （10）的一分发服务器（20）。发送一第一制造信息（vi）包含冗余anfragenachricht （rq1）至第一verarbeitungsserver （30），的第二种制造信息（v （i）包含冗余anfragenachricht （rq2）到第二verarbeitungsserver （50）通过发行服务器（20）.with （vi）（vi）用于被处理的信息的一个相似的或相同工艺的verarbeitungsserver 彼此是redundant.-以第一冗余anfragenachricht （rq1）信息为基础创建第一冗余应答信息（1 '），通过第一verarbeitungsserver （30）发送应答信息到第一redundancya 分发服务器（20）。创建第二个冗余应答信息（'1 '）以第二条冗余anfragenachricht （rq2）信息为基础（vi）和发送应答信息，通过第二verarbeitungsserver （50）的第二redundancya 分发服务器（20），生成并发送一应答信息（一）以为基础第一冗余应答信息（1 '）和第二冗余应答信息（'1 '）从分发服务器到控制单元。
",H04L1/08
EP2015084B1,EP08010847.5,Vector Informatik GmbH,"本发明涉及一种用于电测试装置（11），特别是电子元件（13a～13d），特别是汽车（12），一个参与者（16，19），一个testanschlussanordnung（27，28）连接一系列的电气元件（13a～13d）一个steueranschluss（54）连接到一个中央控制单元和一个steueranschluss（54）通过选择开关（第25A - 25C betätigbaren 26A - 26C）至少有一个可选的引擎负荷和\/或至少一个stimulussignals（33）在testanschlüsse（27A，28A - 28D款）testanschlussanordnung（27，28）和至少一个测试模块，其中一个内部（16，19）在选择开关（第25A - 25C，第26A - 26C testanschlussanordnung）与（27，28）verbindbare stimulationseinrichtung（30A，30B）用于产生至少一个stimulussignals（33）和\/或一种内在的，通过选择开关（第25A - 25C，第26A - 26C testanschlussanordnung）与（27，28）verbindbare L方法形成的asteinrichtüng testanschlussanordnung（27，28）的基础上的负载。
",G01R31/00
EP3402035A1,EP18170916.3,Vector Informatik GmbH,"本发明涉及一种充电电池（24），特别是一个汽车电池（24）电力在一个至少有一个ladeanschluss（41）的电池（24）和固定（40），ladeeinrichtung其中ladeeinrichtung（40）和电池（26），在ladesteuerungseinrichtung电池（24）安装与电池（24），形成一个系统，在一个通信线路（46）是相互联系的，全面的：anforderungsnachricht（60）发送一个带有电ladebedarfsangabe（62）和（63）的ladezeitangabe电池ladesteuerungseinrichtung（26）固定在固定ladeeinrichtunG（40），其中一个ladebedarfsangabe（62）充电电池（24）必要的电气ladebedarf，特别是，一个要求的能量和\/或充电电池（24）所需的电气性能ladezeitangabe（63）和一个充电电池（24）可用时间说明发送一个ladeplans（72）通过固定ladeeinrichtung（40）到电池（26），ladesteuerungseinrichtung其中一个最大ladeplan（72）中的充电电池（24）在一个内部的电气性能bereitstellbarer ladezeitangabe（63）的ladezeitraums包含确定在充电电池（24）的电性能为bereitstellbaren（72），这样，ladeplan在第一ladezeitabschnitt（TA1）ladezeitraums第一的最大电功率（PA1010）中的至少一个第一和第二ladezeitabsc ladezeitabschnitt（TA1）hnitt（TA2）的第二ladezeitraums最大电功率（PA2）充电电池（24）给其中第一的电气性能（PA1010）最大的一个因素大于第二最大电功率（PA2），-充电电池（24）根据ladeplan。
",H02J7/00
EP3379750A1,EP18160248.3,Vector Informatik GmbH,"（vi）本发明涉及一种方法，用于处理一制造信息通过一serveranordnung （10）包含一公路车辆（90）的一控制面板（95）的anfragenachricht （rq），经由一通信网络（nw），特别地互联网或一移动式网络，公路车辆（90）用步骤连接：-通过控制单元（95）经由通信网络（nw）发送anfragenachricht （rq）到serveranordnung （10）的一分发服务器（20）。发送一第一制造信息（vi）包含冗余anfragenachricht （rq1）至第一verarbeitungsserver （30），的第二种制造信息（v （i）包含冗余anfragenachricht （rq2）到第二verarbeitungsserver （50）通过发行服务器（20）.with （vi）（vi）用于被处理的信息的一个相似的或相同工艺的verarbeitungsserver 彼此是redundant.-以第一冗余anfragenachricht （rq1）信息为基础创建第一冗余应答信息（1 '），通过第一verarbeitungsserver （30）发送应答信息到第一redundancya 分发服务器（20）。创建第二个冗余应答信息（'1 '）以第二条冗余anfragenachricht （rq2）信息为基础（vi）和发送应答信息，通过第二verarbeitungsserver （50）的第二redundancya 分发服务器（20），生成并发送一应答信息（一）以为基础第一冗余应答信息（1 '）和第二冗余应答信息（'1 '）从分发服务器到控制单元。
",H04L1/08
EP2682865B1,EP12005020.8,Vector Informatik GmbH,"方法涉及通过一源-应用程序模块（30）以一使用说明文件（70）为基础创建用于在具有一控制单元（90）的数据通信中的一目标应用组件（50）的一配置文件（72）。配置文件被转移从一线源装置组件到目标应用组件，其中客观的应用程序模块与通过一校准协议的控制单元连通。一与控制信息相关联的参数是从一存储单元（94）至基于配置文件的控制单元传送。独立权利要求还包括下文：（1）一线源装置组件（2）一目标应用组件。
",G06F8/51
EP1998498B1,EP07010536.6,Vector Informatik GmbH,"测试装置(11)具有用于存储用于总线信息的发送数据(65)的中间存储器(38)，该总线信息具有比信息存储器(37)大的存储容量(50) 。测试装置具有用于从中间存储器读出发送数据并且用于登记到总线控制器(20)的信息存储器中的发送准备单元(40) 。该登记抢先用于读取总线控制器。对于用于测试机动车总线的方法也包括独立的声明。
",H04L12/861
EP2665227B1,EP12003938.3,VECTOR INFORMATIK GMBH,"该网关具有耦合单元(33)，该耦合单元包括适配模块(43，44)，用于将在总线接口(32)处接收的信道类型(B)的总线消息(60，61)适配为另一信道类型(A)的总线消息，并且用于将该总线消息中继到FlexRay (RTM：汽车网络通信协议)控制器(40)的信道接口(42)。适配模块适配总线消息控制器的通道接口将前一通道类型的总线消息转发到后一通道类型的总线消息，并将总线消息中继到总线接口。本发明还涉及一种用于运行汽车网络通信协议网关的方法。
",H04L12/46
EP2682865A1,EP12005020.8,Vector Informatik GmbH,"方法涉及通过一源-应用程序模块（30）以一使用说明文件（70）为基础创建用于在具有一控制单元（90）的数据通信中的一目标应用组件（50）的一配置文件（72）。配置文件被转移从一线源装置组件到目标应用组件，其中客观的应用程序模块与通过一校准协议的控制单元连通。一与控制信息相关联的参数是从一存储单元（94）至基于配置文件的控制单元传送。独立权利要求还包括下文：（1）一线源装置组件（2）一目标应用组件。
",G06F9/45
EP2015084A3,EP08010847.5,Vector Informatik GmbH,"本发明涉及一种用于电测试装置（11），特别是电子元件（13a～13d），特别是汽车（12），一个参与者（16，19），一个testanschlussanordnung（27，28）连接一系列的电气元件（13a～13d）一个steueranschluss（54）连接到一个中央控制单元和一个steueranschluss（54）通过选择开关（第25A - 25C betätigbaren 26A - 26C）至少有一个可选的引擎负荷和\/或至少一个stimulussignals（33）在testanschlüsse（27A，28A - 28D款）testanschlussanordnung（27，28）和至少一个测试模块，其中一个内部（16，19）在选择开关（第25A - 25C，第26A - 26C testanschlussanordnung）与（27，28）verbindbare stimulationseinrichtung（30A，30B）用于产生至少一个stimulussignals（33）和\/或一种内在的，通过选择开关（第25A - 25C，第26A - 26C testanschlussanordnung）与（27，28）verbindbare L方法形成的asteinrichtüng testanschlussanordnung（27，28）的基础上的负载。
",G01R31/00
JP2013258690A,JP2013105257,ベクター  インフォマティク  ゲーエムベーハー,"“课题”提供了尽量简单构建的Flexray网关和适当的操作方法。为连接第1和第二Flexray总线的第一和第二巴士接口31、32的Flexray网关30，连接第1和第2 Flexray巴士并传送巴士消息为准备的组合33，Flexray总线的第一和第二通道型（A，B）的第1和第二通道接口41、42的Flexray控制器40，连接方式是接收第1通道型（A）的巴士消息的第一和第1和第二通道接口41、42的Flexray控制器40。2公交接口被构成，在第2公交接口发送的第一通道型（A）的巴士信息和在第2通道接口42发送的第2通道型（B）的巴士消息开枪互相适合进行转播。【选择图】图3
",H04L12/46
US20130311695A1,US13894977,Vector Informatik GmbH,"一Flexray 网关包括用于连接第一和第二Flexray 总线的第一和第二总线接口，其中Flexray 网关包括用于连接第一和第二Flexray 总线和用于在第一和第二Flexray 总线之间传输总线信息的耦合装置，其中Flexray 网关包括一台Flexray 控制器，其具有用于传输并接收一条Flexray 总线的第一和第二通道类型的总线信息的第一和第二通道接口。
",G06F13/42
EP2665227A1,EP12003938.3,Vector Informatik GmbH,"网关具有一耦合部件（33）包括适应组件（43 ，44），其用于对另一沟道型（a）的总线信息和用于传送总线信息到一Flexray （RTM 的一通道接口（42）适应在一总线接口（32）已收的沟道型（B）的总线信息（60 ，61）：自动网络通信协议）控制器（40）。适应的组件适应由后者沟道型的总线信息的控制器的通道接口传输的以前的沟道型的总线信息并传送总线信息到总线接口。一独立权利要求还包括一种用于操作一自动网络通信协议网关的方法。
",H04L12/46
EP2061215B1,EP07022333.4,VECTOR INFORMATIK GMBH,"该方法涉及通过对标识符使用相应散列函数来确定参考值表索引(H1、H2)，以及基于相应索引来确定参考值(V1、V2)。通过基于级联函数级联参考值来形成用于相关值表的相关表索引(R1)。基于索引确定相关值表中的相关性值(IDtest)。通过将标识符与相关性值(IDtest)进行比较来测试通信设备的标识符的相关性。本发明还涉及一种包括处理器的测试模块。
",H04L29/12
EP1941330B1,EP06828806.7,VECTOR INFORMATIK GMBH,"本发明涉及一种用于检测控制设备程序(13)的至少一个控制设备变量(12、41；62)的测量装置(11；111)，所述控制设备程序在控制设备(14)中由控制设备处理器(15)执行，其中，所述控制单元处理器(15)使用存储器数据连接(18)将所述至少一个控制单元变量(12，41；62)写入所述控制单元的控制单元存储器(19)，具有一侦测装置(31)以侦测在记忆体资料连接(18)上所传输之资料(30)。测量装置(11；111)具有缓冲存储器，检测装置(31)将通过存储器数据连接(18)传输的数据(30)写入该缓冲存储器，以及一映射记忆体(34)，其中，它使用缓冲存储在缓冲存储器(42；142)中的数据(29')来产生控制单元存储器的至少一部分的存储器映射(33)。当读取装置(35)读取至少一个控制单位变量(12，41；62)时，测量装置(11；111)至少在映射存储器的该部分中阻止数据从缓冲存储器(42，142)写入映射存储器。
",G05B19/042
EP2061215A1,EP07022333.4,Vector Informatik GmbH,"方法涉及通过使用在一个标识符上的相应散列-功能确定参考值-表检索引（H1 、H2），基于相应索引确定参考值（V1 、V2）。用于相关值-表的相关性-表检索引（R1）通过连接基于连接的功能的参考值形成。在相关值-表中的一相关值（IDtest）测定基于指数。用于一个通信设备的标识符的相关性通过与相关值（IDtest）的标识符的比较测试。一独立权利要求还包括一处理器的一测试模块。
",H04L29/12
JP2009008667A,JP2008136775,フェクター  インフォーマティク  ゲーエムベーハー,"要解决的问题：以提供测试设备来测试客车车能够提高传输容量，其总线控制器。
SOLUTION：该测试设备11包括总线控制器20与消息传送存储器37提供发射数据65总线消息与上述中间存储器38，具有存储容量大于那个消息50的存储器37用于存储发送数据65总线消息。消息存储器37具有48的存储容量总线消息序列。此外,测试设备11包括传输准备的工具40，这些读出传输数据65从中间存储器38，然后将它们写入到消息中，总线控制器37的存储器20，在读出之前总线控制器的20 。因此，附加的总线消息传送的总线控制器20经由总线12用于汽车在传输周期31和32序列33或传输周期，扩展了总线消息序列。
",G01R31/00
EP2015084A2,EP08010847.5,Vector Informatik GmbH,"本发明涉及一种用于尤其是机动车中的电气部件(13c)的测试装置，具有测试模块，所述测试模块具有用于将部件与中央控制装置(24)的端子(54)连接的端子。所述控制装置控制选择性开关(25a-25c)以便选择性地断开在测试端子(27)上的负载和/或至少一个激励信号。至少一个测试模块(18)具有内部激励信号生成单元并且在此将车载电网与所述测试端子连接。
",G01R31/00
EP1999587A2,EP07711976.6,Vector Informatik GmbH,"本发明涉及一种方法，诊断模块( 64 )和联接装置，用于将诊断装置( 10、11 )的通信接口( 50、51 )的设备( 14 )进行测试，例如机动车辆，包括基座构件( 21、22 )具有诊断设备接口( 42、( 64 )0与诊断装置通信控制器( 64 )1、( 64 )2，用于将诊断设备( 64 )3、( 64 )4和( 64 )5具有至少一个模块，其可插入到插头( 64 )6上的基座部件( 64 )7、( 64 )8通过通信收发器以形成用于通信接口测试接口( 64 )9 ( 100、( 101 ( 102的装置进行测试，其中联接装置( 103、( 104发送接收的测试接口( 105至( 106诊断设备接口，和/或接收的( 107 ( 108诊断设备接口，向测试接口( 109 11 )0 。基座部件( 21、22 )和/或至少一个模块( 23-25 )包括测试接口控制器( 56、77 )、特别是总线控制器，其可以被配置为具有通信接口( 50、51 )的装置( 14 )，通过可装载用于测试的操作软件( 61 ) 。
",G06F11/273
EP1998498A1,EP07010536.6,Vector Informatik GmbH,"测试装置(11)具有用于存储用于总线信息的发送数据(65)的中间存储器(38)，该总线信息具有比信息存储器(37)大的存储容量(50) 。测试装置具有用于从中间存储器读出发送数据并且用于登记到总线控制器(20)的信息存储器中的发送准备单元(40) 。该登记抢先用于读取总线控制器。对于用于测试机动车总线的方法也包括独立的声明。
",H04L12/26
EP1941330A1,EP06828806.7,Vector Informatik GmbH,"本发明涉及一种测量装置( 11；111 )，用于检测至少一个控制单元变量( 12、41；控制单元62 )的执行的程序( 13 )在控制单元( 14 )的处理器( 15 )通过控制单元，其中所述控制单元处理器( 15 )写入至少一个控制单元的可变( 12、41；62 )连接到控制单元( 19 )的存储器控制单元使用内存连接( 18 )，检测装置( 31 )检测数据( 30 )进行传输的存储器连接( 18 ) 。该测量装置( 11；111 )具有一缓冲存储器，将检测装置( 31 )写入该数据( 30 )，这些已传送使用该存贮器数据连接( 18 )和( 34 )的映射存储器，它采用数据' )缓冲存储( 29缓存( 42；142 )产生映射( 33 )，至少部分的控制单元的存储器中。该测量装置( 11；111 )块写入的数据的缓冲存储器( 42、142 )与映射存储器至少在部分映射存储器同时读取装置( 35 )读取至少一个控制单元变量( 12、41；62 ) 。
",G05B19/042
EP1248430B1,EP01108787.1,VECTOR INFORMATIK GMBH,"该过滤遮罩决定方法决定该通讯装置(11，12，13)之相关及不相关字元，为每个相关字符提供至少一个输出过滤掩码，组合以提供组合过滤掩码，通过检查所有不相关字符被消除来验证该组合过滤掩码。还包括的是用于以下的独立权利要求：(a)通信设备使用组合过滤掩码用于传输字符的相关性测试；(b)生成模块，用于确定用于所发送的字符的相关性测试的过滤掩码；(c)开发工具、编程工具或通信设备，具有用于确定用于所发送字符的相关性测试的过滤掩码的生成模块
",H04L29/06
EP1349348B1,EP02400016.8,VECTOR INFORMATIK GMBH,"基于分配给相应通信设备(K1-K4)的起始标识符(SI1-SI3)来确定通信设备的判决值。将所确定的判定值与通过网络(NET)接收的判定值进行比较。根据比较结果选择适当的通信标识符(CI1-CI4)，并通过网络发送该标识符。还包括以下独立权利要求：(1)通信设备；(2)通信模块；存储通信模块运行程序的存储装置。
",H04L29/12
EP1223725B1,EP01100723.4,VECTOR INFORMATIK GMBH,"该方法涉及通过计算规则确定第一相关性表位置，在发生冲突的情况下通过增量规则确定至少一个备选位置，直到找到与另一标识符不关联的空位，如果在第一位或第二位输入标识符或相关值，则将其分类为相关标识符；如果在第一位或第二位输入非使用项，则将其分类为无关标识符。该方法涉及使用计算规则确定标识符在相关表中的第一位，在发生冲突的情况下，使用增量规则确定至少一个替代位置，其中第一个位置不可用，直到找到与另一标识符不关联的空位，如果标识符或关联值输入到第一位或第二位，则将标识符分类为相关，如果未使用输入到第一位或第二位，则将其分类为无关。独立权利要求还包括以下内容：为通信设备生成相关性表的方法、用于相关性检查的测试模块、为通信设备生成相关性表的生成模块、通信设备、，机动车和/或开发设备以及用于测试模块和/或生成模块的存储介质。
",H04L29/12
US06697756B2,US10117052,VECTOR INFORMATIK GMBH,"一种确定过滤面罩的相关性检测的标识符，其能够被传送给通信设备经由通信网络中的消息，具有如下步骤：确定相关性相关通信设备的标识符，并且标识符不切题的偏离度的通信设备中，确定至少一个初始过滤面罩在所有情况下分别相关性标识符，可以把该马马虎虎各自的相关性标识符并且不马马虎虎偏离度的标识符，组合至少两个初始过滤器掩码作为组合过滤面罩，这种相关性马马虎虎相应标识符与相应的初始过滤面罩和测试各个组合过滤面罩，它为保留器代替其相应的基本初始过滤面罩，如果所有标识符能够偏离度进行掩蔽组合过滤面罩或为拒绝，此仇不报非君子偏离度的至少一个标识符。
",G06F19/00
EP1349348A1,EP02400016.8,Vector Informatik GmbH,"通信设备的决策值测定基于启动分配给相应通信设备（K1-K4）的标识符（SI1-SI3）。确定的决策值与从网络（NET）接收的决策值相比。选择一适当的通信标识符（CI1-CI4）取决于比较结果，标识符在网络上传送。独立权利要求还包括下文：（1）通信设备；（2）通信模块；和（3）存储器存储通信组件操作程序。
",H04L29/12
US20030182407A1,US10391615,VECTOR INFORMATIK GMBH,"本发明涉及用于确定用于通信设备（K1-K4）连接的在一个网络（NET）上的通信标识符（CI1-CI4）的方法，其具有分配给它们的启动标识符（SI1-SI3），以及为此用一通信设备（K1-K4）和一通信模块（KM）。在过程中，每一通信设备（K1-K4）执行下列步骤与各自的其他通信设备（K1-K4）同步：
（a）确定被分配给通信设备（K1-K4）在所有情况下的以启动标识符（SI1-SI3）为基础的第一本身的决策值，
（b）在网络（NET）上发送本身的决策值，
（c）与本身的决策值与被接收的决策值比较相比
（d）要求至少一个通信标识符（CI1-CI4）并在网络（NET）上传输这至少一个通信标识符（CI1-CI4），如果没有被接收的决策值在步骤（c）中可用，或如果它在步骤（c）中确定只有一个决策值被接收，各自的本身的决策值比所述被接收的决策值的具有一高次。
",G06F15/177
JP特開2002-374267(P2002-374267A)A,JP特願2002-105200(P2002-105200),ベクター インフォマティク ゲーエムベーハー,"要解决的问题：提供一种确定最小数量的翻转机掩模的相关性测试标识符可以传输到通信设备经由通信网络中的消息。
SOLUTION：方法确定滤波器掩模包括:确定至少一个初始过滤面罩在所有情况下分别相关标识符，这种马马虎虎各自的相关标识符并不马马虎虎的不切题的标识符，组合至少两个初始过滤器掩码作为组合过滤面罩，这种马马虎虎各自的相关标识符与相应的初始过滤器掩模；测试各组合过滤面罩，它为保留器代替其相应的基本初始过滤面罩，当所有不切题的标识符能够进行掩蔽过滤面罩或丢弃的组合，该组合过滤面罩，如果至少一个不切题的标识符马马虎虎。
",H04L12/40
JP特開2002-305524(P2002-305524A)A,JP特願2002-4771(P2002-4771),ベクター インフォマティク ゲーエムベーハー,"要解决的问题：提供一种测试方法的相关性测试标识符能够经由通信网络发送给通信设备的消息中，测试模块，因此生成模块用于相关性表为性急的方法。
SOLUTION：本发明的测试方法，第一位置在相关性表是追求标识符基于计算尺应用于标识符或基于第二可选位置，后者使用装上规则应用于该标识符的情况中，冲突，其中第一位置不可用直到可用位置被找到。该标识符被指定为挨边，当在第一或第二位置标识符或相关联的值，它是输入也可以为是不相关的，当在第一或第二位置时不使用标记被输入。
",H04L12/40
US20020147562A1,US10117052,VECTOR INFORMATIK GMBH,"一种确定过滤面罩的相关性检测的标识符，其能够经由通信网络发送给通信设备的消息中，具有以下步骤：确定相关性标识符相关的通信，确定标识符不切题的偏离度的通信手段，确定至少一个初始过滤面罩在所有情况下分别相关性标识符，可以把该马马虎虎各自的相关性标识符并且不马马虎虎偏离度的标识符，组合至少两个初始过滤器掩码作为组合过滤面罩，这种相关性马马虎虎相应标识符与相应的初始过滤器掩码，并测定各组合过滤面罩，它为保留器代替其相应的基本初始过滤面罩，如果所有标识符能够偏离度进行掩蔽组合过滤面罩或为拒绝，此仇不报非君子偏离度的至少一个标识符。
",G06F19/00
EP1248430A1,EP01108787.1,Vector Informatik GmbH,"过滤面罩测定方法对于通信设备（11，12，13）确定有关的和不相关的字符，具有用于每一并联字符的至少一个输出滤波器掩模的规定，结合提供得到所有的不相关的字符被消除的检查的证实的一个组合过滤器掩模。此外包括用于下文的独立权利要求：（a）使用组合过滤器的一通信设备为传输的字符的相关性测试掩盖；（b）用于对用于传输的字符的相关性测试的过滤面罩的测定的一发电模块；（c）具有用于对用于传输的字符的相关性测试的过滤面罩的测定的一发电模块的一开发工具，一件程序设计工具，或一通信设备
",H04L29/06
US20020095520A1,US10036456,PROF. VECTOR INFORMATIK GMBH,"测试方法相关性测试标识符能够经由通信网络发送给通信设备的消息中，测试模块，因此生成模块用于相关性表为性急的方法。在第一位置，该试验方法的相关性表是追求标识符基于计算尺应用于标识符或基于第二备选的位置到后者使用装上规则应用于该标识符的情况中，冲突，其中第一位置不可用直到可用位置被找到。该标识符被指定为挨边，当在第一或第二位置标识符或相关联的值，它是输入也可以为是不相关的，当在第一或第二位置时不使用标记被输入。
",G06F15/16
EP1223725A1,EP01100723.4,Vector Informatik GmbH,"通过一计算规则，方法涉及确定第一相关性搁置放置，在碰撞的情形下通过一增量规则确定至少一个替换地方直到发现不与另一标识符相关联的采空区，分类标识符同样有关的如果它或一结合值在被输入到第一或第二地方和分类它如同不相关的如果进行一不使用条目到第一或第二地方。方法涉及使用一计算规则确定用于标识符的在一张相关性表中的一第一名，确定至少一个替换地方，在碰撞的情形下使用一增量规则，因此直到发现不与另一标识符相关联的一采空区，第一名不可用，分类标识符同样有关的如果标识符或一结合值进入第一或第二地方和分类它作为不相关的如果进行一不使用条目到第一或第二地方。独立权利要求还包括下文：一种用于产生用于一通信设备的一张相关性表，用于相关性检查，一发电模块的一测试模块的方法，其用于产生一张相关性表，其用于测试模块和/或一发电模块的一通信设备、一通信设备、一辆机动车和/或开发设备和一储存介质。
",H04L29/12