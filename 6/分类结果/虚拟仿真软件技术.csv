﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN117745909A,CN202311741028.6,"本发明涉及一种基于Unity引擎的高阶辅助驾驶超声波传感器数据渲染方法，包括以下步骤：步骤1：建模制作椭圆屏幕并展平UV以用作渲染的画布，U方向用于区分各雷达区范围，V方向用于控制水波纹的距离；步骤2：在un ity引擎中，通过shader在这个画布上区分出对应数量和位置的雷达区；步骤3：在每个雷达区内再细分为前段、中段、后段；步骤4：通过计算使每两段相邻雷达区连接部分的前段、后段保持平滑衔接，并通过偏移UV的V方向来控制水波纹的距离；步骤5：通过脚本将雷达数据传入shader，完成PDC弧段的渲染。其能够改善弧段衔接效果、实现动态曲率变化、并满足用户对于复杂需求的多样性。
",G06T15/00,上海怿星电子科技有限公司,虚拟仿真软件技术,2.0,IPC分类号匹配: G06T15/00,"实现, 包括, 方法, 控制, 计算","雷达, 驾驶, 传感器, 控制"
