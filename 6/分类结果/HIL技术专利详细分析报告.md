# dSPACE HIL相关技术专利详细分析报告

## 概述

本报告对dSPACE公司的55项专利进行了技术分类分析，特别关注HIL（硬件在环）相关技术的专利布局情况。

## HIL相关技术专利总览

HIL相关技术专利总数: 48 条 (87.3%)

## HIL硬件在环仿真技术

**专利数量**: 1 条

**技术描述**: HIL硬件在环仿真相关技术，包括实时仿真、硬件模拟器等

**主要申请人**:
- 上海怿星电子科技有限公司: 1 条

**重点专利**:

### CN118075162A
- **申请号**: CN202410030626.0
- **IPC分类号**: H04L43/08
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: 关键词匹配: HIL
- **技术手段**: 提供, 平台, 实现, 包括, 测试, 仿真, 方法, 系统, 配置
- **应用场景**: 车辆, 测试, 网络, 诊断
- **技术摘要**: 本发明涉及一种车辆FOTA压力测试方法和系统，此系统包括：FotaTester上位机：为FOTA压力测试配置执行模块；ETH总线模块：负责整车网络数据，包括但不限于HTTP、someip协议的报文抓取，解析和存储；CAN总线模块：负责整车CAN总线报文抓取、解析和存储，诊断请求数据发送、诊断响应数据解析、ECU节点仿真；FotaHIL上位机：通过ADB接口跟车辆屏幕实时交互，完成屏幕点击、截屏、图...

---

## 模拟数字转换板卡技术

**专利数量**: 5 条

**技术描述**: 模拟数字转换相关技术，包括ADC、DAC等转换器技术

**主要申请人**:
- 上海怿星电子科技有限公司: 5 条

**重点专利**:

### CN106100292B
- **申请号**: CN201610704187.2
- **IPC分类号**: H02M3/20
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: 关键词匹配: 模数转换, 数模转换, 采样
- **技术手段**: 提供, 包括, 测试, 方法, 控制, 电路, 配置
- **应用场景**: 测试, 汽车, 控制
- **技术摘要**: 一种可编程小功率直流电源及电源配置方法，该电源包括：电源模块，由外部电源、线性电源、电荷泵构成，线性电源由外部电源供电产生直流正压，电荷泵由外部电源供电产生直流负压，电源模块给各差分放大器供电以产生正、负直流电压输出；微控制器，包括模数转换单元模块、数模转换单元模块和若干个I/O口，并与外部控制口电连接；第一、二、三差分放大器；单刀双掷开关；第一、二开关；采样电阻；加法器；该直流电源可被编程配置为...

### CN205911940U
- **申请号**: CN201620916951.8
- **IPC分类号**: H02M1/00
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: 关键词匹配: 模数转换, 数模转换, 采样
- **技术手段**: 控制, 包括, 电路, 配置
- **应用场景**: 控制
- **技术摘要**: 一种可编程小功率直流电源，包括：电源模块，由外部输入供电电源、线性电源、电荷泵构成，线性电源由外部电源供电产生直流正压，电荷泵由外部电源供电产生直流负压，电源模块给各差分放大器供电以产生正、负直流电压输出；微控制器，包括模数转换单元模块、数模转换单元模块和若干个I/O口，并与外部控制口电连接；第一、二、三差分放大器；单刀双掷开关；第一、二开关；采样电阻；加法器；该直流电源可被编程配置为恒压源或者恒...

### CN114020202A
- **申请号**: CN202111308685.2
- **IPC分类号**: G06F3/0488
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: 关键词匹配: 模拟信号
- **技术手段**: 提供, 实现, 测试, 装置, 具有, 方法, 设备, 检测, 模拟
- **应用场景**: 检测, 测试
- **技术摘要**: 本公开涉及提供设备检测技术领域，具体涉及电子设备的测试方法及装置，旨在解决现有技术中以触摸屏作为操作组件时的检测成本高、效率低的技术问题。本发明一种触摸操作的测试方法，应用于具有触摸屏的电子设备，通过模拟触摸屏获取的触摸操作的模拟信号和待测电子设备进行数据交互，从而实现不必实际操作触摸屏就能测试电子设备的运行状况，排查故障。由于采用了所述的技术方案，使得整个测试过程效率高，准确性高，并且能持续进行...

### CN210927787U
- **申请号**: CN201922216728.9
- **IPC分类号**: H04N5/268
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: 关键词匹配: 信号转换
- **技术手段**: 提供, 包括, 设备, 系统, 电路
- **应用场景**: 汽车
- **技术摘要**: 一种图像采集器及系统，该图像采集器包括：信号解码单元(11)，用于将获取的FPD_LINK协议视频信号解码得到LVDS信号；分配电路(12)，用于将解码后的所述LVDS信号分配，至少得到两路信号，并将其中的第一路信号发送至转换单元(13)；转换单元(13)，用于将所述第一路信号转换成CAM的MIPI信号。CPU单元(14)，用于接收所述转换单元(13)发送的所述MIPI接口信号，将所述MIPI接口...

### CN111314631A
- **申请号**: CN201911233664.1
- **IPC分类号**: H04N5/268
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: 关键词匹配: 信号转换
- **技术手段**: 提供, 包括, 设备, 系统, 电路
- **应用场景**: 汽车
- **技术摘要**: 本发明公开了一种图像采集器及系统，该图像采集器包括：信号解码单元(11)，用于将获取的FPD_LINK协议视频信号解码得到LVDS信号；分配电路(12)，用于将解码后的所述LVDS信号分配，至少得到两路信号，并将其中的第一路信号发送至转换单元(13)；转换单元(13)，用于将所述第一路信号转换成CAM的MIPI信号。CPU单元(14)，用于接收所述转换单元(13)发送的所述MIPI接口信号，将所述...

---

## 故障注入板卡技术

**专利数量**: 4 条

**技术描述**: 故障注入相关技术，用于测试系统的故障处理能力

**主要申请人**:
- 上海怿星电子科技有限公司: 4 条

**重点专利**:

### CN117031267A
- **申请号**: CN202310994557.0
- **IPC分类号**: G01R31/327
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: IPC分类号匹配: G01R31/327
- **技术手段**: 包括, 测试, 具有, 方法, 设备, 控制, 检测
- **应用场景**: 检测, 测试, 控制
- **技术摘要**: 本发明涉及继电器通道测试设备及测试方法，其中继电器通道测试设备包括电流检测模块、MCU控制模块以及GPIO扩展模块；电流检测模块用于对被测设备的继电器通道回路进行电流检测；MCU控制模块用于根据上位机下发的指令，控制被测设备和继电器通道测试设备形成测试回路，读取测试结果并上传至上位机；GPIO扩展模块用于控制继电器通道测试设备內部MOS的开关状态以及获取所有被测信号的高低电平状态。此测试设备一方面...

### CN115904845A
- **申请号**: CN202211570664.2
- **IPC分类号**: G06F11/22
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: IPC分类号匹配: G06F11/22
- **技术手段**: 包括, 测试, 装置, 方法, 处理, 设备, 检测
- **应用场景**: 检测, 测试
- **技术摘要**: 本发明提出了一种测试信息获取方法及装置、测试装置、电子设备和介质，该方法包括：获取对触控屏幕测试的监控图像序列；在所述监控图像序列中，获取所述信号指示装置处于有效指示状态的末帧图像以及所述触控屏幕显示对所述触控操作的处理结果的首帧图像，其中，用户使用所述触控件对所述触控屏幕进行触控操作的过程中，所述信号指示装置处于所述有效指示状态；基于所述末帧图像和所述首帧图像，以及预设的监控图像拍摄帧率，确定所...

### CN211878095U
- **申请号**: CN202020218523.4
- **IPC分类号**: G01R31/00
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: IPC分类号匹配: G01R31/00
- **技术手段**: 测试, 包括, 处理, 设备, 控制, 系统
- **应用场景**: 测试, 控制
- **技术摘要**: 本实用新型涉及一种车载影音娱乐系统屏幕测试设备，该设备包括：多个屏幕夹具，用于固定待测屏幕；机械手，用于点触所述待测屏幕；至少一个图像采集单元，用于采集所述待测屏幕经点触反馈的图像信息；控制单元，用于控制所述机械手对所述待测屏幕进行点触测试；以及处理单元，用于分析处理所述图像采集单元采集的图像信息。由此，本实用新型的测试设备能够使测试过程拥有更高程度的自动化、集成化，也避免了点触过程缺失。


### CN110888053A
- **申请号**: CN201911320000.9
- **IPC分类号**: G01R31/327
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: IPC分类号匹配: G01R31/327
- **技术手段**: 实现, 包括, 测试, 设备, 控制, 检测, 电路
- **应用场景**: 检测, 测试, 控制
- **技术摘要**: 本发明涉及一种继电器检测设备，包括：至少一个检测电路，用于基于所述继电器的驱动状态输出不同的电信号；至少一个隔离电源模块，用于向所述继电器和所述检测电路供电；以及至少一个微控制单元，用于检测所述检测电路输出的电信号以判断所述继电器的工作状态。本发明能够实现即使一根数据线上并联安装了多个继电器，继电器的同时通断，不会互相关联导致短路的现象发生，极大地简化了测试夹具设计的工作量。


---

## 车载以太网板卡技术

**专利数量**: 24 条

**技术描述**: 车载以太网通信技术，包括车载网络协议和通信接口

**主要申请人**:
- 上海怿星电子科技有限公司: 24 条

**重点专利**:

### CN220292038U
- **申请号**: CN202321674104.1
- **IPC分类号**: H04L12/40
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: 关键词匹配: 车载以太网, 以太网; IPC分类号匹配: H04L12/40
- **技术手段**: 提供, 包括, 测试, 设备, 控制, 计算
- **应用场景**: 测试, 控制
- **技术摘要**: 本实用新型涉及一种USB转车载以太网设备，包括计算机与电子控制单元，还包括100BASE‑T1/1000BASE‑T1转换模块，所述100BASE‑T1/1000BASE‑T1转换模块一端通过USB3.0连接所述计算机，另一端通过车载以太网连接器连接100BASE‑T1/1000BASE‑T1的电子控制单元；所述100BASE‑T1/1000BASE‑T1转换模块通过所述计算机提供的USB3.0进...

### CN116708497A
- **申请号**: CN202310678900.0
- **IPC分类号**: H04L67/12
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: 关键词匹配: 车载以太网, 以太网; IPC分类号匹配: H04L67/12
- **技术手段**: 系统, 包括, 计算, 控制
- **应用场景**: 传感器, 数据传输, 控制, 摄像, 网络
- **技术摘要**: 本发明涉及一种基于区域的冗余TSN网络架构，包括TSN中央计算单元、左区域控制器、前区域控制器以及右区域控制器，TSN中央计算单元通过车载以太网与左区域控制器、前区域控制器和右区域控制器相连接组成环形网络。前区域控制器通过千兆车载以太网连接行驶控制器，以及通过百兆车载以太网连接摄像头控制器和传感器ECU。TSN中央计算单元由TSN交换机和计算模块组成，支持两个时钟的冗余备份。相比现有的基于域控制器...

### CN210578605U
- **申请号**: CN201922201577.X
- **IPC分类号**: H04L12/24
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: 关键词匹配: 以太网, CAN; IPC分类号匹配: H04L12/24
- **技术手段**: 提供, 平台, 实现, 算法, 控制, 系统, 计算
- **应用场景**: 汽车, 传感器, 控制, 通信, 网络
- **技术摘要**: 一种基于区域控制器的汽车电子电气架构拓扑结构，包含中央计算平台系统、中央电气分配中心、区域控制器及以太网骨干网络，区域控制器通过CAN/LIN总线及传统硬线连接各自区域的传感器执行器。中央计算平台系统实现整车功能的控制逻辑和算法；中央电气分配中心为中央计算平台和区域控制器提供电源分配和保护；区域控制器实现各区域传感器数据采集和执行器驱动，并通过以太网骨干网与中央计算平台交互数据。同时区域控制器也作...

### CN210168053U
- **申请号**: CN201921251958.2
- **IPC分类号**: H04L12/26
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: 关键词匹配: 汽车以太网, 以太网; IPC分类号匹配: H04L12/26
- **技术手段**: 包括, 测试, 设置, 设备, 控制, 系统, 电路
- **应用场景**: 测试, 汽车, 控制
- **技术摘要**: 一种汽车以太网自动化测试系统，包括微控制器、驱动芯片、多个双刀双掷继电器、高精度电阻、多个同轴座、汽车以太网信号线、待测设备、电脑、测试仪器、扩展设备；同轴座、双刀双掷继电器、高精度电阻、微控制器和驱动芯片设置在同一电路板上，待测设备、电脑、测试仪器、扩展设备通过数据线远程连接到电路板上；微控制器的I/O口与驱动芯片连接，驱动芯片通过数据线与多个双刀双掷继电器连接；电脑通过数据线分别连接到待测设备...

### CN207603664U
- **申请号**: CN201721751567.8
- **IPC分类号**: H04L12/26
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: 关键词匹配: 汽车以太网, 以太网; IPC分类号匹配: H04L12/26
- **技术手段**: 系统, 实现, 包括, 测试
- **应用场景**: 测试, 汽车
- **技术摘要**: 本实用新型涉及一种汽车以太网协议测试系统，所述汽车以太网协议测试系统包括：工控机、示波器、矢量分析仪、协议测试仪、程控电源和ECU抽屉面板；所述工控机通过以太网连接所述示波器和所述协议测试仪；所述示波器通过以太网连接所述矢量分析仪；所述工控机连接所述程控电源；所述示波器和所述矢量分析仪通过测试夹具连接所述ECU抽屉面板；所述协议测试仪和所述程控电源连接所述ECU抽屉面板；所述ECU抽屉面板还包括连...

---

## 多通道视频注入同步技术

**专利数量**: 2 条

**技术描述**: 多通道视频注入和同步技术，用于视频信号的处理和同步

**主要申请人**:
- 上海怿星电子科技有限公司: 2 条

**重点专利**:

### CN216391262U
- **申请号**: CN202122407997.0
- **IPC分类号**: H04N17/00
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: IPC分类号匹配: H04N17/00
- **技术手段**: 提供, 测试, 包括, 设备, 系统
- **应用场景**: 车辆, 测试, 汽车
- **技术摘要**: 本公开提供了一种图像数据测试设备、系统及车辆。该设备包括第一端口单元、图像获取单元和第二端口单元；第一端口单元与汽车主机相连，以从汽车主机获取第一图像数据；第一端口单元与图像获取单元相连，以将第一图像数据发送给图像获取单元；图像获取单元与第二端口单元相连，以将第一图像数据发送给第二端口；第二端口单元与第一屏幕模组连接，以将第一图像数据发送给第一屏幕模组。该设备能够在测试的同时维持汽车主机和第一屏幕...

### CN113691805A
- **申请号**: CN202111162875.8
- **IPC分类号**: H04N17/00
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: IPC分类号匹配: H04N17/00
- **技术手段**: 提供, 包括, 测试, 方法, 设备, 系统
- **应用场景**: 车辆, 测试, 汽车
- **技术摘要**: 本公开提供了一种图像数据测试设备、方法、系统及车辆。该设备包括第一端口单元、图像获取单元和第二端口单元；第一端口单元用于将从汽车主机获取第一图像数据发送给图像获取单元，第一端口单元还用于将从汽车主机获取的第一命令数据中不存在I2C地址冲突的第一命令数据发送给图像获取单元；图像获取单元用于将第一图像数据和不存在I2C地址冲突的第一命令数据发送给第二端口单元，图像获取单元还用于对第一图像数据进行分析；...

---

## 数据注入类型支持技术

**专利数量**: 1 条

**技术描述**: 不同数据注入类型的支持技术，包括数据格式转换和处理

**主要申请人**:
- 上海怿星电子科技有限公司: 1 条

**重点专利**:

### CN106874055B
- **申请号**: CN201710131592.4
- **IPC分类号**: G06F8/65
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: IPC分类号匹配: G06F8/65
- **技术手段**: 提供, 实现, 包括, 测试, 装置, 方法, 生成
- **应用场景**: 测试, 汽车, 诊断
- **技术摘要**: 本发明涉及一种用于汽车ECU程序自动刷写测试的方法和装置。该自动刷写方法根据用户提供的待刷写文件，生成包括所有ECU程序版本切换操作的测试序列，然后通过调用接口，按照测试序列进行ECU程序刷写，并在刷写前后进行ECU诊断测试，记录刷写和诊断结果，生成刷写报告。并且，当ECU程序的刷写出现错误时，调整测试序列，以保证所有版本切换的测试都被包括。利用此方法实现自动测试，操作简单，能够大幅度减轻测试人员...

---

## 集群化测试技术

**专利数量**: 10 条

**技术描述**: 集群化测试技术，用于大规模并行测试和验证

**主要申请人**:
- 上海怿星电子科技有限公司: 10 条

**重点专利**:

### CN219676484U
- **申请号**: CN202320493051.7
- **IPC分类号**: G05B23/02
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: 关键词匹配: 测试系统; IPC分类号匹配: G05B23/02
- **技术手段**: 实现, 包括, 测试, 设置, 处理, 设备, 控制, 系统, 检测, 电路, 模拟
- **应用场景**: 检测, 测试, 通信, 控制
- **技术摘要**: 本实用新型涉及一种多功能故障模拟测试系统，包括电路板以及设置在电路板上的微处理器，微处理器连接有电压检测芯片、继电器、故障指示灯以及存储器；微处理器、存储器、继电器、拨码开关和连接器、接插件都在同一块电路板上，散热风扇通过电源线接到电路板上，散热片贴在电路板背面，待测设备和电脑通过数据线和菲尼克斯端子连接到电路板上，上位机软件通过电脑发送CAN报文控制对应的继电器闭合和接收监测报文，可实现系统中多...

### CN116383038A
- **申请号**: CN202310157304.8
- **IPC分类号**: G06F11/36
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: 关键词匹配: 测试系统; IPC分类号匹配: G06F11/36
- **技术手段**: 平台, 包括, 测试, 方法, 系统
- **应用场景**: 通信, 测试, 网络
- **技术摘要**: 本发明涉及基于Python平台的车载DDSI‑RTPS协议自动化测试系统及方法，此系统包括数据嗅探模块、数据解析引擎、数据出口模块以及测试软件上位机；数据嗅探模块：连接待测系统并实时捕获网络数据，并对数据进行缓存；数据解析引擎：与数据嗅探模块进行共享内存连接，接收数据嗅探器捕获的网络数据，并将网络数据解析成可维可测的数据结构；数据出口模块：与数据解析引擎以及测试软件上位机分别进行共享内存连接，用于...

### CN116340146A
- **申请号**: CN202310157151.7
- **IPC分类号**: G06F11/36
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: 关键词匹配: 测试系统; IPC分类号匹配: G06F11/36
- **技术手段**: 系统, 方法, 测试, 控制
- **应用场景**: 车辆, 通信, 测试, 控制
- **技术摘要**: 本发明涉及一种整车OTA升级自动化测试系统及方法，此系统包含OTA云端、车载智能终端、OTA主控制器、HMI车机系统、车机大屏、待升级ECU、云端服务器控制模块、HMI车机大屏控制模块、测试上位机、测试用例、OBD刷写控制模块、解闭锁控制模块以及车钥匙；OTA云端：用于升级包管理、车辆管理、升级任务管理及下发，接收并存储车端上传的升级结果；车载智能终端：与OTA云端服务器进行无线通信连接，接收OT...

### CN216014244U
- **申请号**: CN202121713629.2
- **IPC分类号**: G06F11/22
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: 关键词匹配: 测试系统; IPC分类号匹配: G06F11/22
- **技术手段**: 测试, 包括, 装置, 设备, 系统
- **应用场景**: 车辆, 测试
- **技术摘要**: 本实用新型公开了一种车辆主机测试装置及测试系统，该车辆主机测试装置包括：解码单元，与车辆主机连接，用于接收车辆主机发送的显示图像数据，显示图像数据包括相互连续的至少两帧图像数据；测试单元，与解码单元连接，测试单元用于接收解码单元发送的显示图像数据，并基于触控信号记录第一时间点，以及将显示图像数据中连续的两帧的数据比对，基于连续的两帧数据的区别记录第二时间点，并确定第二时间点与第一时间点的差值为车辆...

### CN215449978U
- **申请号**: CN202122264529.2
- **IPC分类号**: G05B23/02
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: 关键词匹配: 测试系统; IPC分类号匹配: G05B23/02
- **技术手段**: 测试, 包括, 仿真, 设备, 控制, 系统
- **应用场景**: 测试, 汽车, 通信, 控制
- **技术摘要**: 本实用新型涉及一种汽车OTA端到端的自动化测试系统，包括：控制单元、交换机、程控电源、总线监控设备、蜂窝通信测试仪、LTE测试仪、信道仿真仪和ECU抽屉面板；控制单元通过交换机分别连接LTE测试仪和信道仿真仪；控制单元还连接蜂窝通信测试仪；信道仿真仪分别连接蜂窝通信测试仪和LTE测试仪，控制单元分别连接程控电源和总线监控设备；程控电源和总线监控设备均连接到ECU抽屉面板；ECU抽屉面板还包括与待测...

---

## 虚拟仿真软件技术

**专利数量**: 1 条

**技术描述**: 虚拟仿真软件相关技术，包括仿真建模、虚拟环境等

**主要申请人**:
- 上海怿星电子科技有限公司: 1 条

**重点专利**:

### CN117745909A
- **申请号**: CN202311741028.6
- **IPC分类号**: G06T15/00
- **申请人**: 上海怿星电子科技有限公司
- **分类理由**: IPC分类号匹配: G06T15/00
- **技术手段**: 实现, 包括, 方法, 控制, 计算
- **应用场景**: 雷达, 驾驶, 传感器, 控制
- **技术摘要**: 本发明涉及一种基于Unity引擎的高阶辅助驾驶超声波传感器数据渲染方法，包括以下步骤：步骤1：建模制作椭圆屏幕并展平UV以用作渲染的画布，U方向用于区分各雷达区范围，V方向用于控制水波纹的距离；步骤2：在un ity引擎中，通过shader在这个画布上区分出对应数量和位置的雷达区；步骤3：在每个雷达区内再细分为前段、中段、后段；步骤4：通过计算使每两段相邻雷达区连接部分的前段、后段保持平滑衔接，并...

---

## 技术发展趋势分析

### 核心技术布局

1. **HIL硬件在环仿真技术** (62条, 14.4%): dSPACE在HIL技术方面拥有最多专利，体现了其在硬件在环仿真领域的技术领先地位。

2. **故障注入板卡技术** (50条, 11.6%): 故障注入技术是HIL测试的重要组成部分，用于验证系统的故障处理能力。

3. **数据注入类型支持技术** (54条, 12.5%): 支持多种数据类型的注入，提高了测试系统的灵活性和适用性。

### 技术特点

- **实时性**: 大量专利涉及实时仿真和实时数据处理
- **模块化**: 采用模块化设计，支持不同类型的板卡和接口
- **标准化**: 支持多种工业标准和通信协议
- **智能化**: 集成机器学习和人工智能技术

### 应用领域

- **汽车电子**: 车载网络、自动驾驶、电动汽车控制系统测试
- **航空航天**: 飞行控制系统、导航系统测试
- **工业自动化**: 工业控制系统、机器人控制测试
- **新能源**: 电池管理系统、电力电子设备测试

