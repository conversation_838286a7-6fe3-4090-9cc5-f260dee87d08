﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN117031267A,CN202310994557.0,"本发明涉及继电器通道测试设备及测试方法，其中继电器通道测试设备包括电流检测模块、MCU控制模块以及GPIO扩展模块；电流检测模块用于对被测设备的继电器通道回路进行电流检测；MCU控制模块用于根据上位机下发的指令，控制被测设备和继电器通道测试设备形成测试回路，读取测试结果并上传至上位机；GPIO扩展模块用于控制继电器通道测试设备內部MOS的开关状态以及获取所有被测信号的高低电平状态。此测试设备一方面具有通用性，针对某一类包含继电器的产品，提出具有通用性的测试方案，几乎能适配所有带继电器的产品，降低了产品的整体研发成本；另一方面此检查设备具有可扩展性，由于不同产品继电器数量有差异，提出具有继电器数量可扩展的测试方案。
",G01R31/327,上海怿星电子科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/327,"包括, 测试, 具有, 方法, 设备, 控制, 检测","检测, 测试, 控制"
CN115904845A,CN202211570664.2,"本发明提出了一种测试信息获取方法及装置、测试装置、电子设备和介质，该方法包括：获取对触控屏幕测试的监控图像序列；在所述监控图像序列中，获取所述信号指示装置处于有效指示状态的末帧图像以及所述触控屏幕显示对所述触控操作的处理结果的首帧图像，其中，用户使用所述触控件对所述触控屏幕进行触控操作的过程中，所述信号指示装置处于所述有效指示状态；基于所述末帧图像和所述首帧图像，以及预设的监控图像拍摄帧率，确定所述触控屏幕对用户操作的响应时间。本发明的技术方案，降低了触控屏幕对用户操作的响应时间的检测难度，同时也能够获得更为准确的检测结果，提升了响应时间检测的便利性和准确性。
",G06F11/22,上海怿星电子科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/22,"包括, 测试, 装置, 方法, 处理, 设备, 检测","检测, 测试"
CN211878095U,CN202020218523.4,"本实用新型涉及一种车载影音娱乐系统屏幕测试设备，该设备包括：多个屏幕夹具，用于固定待测屏幕；机械手，用于点触所述待测屏幕；至少一个图像采集单元，用于采集所述待测屏幕经点触反馈的图像信息；控制单元，用于控制所述机械手对所述待测屏幕进行点触测试；以及处理单元，用于分析处理所述图像采集单元采集的图像信息。由此，本实用新型的测试设备能够使测试过程拥有更高程度的自动化、集成化，也避免了点触过程缺失。
",G01R31/00,上海怿星电子科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/00,"测试, 包括, 处理, 设备, 控制, 系统","测试, 控制"
CN110888053A,CN201911320000.9,"本发明涉及一种继电器检测设备，包括：至少一个检测电路，用于基于所述继电器的驱动状态输出不同的电信号；至少一个隔离电源模块，用于向所述继电器和所述检测电路供电；以及至少一个微控制单元，用于检测所述检测电路输出的电信号以判断所述继电器的工作状态。本发明能够实现即使一根数据线上并联安装了多个继电器，继电器的同时通断，不会互相关联导致短路的现象发生，极大地简化了测试夹具设计的工作量。
",G01R31/327,上海怿星电子科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/327,"实现, 包括, 测试, 设备, 控制, 检测, 电路","检测, 测试, 控制"
