﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN220383073U,CN202320493045.1,"本实用新型涉及一种万兆以太网转换器，包括一体式壳体，一体式壳体上设置有五个拨码开关，一体式壳体的内部设置有PHY芯片、主控芯片、散热片以及两个存储器，主控芯片通过MDIO接口控制PHY芯片，PHY芯片通过SerDes接口连接电模块；主控芯片通过读取拨码开关的逻辑电平，确认当前需要配置的模式和速率，根据需要配置的模式和速率通过MDIO接口配置PHY芯片，PHY芯片通过SerDes接口与电模块的SerDes接口连接进行数据转换，完成Tx到T1信号的转换。其配合电模块使用，可以实时监控链路的连接状态、实时控制数据传输速率和工作模式。
",H04L12/02,上海怿星电子科技有限公司,车载以太网板卡技术,3.0,关键词匹配: 以太网; IPC分类号匹配: H04L12/02,"控制, 包括, 设置, 配置","数据传输, 控制"
CN220292038U,CN202321674104.1,"本实用新型涉及一种USB转车载以太网设备，包括计算机与电子控制单元，还包括100BASE‑T1/1000BASE‑T1转换模块，所述100BASE‑T1/1000BASE‑T1转换模块一端通过USB3.0连接所述计算机，另一端通过车载以太网连接器连接100BASE‑T1/1000BASE‑T1的电子控制单元；所述100BASE‑T1/1000BASE‑T1转换模块通过所述计算机提供的USB3.0进行供电，测试过程中所述计算机中的数据会通过USB3.0两对差分线数据转换为一对双绞线的100BASE‑T1/1000BASE‑T1数据。
",H04L12/40,上海怿星电子科技有限公司,车载以太网板卡技术,4.0,"关键词匹配: 车载以太网, 以太网; IPC分类号匹配: H04L12/40","提供, 包括, 测试, 设备, 控制, 计算","测试, 控制"
CN220139579U,CN202321674096.0,"本实用新型涉及一种分布式车载以太网自动化测试系统，包括云端服务器以及分别与所述云端服务器连接的测试上位机、至少两个的单机测试系统，所述云端服务器用于管理测试系统；所述测试上位机用于自动化测试的管理，包括测试用例的编写、测试序列的下发、测试结果的统计、及缺陷管理，所述单机测试系统用于测试车载以太网的网络通信。其很好的解决了车载以太网自动化测试系统高成本硬件问题，同时具备分布式自动化测试能力，提升自动化测试效率。
",H04L43/06,上海怿星电子科技有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载以太网, 以太网","系统, 测试, 包括","通信, 测试, 网络"
CN220022822U,CN202320493052.1,"本实用新型涉及一种车载以太网互操作性测试系统，包括上位机软件，还包括与上位机软件连接的数据处理模块，数据处理模块连接有测试用例执行模块、PHY状态反馈模块以及故障注入模块，测试用例执行模块连接有数据反馈模块，故障注入模块连接有噪声注入模块，数据反馈模块与噪声注入模块共同连接有数据路由模块，数据路由模块连接有DUT；上位机软件通过电脑发送UDP报文与下位机进行数据通信，可控制下位机内部的PHY芯片以及测试线路，实现Link Partner的功能，同时上位机通过下位机的T1、CAN、CANFD、UART四种通信口与DUT进行通讯交互，并且通过程控电源电流大小计算DUT工作状态从而实现自动化测试。其改善测试效率低下，以及测试系统消耗资源过多的问题。
",H04L43/50,上海怿星电子科技有限公司,车载以太网板卡技术,3.0,"关键词匹配: 车载以太网, 以太网, CAN","实现, 包括, 测试, 处理, 控制, 系统, 计算","通信, 测试, 控制"
CN116896516A,CN202310994555.1,"本发明涉及一种基于秒脉冲方法的时间同步精度测试系统及方法，此系统包括硬件模块和以及设置在计算机中的软件模块，硬件模块通过采集接口连接被测件秒脉冲信号，硬件模块和计算机之间通过以太网实现信息交互；硬件模块提供足够数量的采集通道，负责捕捉秒脉冲信号，通过以太网实时传输秒脉冲的沿变时间戳信息；软件模块运行在计算机之上，利用计算机网卡接收硬件模块发送的数据，执行专用算法处理数据并计算各从通道时钟同步精度，通过人机交互界面实时监测测试过程和测试数据，最终生成测试报告和数据记录文件。此系统支持多通道的秒脉冲信号采集，可高效且方便的实现车载网络时钟同步精度的测试。
",H04L43/08,上海怿星电子科技有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载网络, 以太网","提供, 实现, 包括, 测试, 算法, 方法, 设置, 处理, 生成, 系统, 计算","测试, 网络"
CN116708497A,CN202310678900.0,"本发明涉及一种基于区域的冗余TSN网络架构，包括TSN中央计算单元、左区域控制器、前区域控制器以及右区域控制器，TSN中央计算单元通过车载以太网与左区域控制器、前区域控制器和右区域控制器相连接组成环形网络。前区域控制器通过千兆车载以太网连接行驶控制器，以及通过百兆车载以太网连接摄像头控制器和传感器ECU。TSN中央计算单元由TSN交换机和计算模块组成，支持两个时钟的冗余备份。相比现有的基于域控制器的电子电器架构和点对点的视频传输方式，能够节省线束和高效率利用以太网带宽。环形网络架构支持数据的链路冗余传输，提高了系统数据传输的可靠性。多个时钟的冗余可支持时间同步可靠性和保证网络数据低延迟传输的实时性。
",H04L67/12,上海怿星电子科技有限公司,车载以太网板卡技术,4.0,"关键词匹配: 车载以太网, 以太网; IPC分类号匹配: H04L67/12","系统, 包括, 计算, 控制","传感器, 数据传输, 控制, 摄像, 网络"
CN116707911A,CN202310678901.5,"本发明涉及车载网络架构技术领域，公开了一种网络传入流的计量方法，分为两个方面：第一方面，该方法通过信用来计量传入流的以太网帧，以防御对交换机的DoS攻击；第二方面，该方法通过单独控制网络中每个端口上的传入流，可适应交换机输出端口的基于信用整形算法的行为。本发明的优点是可以与AVB/TSN协议一起实施，以防御对交换机的DoS攻击，同时不影响正常数据流的传输延迟。节省硬件成本。通过单独控制网络中每个端口上的传入流来保护车载网络的完整性和可用性。
",H04L9/40,上海怿星电子科技有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载网络, 以太网","方法, 算法, 控制","网络, 控制"
CN113422706B,CN202110678577.8,"本申请实施例公开了一种检测网络协议栈一致性的方法及车辆，检测网络协议栈一致性的方法包括：构建第一域控制器为测试设备；构建第二域控制器为被测设备；以所述第二域控制器为服务端，在所述第一域控制器和所述第二域控制器之间进行以太网通信测试；使所述第一域控制器向所述第二域控制器发送触发指令；响应所述触发指令，所述第二域控制器为加载了被测端互操作应用的客户端，在所述第二域控制器配合第一域控制器发起的以太网通信测试；其中，所述第一域控制器和所述第二域控制器分别存储响应的测试数据和测试报告。本申请实施例能够节省网络协议栈一致性检测的成本。
",H04L43/18,上海怿星电子科技有限公司,车载以太网板卡技术,1.0,关键词匹配: 以太网,"包括, 测试, 方法, 设备, 控制, 检测","通信, 测试, 控制, 车辆, 检测, 网络"
CN216301005U,CN202122090223.X,"一种高级驾驶辅助系统的传感器采集设备，包括：数据复制模块(100)，用于将路况数据复制为两份，一份用于传输给汽车控制器(200)，另一份用于存储；所述存储的数据用于数据回放。本发明在汽车传感器和控制器(200)之间增加一个实时的数据采集设备，不影响传感器和控制器(200)直接的数据传输的同时对数据进行记录，以获得大量的实际测试的数据，增加实验室开发阶段的数据素材。
",B60R16/023,上海怿星电子科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/023,"测试, 包括, 设备, 控制, 系统","驾驶, 测试, 汽车, 数据传输, 控制, 传感器"
CN113650572A,CN202111023433.5,"一种高级驾驶辅助系统的传感器采集设备及方法，其中，采集设备包括：数据复制模块(100)，用于将路况数据复制为两份，一份用于传输给汽车控制器(200)，另一份用于存储；所述存储的数据用于数据回放。本发明在汽车传感器和控制器(200)之间增加一个实时的数据采集设备，不影响传感器和控制器(200)直接的数据传输的同时对数据进行记录，以获得大量的实际测试的数据，增加实验室开发阶段的数据素材。
",B60R16/023,上海怿星电子科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/023,"包括, 测试, 方法, 设备, 控制, 系统","驾驶, 测试, 汽车, 数据传输, 控制, 传感器"
CN110579992B,CN201910995691.6,"一种车载以太网点对点唤醒/休眠控制系统及方法，当车辆处于停车状态，不再需要以太网通信功能时，通过物理层芯片的休眠过程使以太网电子控制单元进入低能耗模式，降低耗电；在需要以太网通信功能时，通过本地唤醒或者远程唤醒过程唤醒已经进入休眠状态的以太网电子控制单元，使以太网电子控制单元建立链路连接，以恢复以太网通信。还提出了一种车载以太网多节点局部唤醒/休眠控制系统及方法，多节点局部休眠唤醒系统包括网关路由节点和多个单节点电子控制单元，实现了在车载以太网多节点网络中，部分节点需要处于唤醒状态以实现通信过程确保功能实现，而部分节点此时由于本身功能未使用且其他节点无需与其通信而处于休眠状态以实现功耗降低。
",G05B19/042,上海怿星电子科技有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载以太网, 以太网","实现, 包括, 方法, 控制, 系统","车辆, 通信, 网络, 控制"
CN210578605U,CN201922201577.X,"一种基于区域控制器的汽车电子电气架构拓扑结构，包含中央计算平台系统、中央电气分配中心、区域控制器及以太网骨干网络，区域控制器通过CAN/LIN总线及传统硬线连接各自区域的传感器执行器。中央计算平台系统实现整车功能的控制逻辑和算法；中央电气分配中心为中央计算平台和区域控制器提供电源分配和保护；区域控制器实现各区域传感器数据采集和执行器驱动，并通过以太网骨干网与中央计算平台交互数据。同时区域控制器也作为二级电气分配中心，集中给区域内各电器件供电。该架构拓扑结构使整车分散的电器件通过就近的区域控制器集中通信和供电，节约线束，降低成本；同时，功能算力的集中化可减少控制器数量，提升架构的可拓展性。
",H04L12/24,上海怿星电子科技有限公司,车载以太网板卡技术,4.0,"关键词匹配: 以太网, CAN; IPC分类号匹配: H04L12/24","提供, 平台, 实现, 算法, 控制, 系统, 计算","汽车, 传感器, 控制, 通信, 网络"
CN210168053U,CN201921251958.2,"一种汽车以太网自动化测试系统，包括微控制器、驱动芯片、多个双刀双掷继电器、高精度电阻、多个同轴座、汽车以太网信号线、待测设备、电脑、测试仪器、扩展设备；同轴座、双刀双掷继电器、高精度电阻、微控制器和驱动芯片设置在同一电路板上，待测设备、电脑、测试仪器、扩展设备通过数据线远程连接到电路板上；微控制器的I/O口与驱动芯片连接，驱动芯片通过数据线与多个双刀双掷继电器连接；电脑通过数据线分别连接到待测设备、扩展设备A、B和多个测试仪器；测试仪器分别通过各自的同轴座连接到相应的双刀双掷继电器；该测试系统自动化程度高，大大消除了人工干预的人力消耗和错误；减少了测试时间，有效提高了测试效率。
",H04L12/26,上海怿星电子科技有限公司,车载以太网板卡技术,4.0,"关键词匹配: 汽车以太网, 以太网; IPC分类号匹配: H04L12/26","包括, 测试, 设置, 设备, 控制, 系统, 电路","测试, 汽车, 控制"
CN210166153U,CN201921236051.9,"一种汽车信息娱乐功能的自动化测试系统，包括：NI板卡、路由器、机器人控制器、话筒、喇叭、工业相机、触控屏、VT板卡、工控机、程控电源和工业机器人；话筒通过同轴线连接到NI板卡，NI板卡通过以太网分别连接路由器和工业相机，路由器通过以太网分别连接到机器人控制器和工控机，工控机通过以太网连接到VT板卡，VT板卡分别通过数据线和供电线连接被测件，触控屏通过LVDS线连接被测件，喇叭通过数据线连接被测件，程控电源通过数据线连接工控机；触摸屏前布置工业机器人，工业机器人前端安装触控笔；触摸屏垂直上方布置工业相机进行图像信息采集。通过整合多个功能模块，实现统一管理与协调调用，适用性强且方便扩展的自动化测试目的。
",G01M17/007,上海怿星电子科技有限公司,车载以太网板卡技术,1.0,关键词匹配: 以太网,"实现, 包括, 测试, 控制, 系统","测试, 汽车, 控制"
CN210041853U,CN201921240223.X,"一种汽车CAN/LIN总线自动化测试系统，包括：上位机、程控电源、总线监控和控制设备、VT系统、示波器、万用表、被测ECU、接插件以及总线通断控制设备，上位机用于运行自动化测试程序，通过控制程控电源、VT系统、示波器、万用表来完成测试内容；总线监控和控制设备根据上位机指令决定是否处理接收的总线报文以及发送该报文到CAN/LIN总线上；VT系统根据上位机指令控制ECU供电和示波器、万用表是否连接到测试总线上；上位机还用于对总线监控和控制设备的通信报文传输情况和预先存储的判定条件进行比较，来判断被测ECU的相应功能是否符合测试规范要求。本测试系统的自动化程度高，能够极大的节约人力成本，提高测试效率和精度。
",H04L12/26,上海怿星电子科技有限公司,车载以太网板卡技术,3.0,关键词匹配: CAN; IPC分类号匹配: H04L12/26,"测试, 包括, 处理, 设备, 控制, 系统","测试, 汽车, 通信, 控制"
CN210037223U,CN201921236029.4,"一种集成车载信息娱乐系统的测试台架，分为四层暗室，由上到下依次为：第1‑第4暗室；每层暗室均包含一个工业相机和一个控制器夹具；将车载信息娱乐系统的待测控制器安装固定在每个暗室中的控制机夹具上，工业相机安装于可多方向调节的工业相机底座上；每个工业相机均采用以太网与外置电脑进行连接；使工业相机能够清晰的采集待测控制器的全部画面并数据传输至外置电脑处理，外置电脑观察并记录工业相机拍摄的图像以完成测试。该测试台架能够有效弥补车载信息娱乐系统桌面测试中线束混乱，控制器更换繁琐和过于依赖人工判断测试结果的问题；降低了搭建测试环境的时间，提高了测试效率。
",G01M17/007,上海怿星电子科技有限公司,车载以太网板卡技术,1.0,关键词匹配: 以太网,"系统, 测试, 处理, 控制","测试, 数据传输, 控制"
CN110471400A,CN201910813890.0,"一种车载以太网实车测试方法，通过对现有线束进行改造，将各通道的实车线束从中间断开，并用各延长线引出至合适位置，并在各延长线终端安装连接器；车辆静止状态测试时，将内部线束桥接的以太网数据采集模块与各延长线终端的连接器连接，使用以太网数据采集模块及上位机进行以太网数据的实时分析及测试；车辆运动状态测试时，将内部线束桥接的以太网实车数据记录仪与各延长线终端的连接器连接，使用以太网实车数据记录仪进行测试数据记录；跑车测试结束后，将数据记录仪取出连接至上位机，进行以太网的数据分析及处理。有效解决了现有技术中所有线束均捆绑到一起，缺乏有效方法对其进行测试的问题，并可有效分析信号丢失、信号延迟问题。
",G05B23/02,上海怿星电子科技有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载以太网, 以太网","方法, 测试, 处理","车辆, 测试"
CN110460621A,CN201910854362.X,"一种基于SOME/IP协议的车载以太网总线数据与CAN总线数据的转换方法及系统，转换方法包括：接收要转换成以太网数据的CAN总线数据；将若干个所接收的CAN总线数据编码、封装成相应的以太网数据，并发送到对应的以太网设备中；接收要转换成CAN总线数据的以太网数据；将若干个所接收的以太网数据解析并封装成CAN总线数据，并发送到对应的CAN网络设备中；以太网数据包括一个或多个SOME/IP报文数据；将若干个所接收的CAN总线数据编码、封装成相应的以太网数据包括将所接收的CAN总线报文数据的CAN ID编码到SOME/IP报文数据的报头；本发明能够实现汽车内CAN总线协议通信与SOME/IP协议通信间的协议转换，区别于传统的面向信号的通信方式，实现了面向服务架构的车载以太网网关通信。
",H04L29/06,上海怿星电子科技有限公司,车载以太网板卡技术,3.0,"关键词匹配: 车载以太网, 以太网, CAN","实现, 包括, 方法, 设备, 系统","通信, 汽车, 网络"
CN209233850U,CN201920051662.X,"一种便携式单节点网络自动化测试系统，包括测试机箱，由中间层板将机箱分为上、下两层；下层包括开关电源、可编程电源模块、USB集线器和插排；上层包括CAN总线干扰仪、总线示波器、硬件接口卡、接线端子、第一PCB板和第二PCB板；各组成测试设备通过内部线束连接构成总线测试电路，并通过测试机箱前面板转换成标准接口，提供对ECU的统一标准化接口；可编程电源模块输出供电电压至测试机箱的前面板标准接口。该测试系统能够有效地弥补机柜式测试系统不便携带和多个单一测试工具连接繁琐的缺点；同时使得车载控制器的测试环境更加灵活多变，加快开发进程。
",H04L12/26,上海怿星电子科技有限公司,车载以太网板卡技术,3.0,关键词匹配: CAN; IPC分类号匹配: H04L12/26,"提供, 包括, 测试, 设备, 控制, 系统, 工具, 电路","测试, 网络, 控制"
CN207601619U,CN201721751522.0,"本实用新型涉及一种基于BroadR‑Reach以太网的汽车网关原型系统，所述汽车网关原型系统包括：处理器、第一5口汽车以太网交换芯片、第二5口汽车以太网交换芯片、第一双口100Base‑T1以太网PHY收发器、第二双口100Base‑T1以太网PHY收发器、单口100Base‑T1以太网PHY收发器、单口1000Base‑T1以太网PHY收发器和单口10Base‑T/100Base‑Tx/1000Base‑T以太网PHY收发器；以及包括CAN总线接口、LIN总线接口、USB接口、RS232接口、WIFI接口、蓝牙接口、LTE接口和供电接口。该原型系统能够快速搭建基于BroadR‑Reach以太网的汽车网关软硬件开发平台，用于基于BroadR‑Reach以太网的汽车网关的开发和验证。
",G05B23/02,上海怿星电子科技有限公司,车载以太网板卡技术,3.0,"关键词匹配: 汽车以太网, 以太网, CAN","系统, 平台, 包括, 处理","汽车, 验证"
CN207601577U,CN201721751654.3,"本实用新型涉及一种基于BroadR‑Reach以太网的汽车液晶仪表原型系统，所述汽车液晶仪表原型系统包括：处理器、CAN收发器、BroadR‑Reach物理层模块、第一DB9模块、第二DB9模块、USB接口、数据采集视频接口、音频接口和LVDS接口；其中，所述处理器与所述CAN收发器及所述BroadR‑Reach物理层模块分别相连接；所述处理器与所述USB接口、所述数据采集视频接口、所述音频接口和所述LVDS接口分别连接。该汽车液晶仪表原型系统，能够使开发人员根据具体车型的应用做相应的软硬件裁减及应用层功能的开发。使用上述汽车液晶仪表原型系统能够大大降低开发难度，缩短开发周期。
",G05B19/042,上海怿星电子科技有限公司,车载以太网板卡技术,2.0,"关键词匹配: 以太网, CAN","系统, 包括, 处理",汽车
CN207603664U,CN201721751567.8,"本实用新型涉及一种汽车以太网协议测试系统，所述汽车以太网协议测试系统包括：工控机、示波器、矢量分析仪、协议测试仪、程控电源和ECU抽屉面板；所述工控机通过以太网连接所述示波器和所述协议测试仪；所述示波器通过以太网连接所述矢量分析仪；所述工控机连接所述程控电源；所述示波器和所述矢量分析仪通过测试夹具连接所述ECU抽屉面板；所述协议测试仪和所述程控电源连接所述ECU抽屉面板；所述ECU抽屉面板还包括连接待测ECU的接口。上述汽车以太网协议测试系统能够实现汽车以太网L1‑L7层的多协议测试。
",H04L12/26,上海怿星电子科技有限公司,车载以太网板卡技术,4.0,"关键词匹配: 汽车以太网, 以太网; IPC分类号匹配: H04L12/26","系统, 实现, 包括, 测试","测试, 汽车"
CN207594873U,CN201721751587.5,"本实用新型涉及一种基于BroadR‑Reach以太网的汽车车机原型系统，所述汽车车机原型系统包括：处理器、CAN收发器、BroadR‑Reach物理层模块、第一DB9模块、第二DB9模块、第一USB接口、第二USB接口、数据采集视频接口、音频接口、LVDS接口、SD卡接口、WIFI接口、蓝牙接口、LTE接口和GPS模块。该汽车车机原型系统能够使开发人员根据具体车型的应用做相应的软硬件裁减及应用层功能的开发。使用上述汽车车机原型系统能够大大降低开发难度，缩短开发周期。
",B60R16/023,上海怿星电子科技有限公司,车载以太网板卡技术,4.0,"关键词匹配: 以太网, CAN; IPC分类号匹配: B60R16/023","系统, 包括, 处理",汽车
CN206575438U,CN201720217509.0,"本实用新型涉及一种测试基于BroadR‑Reach的车载以太网测试转换模块以及测试系统。所述转换模块包括物理层工作模式开关、微控制器、供电部、100BASE‑Tx物理层模块、100BASE‑T1物理层模块、电源指示器和以太网状态指示器，第一端口和第二端口。所述测试系统包括计算机、电子控制单元和所述转换模块。
",H04L12/26,上海怿星电子科技有限公司,车载以太网板卡技术,4.0,"关键词匹配: 车载以太网, 以太网; IPC分类号匹配: H04L12/26","测试, 包括, 控制, 系统, 计算","测试, 控制"
