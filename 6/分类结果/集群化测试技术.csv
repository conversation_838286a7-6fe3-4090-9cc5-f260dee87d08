﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN219676484U,CN202320493051.7,"本实用新型涉及一种多功能故障模拟测试系统，包括电路板以及设置在电路板上的微处理器，微处理器连接有电压检测芯片、继电器、故障指示灯以及存储器；微处理器、存储器、继电器、拨码开关和连接器、接插件都在同一块电路板上，散热风扇通过电源线接到电路板上，散热片贴在电路板背面，待测设备和电脑通过数据线和菲尼克斯端子连接到电路板上，上位机软件通过电脑发送CAN报文控制对应的继电器闭合和接收监测报文，可实现系统中多种ECU供电和总线通信控制，以及故障模拟测试功能。其解决了当前的故障模拟测试系统，测试效率低下，测试系统接线复杂，扩展性较差，无法满足更高要求的模拟故障测试以及无法同时测试不同总线通信的设备的问题。
",G05B23/02,上海怿星电子科技有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G05B23/02,"实现, 包括, 测试, 设置, 处理, 设备, 控制, 系统, 检测, 电路, 模拟","检测, 测试, 通信, 控制"
CN116383038A,CN202310157304.8,"本发明涉及基于Python平台的车载DDSI‑RTPS协议自动化测试系统及方法，此系统包括数据嗅探模块、数据解析引擎、数据出口模块以及测试软件上位机；数据嗅探模块：连接待测系统并实时捕获网络数据，并对数据进行缓存；数据解析引擎：与数据嗅探模块进行共享内存连接，接收数据嗅探器捕获的网络数据，并将网络数据解析成可维可测的数据结构；数据出口模块：与数据解析引擎以及测试软件上位机分别进行共享内存连接，用于将数据实时向外暴露；测试软件上位机：解析测试用例，通过数据嗅探模块、数据解析引擎、数据出口模块执行车载RTPS协议自动化测试流程，最终输出测试结果。此系统其能够有效解决车载DDS通信RTPS协议无法从数据根源上发现数据丢失、数据错误的问题。
",G06F11/36,上海怿星电子科技有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G06F11/36,"平台, 包括, 测试, 方法, 系统","通信, 测试, 网络"
CN116340146A,CN202310157151.7,"本发明涉及一种整车OTA升级自动化测试系统及方法，此系统包含OTA云端、车载智能终端、OTA主控制器、HMI车机系统、车机大屏、待升级ECU、云端服务器控制模块、HMI车机大屏控制模块、测试上位机、测试用例、OBD刷写控制模块、解闭锁控制模块以及车钥匙；OTA云端：用于升级包管理、车辆管理、升级任务管理及下发，接收并存储车端上传的升级结果；车载智能终端：与OTA云端服务器进行无线通信连接，接收OTA云端服务器下发的升级任务，并将车端升级结果上传至OTA云端服务器；OTA主控制器：与车载智能终端进行以太网通信连接，接收车载智能终端转发的升级任务指令，控制并执行OTA升级流程，并将升级结果发送至车载智能终端。
",G06F11/36,上海怿星电子科技有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G06F11/36,"系统, 方法, 测试, 控制","车辆, 通信, 测试, 控制"
CN216014244U,CN202121713629.2,"本实用新型公开了一种车辆主机测试装置及测试系统，该车辆主机测试装置包括：解码单元，与车辆主机连接，用于接收车辆主机发送的显示图像数据，显示图像数据包括相互连续的至少两帧图像数据；测试单元，与解码单元连接，测试单元用于接收解码单元发送的显示图像数据，并基于触控信号记录第一时间点，以及将显示图像数据中连续的两帧的数据比对，基于连续的两帧数据的区别记录第二时间点，并确定第二时间点与第一时间点的差值为车辆主机的触控信号的延迟时间。通过测试单元能够确定车辆主机的触控信号延时时间，并且能测试车辆主机目前是否还在正常工作，测试设备的体积小，成本低，而且极大的方便了长时间自动测试的需要。
",G06F11/22,上海怿星电子科技有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G06F11/22,"测试, 包括, 装置, 设备, 系统","车辆, 测试"
CN216014047U,CN202121890402.5,"一种驾驶辅助系统测试设备及车辆，其中测试设备包括：存储器，其存储有传感器数据；微处理器，其用于读取存储器中存储的传感器数据；现场可编程逻辑门阵列，其用于接收传感器数据并处理为与其相对应的模拟信号进行输出；汽车控制器，其用于接收模拟信号并进行测试。对汽车控制器直接输出预先实际采集到的数据进行测试，方便在实验室对ADAS的控制器进行测试以验证开发阶段的硬件软件是否满足了系统要求，极大的降低了路测的工作量，安全高效，可进行长时间更多样本的测量。
",G05B23/02,上海怿星电子科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"测试, 包括, 处理, 设备, 控制, 系统, 模拟","驾驶, 测试, 汽车, 验证, 控制, 车辆, 传感器"
CN215449978U,CN202122264529.2,"本实用新型涉及一种汽车OTA端到端的自动化测试系统，包括：控制单元、交换机、程控电源、总线监控设备、蜂窝通信测试仪、LTE测试仪、信道仿真仪和ECU抽屉面板；控制单元通过交换机分别连接LTE测试仪和信道仿真仪；控制单元还连接蜂窝通信测试仪；信道仿真仪分别连接蜂窝通信测试仪和LTE测试仪，控制单元分别连接程控电源和总线监控设备；程控电源和总线监控设备均连接到ECU抽屉面板；ECU抽屉面板还包括与待测ECU连接的接口。该自动化测试系统能够有效地完成汽车OTA测试，根据主机厂的不同需求完成服务云端测试、通信管道端测试和车端测试；大大丰富了汽车OTA测试的内容，降低了搭建测试环境的时间、减少了测试过程的无效操作，且能形成统一的报告输出。
",G05B23/02,上海怿星电子科技有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G05B23/02,"测试, 包括, 仿真, 设备, 控制, 系统","测试, 汽车, 通信, 控制"
CN113671936A,CN202110926790.6,"一种驾驶辅助系统测试方法、系统、电子设备及存储介质，其中测试方法包括：获取预先采集的模拟的传感器数据；获取的传感器数据传输至FPGA，经FPGA处理为与模拟的传感器数据相对应的模拟信号并传输至汽车控制器；获取所述汽车控制器输出的数据信号进行测试。对汽车控制器直接输出预先实际采集到的数据进行测试，方便在实验室对ADAS的控制器进行测试以验证开发阶段的硬件软件是否满足了系统要求，极大的降低了路测的工作量，安全高效，可进行长时间更多样本的测量。
",G05B23/02,上海怿星电子科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"包括, 测试, 方法, 处理, 设备, 控制, 系统, 模拟","驾驶, 测试, 汽车, 验证, 数据传输, 控制, 传感器"
CN113391971A,CN202110846089.3,"本发明公开了一种车辆主机测试装置及测试系统，该车辆主机测试装置包括：解码单元，与车辆主机连接，用于接收车辆主机发送的显示图像数据，显示图像数据包括相互连续的至少两帧图像数据；测试单元，与解码单元连接，测试单元用于接收解码单元发送的显示图像数据，并基于触控信号记录第一时间点，以及将显示图像数据中连续的两帧的数据比对，基于连续的两帧数据的区别记录第二时间点，并确定第二时间点与第一时间点的差值为车辆主机的触控信号的延迟时间。通过测试单元能够确定车辆主机的触控信号延时时间，并且能测试车辆主机目前是否还在正常工作，测试设备的体积小，成本低，而且极大的方便了长时间自动测试的需要。
",G06F11/22,上海怿星电子科技有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G06F11/22,"测试, 包括, 装置, 设备, 系统","车辆, 测试"
CN206788645U,CN201720523388.2,"本实用新型涉及一种集成汽车电子控制器ECU的测试台架，该测试台架包括：桌面板；设置于桌面板下方、用于放置待测的ECU和测试文件的一个或多个储藏柜；设置于所述桌面板上方的一层或多层ECU托板；设置于ECU托板上且位于该托板后部的滑轨，所述滑轨将断线盒挂至轨道上，再将被测的ECU接在对应的断线盒上，实现ECU的并网测试；设置在断线盒背部的可滑动的卡片，其将断线盒挂至所述滑轨上。采用该方案既便于更换被测对象，节省空间，又美观大方，便于管理。
",G05B23/02,上海怿星电子科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"实现, 包括, 测试, 设置, 控制","测试, 汽车, 控制"
CN206523784U,CN201720215638.6,"本实用新型涉及一种ECU断线盒和汽车网络集成测试系统。所述ECU断线盒基于CAN总线远程控制，包括：继电器组、单片机、CAN收发器电路、断线盒供电电路。所述汽车网络集成测试系统包括个人计算机和程序控制电源，所述汽车网络集成测试系统还包括多个ECU断线盒，所述多个ECU断线盒为菊花链连接；所述多个ECU断线盒由所述程序控制电源供电，并且与一条CAN总线连接。
",G05B23/02,上海怿星电子科技有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G05B23/02,"测试, 包括, 控制, 系统, 计算, 电路","测试, 汽车, 网络, 控制"
