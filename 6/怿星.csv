﻿公开(公告)号,申请号,原始申请人,摘要(中文),IPC主分类号
CN118075162A,CN202410030626.0,上海怿星电子科技有限公司,"本发明涉及一种车辆FOTA压力测试方法和系统，此系统包括：FotaTester上位机：为FOTA压力测试配置执行模块；ETH总线模块：负责整车网络数据，包括但不限于HTTP、someip协议的报文抓取，解析和存储；CAN总线模块：负责整车CAN总线报文抓取、解析和存储，诊断请求数据发送、诊断响应数据解析、ECU节点仿真；FotaHIL上位机：通过ADB接口跟车辆屏幕实时交互，完成屏幕点击、截屏、图片文字识别、标定屏幕坐标和内容，并对外提供端口10001的TCP服务，adb指令通过TCP client的方式转发给FotaHIL上位机；FotaCloud模块：通过Python的requests模块，实现FOTA云平台API接口的调用，完成任务下发、文件上传、升级进度获取功能，并封装成相关功能函数供测试上位机FotaTester调用。
",H04L43/08
CN117745893A,CN202311741113.2,上海怿星电子科技有限公司,"本发明涉及一种基于Un ity引擎的高阶辅助驾驶感知对象实时加载方法，包括以下步骤：步骤1：获得智驾感知障碍物数组，调用渲染接口；步骤2：将当前对象数组ID与上一帧数组对比，根据ID决定可复用对象、需要新创建对象、需要回收对象；步骤3：设置对象位置、旋转、状态信息；步骤4：保证动画组件的正常播放。其建立一套新的对象加载方式，继承对象池的高性能特性，确保在运行时动态创建和销毁对象的开销较少，同时保持动画组件的正常播放。相比于现有技术，其主要优势在于通过精心设计的对象加载方式，有效地解决了传统对象池加载方案中存在的动画不连贯性问题。
",G06T13/20
CN117745909A,CN202311741028.6,上海怿星电子科技有限公司,"本发明涉及一种基于Unity引擎的高阶辅助驾驶超声波传感器数据渲染方法，包括以下步骤：步骤1：建模制作椭圆屏幕并展平UV以用作渲染的画布，U方向用于区分各雷达区范围，V方向用于控制水波纹的距离；步骤2：在un ity引擎中，通过shader在这个画布上区分出对应数量和位置的雷达区；步骤3：在每个雷达区内再细分为前段、中段、后段；步骤4：通过计算使每两段相邻雷达区连接部分的前段、后段保持平滑衔接，并通过偏移UV的V方向来控制水波纹的距离；步骤5：通过脚本将雷达数据传入shader，完成PDC弧段的渲染。其能够改善弧段衔接效果、实现动态曲率变化、并满足用户对于复杂需求的多样性。
",G06T15/00
CN220383073U,CN202320493045.1,上海怿星电子科技有限公司,"本实用新型涉及一种万兆以太网转换器，包括一体式壳体，一体式壳体上设置有五个拨码开关，一体式壳体的内部设置有PHY芯片、主控芯片、散热片以及两个存储器，主控芯片通过MDIO接口控制PHY芯片，PHY芯片通过SerDes接口连接电模块；主控芯片通过读取拨码开关的逻辑电平，确认当前需要配置的模式和速率，根据需要配置的模式和速率通过MDIO接口配置PHY芯片，PHY芯片通过SerDes接口与电模块的SerDes接口连接进行数据转换，完成Tx到T1信号的转换。其配合电模块使用，可以实时监控链路的连接状态、实时控制数据传输速率和工作模式。
",H04L12/02
CN220323868U,CN202321674091.8,上海怿星电子科技有限公司,"本实用新型涉及一种可以测试车机屏幕响应时间的触控笔，包括外壳体，还包括触控笔头以及弹性驱动杆，所述外壳体内设置有微动开关，所述弹性驱动杆弹性滑动设置在所述外壳体内，所述弹性驱动杆的一端与所述触控笔头连接，另一端与所述微动开关可接触分离，所述触控笔头设置在所述外壳体一端的外侧，所述微动开关通过导线连接指示灯电路并控制所述指示灯电路的通断。其用于测试车载屏幕点击后设备的响应时间，其使用了微动开关以及机械顶针按压的电信号与光信号转化的传导装置，对于实际操作指令的响应时间可以做较为准确的测试，可以提高测试有效性。
",G06F3/042
CN220292038U,CN202321674104.1,上海怿星电子科技有限公司,"本实用新型涉及一种USB转车载以太网设备，包括计算机与电子控制单元，还包括100BASE‑T1/1000BASE‑T1转换模块，所述100BASE‑T1/1000BASE‑T1转换模块一端通过USB3.0连接所述计算机，另一端通过车载以太网连接器连接100BASE‑T1/1000BASE‑T1的电子控制单元；所述100BASE‑T1/1000BASE‑T1转换模块通过所述计算机提供的USB3.0进行供电，测试过程中所述计算机中的数据会通过USB3.0两对差分线数据转换为一对双绞线的100BASE‑T1/1000BASE‑T1数据。
",H04L12/40
CN220139579U,CN202321674096.0,上海怿星电子科技有限公司,"本实用新型涉及一种分布式车载以太网自动化测试系统，包括云端服务器以及分别与所述云端服务器连接的测试上位机、至少两个的单机测试系统，所述云端服务器用于管理测试系统；所述测试上位机用于自动化测试的管理，包括测试用例的编写、测试序列的下发、测试结果的统计、及缺陷管理，所述单机测试系统用于测试车载以太网的网络通信。其很好的解决了车载以太网自动化测试系统高成本硬件问题，同时具备分布式自动化测试能力，提升自动化测试效率。
",H04L43/06
CN220022822U,CN202320493052.1,上海怿星电子科技有限公司,"本实用新型涉及一种车载以太网互操作性测试系统，包括上位机软件，还包括与上位机软件连接的数据处理模块，数据处理模块连接有测试用例执行模块、PHY状态反馈模块以及故障注入模块，测试用例执行模块连接有数据反馈模块，故障注入模块连接有噪声注入模块，数据反馈模块与噪声注入模块共同连接有数据路由模块，数据路由模块连接有DUT；上位机软件通过电脑发送UDP报文与下位机进行数据通信，可控制下位机内部的PHY芯片以及测试线路，实现Link Partner的功能，同时上位机通过下位机的T1、CAN、CANFD、UART四种通信口与DUT进行通讯交互，并且通过程控电源电流大小计算DUT工作状态从而实现自动化测试。其改善测试效率低下，以及测试系统消耗资源过多的问题。
",H04L43/50
CN117031267A,CN202310994557.0,上海怿星电子科技有限公司,"本发明涉及继电器通道测试设备及测试方法，其中继电器通道测试设备包括电流检测模块、MCU控制模块以及GPIO扩展模块；电流检测模块用于对被测设备的继电器通道回路进行电流检测；MCU控制模块用于根据上位机下发的指令，控制被测设备和继电器通道测试设备形成测试回路，读取测试结果并上传至上位机；GPIO扩展模块用于控制继电器通道测试设备內部MOS的开关状态以及获取所有被测信号的高低电平状态。此测试设备一方面具有通用性，针对某一类包含继电器的产品，提出具有通用性的测试方案，几乎能适配所有带继电器的产品，降低了产品的整体研发成本；另一方面此检查设备具有可扩展性，由于不同产品继电器数量有差异，提出具有继电器数量可扩展的测试方案。
",G01R31/327
CN116896516A,CN202310994555.1,上海怿星电子科技有限公司,"本发明涉及一种基于秒脉冲方法的时间同步精度测试系统及方法，此系统包括硬件模块和以及设置在计算机中的软件模块，硬件模块通过采集接口连接被测件秒脉冲信号，硬件模块和计算机之间通过以太网实现信息交互；硬件模块提供足够数量的采集通道，负责捕捉秒脉冲信号，通过以太网实时传输秒脉冲的沿变时间戳信息；软件模块运行在计算机之上，利用计算机网卡接收硬件模块发送的数据，执行专用算法处理数据并计算各从通道时钟同步精度，通过人机交互界面实时监测测试过程和测试数据，最终生成测试报告和数据记录文件。此系统支持多通道的秒脉冲信号采集，可高效且方便的实现车载网络时钟同步精度的测试。
",H04L43/08
CN219676484U,CN202320493051.7,上海怿星电子科技有限公司,"本实用新型涉及一种多功能故障模拟测试系统，包括电路板以及设置在电路板上的微处理器，微处理器连接有电压检测芯片、继电器、故障指示灯以及存储器；微处理器、存储器、继电器、拨码开关和连接器、接插件都在同一块电路板上，散热风扇通过电源线接到电路板上，散热片贴在电路板背面，待测设备和电脑通过数据线和菲尼克斯端子连接到电路板上，上位机软件通过电脑发送CAN报文控制对应的继电器闭合和接收监测报文，可实现系统中多种ECU供电和总线通信控制，以及故障模拟测试功能。其解决了当前的故障模拟测试系统，测试效率低下，测试系统接线复杂，扩展性较差，无法满足更高要求的模拟故障测试以及无法同时测试不同总线通信的设备的问题。
",G05B23/02
CN116708497A,CN202310678900.0,上海怿星电子科技有限公司,"本发明涉及一种基于区域的冗余TSN网络架构，包括TSN中央计算单元、左区域控制器、前区域控制器以及右区域控制器，TSN中央计算单元通过车载以太网与左区域控制器、前区域控制器和右区域控制器相连接组成环形网络。前区域控制器通过千兆车载以太网连接行驶控制器，以及通过百兆车载以太网连接摄像头控制器和传感器ECU。TSN中央计算单元由TSN交换机和计算模块组成，支持两个时钟的冗余备份。相比现有的基于域控制器的电子电器架构和点对点的视频传输方式，能够节省线束和高效率利用以太网带宽。环形网络架构支持数据的链路冗余传输，提高了系统数据传输的可靠性。多个时钟的冗余可支持时间同步可靠性和保证网络数据低延迟传输的实时性。
",H04L67/12
CN116707911A,CN202310678901.5,上海怿星电子科技有限公司,"本发明涉及车载网络架构技术领域，公开了一种网络传入流的计量方法，分为两个方面：第一方面，该方法通过信用来计量传入流的以太网帧，以防御对交换机的DoS攻击；第二方面，该方法通过单独控制网络中每个端口上的传入流，可适应交换机输出端口的基于信用整形算法的行为。本发明的优点是可以与AVB/TSN协议一起实施，以防御对交换机的DoS攻击，同时不影响正常数据流的传输延迟。节省硬件成本。通过单独控制网络中每个端口上的传入流来保护车载网络的完整性和可用性。
",H04L9/40
CN116383038A,CN202310157304.8,上海怿星电子科技有限公司,"本发明涉及基于Python平台的车载DDSI‑RTPS协议自动化测试系统及方法，此系统包括数据嗅探模块、数据解析引擎、数据出口模块以及测试软件上位机；数据嗅探模块：连接待测系统并实时捕获网络数据，并对数据进行缓存；数据解析引擎：与数据嗅探模块进行共享内存连接，接收数据嗅探器捕获的网络数据，并将网络数据解析成可维可测的数据结构；数据出口模块：与数据解析引擎以及测试软件上位机分别进行共享内存连接，用于将数据实时向外暴露；测试软件上位机：解析测试用例，通过数据嗅探模块、数据解析引擎、数据出口模块执行车载RTPS协议自动化测试流程，最终输出测试结果。此系统其能够有效解决车载DDS通信RTPS协议无法从数据根源上发现数据丢失、数据错误的问题。
",G06F11/36
CN116340146A,CN202310157151.7,上海怿星电子科技有限公司,"本发明涉及一种整车OTA升级自动化测试系统及方法，此系统包含OTA云端、车载智能终端、OTA主控制器、HMI车机系统、车机大屏、待升级ECU、云端服务器控制模块、HMI车机大屏控制模块、测试上位机、测试用例、OBD刷写控制模块、解闭锁控制模块以及车钥匙；OTA云端：用于升级包管理、车辆管理、升级任务管理及下发，接收并存储车端上传的升级结果；车载智能终端：与OTA云端服务器进行无线通信连接，接收OTA云端服务器下发的升级任务，并将车端升级结果上传至OTA云端服务器；OTA主控制器：与车载智能终端进行以太网通信连接，接收车载智能终端转发的升级任务指令，控制并执行OTA升级流程，并将升级结果发送至车载智能终端。
",G06F11/36
CN115904845A,CN202211570664.2,上海怿星电子科技有限公司,"本发明提出了一种测试信息获取方法及装置、测试装置、电子设备和介质，该方法包括：获取对触控屏幕测试的监控图像序列；在所述监控图像序列中，获取所述信号指示装置处于有效指示状态的末帧图像以及所述触控屏幕显示对所述触控操作的处理结果的首帧图像，其中，用户使用所述触控件对所述触控屏幕进行触控操作的过程中，所述信号指示装置处于所述有效指示状态；基于所述末帧图像和所述首帧图像，以及预设的监控图像拍摄帧率，确定所述触控屏幕对用户操作的响应时间。本发明的技术方案，降低了触控屏幕对用户操作的响应时间的检测难度，同时也能够获得更为准确的检测结果，提升了响应时间检测的便利性和准确性。
",G06F11/22
CN113422706B,CN202110678577.8,上海怿星电子科技有限公司,"本申请实施例公开了一种检测网络协议栈一致性的方法及车辆，检测网络协议栈一致性的方法包括：构建第一域控制器为测试设备；构建第二域控制器为被测设备；以所述第二域控制器为服务端，在所述第一域控制器和所述第二域控制器之间进行以太网通信测试；使所述第一域控制器向所述第二域控制器发送触发指令；响应所述触发指令，所述第二域控制器为加载了被测端互操作应用的客户端，在所述第二域控制器配合第一域控制器发起的以太网通信测试；其中，所述第一域控制器和所述第二域控制器分别存储响应的测试数据和测试报告。本申请实施例能够节省网络协议栈一致性检测的成本。
",H04L43/18
CN216391262U,CN202122407997.0,上海怿星电子科技有限公司,"本公开提供了一种图像数据测试设备、系统及车辆。该设备包括第一端口单元、图像获取单元和第二端口单元；第一端口单元与汽车主机相连，以从汽车主机获取第一图像数据；第一端口单元与图像获取单元相连，以将第一图像数据发送给图像获取单元；图像获取单元与第二端口单元相连，以将第一图像数据发送给第二端口；第二端口单元与第一屏幕模组连接，以将第一图像数据发送给第一屏幕模组。该设备能够在测试的同时维持汽车主机和第一屏幕模组的正常工作。
",H04N17/00
CN216301005U,CN202122090223.X,上海怿星电子科技有限公司,"一种高级驾驶辅助系统的传感器采集设备，包括：数据复制模块(100)，用于将路况数据复制为两份，一份用于传输给汽车控制器(200)，另一份用于存储；所述存储的数据用于数据回放。本发明在汽车传感器和控制器(200)之间增加一个实时的数据采集设备，不影响传感器和控制器(200)直接的数据传输的同时对数据进行记录，以获得大量的实际测试的数据，增加实验室开发阶段的数据素材。
",B60R16/023
CN216014244U,CN202121713629.2,上海怿星电子科技有限公司,"本实用新型公开了一种车辆主机测试装置及测试系统，该车辆主机测试装置包括：解码单元，与车辆主机连接，用于接收车辆主机发送的显示图像数据，显示图像数据包括相互连续的至少两帧图像数据；测试单元，与解码单元连接，测试单元用于接收解码单元发送的显示图像数据，并基于触控信号记录第一时间点，以及将显示图像数据中连续的两帧的数据比对，基于连续的两帧数据的区别记录第二时间点，并确定第二时间点与第一时间点的差值为车辆主机的触控信号的延迟时间。通过测试单元能够确定车辆主机的触控信号延时时间，并且能测试车辆主机目前是否还在正常工作，测试设备的体积小，成本低，而且极大的方便了长时间自动测试的需要。
",G06F11/22
CN216014047U,CN202121890402.5,上海怿星电子科技有限公司,"一种驾驶辅助系统测试设备及车辆，其中测试设备包括：存储器，其存储有传感器数据；微处理器，其用于读取存储器中存储的传感器数据；现场可编程逻辑门阵列，其用于接收传感器数据并处理为与其相对应的模拟信号进行输出；汽车控制器，其用于接收模拟信号并进行测试。对汽车控制器直接输出预先实际采集到的数据进行测试，方便在实验室对ADAS的控制器进行测试以验证开发阶段的硬件软件是否满足了系统要求，极大的降低了路测的工作量，安全高效，可进行长时间更多样本的测量。
",G05B23/02
CN114020202A,CN202111308685.2,上海怿星电子科技有限公司,"本公开涉及提供设备检测技术领域，具体涉及电子设备的测试方法及装置，旨在解决现有技术中以触摸屏作为操作组件时的检测成本高、效率低的技术问题。本发明一种触摸操作的测试方法，应用于具有触摸屏的电子设备，通过模拟触摸屏获取的触摸操作的模拟信号和待测电子设备进行数据交互，从而实现不必实际操作触摸屏就能测试电子设备的运行状况，排查故障。由于采用了所述的技术方案，使得整个测试过程效率高，准确性高，并且能持续进行较长时间。
",G06F3/0488
CN215449978U,CN202122264529.2,上海怿星电子科技有限公司,"本实用新型涉及一种汽车OTA端到端的自动化测试系统，包括：控制单元、交换机、程控电源、总线监控设备、蜂窝通信测试仪、LTE测试仪、信道仿真仪和ECU抽屉面板；控制单元通过交换机分别连接LTE测试仪和信道仿真仪；控制单元还连接蜂窝通信测试仪；信道仿真仪分别连接蜂窝通信测试仪和LTE测试仪，控制单元分别连接程控电源和总线监控设备；程控电源和总线监控设备均连接到ECU抽屉面板；ECU抽屉面板还包括与待测ECU连接的接口。该自动化测试系统能够有效地完成汽车OTA测试，根据主机厂的不同需求完成服务云端测试、通信管道端测试和车端测试；大大丰富了汽车OTA测试的内容，降低了搭建测试环境的时间、减少了测试过程的无效操作，且能形成统一的报告输出。
",G05B23/02
CN113691805A,CN202111162875.8,上海怿星电子科技有限公司,"本公开提供了一种图像数据测试设备、方法、系统及车辆。该设备包括第一端口单元、图像获取单元和第二端口单元；第一端口单元用于将从汽车主机获取第一图像数据发送给图像获取单元，第一端口单元还用于将从汽车主机获取的第一命令数据中不存在I2C地址冲突的第一命令数据发送给图像获取单元；图像获取单元用于将第一图像数据和不存在I2C地址冲突的第一命令数据发送给第二端口单元，图像获取单元还用于对第一图像数据进行分析；第二端口单元用于将第一图像数据发送给第一屏幕模组，第二端口单元还用于将不存在I2C地址冲突的第一命令数据发送给第一屏幕模组。该设备能够在测试的同时维持汽车主机和第一屏幕模组的正常工作。
",H04N17/00
CN113671936A,CN202110926790.6,上海怿星电子科技有限公司,"一种驾驶辅助系统测试方法、系统、电子设备及存储介质，其中测试方法包括：获取预先采集的模拟的传感器数据；获取的传感器数据传输至FPGA，经FPGA处理为与模拟的传感器数据相对应的模拟信号并传输至汽车控制器；获取所述汽车控制器输出的数据信号进行测试。对汽车控制器直接输出预先实际采集到的数据进行测试，方便在实验室对ADAS的控制器进行测试以验证开发阶段的硬件软件是否满足了系统要求，极大的降低了路测的工作量，安全高效，可进行长时间更多样本的测量。
",G05B23/02
CN113650572A,CN202111023433.5,上海怿星电子科技有限公司,"一种高级驾驶辅助系统的传感器采集设备及方法，其中，采集设备包括：数据复制模块(100)，用于将路况数据复制为两份，一份用于传输给汽车控制器(200)，另一份用于存储；所述存储的数据用于数据回放。本发明在汽车传感器和控制器(200)之间增加一个实时的数据采集设备，不影响传感器和控制器(200)直接的数据传输的同时对数据进行记录，以获得大量的实际测试的数据，增加实验室开发阶段的数据素材。
",B60R16/023
CN113391971A,CN202110846089.3,上海怿星电子科技有限公司,"本发明公开了一种车辆主机测试装置及测试系统，该车辆主机测试装置包括：解码单元，与车辆主机连接，用于接收车辆主机发送的显示图像数据，显示图像数据包括相互连续的至少两帧图像数据；测试单元，与解码单元连接，测试单元用于接收解码单元发送的显示图像数据，并基于触控信号记录第一时间点，以及将显示图像数据中连续的两帧的数据比对，基于连续的两帧数据的区别记录第二时间点，并确定第二时间点与第一时间点的差值为车辆主机的触控信号的延迟时间。通过测试单元能够确定车辆主机的触控信号延时时间，并且能测试车辆主机目前是否还在正常工作，测试设备的体积小，成本低，而且极大的方便了长时间自动测试的需要。
",G06F11/22
CN110579992B,CN201910995691.6,上海怿星电子科技有限公司,"一种车载以太网点对点唤醒/休眠控制系统及方法，当车辆处于停车状态，不再需要以太网通信功能时，通过物理层芯片的休眠过程使以太网电子控制单元进入低能耗模式，降低耗电；在需要以太网通信功能时，通过本地唤醒或者远程唤醒过程唤醒已经进入休眠状态的以太网电子控制单元，使以太网电子控制单元建立链路连接，以恢复以太网通信。还提出了一种车载以太网多节点局部唤醒/休眠控制系统及方法，多节点局部休眠唤醒系统包括网关路由节点和多个单节点电子控制单元，实现了在车载以太网多节点网络中，部分节点需要处于唤醒状态以实现通信过程确保功能实现，而部分节点此时由于本身功能未使用且其他节点无需与其通信而处于休眠状态以实现功耗降低。
",G05B19/042
CN211878095U,CN202020218523.4,上海怿星电子科技有限公司,"本实用新型涉及一种车载影音娱乐系统屏幕测试设备，该设备包括：多个屏幕夹具，用于固定待测屏幕；机械手，用于点触所述待测屏幕；至少一个图像采集单元，用于采集所述待测屏幕经点触反馈的图像信息；控制单元，用于控制所述机械手对所述待测屏幕进行点触测试；以及处理单元，用于分析处理所述图像采集单元采集的图像信息。由此，本实用新型的测试设备能够使测试过程拥有更高程度的自动化、集成化，也避免了点触过程缺失。
",G01R31/00
CN210927787U,CN201922216728.9,上海怿星电子科技有限公司,"一种图像采集器及系统，该图像采集器包括：信号解码单元(11)，用于将获取的FPD_LINK协议视频信号解码得到LVDS信号；分配电路(12)，用于将解码后的所述LVDS信号分配，至少得到两路信号，并将其中的第一路信号发送至转换单元(13)；转换单元(13)，用于将所述第一路信号转换成CAM的MIPI信号。CPU单元(14)，用于接收所述转换单元(13)发送的所述MIPI接口信号，将所述MIPI接口信号经解码转换得到流媒体视频。本实用新型实施方式的图像采集器，将汽车主机显示在汽车液晶屏幕上的视频图像信息，存储在可移动设备上，能够在汽车研发阶段，随时调取及观察汽车主机发送的信息，为改进汽车的系统提供了基础。
",H04N5/268
CN111314631A,CN201911233664.1,上海怿星电子科技有限公司,"本发明公开了一种图像采集器及系统，该图像采集器包括：信号解码单元(11)，用于将获取的FPD_LINK协议视频信号解码得到LVDS信号；分配电路(12)，用于将解码后的所述LVDS信号分配，至少得到两路信号，并将其中的第一路信号发送至转换单元(13)；转换单元(13)，用于将所述第一路信号转换成CAM的MIPI信号。CPU单元(14)，用于接收所述转换单元(13)发送的所述MIPI接口信号，将所述MIPI接口信号经解码转换得到流媒体视频。本发明实施方式的图像采集器，将汽车主机显示在汽车液晶屏幕上的视频图像信息，存储在可移动设备上，能够在汽车研发阶段，随时调取及观察汽车主机发送的信息，为改进汽车的系统提供了基础。
",H04N5/268
CN210578605U,CN201922201577.X,上海怿星电子科技有限公司,"一种基于区域控制器的汽车电子电气架构拓扑结构，包含中央计算平台系统、中央电气分配中心、区域控制器及以太网骨干网络，区域控制器通过CAN/LIN总线及传统硬线连接各自区域的传感器执行器。中央计算平台系统实现整车功能的控制逻辑和算法；中央电气分配中心为中央计算平台和区域控制器提供电源分配和保护；区域控制器实现各区域传感器数据采集和执行器驱动，并通过以太网骨干网与中央计算平台交互数据。同时区域控制器也作为二级电气分配中心，集中给区域内各电器件供电。该架构拓扑结构使整车分散的电器件通过就近的区域控制器集中通信和供电，节约线束，降低成本；同时，功能算力的集中化可减少控制器数量，提升架构的可拓展性。
",H04L12/24
CN210168053U,CN201921251958.2,上海怿星电子科技有限公司,"一种汽车以太网自动化测试系统，包括微控制器、驱动芯片、多个双刀双掷继电器、高精度电阻、多个同轴座、汽车以太网信号线、待测设备、电脑、测试仪器、扩展设备；同轴座、双刀双掷继电器、高精度电阻、微控制器和驱动芯片设置在同一电路板上，待测设备、电脑、测试仪器、扩展设备通过数据线远程连接到电路板上；微控制器的I/O口与驱动芯片连接，驱动芯片通过数据线与多个双刀双掷继电器连接；电脑通过数据线分别连接到待测设备、扩展设备A、B和多个测试仪器；测试仪器分别通过各自的同轴座连接到相应的双刀双掷继电器；该测试系统自动化程度高，大大消除了人工干预的人力消耗和错误；减少了测试时间，有效提高了测试效率。
",H04L12/26
CN210166153U,CN201921236051.9,上海怿星电子科技有限公司,"一种汽车信息娱乐功能的自动化测试系统，包括：NI板卡、路由器、机器人控制器、话筒、喇叭、工业相机、触控屏、VT板卡、工控机、程控电源和工业机器人；话筒通过同轴线连接到NI板卡，NI板卡通过以太网分别连接路由器和工业相机，路由器通过以太网分别连接到机器人控制器和工控机，工控机通过以太网连接到VT板卡，VT板卡分别通过数据线和供电线连接被测件，触控屏通过LVDS线连接被测件，喇叭通过数据线连接被测件，程控电源通过数据线连接工控机；触摸屏前布置工业机器人，工业机器人前端安装触控笔；触摸屏垂直上方布置工业相机进行图像信息采集。通过整合多个功能模块，实现统一管理与协调调用，适用性强且方便扩展的自动化测试目的。
",G01M17/007
CN110888053A,CN201911320000.9,上海怿星电子科技有限公司,"本发明涉及一种继电器检测设备，包括：至少一个检测电路，用于基于所述继电器的驱动状态输出不同的电信号；至少一个隔离电源模块，用于向所述继电器和所述检测电路供电；以及至少一个微控制单元，用于检测所述检测电路输出的电信号以判断所述继电器的工作状态。本发明能够实现即使一根数据线上并联安装了多个继电器，继电器的同时通断，不会互相关联导致短路的现象发生，极大地简化了测试夹具设计的工作量。
",G01R31/327
CN210041853U,CN201921240223.X,上海怿星电子科技有限公司,"一种汽车CAN/LIN总线自动化测试系统，包括：上位机、程控电源、总线监控和控制设备、VT系统、示波器、万用表、被测ECU、接插件以及总线通断控制设备，上位机用于运行自动化测试程序，通过控制程控电源、VT系统、示波器、万用表来完成测试内容；总线监控和控制设备根据上位机指令决定是否处理接收的总线报文以及发送该报文到CAN/LIN总线上；VT系统根据上位机指令控制ECU供电和示波器、万用表是否连接到测试总线上；上位机还用于对总线监控和控制设备的通信报文传输情况和预先存储的判定条件进行比较，来判断被测ECU的相应功能是否符合测试规范要求。本测试系统的自动化程度高，能够极大的节约人力成本，提高测试效率和精度。
",H04L12/26
CN210037223U,CN201921236029.4,上海怿星电子科技有限公司,"一种集成车载信息娱乐系统的测试台架，分为四层暗室，由上到下依次为：第1‑第4暗室；每层暗室均包含一个工业相机和一个控制器夹具；将车载信息娱乐系统的待测控制器安装固定在每个暗室中的控制机夹具上，工业相机安装于可多方向调节的工业相机底座上；每个工业相机均采用以太网与外置电脑进行连接；使工业相机能够清晰的采集待测控制器的全部画面并数据传输至外置电脑处理，外置电脑观察并记录工业相机拍摄的图像以完成测试。该测试台架能够有效弥补车载信息娱乐系统桌面测试中线束混乱，控制器更换繁琐和过于依赖人工判断测试结果的问题；降低了搭建测试环境的时间，提高了测试效率。
",G01M17/007
CN106874055B,CN201710131592.4,上海怿星电子科技有限公司,"本发明涉及一种用于汽车ECU程序自动刷写测试的方法和装置。该自动刷写方法根据用户提供的待刷写文件，生成包括所有ECU程序版本切换操作的测试序列，然后通过调用接口，按照测试序列进行ECU程序刷写，并在刷写前后进行ECU诊断测试，记录刷写和诊断结果，生成刷写报告。并且，当ECU程序的刷写出现错误时，调整测试序列，以保证所有版本切换的测试都被包括。利用此方法实现自动测试，操作简单，能够大幅度减轻测试人员的工作强度。
",G06F8/65
CN110543718A,CN201910814340.0,上海怿星电子科技有限公司,"本发明提供一种基于客户特性的汽车电子电气架构变形配置方法，包括：收集整车配置清单，在150％电子电气架构平台模型中定义整车配置清单中客户特性之间的约束关系；检查整车配置清单中所有的客户特性在150％电子电气架构平台模型中与需求以及技术实现方案是否存在关联关系，如果某些客户特性缺失关联关系，则定义其在150％电子电气架构平台模型中与需求以及技术实现方案的关联关系；根据整车配置清单中车型的变形定义整车电子电气架构变形；根据控制算法自动为相关车型的电子电气架构变形选取与其相关的客户特性及其需求与技术实现方案。由此能迅速和轻松地实现整车电子电气架构变形配置。
",G06F17/50
CN110471400A,CN201910813890.0,上海怿星电子科技有限公司,"一种车载以太网实车测试方法，通过对现有线束进行改造，将各通道的实车线束从中间断开，并用各延长线引出至合适位置，并在各延长线终端安装连接器；车辆静止状态测试时，将内部线束桥接的以太网数据采集模块与各延长线终端的连接器连接，使用以太网数据采集模块及上位机进行以太网数据的实时分析及测试；车辆运动状态测试时，将内部线束桥接的以太网实车数据记录仪与各延长线终端的连接器连接，使用以太网实车数据记录仪进行测试数据记录；跑车测试结束后，将数据记录仪取出连接至上位机，进行以太网的数据分析及处理。有效解决了现有技术中所有线束均捆绑到一起，缺乏有效方法对其进行测试的问题，并可有效分析信号丢失、信号延迟问题。
",G05B23/02
CN110460621A,CN201910854362.X,上海怿星电子科技有限公司,"一种基于SOME/IP协议的车载以太网总线数据与CAN总线数据的转换方法及系统，转换方法包括：接收要转换成以太网数据的CAN总线数据；将若干个所接收的CAN总线数据编码、封装成相应的以太网数据，并发送到对应的以太网设备中；接收要转换成CAN总线数据的以太网数据；将若干个所接收的以太网数据解析并封装成CAN总线数据，并发送到对应的CAN网络设备中；以太网数据包括一个或多个SOME/IP报文数据；将若干个所接收的CAN总线数据编码、封装成相应的以太网数据包括将所接收的CAN总线报文数据的CAN ID编码到SOME/IP报文数据的报头；本发明能够实现汽车内CAN总线协议通信与SOME/IP协议通信间的协议转换，区别于传统的面向信号的通信方式，实现了面向服务架构的车载以太网网关通信。
",H04L29/06
CN209233850U,CN201920051662.X,上海怿星电子科技有限公司,"一种便携式单节点网络自动化测试系统，包括测试机箱，由中间层板将机箱分为上、下两层；下层包括开关电源、可编程电源模块、USB集线器和插排；上层包括CAN总线干扰仪、总线示波器、硬件接口卡、接线端子、第一PCB板和第二PCB板；各组成测试设备通过内部线束连接构成总线测试电路，并通过测试机箱前面板转换成标准接口，提供对ECU的统一标准化接口；可编程电源模块输出供电电压至测试机箱的前面板标准接口。该测试系统能够有效地弥补机柜式测试系统不便携带和多个单一测试工具连接繁琐的缺点；同时使得车载控制器的测试环境更加灵活多变，加快开发进程。
",H04L12/26
CN106100292B,CN201610704187.2,上海怿星电子科技有限公司,"一种可编程小功率直流电源及电源配置方法，该电源包括：电源模块，由外部电源、线性电源、电荷泵构成，线性电源由外部电源供电产生直流正压，电荷泵由外部电源供电产生直流负压，电源模块给各差分放大器供电以产生正、负直流电压输出；微控制器，包括模数转换单元模块、数模转换单元模块和若干个I/O口，并与外部控制口电连接；第一、二、三差分放大器；单刀双掷开关；第一、二开关；采样电阻；加法器；该直流电源可被编程配置为恒压源或者恒流源输出给外界电路；本发明结构简单，方便配置，能够方便、灵活的为汽车测试领域提供所需要的较小电压和电流范围。
",H02M3/20
CN207601619U,CN201721751522.0,上海怿星电子科技有限公司,"本实用新型涉及一种基于BroadR‑Reach以太网的汽车网关原型系统，所述汽车网关原型系统包括：处理器、第一5口汽车以太网交换芯片、第二5口汽车以太网交换芯片、第一双口100Base‑T1以太网PHY收发器、第二双口100Base‑T1以太网PHY收发器、单口100Base‑T1以太网PHY收发器、单口1000Base‑T1以太网PHY收发器和单口10Base‑T/100Base‑Tx/1000Base‑T以太网PHY收发器；以及包括CAN总线接口、LIN总线接口、USB接口、RS232接口、WIFI接口、蓝牙接口、LTE接口和供电接口。该原型系统能够快速搭建基于BroadR‑Reach以太网的汽车网关软硬件开发平台，用于基于BroadR‑Reach以太网的汽车网关的开发和验证。
",G05B23/02
CN207601577U,CN201721751654.3,上海怿星电子科技有限公司,"本实用新型涉及一种基于BroadR‑Reach以太网的汽车液晶仪表原型系统，所述汽车液晶仪表原型系统包括：处理器、CAN收发器、BroadR‑Reach物理层模块、第一DB9模块、第二DB9模块、USB接口、数据采集视频接口、音频接口和LVDS接口；其中，所述处理器与所述CAN收发器及所述BroadR‑Reach物理层模块分别相连接；所述处理器与所述USB接口、所述数据采集视频接口、所述音频接口和所述LVDS接口分别连接。该汽车液晶仪表原型系统，能够使开发人员根据具体车型的应用做相应的软硬件裁减及应用层功能的开发。使用上述汽车液晶仪表原型系统能够大大降低开发难度，缩短开发周期。
",G05B19/042
CN207603664U,CN201721751567.8,上海怿星电子科技有限公司,"本实用新型涉及一种汽车以太网协议测试系统，所述汽车以太网协议测试系统包括：工控机、示波器、矢量分析仪、协议测试仪、程控电源和ECU抽屉面板；所述工控机通过以太网连接所述示波器和所述协议测试仪；所述示波器通过以太网连接所述矢量分析仪；所述工控机连接所述程控电源；所述示波器和所述矢量分析仪通过测试夹具连接所述ECU抽屉面板；所述协议测试仪和所述程控电源连接所述ECU抽屉面板；所述ECU抽屉面板还包括连接待测ECU的接口。上述汽车以太网协议测试系统能够实现汽车以太网L1‑L7层的多协议测试。
",H04L12/26
CN207594873U,CN201721751587.5,上海怿星电子科技有限公司,"本实用新型涉及一种基于BroadR‑Reach以太网的汽车车机原型系统，所述汽车车机原型系统包括：处理器、CAN收发器、BroadR‑Reach物理层模块、第一DB9模块、第二DB9模块、第一USB接口、第二USB接口、数据采集视频接口、音频接口、LVDS接口、SD卡接口、WIFI接口、蓝牙接口、LTE接口和GPS模块。该汽车车机原型系统能够使开发人员根据具体车型的应用做相应的软硬件裁减及应用层功能的开发。使用上述汽车车机原型系统能够大大降低开发难度，缩短开发周期。
",B60R16/023
CN206788645U,CN201720523388.2,上海怿星电子科技有限公司,"本实用新型涉及一种集成汽车电子控制器ECU的测试台架，该测试台架包括：桌面板；设置于桌面板下方、用于放置待测的ECU和测试文件的一个或多个储藏柜；设置于所述桌面板上方的一层或多层ECU托板；设置于ECU托板上且位于该托板后部的滑轨，所述滑轨将断线盒挂至轨道上，再将被测的ECU接在对应的断线盒上，实现ECU的并网测试；设置在断线盒背部的可滑动的卡片，其将断线盒挂至所述滑轨上。采用该方案既便于更换被测对象，节省空间，又美观大方，便于管理。
",G05B23/02
CN206678792U,CN201720215977.4,上海怿星电子科技有限公司,"本实用新型涉及一种双CPU控制的车用仪表显示系统，所述车用显示系统包括：屏幕、第一控制模块和第二控制模块；所述屏幕具有屏幕接口；所述第一控制模块包括第一CPU和微控制器；以及所述第二控制模块包括第二CPU；其中，所述第二CPU通过数据线连接所述第一CPU，并能够将需要显示的内容转换成媒体数据，通过数据线发送给所述第一CPU；所述第一CPU连接所述屏幕并为所述屏幕提供所述显示数据。本实用新型降低了成本的同时有着更好的用户体验，并且具有较高的安全性。
",B60K35/00
CN206575438U,CN201720217509.0,上海怿星电子科技有限公司,"本实用新型涉及一种测试基于BroadR‑Reach的车载以太网测试转换模块以及测试系统。所述转换模块包括物理层工作模式开关、微控制器、供电部、100BASE‑Tx物理层模块、100BASE‑T1物理层模块、电源指示器和以太网状态指示器，第一端口和第二端口。所述测试系统包括计算机、电子控制单元和所述转换模块。
",H04L12/26
CN206524006U,CN201720215979.3,上海怿星电子科技有限公司,"本实用新型涉及一种中远红外车道标示系统，所述系统包括标示组件和检测组件；其中所述标示组件具有中远红外发射模块，所述中远红外发射模块用于接收信息处理单元的信息并发出中远红外线；所述检测组件包括：中远红外摄像头，所述中远红外摄像头设置在车辆中，并能够检测到所述中远红外发射模块所发射的中远红外线，并提供指示信息。利用所述系统能够保证车辆在恶劣天气下安全行驶。
",G08G1/0967
CN206523784U,CN201720215638.6,上海怿星电子科技有限公司,"本实用新型涉及一种ECU断线盒和汽车网络集成测试系统。所述ECU断线盒基于CAN总线远程控制，包括：继电器组、单片机、CAN收发器电路、断线盒供电电路。所述汽车网络集成测试系统包括个人计算机和程序控制电源，所述汽车网络集成测试系统还包括多个ECU断线盒，所述多个ECU断线盒为菊花链连接；所述多个ECU断线盒由所述程序控制电源供电，并且与一条CAN总线连接。
",G05B23/02
CN106710274A,CN201710132292.8,上海怿星电子科技有限公司,"本发明涉及一种中远红外车道标示系统及标示方法，所述系统包括标示组件和检测组件；其中所述标示组件具有中远红外发射模块，所述中远红外发射模块用于接收信息处理单元的信息并发出中远红外线；所述检测组件包括：中远红外摄像头，所述中远红外摄像头设置在车辆中，并能够检测到所述中远红外发射模块所发射的中远红外线，并提供指示信息。利用所述系统和方法能够保证车辆在恶劣天气下安全行驶。
",G08G1/0967
CN205905796U,CN201620916994.6,上海怿星电子科技有限公司,"一种用于汽车方向盘的多功能操作组件，其集成手指滑动导航模组、若干按键和微控制器MCU组合而成，手指滑动导航模组、所有按键和微控制器MCU封装成一个整体模块，手指滑动导航模组和按键排列方式根据人体工程学优化设计集成到方向盘中；使用时手指在手指滑动导航模组上向任意方向滑动，手指滑动导航模组里面的芯片将滑动方向和速度转换成相对应的数据通过数据线传送给微控制器，微控制器负责控制和识别手指滑动导航模组和按键的信号，根据系统情况作对应的处理，同时按键可以提供辅助功能完成如确认、返回等操作，以提供更好的用户操作体验。
",B60K37/06
CN205911940U,CN201620916951.8,上海怿星电子科技有限公司,"一种可编程小功率直流电源，包括：电源模块，由外部输入供电电源、线性电源、电荷泵构成，线性电源由外部电源供电产生直流正压，电荷泵由外部电源供电产生直流负压，电源模块给各差分放大器供电以产生正、负直流电压输出；微控制器，包括模数转换单元模块、数模转换单元模块和若干个I/O口，并与外部控制口电连接；第一、二、三差分放大器；单刀双掷开关；第一、二开关；采样电阻；加法器；该直流电源可被编程配置为恒压源或者恒流源输出给外界电路。
",H02M1/00