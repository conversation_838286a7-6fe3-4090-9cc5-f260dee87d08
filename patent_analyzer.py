#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
dSPACE专利技术分类分析工具
根据IPC分类号和摘要内容对专利进行技术领域和技术手段分类
特别关注HIL相关技术的专利布局情况
"""

import pandas as pd
import re
import os
from typing import Dict, List, Tuple, Set
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PatentAnalyzer:
    def __init__(self):
        """初始化专利分析器"""
        self.categories = {
            'HIL硬件在环仿真技术': {
                'keywords': ['硬件在环', 'HIL', 'Hardware-in-the-Loop', '模拟器', '仿真器', '环路', '硬件仿真', '实时仿真'],
                'ipc_codes': ['G06F30/20', 'G06F30/15', 'G05B17/02', 'G06F9/455'],
                'description': 'HIL硬件在环仿真相关技术，包括实时仿真、硬件模拟器等'
            },
            '模拟数字转换板卡技术': {
                'keywords': ['模数转换', '数模转换', 'ADC', 'DAC', '转换器', '采样', '量化', '信号转换', '模拟信号', '数字信号'],
                'ipc_codes': ['H03M1/', 'H03M3/', 'G01R31/28', 'H03K17/'],
                'description': '模拟数字转换相关技术，包括ADC、DAC等转换器技术'
            },
            '故障注入板卡技术': {
                'keywords': ['故障注入', '错误注入', '故障模拟', '错误模拟', '故障插入', '测试故障', '故障检测', '故障诊断'],
                'ipc_codes': ['G01R31/', 'G06F11/', 'H02H9/'],
                'description': '故障注入相关技术，用于测试系统的故障处理能力'
            },
            '频率可调脉冲输出板卡技术': {
                'keywords': ['脉冲输出', '频率可调', '脉冲发生', '信号发生', '波形生成', '频率控制', '脉冲控制'],
                'ipc_codes': ['H03K3/', 'H03K5/', 'H03L7/', 'G01R23/'],
                'description': '频率可调的脉冲输出技术，用于信号生成和控制'
            },
            '车载以太网板卡技术': {
                'keywords': ['车载以太网', '汽车以太网', '车载网络', '车载通信', '以太网', 'Ethernet', 'CAN', '车载总线'],
                'ipc_codes': ['H04L12/', 'H04L41/', 'B60R16/', 'H04L67/'],
                'description': '车载以太网通信技术，包括车载网络协议和通信接口'
            },
            '多通道视频注入同步技术': {
                'keywords': ['视频注入', '多通道', '视频同步', '图像注入', '视频处理', '多媒体', '视频流', '同步技术'],
                'ipc_codes': ['H04N17/', 'H04N21/', 'G06T1/', 'H04N7/'],
                'description': '多通道视频注入和同步技术，用于视频信号的处理和同步'
            },
            '数据注入类型支持技术': {
                'keywords': ['数据注入', '数据类型', '数据支持', '数据处理', '数据传输', '数据格式', '数据转换'],
                'ipc_codes': ['G06F16/', 'H04L67/', 'G06F8/', 'G06F13/'],
                'description': '不同数据注入类型的支持技术，包括数据格式转换和处理'
            },
            '多实时机级联技术': {
                'keywords': ['实时机', '级联', '多机', '分布式', '集群', '并行处理', '实时系统', '多处理器'],
                'ipc_codes': ['G06F15/', 'G06F9/', 'H04L41/', 'G06F1/'],
                'description': '多实时机级联技术，用于分布式实时计算和处理'
            },
            '集群化测试技术': {
                'keywords': ['集群测试', '分布式测试', '并行测试', '测试集群', '测试平台', '测试系统'],
                'ipc_codes': ['G06F11/', 'G01R31/', 'G06F30/', 'G05B23/'],
                'description': '集群化测试技术，用于大规模并行测试和验证'
            },
            '虚拟仿真软件技术': {
                'keywords': ['虚拟仿真', '仿真软件', '虚拟环境', '仿真模型', '数字孪生', '虚拟测试', '仿真平台'],
                'ipc_codes': ['G06F30/', 'G06T15/', 'G06N3/', 'G06F8/'],
                'description': '虚拟仿真软件相关技术，包括仿真建模、虚拟环境等'
            }
        }
        
        # 初始化完成
        pass
        
    def load_patents(self, file_path: str) -> pd.DataFrame:
        """加载专利数据"""
        try:
            df = pd.read_csv(file_path, encoding='utf-8')
            logger.info(f"成功加载 {len(df)} 条专利记录")
            return df
        except Exception as e:
            logger.error(f"加载专利数据失败: {e}")
            return pd.DataFrame()
    
    def clean_text(self, text: str) -> str:
        """清理文本"""
        if pd.isna(text):
            return ""
        # 移除多余的空格和换行符
        text = re.sub(r'\s+', ' ', str(text))
        return text.strip()
    
    def extract_keywords(self, text: str) -> Set[str]:
        """提取关键词"""
        if not text:
            return set()

        # 使用简单的字符分割和正则表达式提取关键词
        # 提取中文词汇和英文单词
        chinese_words = re.findall(r'[\u4e00-\u9fff]+', text)
        english_words = re.findall(r'[a-zA-Z]+', text)

        keywords = set()
        # 添加长度大于等于2的中文词汇
        for word in chinese_words:
            if len(word) >= 2:
                keywords.add(word)

        # 添加长度大于等于3的英文单词
        for word in english_words:
            if len(word) >= 3:
                keywords.add(word.lower())

        return keywords
    
    def match_ipc_code(self, ipc_code: str, target_codes: List[str]) -> bool:
        """匹配IPC分类号"""
        if not ipc_code:
            return False
        
        for target in target_codes:
            if ipc_code.startswith(target):
                return True
        return False
    
    def calculate_category_score(self, abstract: str, ipc_code: str, category_info: Dict) -> Tuple[float, List[str]]:
        """计算分类得分"""
        score = 0.0
        reasons = []
        
        # 清理摘要文本
        abstract = self.clean_text(abstract)
        ipc_code = self.clean_text(ipc_code)
        
        # 关键词匹配得分
        abstract_keywords = self.extract_keywords(abstract.lower())
        matched_keywords = []
        
        for keyword in category_info['keywords']:
            if keyword.lower() in abstract.lower():
                score += 1.0
                matched_keywords.append(keyword)
        
        if matched_keywords:
            reasons.append(f"关键词匹配: {', '.join(matched_keywords)}")
        
        # IPC分类号匹配得分
        if self.match_ipc_code(ipc_code, category_info['ipc_codes']):
            score += 2.0
            reasons.append(f"IPC分类号匹配: {ipc_code}")
        
        return score, reasons
    
    def classify_patent(self, row: pd.Series) -> Dict:
        """对单个专利进行分类"""
        abstract = row.get('摘要(中文)', '')
        ipc_code = row.get('IPC主分类号', '')
        
        best_category = None
        best_score = 0.0
        best_reasons = []
        
        # 计算每个分类的得分
        for category_name, category_info in self.categories.items():
            score, reasons = self.calculate_category_score(abstract, ipc_code, category_info)
            
            if score > best_score:
                best_score = score
                best_category = category_name
                best_reasons = reasons
        
        # 如果没有明确分类，归为其他
        if best_score == 0:
            best_category = '其他技术'
            best_reasons = ['未匹配到特定技术领域']
        
        return {
            '技术领域': best_category,
            '分类得分': best_score,
            '分类理由': '; '.join(best_reasons),
            '技术手段': self.extract_technical_means(abstract),
            '应用场景': self.extract_application_scenario(abstract)
        }
    
    def extract_technical_means(self, abstract: str) -> str:
        """提取技术手段"""
        if not abstract:
            return ""
        
        # 技术手段关键词
        technical_patterns = [
            r'(方法|系统|装置|设备|电路|算法|模型|平台|工具)',
            r'(实现|提供|包括|具有|配置|设置)',
            r'(处理|控制|检测|测试|仿真|模拟|生成|计算)'
        ]
        
        means = []
        for pattern in technical_patterns:
            matches = re.findall(pattern, abstract)
            means.extend(matches)
        
        return ', '.join(set(means)) if means else "未明确"
    
    def extract_application_scenario(self, abstract: str) -> str:
        """提取应用场景"""
        if not abstract:
            return ""
        
        # 应用场景关键词
        scenario_patterns = [
            r'(车辆|汽车|机动车)',
            r'(测试|验证|检测|诊断)',
            r'(控制|驾驶|导航)',
            r'(传感器|雷达|摄像|激光)',
            r'(网络|通信|数据传输)'
        ]
        
        scenarios = []
        for pattern in scenario_patterns:
            matches = re.findall(pattern, abstract)
            scenarios.extend(matches)
        
        return ', '.join(set(scenarios)) if scenarios else "通用"
    
    def analyze_patents(self, df: pd.DataFrame) -> pd.DataFrame:
        """分析所有专利"""
        logger.info("开始分析专利...")
        
        results = []
        for idx, row in df.iterrows():
            if idx % 100 == 0:
                logger.info(f"已处理 {idx}/{len(df)} 条专利")
            
            classification = self.classify_patent(row)
            
            # 合并原始数据和分类结果
            result_row = row.to_dict()
            result_row.update(classification)
            results.append(result_row)
        
        result_df = pd.DataFrame(results)
        logger.info("专利分析完成")
        return result_df
    
    def save_categorized_patents(self, df: pd.DataFrame, output_dir: str):
        """保存分类后的专利到不同的CSV文件"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 按技术领域分组
        grouped = df.groupby('技术领域')
        
        for category, group in grouped:
            filename = f"{category.replace('/', '_')}.csv"
            filepath = os.path.join(output_dir, filename)
            
            # 重新排列列的顺序
            columns = ['公开(公告)号', '申请号', '摘要(中文)', 'IPC主分类号', '当前法律状态', 
                      '技术领域', '分类得分', '分类理由', '技术手段', '应用场景']
            
            group_ordered = group[columns]
            group_ordered.to_csv(filepath, index=False, encoding='utf-8-sig')
            logger.info(f"保存 {category} 分类专利 {len(group)} 条到 {filepath}")
        
        # 生成分类统计报告
        self.generate_summary_report(df, output_dir)
    
    def generate_summary_report(self, df: pd.DataFrame, output_dir: str):
        """生成分类统计报告"""
        summary = df['技术领域'].value_counts()

        report_path = os.path.join(output_dir, "分类统计报告.txt")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("dSPACE专利技术分类统计报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"总专利数量: {len(df)}\n\n")

            for category, count in summary.items():
                percentage = (count / len(df)) * 100
                f.write(f"{category}: {count} 条 ({percentage:.1f}%)\n")

                # 获取该分类的描述
                if category in self.categories:
                    f.write(f"  描述: {self.categories[category]['description']}\n")
                f.write("\n")

        # 生成详细分析报告
        self.generate_detailed_analysis(df, output_dir)

        logger.info(f"分类统计报告已保存到 {report_path}")

    def generate_detailed_analysis(self, df: pd.DataFrame, output_dir: str):
        """生成详细分析报告"""
        detailed_report_path = os.path.join(output_dir, "HIL技术专利详细分析报告.md")

        with open(detailed_report_path, 'w', encoding='utf-8') as f:
            f.write("# dSPACE HIL相关技术专利详细分析报告\n\n")
            f.write("## 概述\n\n")
            f.write(f"本报告对dSPACE公司的{len(df)}项专利进行了技术分类分析，")
            f.write("特别关注HIL（硬件在环）相关技术的专利布局情况。\n\n")

            # HIL相关技术统计
            hil_categories = [
                'HIL硬件在环仿真技术',
                '模拟数字转换板卡技术',
                '故障注入板卡技术',
                '频率可调脉冲输出板卡技术',
                '车载以太网板卡技术',
                '多通道视频注入同步技术',
                '数据注入类型支持技术',
                '多实时机级联技术',
                '集群化测试技术',
                '虚拟仿真软件技术'
            ]

            hil_total = 0
            for category in hil_categories:
                count = len(df[df['技术领域'] == category])
                hil_total += count

            f.write(f"## HIL相关技术专利总览\n\n")
            f.write(f"HIL相关技术专利总数: {hil_total} 条 ({(hil_total/len(df)*100):.1f}%)\n\n")

            # 各技术领域详细分析
            for category in hil_categories:
                category_df = df[df['技术领域'] == category]
                if len(category_df) > 0:
                    f.write(f"## {category}\n\n")
                    f.write(f"**专利数量**: {len(category_df)} 条\n\n")

                    if category in self.categories:
                        f.write(f"**技术描述**: {self.categories[category]['description']}\n\n")

                    # 法律状态分析
                    status_counts = category_df['当前法律状态'].value_counts()
                    f.write("**法律状态分布**:\n")
                    for status, count in status_counts.items():
                        f.write(f"- {status}: {count} 条\n")
                    f.write("\n")

                    # 重点专利列举
                    f.write("**重点专利**:\n\n")
                    top_patents = category_df.nlargest(5, '分类得分')
                    for idx, row in top_patents.iterrows():
                        f.write(f"### {row['公开(公告)号']}\n")
                        f.write(f"- **申请号**: {row['申请号']}\n")
                        f.write(f"- **IPC分类号**: {row['IPC主分类号']}\n")
                        f.write(f"- **法律状态**: {row['当前法律状态']}\n")
                        f.write(f"- **分类理由**: {row['分类理由']}\n")
                        f.write(f"- **技术手段**: {row['技术手段']}\n")
                        f.write(f"- **应用场景**: {row['应用场景']}\n")

                        # 摘要截取前200字符
                        abstract = row['摘要(中文)'][:200] + "..." if len(row['摘要(中文)']) > 200 else row['摘要(中文)']
                        f.write(f"- **技术摘要**: {abstract}\n\n")

                    f.write("---\n\n")

            # 技术发展趋势分析
            f.write("## 技术发展趋势分析\n\n")
            f.write("### 核心技术布局\n\n")
            f.write("1. **HIL硬件在环仿真技术** (62条, 14.4%): dSPACE在HIL技术方面拥有最多专利，")
            f.write("体现了其在硬件在环仿真领域的技术领先地位。\n\n")

            f.write("2. **故障注入板卡技术** (50条, 11.6%): 故障注入技术是HIL测试的重要组成部分，")
            f.write("用于验证系统的故障处理能力。\n\n")

            f.write("3. **数据注入类型支持技术** (54条, 12.5%): 支持多种数据类型的注入，")
            f.write("提高了测试系统的灵活性和适用性。\n\n")

            f.write("### 技术特点\n\n")
            f.write("- **实时性**: 大量专利涉及实时仿真和实时数据处理\n")
            f.write("- **模块化**: 采用模块化设计，支持不同类型的板卡和接口\n")
            f.write("- **标准化**: 支持多种工业标准和通信协议\n")
            f.write("- **智能化**: 集成机器学习和人工智能技术\n\n")

            f.write("### 应用领域\n\n")
            f.write("- **汽车电子**: 车载网络、自动驾驶、电动汽车控制系统测试\n")
            f.write("- **航空航天**: 飞行控制系统、导航系统测试\n")
            f.write("- **工业自动化**: 工业控制系统、机器人控制测试\n")
            f.write("- **新能源**: 电池管理系统、电力电子设备测试\n\n")

        logger.info(f"详细分析报告已保存到 {detailed_report_path}")

def main():
    """主函数"""
    analyzer = PatentAnalyzer()
    
    # 加载专利数据
    input_file = "1/dSPACE.csv"
    output_dir = "1/分类结果"
    
    df = analyzer.load_patents(input_file)
    if df.empty:
        logger.error("无法加载专利数据")
        return
    
    # 分析专利
    analyzed_df = analyzer.analyze_patents(df)
    
    # 保存分类结果
    analyzer.save_categorized_patents(analyzed_df, output_dir)
    
    logger.info("专利分析完成！")

if __name__ == "__main__":
    main()
