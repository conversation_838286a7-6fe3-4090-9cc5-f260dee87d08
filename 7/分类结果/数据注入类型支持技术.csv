﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,技术领域,分类得分,分类理由,技术手段,应用场景
CN117763194B,CN202311754382.2,"本发明涉及一种基于大语言模型的车载数据语音标签系统，尤其涉及汽车数据处理技术领域，包括数据记录模块，用以对车辆的路测数据进行采集；语音记录模块，用以对语音记录的开启进行判断，并对语音记录开启的判断过程进行修正，还用以对记录后的语音进行预处理和存储；语音处理模块，用以通过LLM大语言模型对已存储的语音进行标签识别并设置索引；数据上传模块，用以将语音识别后的语音进行切片处理并上传至云端进行分类存储；反馈训练模块，用以根据对LLM大语言模型进行训练，并对语音处理的有效性进行反馈，还用以对反馈过程进行补偿。本发明提高了车载数据的语音标签效率。
",G06F16/65,数据注入类型支持技术,3.0,关键词匹配: 数据处理; IPC分类号匹配: G06F16/65,"包括, 模型, 设置, 处理, 系统","车辆, 汽车"
CN117763194A,CN202311754382.2,"本发明涉及一种基于大语言模型的车载数据语音标签系统，尤其涉及汽车数据处理技术领域，包括数据记录模块，用以对车辆的路测数据进行采集；语音记录模块，用以对语音记录的开启进行判断，并对语音记录开启的判断过程进行修正，还用以对记录后的语音进行预处理和存储；语音处理模块，用以通过LLM大语言模型对已存储的语音进行标签识别并设置索引；数据上传模块，用以将语音识别后的语音进行切片处理并上传至云端进行分类存储；反馈训练模块，用以根据对LLM大语言模型进行训练，并对语音处理的有效性进行反馈，还用以对反馈过程进行补偿。本发明提高了车载数据的语音标签效率。
",G06F16/65,数据注入类型支持技术,3.0,关键词匹配: 数据处理; IPC分类号匹配: G06F16/65,"包括, 模型, 设置, 处理, 系统","车辆, 汽车"
CN114863588B,CN202210786694.0,"本发明提供一种数据记录仪，其包括电源控制模块、内置的存储介质、总线交换控制器和处理器、设备电源接口和总线电源接口；设备电源接口用于与车载主机连接，总线电源接口用于与外部电源设备连接；电源控制模块连接于设备电源接口和总线电源接口，用于向总线交换控制器和存储介质供电；存储介质连接于总线交换控制器、并通过总线交换控制器分别与处理器和外部电源设备连接，以使存储介质能够被处理器或外部电源设备访问以进行数据传输。本发明的数据记录仪采取内置的存储介质，其一降低了数据记录仪的物理尺寸；其二能够实现数据高速下载。
",G07C5/08,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"提供, 实现, 包括, 处理, 设备, 控制","数据传输, 控制"
CN114863588A,CN202210786694.0,"本发明提供一种数据记录仪，其包括电源控制模块、内置的存储介质、总线交换控制器和处理器、设备电源接口和总线电源接口；设备电源接口用于与车载主机连接，总线电源接口用于与外部电源设备连接；电源控制模块连接于设备电源接口和总线电源接口，用于向总线交换控制器和存储介质供电；存储介质连接于总线交换控制器、并通过总线交换控制器分别与处理器和外部电源设备连接，以使存储介质能够被处理器或外部电源设备访问以进行数据传输。本发明的数据记录仪采取内置的存储介质，其一降低了数据记录仪的物理尺寸；其二能够实现数据高速下载。
",G07C5/08,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"提供, 实现, 包括, 处理, 设备, 控制","数据传输, 控制"
CN113934673B,CN202111536030.0,"本申请公开了一种数据传输隔离电路及数据传输设备，所述隔离电路具有多个接收端口以及发送端口，所述隔离电路的多个接收端口与多个数据发送设备的通用异步收发传输器的发送端口连接；所述隔离电路的发送端口与数据接收设备的通用异步收发传输器的接收端口连接；所述隔离电路能够实现多个数据发送设备的通用异步收发传输器的发送端口输出信号的与操作。本申请提供的技术方案能够屏蔽信号之间的干扰，达到多路串口双向通讯的目的，使车机系统开发和测试人员能够在不同设备与接口类型上读写车机日志和设置。
",G06F13/42,数据注入类型支持技术,3.0,关键词匹配: 数据传输; IPC分类号匹配: G06F13/42,"提供, 实现, 测试, 具有, 设置, 设备, 系统, 电路","测试, 数据传输"
CN113934673A,CN202111536030.0,"本申请公开了一种数据传输隔离电路及数据传输设备，所述隔离电路具有多个接收端口以及发送端口，所述隔离电路的多个接收端口与多个数据发送设备的通用异步收发传输器的发送端口连接；所述隔离电路的发送端口与数据接收设备的通用异步收发传输器的接收端口连接；所述隔离电路能够实现多个数据发送设备的通用异步收发传输器的发送端口输出信号的与操作。本申请提供的技术方案能够屏蔽信号之间的干扰，达到多路串口双向通讯的目的，使车机系统开发和测试人员能够在不同设备与接口类型上读写车机日志和设置。
",G06F13/42,数据注入类型支持技术,3.0,关键词匹配: 数据传输; IPC分类号匹配: G06F13/42,"提供, 实现, 测试, 具有, 设置, 设备, 系统, 电路","测试, 数据传输"
