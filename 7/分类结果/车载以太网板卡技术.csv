﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,技术领域,分类得分,分类理由,技术手段,应用场景
CN118015726A,CN202311864563.0,"本发明涉及一种车载数据记录仪的数据监听方法及系统，尤其涉及车载数据监听技术领域，包括步骤S1，通过桌面软件配置目标总线端口和协议；步骤S2，从总线端口接收数据；步骤S3，使用FPGA将数据存储到DDR；步骤S4，通过SoC从DDR读取数据；步骤S5，通过高速以太网接口转发数据；步骤S6，使用专用软件对接收到的数据进行解析。本发明设计了一种车载数据记录仪的数据监听方法及系统，通过应用FPGA和高速以太网技术，数据能够直接从总线被捕获，经过处理后直接存入DDR，并通过zero‑copy技术直接从DDR中读取并转发，在无需更改硬件配置或线缆连接的基础上使得数据记录仪可以灵活地适应各种测试需求，提高了车载数据监听效率。
",G07C5/08,车载以太网板卡技术,1.0,关键词匹配: 以太网,"包括, 测试, 方法, 处理, 系统, 配置",测试
CN117533251B,CN202410020462.3,"本发明涉及一种用于车载总线数据记录仪的分布式文件系统，尤其涉及车载总线数据处理技术领域，包括总线通讯控制模块，用以对车载总线数据进行采集；主节点控制模块，用以对子节点进行启动；调度策略模块，用以对车载总线数据设置调度策略，并根据数据特征变更写入场景，对调度策略进行补偿，还用以对车载总线数据进行热备策略判断；数据预处理模块，用以对车载总线数据进行预处理，得到预处理后的程式命令数据和Key‑Value式的快速索引数据；数据记录模块，用以将预处理后的程式命令数据和Key‑Value式的快速索引数据记录于子节点中，还用以将预处理后的程式命令数据和Key‑Value式的快速索引数据传输至热备设备中。本发明提高了车载总线数据的记录效率。
",B60R16/023,车载以太网板卡技术,3.0,关键词匹配: 车载总线; IPC分类号匹配: B60R16/023,"包括, 设置, 处理, 设备, 控制, 系统","数据传输, 控制"
CN117864040A,CN202410091952.2,"本发明提供一种通过可穿戴设备控制座舱功能的控制系统，其包括：可穿戴设备、嵌入式设备和车机子系统，其中，所述可穿戴设备具有至少三轴以上的方位、轨迹感知能力，用于和所述嵌入式设备进行无线通信；所述嵌入式设备将所述无线通信信息转换为车机识别的控制器局域网信号即控制器信号，再通过控制器局域网总线通信的方式把所述控制器信号发送至车机中控或网关后，传递至车机内部的相应控制器，所述控制器做出相应反馈。本发明可以以相对较低的价格，完成适用于大部分智能可穿戴设备和车机系统之间的匹配，同时可以帮助乘客通过可穿戴设备完成车内较多交互设备的控制，以及非自己区域内的控制器的控制。本发明可以提高乘客的娱乐性和交互体验。
",B60R16/023,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/023,"提供, 包括, 具有, 设备, 控制, 系统","通信, 控制"
CN117533251A,CN202410020462.3,"本发明涉及一种用于车载总线数据记录仪的分布式文件系统，尤其涉及车载总线数据处理技术领域，包括总线通讯控制模块，用以对车载总线数据进行采集；主节点控制模块，用以对子节点进行启动；调度策略模块，用以对车载总线数据设置调度策略，并根据数据特征变更写入场景，对调度策略进行补偿，还用以对车载总线数据进行热备策略判断；数据预处理模块，用以对车载总线数据进行预处理，得到预处理后的程式命令数据和Key‑Value式的快速索引数据；数据记录模块，用以将预处理后的程式命令数据和Key‑Value式的快速索引数据记录于子节点中，还用以将预处理后的程式命令数据和Key‑Value式的快速索引数据传输至热备设备中。本发明提高了车载总线数据的记录效率。
",B60R16/023,车载以太网板卡技术,3.0,关键词匹配: 车载总线; IPC分类号匹配: B60R16/023,"包括, 设置, 处理, 设备, 控制, 系统","数据传输, 控制"
CN115190031B,CN202210796410.6,"本公开的实施例公开了一种CAN总线数据仿真系统，该系统包括仿真单元、控制单元、收发单元以及以太网：仿真单元用于对CAN总线数据文件进行解析并生成CAN消息和CAN消息对应的发送时间信息，且仿真单元设置为定时向控制单元发送CAN消息集；控制单元用于接收CAN消息集，并将CAN消息集中的CAN消息进行排序，且控制单元设置为定时向收发单元发送CAN消息和发送时间信息；收发单元用于接收控制单元发送的CAN消息和发送时间信息，并按照发送时间信息定时发送CAN消息。该实施方式实现了以更低的成本完成周期性动态仿真及总线数据回注的需求。
",H04L41/14,车载以太网板卡技术,4.0,"关键词匹配: 以太网, CAN; IPC分类号匹配: H04L41/14","实现, 包括, 仿真, 生成, 设置, 控制, 系统",控制
CN115190031A,CN202210796410.6,"本公开的实施例公开了一种CAN总线数据仿真系统，该系统包括仿真单元、控制单元、收发单元以及以太网：仿真单元用于对CAN总线数据文件进行解析并生成CAN消息和CAN消息对应的发送时间信息，且仿真单元设置为定时向控制单元发送CAN消息集；控制单元用于接收CAN消息集，并将CAN消息集中的CAN消息进行排序，且控制单元设置为定时向收发单元发送CAN消息和发送时间信息；收发单元用于接收控制单元发送的CAN消息和发送时间信息，并按照发送时间信息定时发送CAN消息。该实施方式实现了以更低的成本完成周期性动态仿真及总线数据回注的需求。
",H04L41/14,车载以太网板卡技术,4.0,"关键词匹配: 以太网, CAN; IPC分类号匹配: H04L41/14","实现, 包括, 仿真, 生成, 设置, 控制, 系统",控制
CN114844741A,CN202210763425.2,"本公开的实施例公开了CAN总线数据存储方法、装置、电子设备和计算机可读介质。该方法包括：获取原始总线文件，其中，原始总线文件包括总线数据和总线数据对应的文本信息；将原始总线文件上传至分布式数据库中；在分布式数据库中，根据文本信息对总线数据进行解析，得到至少一个目标数据；对于至少一个目标数据中的每个目标数据，将目标数据写入分布式数据库中的目标表格。该实施方式解决了用户对于海量CAN总线数据存储问题的困扰，利用表格存储数据的方式也使得用户在数据查询时更加省时省力，提高用户体验和工作效率。
",H04L12/40,车载以太网板卡技术,3.0,关键词匹配: CAN; IPC分类号匹配: H04L12/40,"包括, 装置, 方法, 设备, 计算",通用
