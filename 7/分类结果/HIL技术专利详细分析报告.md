# dSPACE HIL相关技术专利详细分析报告

## 概述

本报告对dSPACE公司的20项专利进行了技术分类分析，特别关注HIL（硬件在环）相关技术的专利布局情况。

## HIL相关技术专利总览

HIL相关技术专利总数: 15 条 (75.0%)

## HIL硬件在环仿真技术

**专利数量**: 1 条

**技术描述**: HIL硬件在环仿真相关技术，包括实时仿真、硬件模拟器等

**重点专利**:

### CN308457368S
- **申请号**: CN202330547050.1
- **分类理由**: 关键词匹配: 仿真器
- **技术手段**: 仿真, 模拟
- **应用场景**: 汽车
- **技术摘要**: 1.本外观设计产品的名称：智能车载信号仿真器。
2.本外观设计产品的用途：用于实时模拟仿真汽车总线所需要的各种信号。
3.本外观设计产品的设计要点：在于形状。
4.最能表明设计要点的图片或照片：设计1使用状态参考图1。
5.指定本外观产品为相似设计，指定设计1为基本设计。


---

## 故障注入板卡技术

**专利数量**: 1 条

**技术描述**: 故障注入相关技术，用于测试系统的故障处理能力

**重点专利**:

### CN213634467U
- **申请号**: CN202022927121.4
- **IPC分类号**: G06F11/36
- **分类理由**: IPC分类号匹配: G06F11/36
- **技术手段**: 测试, 包括, 设置, 模拟
- **应用场景**: 测试
- **技术摘要**: 一种数字座舱软件集成静态模拟测试台架，包括：本体、上配合端面、下配合端面、预留槽、落地支架,所述本体呈楔形状，且本体后端敞口设计，位于其楔形状底部固定有下配合端面，位于所述本体上固定设有上配合端面，上配合端面通过螺装或是焊接方式连接于本体上，所述本体内为中空结构，且位于本体内设有格挡，位于所述本体设有落地支架，所述落地支架与本体之间通过连接杆螺装或是焊接方式连接，位于所述下配合端面上第三配合槽及第...

---

## 车载以太网板卡技术

**专利数量**: 7 条

**技术描述**: 车载以太网通信技术，包括车载网络协议和通信接口

**重点专利**:

### CN115190031B
- **申请号**: CN202210796410.6
- **IPC分类号**: H04L41/14
- **分类理由**: 关键词匹配: 以太网, CAN; IPC分类号匹配: H04L41/14
- **技术手段**: 实现, 包括, 仿真, 生成, 设置, 控制, 系统
- **应用场景**: 控制
- **技术摘要**: 本公开的实施例公开了一种CAN总线数据仿真系统，该系统包括仿真单元、控制单元、收发单元以及以太网：仿真单元用于对CAN总线数据文件进行解析并生成CAN消息和CAN消息对应的发送时间信息，且仿真单元设置为定时向控制单元发送CAN消息集；控制单元用于接收CAN消息集，并将CAN消息集中的CAN消息进行排序，且控制单元设置为定时向收发单元发送CAN消息和发送时间信息；收发单元用于接收控制单元发送的CAN...

### CN115190031A
- **申请号**: CN202210796410.6
- **IPC分类号**: H04L41/14
- **分类理由**: 关键词匹配: 以太网, CAN; IPC分类号匹配: H04L41/14
- **技术手段**: 实现, 包括, 仿真, 生成, 设置, 控制, 系统
- **应用场景**: 控制
- **技术摘要**: 本公开的实施例公开了一种CAN总线数据仿真系统，该系统包括仿真单元、控制单元、收发单元以及以太网：仿真单元用于对CAN总线数据文件进行解析并生成CAN消息和CAN消息对应的发送时间信息，且仿真单元设置为定时向控制单元发送CAN消息集；控制单元用于接收CAN消息集，并将CAN消息集中的CAN消息进行排序，且控制单元设置为定时向收发单元发送CAN消息和发送时间信息；收发单元用于接收控制单元发送的CAN...

### CN117533251B
- **申请号**: CN202410020462.3
- **IPC分类号**: B60R16/023
- **分类理由**: 关键词匹配: 车载总线; IPC分类号匹配: B60R16/023
- **技术手段**: 包括, 设置, 处理, 设备, 控制, 系统
- **应用场景**: 数据传输, 控制
- **技术摘要**: 本发明涉及一种用于车载总线数据记录仪的分布式文件系统，尤其涉及车载总线数据处理技术领域，包括总线通讯控制模块，用以对车载总线数据进行采集；主节点控制模块，用以对子节点进行启动；调度策略模块，用以对车载总线数据设置调度策略，并根据数据特征变更写入场景，对调度策略进行补偿，还用以对车载总线数据进行热备策略判断；数据预处理模块，用以对车载总线数据进行预处理，得到预处理后的程式命令数据和Key‑Value...

### CN117533251A
- **申请号**: CN202410020462.3
- **IPC分类号**: B60R16/023
- **分类理由**: 关键词匹配: 车载总线; IPC分类号匹配: B60R16/023
- **技术手段**: 包括, 设置, 处理, 设备, 控制, 系统
- **应用场景**: 数据传输, 控制
- **技术摘要**: 本发明涉及一种用于车载总线数据记录仪的分布式文件系统，尤其涉及车载总线数据处理技术领域，包括总线通讯控制模块，用以对车载总线数据进行采集；主节点控制模块，用以对子节点进行启动；调度策略模块，用以对车载总线数据设置调度策略，并根据数据特征变更写入场景，对调度策略进行补偿，还用以对车载总线数据进行热备策略判断；数据预处理模块，用以对车载总线数据进行预处理，得到预处理后的程式命令数据和Key‑Value...

### CN114844741A
- **申请号**: CN202210763425.2
- **IPC分类号**: H04L12/40
- **分类理由**: 关键词匹配: CAN; IPC分类号匹配: H04L12/40
- **技术手段**: 包括, 装置, 方法, 设备, 计算
- **应用场景**: 通用
- **技术摘要**: 本公开的实施例公开了CAN总线数据存储方法、装置、电子设备和计算机可读介质。该方法包括：获取原始总线文件，其中，原始总线文件包括总线数据和总线数据对应的文本信息；将原始总线文件上传至分布式数据库中；在分布式数据库中，根据文本信息对总线数据进行解析，得到至少一个目标数据；对于至少一个目标数据中的每个目标数据，将目标数据写入分布式数据库中的目标表格。该实施方式解决了用户对于海量CAN总线数据存储问题的...

---

## 数据注入类型支持技术

**专利数量**: 6 条

**技术描述**: 不同数据注入类型的支持技术，包括数据格式转换和处理

**重点专利**:

### CN117763194B
- **申请号**: CN202311754382.2
- **IPC分类号**: G06F16/65
- **分类理由**: 关键词匹配: 数据处理; IPC分类号匹配: G06F16/65
- **技术手段**: 包括, 模型, 设置, 处理, 系统
- **应用场景**: 车辆, 汽车
- **技术摘要**: 本发明涉及一种基于大语言模型的车载数据语音标签系统，尤其涉及汽车数据处理技术领域，包括数据记录模块，用以对车辆的路测数据进行采集；语音记录模块，用以对语音记录的开启进行判断，并对语音记录开启的判断过程进行修正，还用以对记录后的语音进行预处理和存储；语音处理模块，用以通过LLM大语言模型对已存储的语音进行标签识别并设置索引；数据上传模块，用以将语音识别后的语音进行切片处理并上传至云端进行分类存储；反...

### CN117763194A
- **申请号**: CN202311754382.2
- **IPC分类号**: G06F16/65
- **分类理由**: 关键词匹配: 数据处理; IPC分类号匹配: G06F16/65
- **技术手段**: 包括, 模型, 设置, 处理, 系统
- **应用场景**: 车辆, 汽车
- **技术摘要**: 本发明涉及一种基于大语言模型的车载数据语音标签系统，尤其涉及汽车数据处理技术领域，包括数据记录模块，用以对车辆的路测数据进行采集；语音记录模块，用以对语音记录的开启进行判断，并对语音记录开启的判断过程进行修正，还用以对记录后的语音进行预处理和存储；语音处理模块，用以通过LLM大语言模型对已存储的语音进行标签识别并设置索引；数据上传模块，用以将语音识别后的语音进行切片处理并上传至云端进行分类存储；反...

### CN113934673B
- **申请号**: CN202111536030.0
- **IPC分类号**: G06F13/42
- **分类理由**: 关键词匹配: 数据传输; IPC分类号匹配: G06F13/42
- **技术手段**: 提供, 实现, 测试, 具有, 设置, 设备, 系统, 电路
- **应用场景**: 测试, 数据传输
- **技术摘要**: 本申请公开了一种数据传输隔离电路及数据传输设备，所述隔离电路具有多个接收端口以及发送端口，所述隔离电路的多个接收端口与多个数据发送设备的通用异步收发传输器的发送端口连接；所述隔离电路的发送端口与数据接收设备的通用异步收发传输器的接收端口连接；所述隔离电路能够实现多个数据发送设备的通用异步收发传输器的发送端口输出信号的与操作。本申请提供的技术方案能够屏蔽信号之间的干扰，达到多路串口双向通讯的目的，使...

### CN113934673A
- **申请号**: CN202111536030.0
- **IPC分类号**: G06F13/42
- **分类理由**: 关键词匹配: 数据传输; IPC分类号匹配: G06F13/42
- **技术手段**: 提供, 实现, 测试, 具有, 设置, 设备, 系统, 电路
- **应用场景**: 测试, 数据传输
- **技术摘要**: 本申请公开了一种数据传输隔离电路及数据传输设备，所述隔离电路具有多个接收端口以及发送端口，所述隔离电路的多个接收端口与多个数据发送设备的通用异步收发传输器的发送端口连接；所述隔离电路的发送端口与数据接收设备的通用异步收发传输器的接收端口连接；所述隔离电路能够实现多个数据发送设备的通用异步收发传输器的发送端口输出信号的与操作。本申请提供的技术方案能够屏蔽信号之间的干扰，达到多路串口双向通讯的目的，使...

### CN114863588B
- **申请号**: CN202210786694.0
- **IPC分类号**: G07C5/08
- **分类理由**: 关键词匹配: 数据传输
- **技术手段**: 提供, 实现, 包括, 处理, 设备, 控制
- **应用场景**: 数据传输, 控制
- **技术摘要**: 本发明提供一种数据记录仪，其包括电源控制模块、内置的存储介质、总线交换控制器和处理器、设备电源接口和总线电源接口；设备电源接口用于与车载主机连接，总线电源接口用于与外部电源设备连接；电源控制模块连接于设备电源接口和总线电源接口，用于向总线交换控制器和存储介质供电；存储介质连接于总线交换控制器、并通过总线交换控制器分别与处理器和外部电源设备连接，以使存储介质能够被处理器或外部电源设备访问以进行数据传...

---

## 技术发展趋势分析

### 核心技术布局

1. **HIL硬件在环仿真技术** (62条, 14.4%): dSPACE在HIL技术方面拥有最多专利，体现了其在硬件在环仿真领域的技术领先地位。

2. **故障注入板卡技术** (50条, 11.6%): 故障注入技术是HIL测试的重要组成部分，用于验证系统的故障处理能力。

3. **数据注入类型支持技术** (54条, 12.5%): 支持多种数据类型的注入，提高了测试系统的灵活性和适用性。

### 技术特点

- **实时性**: 大量专利涉及实时仿真和实时数据处理
- **模块化**: 采用模块化设计，支持不同类型的板卡和接口
- **标准化**: 支持多种工业标准和通信协议
- **智能化**: 集成机器学习和人工智能技术

### 应用领域

- **汽车电子**: 车载网络、自动驾驶、电动汽车控制系统测试
- **航空航天**: 飞行控制系统、导航系统测试
- **工业自动化**: 工业控制系统、机器人控制测试
- **新能源**: 电池管理系统、电力电子设备测试

