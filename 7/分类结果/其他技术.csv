﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,技术领域,分类得分,分类理由,技术手段,应用场景
CN308457950S,CN202330547049.9,"1.本外观设计产品的名称：智能车载数据记录仪。
2.本外观设计产品的用途：用于记录车载数据总线上的数据。
3.本外观设计产品的设计要点：在于形状。
4.最能表明设计要点的图片或照片：立体图。
",,其他技术,0.0,未匹配到特定技术领域,未明确,通用
CN114936358A,CN202210657106.3,"本发明提供一种基于人机交互的校验方法及校验系统，其中方法包括：生成测试案例的测试脚本，所述测试案例用于对测试台架进行测试；执行所述测试案例的测试脚本；向测试台架施加所述测试案例的测试脚本对应的操控行为；获取测试台架被所述操控行为操控时的第一实时图像；判断获取到的所述第一实时图像是否符合要求：若判断为符合，则生成通过报告；若判断为不符合，则生成失败报告。本发明通过向测试台架施加测试脚本的操控行为对测试台架进行控制，获取测试台架执行操控行为的第一实时图像作为判断基础，并对第一实时图像判断得出测试结论，实现测试台架的控制，测试台架的操控行为是否符合要求的判断，使得校验更全面。
",G06F21/31,其他技术,0.0,未匹配到特定技术领域,"提供, 实现, 包括, 测试, 方法, 生成, 控制, 系统","测试, 控制"
CN113947721B,CN202111558371.8,"本申请公开了一种娱乐系统测试的图像校验方法及系统。该方法包括：获取娱乐系统的中控装置发送至显示设备的图像，并获取摄像头拍摄的显示设备上的图像；对所获取的中控装置发送的图像进行图像识别，得到第一识别结果；对获取的所述摄像头拍摄的显示设备上的图像进行图像识别，得到第二识别结果；对第一识别结果和第二识别结果进行同步，并对第一识别结果和第二识别结果进行图像校验。本申请提供的技术方案能够增加图像识别的精确度并提高识别的速度，既满足了端到端的测试目的，又能提高识别的效率。
",G06V20/10,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 测试, 装置, 方法, 设备, 系统","测试, 摄像"
CN113947721A,CN202111558371.8,"本申请公开了一种娱乐系统测试的图像校验方法及系统。该方法包括：获取娱乐系统的中控装置发送至显示设备的图像，并获取摄像头拍摄的显示设备上的图像；对所获取的中控装置发送的图像进行图像识别，得到第一识别结果；对获取的所述摄像头拍摄的显示设备上的图像进行图像识别，得到第二识别结果；对第一识别结果和第二识别结果进行同步，并对第一识别结果和第二识别结果进行图像校验。本申请提供的技术方案能够增加图像识别的精确度并提高识别的速度，既满足了端到端的测试目的，又能提高识别的效率。
",G06V20/10,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 测试, 装置, 方法, 设备, 系统","测试, 摄像"
CN213768425U,CN202022932924.9,"一种对车载系统进行地图灌装的测试设备，包括：本体、支撑板、第一固定板、支撑架、第一固定体、支撑件、第二固定板、固定盖板、第一连杆、第二连杆、扳手、轴承座、滑轨、托板,所述本体为梯形状，且内部中空，位于所述本体后端固定通过螺装连接支撑板，所述支撑板上端固定与第一固定板螺装连接，位于所述第一固定板上端面通过螺装连接着支撑架，位于所述本体上端面固定对称设有滑轨，所述滑轨上镶嵌设有滑块，位于所述本体上端面后端临近支撑板位置固定对称设有轴承座，轴承座内设有连接轴，位于所述连接轴一侧固定设有扳手，位于所述轴承座一侧与扳手位置设有第一连杆，所述第一连杆与连接轴固定配合，所述第一连杆通过螺装连接着第二连杆。
",B60R11/00,其他技术,0.0,未匹配到特定技术领域,"系统, 测试, 包括, 设备",测试
