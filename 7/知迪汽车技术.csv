﻿公开(公告)号,申请号,摘要(中文),IPC主分类号
CN117763194B,CN202311754382.2,"本发明涉及一种基于大语言模型的车载数据语音标签系统，尤其涉及汽车数据处理技术领域，包括数据记录模块，用以对车辆的路测数据进行采集；语音记录模块，用以对语音记录的开启进行判断，并对语音记录开启的判断过程进行修正，还用以对记录后的语音进行预处理和存储；语音处理模块，用以通过LLM大语言模型对已存储的语音进行标签识别并设置索引；数据上传模块，用以将语音识别后的语音进行切片处理并上传至云端进行分类存储；反馈训练模块，用以根据对LLM大语言模型进行训练，并对语音处理的有效性进行反馈，还用以对反馈过程进行补偿。本发明提高了车载数据的语音标签效率。
",G06F16/65
CN118015726A,CN202311864563.0,"本发明涉及一种车载数据记录仪的数据监听方法及系统，尤其涉及车载数据监听技术领域，包括步骤S1，通过桌面软件配置目标总线端口和协议；步骤S2，从总线端口接收数据；步骤S3，使用FPGA将数据存储到DDR；步骤S4，通过SoC从DDR读取数据；步骤S5，通过高速以太网接口转发数据；步骤S6，使用专用软件对接收到的数据进行解析。本发明设计了一种车载数据记录仪的数据监听方法及系统，通过应用FPGA和高速以太网技术，数据能够直接从总线被捕获，经过处理后直接存入DDR，并通过zero‑copy技术直接从DDR中读取并转发，在无需更改硬件配置或线缆连接的基础上使得数据记录仪可以灵活地适应各种测试需求，提高了车载数据监听效率。
",G07C5/08
CN117533251B,CN202410020462.3,"本发明涉及一种用于车载总线数据记录仪的分布式文件系统，尤其涉及车载总线数据处理技术领域，包括总线通讯控制模块，用以对车载总线数据进行采集；主节点控制模块，用以对子节点进行启动；调度策略模块，用以对车载总线数据设置调度策略，并根据数据特征变更写入场景，对调度策略进行补偿，还用以对车载总线数据进行热备策略判断；数据预处理模块，用以对车载总线数据进行预处理，得到预处理后的程式命令数据和Key‑Value式的快速索引数据；数据记录模块，用以将预处理后的程式命令数据和Key‑Value式的快速索引数据记录于子节点中，还用以将预处理后的程式命令数据和Key‑Value式的快速索引数据传输至热备设备中。本发明提高了车载总线数据的记录效率。
",B60R16/023
CN117864040A,CN202410091952.2,"本发明提供一种通过可穿戴设备控制座舱功能的控制系统，其包括：可穿戴设备、嵌入式设备和车机子系统，其中，所述可穿戴设备具有至少三轴以上的方位、轨迹感知能力，用于和所述嵌入式设备进行无线通信；所述嵌入式设备将所述无线通信信息转换为车机识别的控制器局域网信号即控制器信号，再通过控制器局域网总线通信的方式把所述控制器信号发送至车机中控或网关后，传递至车机内部的相应控制器，所述控制器做出相应反馈。本发明可以以相对较低的价格，完成适用于大部分智能可穿戴设备和车机系统之间的匹配，同时可以帮助乘客通过可穿戴设备完成车内较多交互设备的控制，以及非自己区域内的控制器的控制。本发明可以提高乘客的娱乐性和交互体验。
",B60R16/023
CN117763194A,CN202311754382.2,"本发明涉及一种基于大语言模型的车载数据语音标签系统，尤其涉及汽车数据处理技术领域，包括数据记录模块，用以对车辆的路测数据进行采集；语音记录模块，用以对语音记录的开启进行判断，并对语音记录开启的判断过程进行修正，还用以对记录后的语音进行预处理和存储；语音处理模块，用以通过LLM大语言模型对已存储的语音进行标签识别并设置索引；数据上传模块，用以将语音识别后的语音进行切片处理并上传至云端进行分类存储；反馈训练模块，用以根据对LLM大语言模型进行训练，并对语音处理的有效性进行反馈，还用以对反馈过程进行补偿。本发明提高了车载数据的语音标签效率。
",G06F16/65
CN117533251A,CN202410020462.3,"本发明涉及一种用于车载总线数据记录仪的分布式文件系统，尤其涉及车载总线数据处理技术领域，包括总线通讯控制模块，用以对车载总线数据进行采集；主节点控制模块，用以对子节点进行启动；调度策略模块，用以对车载总线数据设置调度策略，并根据数据特征变更写入场景，对调度策略进行补偿，还用以对车载总线数据进行热备策略判断；数据预处理模块，用以对车载总线数据进行预处理，得到预处理后的程式命令数据和Key‑Value式的快速索引数据；数据记录模块，用以将预处理后的程式命令数据和Key‑Value式的快速索引数据记录于子节点中，还用以将预处理后的程式命令数据和Key‑Value式的快速索引数据传输至热备设备中。本发明提高了车载总线数据的记录效率。
",B60R16/023
CN308457950S,CN202330547049.9,"1.本外观设计产品的名称：智能车载数据记录仪。
2.本外观设计产品的用途：用于记录车载数据总线上的数据。
3.本外观设计产品的设计要点：在于形状。
4.最能表明设计要点的图片或照片：立体图。
",
CN308457368S,CN202330547050.1,"1.本外观设计产品的名称：智能车载信号仿真器。
2.本外观设计产品的用途：用于实时模拟仿真汽车总线所需要的各种信号。
3.本外观设计产品的设计要点：在于形状。
4.最能表明设计要点的图片或照片：设计1使用状态参考图1。
5.指定本外观产品为相似设计，指定设计1为基本设计。
",
CN115190031B,CN202210796410.6,"本公开的实施例公开了一种CAN总线数据仿真系统，该系统包括仿真单元、控制单元、收发单元以及以太网：仿真单元用于对CAN总线数据文件进行解析并生成CAN消息和CAN消息对应的发送时间信息，且仿真单元设置为定时向控制单元发送CAN消息集；控制单元用于接收CAN消息集，并将CAN消息集中的CAN消息进行排序，且控制单元设置为定时向收发单元发送CAN消息和发送时间信息；收发单元用于接收控制单元发送的CAN消息和发送时间信息，并按照发送时间信息定时发送CAN消息。该实施方式实现了以更低的成本完成周期性动态仿真及总线数据回注的需求。
",H04L41/14
CN115190031A,CN202210796410.6,"本公开的实施例公开了一种CAN总线数据仿真系统，该系统包括仿真单元、控制单元、收发单元以及以太网：仿真单元用于对CAN总线数据文件进行解析并生成CAN消息和CAN消息对应的发送时间信息，且仿真单元设置为定时向控制单元发送CAN消息集；控制单元用于接收CAN消息集，并将CAN消息集中的CAN消息进行排序，且控制单元设置为定时向收发单元发送CAN消息和发送时间信息；收发单元用于接收控制单元发送的CAN消息和发送时间信息，并按照发送时间信息定时发送CAN消息。该实施方式实现了以更低的成本完成周期性动态仿真及总线数据回注的需求。
",H04L41/14
CN114863588B,CN202210786694.0,"本发明提供一种数据记录仪，其包括电源控制模块、内置的存储介质、总线交换控制器和处理器、设备电源接口和总线电源接口；设备电源接口用于与车载主机连接，总线电源接口用于与外部电源设备连接；电源控制模块连接于设备电源接口和总线电源接口，用于向总线交换控制器和存储介质供电；存储介质连接于总线交换控制器、并通过总线交换控制器分别与处理器和外部电源设备连接，以使存储介质能够被处理器或外部电源设备访问以进行数据传输。本发明的数据记录仪采取内置的存储介质，其一降低了数据记录仪的物理尺寸；其二能够实现数据高速下载。
",G07C5/08
CN114936358A,CN202210657106.3,"本发明提供一种基于人机交互的校验方法及校验系统，其中方法包括：生成测试案例的测试脚本，所述测试案例用于对测试台架进行测试；执行所述测试案例的测试脚本；向测试台架施加所述测试案例的测试脚本对应的操控行为；获取测试台架被所述操控行为操控时的第一实时图像；判断获取到的所述第一实时图像是否符合要求：若判断为符合，则生成通过报告；若判断为不符合，则生成失败报告。本发明通过向测试台架施加测试脚本的操控行为对测试台架进行控制，获取测试台架执行操控行为的第一实时图像作为判断基础，并对第一实时图像判断得出测试结论，实现测试台架的控制，测试台架的操控行为是否符合要求的判断，使得校验更全面。
",G06F21/31
CN114863588A,CN202210786694.0,"本发明提供一种数据记录仪，其包括电源控制模块、内置的存储介质、总线交换控制器和处理器、设备电源接口和总线电源接口；设备电源接口用于与车载主机连接，总线电源接口用于与外部电源设备连接；电源控制模块连接于设备电源接口和总线电源接口，用于向总线交换控制器和存储介质供电；存储介质连接于总线交换控制器、并通过总线交换控制器分别与处理器和外部电源设备连接，以使存储介质能够被处理器或外部电源设备访问以进行数据传输。本发明的数据记录仪采取内置的存储介质，其一降低了数据记录仪的物理尺寸；其二能够实现数据高速下载。
",G07C5/08
CN114844741A,CN202210763425.2,"本公开的实施例公开了CAN总线数据存储方法、装置、电子设备和计算机可读介质。该方法包括：获取原始总线文件，其中，原始总线文件包括总线数据和总线数据对应的文本信息；将原始总线文件上传至分布式数据库中；在分布式数据库中，根据文本信息对总线数据进行解析，得到至少一个目标数据；对于至少一个目标数据中的每个目标数据，将目标数据写入分布式数据库中的目标表格。该实施方式解决了用户对于海量CAN总线数据存储问题的困扰，利用表格存储数据的方式也使得用户在数据查询时更加省时省力，提高用户体验和工作效率。
",H04L12/40
CN113947721B,CN202111558371.8,"本申请公开了一种娱乐系统测试的图像校验方法及系统。该方法包括：获取娱乐系统的中控装置发送至显示设备的图像，并获取摄像头拍摄的显示设备上的图像；对所获取的中控装置发送的图像进行图像识别，得到第一识别结果；对获取的所述摄像头拍摄的显示设备上的图像进行图像识别，得到第二识别结果；对第一识别结果和第二识别结果进行同步，并对第一识别结果和第二识别结果进行图像校验。本申请提供的技术方案能够增加图像识别的精确度并提高识别的速度，既满足了端到端的测试目的，又能提高识别的效率。
",G06V20/10
CN113934673B,CN202111536030.0,"本申请公开了一种数据传输隔离电路及数据传输设备，所述隔离电路具有多个接收端口以及发送端口，所述隔离电路的多个接收端口与多个数据发送设备的通用异步收发传输器的发送端口连接；所述隔离电路的发送端口与数据接收设备的通用异步收发传输器的接收端口连接；所述隔离电路能够实现多个数据发送设备的通用异步收发传输器的发送端口输出信号的与操作。本申请提供的技术方案能够屏蔽信号之间的干扰，达到多路串口双向通讯的目的，使车机系统开发和测试人员能够在不同设备与接口类型上读写车机日志和设置。
",G06F13/42
CN113947721A,CN202111558371.8,"本申请公开了一种娱乐系统测试的图像校验方法及系统。该方法包括：获取娱乐系统的中控装置发送至显示设备的图像，并获取摄像头拍摄的显示设备上的图像；对所获取的中控装置发送的图像进行图像识别，得到第一识别结果；对获取的所述摄像头拍摄的显示设备上的图像进行图像识别，得到第二识别结果；对第一识别结果和第二识别结果进行同步，并对第一识别结果和第二识别结果进行图像校验。本申请提供的技术方案能够增加图像识别的精确度并提高识别的速度，既满足了端到端的测试目的，又能提高识别的效率。
",G06V20/10
CN113934673A,CN202111536030.0,"本申请公开了一种数据传输隔离电路及数据传输设备，所述隔离电路具有多个接收端口以及发送端口，所述隔离电路的多个接收端口与多个数据发送设备的通用异步收发传输器的发送端口连接；所述隔离电路的发送端口与数据接收设备的通用异步收发传输器的接收端口连接；所述隔离电路能够实现多个数据发送设备的通用异步收发传输器的发送端口输出信号的与操作。本申请提供的技术方案能够屏蔽信号之间的干扰，达到多路串口双向通讯的目的，使车机系统开发和测试人员能够在不同设备与接口类型上读写车机日志和设置。
",G06F13/42
CN213768425U,CN202022932924.9,"一种对车载系统进行地图灌装的测试设备，包括：本体、支撑板、第一固定板、支撑架、第一固定体、支撑件、第二固定板、固定盖板、第一连杆、第二连杆、扳手、轴承座、滑轨、托板,所述本体为梯形状，且内部中空，位于所述本体后端固定通过螺装连接支撑板，所述支撑板上端固定与第一固定板螺装连接，位于所述第一固定板上端面通过螺装连接着支撑架，位于所述本体上端面固定对称设有滑轨，所述滑轨上镶嵌设有滑块，位于所述本体上端面后端临近支撑板位置固定对称设有轴承座，轴承座内设有连接轴，位于所述连接轴一侧固定设有扳手，位于所述轴承座一侧与扳手位置设有第一连杆，所述第一连杆与连接轴固定配合，所述第一连杆通过螺装连接着第二连杆。
",B60R11/00
CN213634467U,CN202022927121.4,"一种数字座舱软件集成静态模拟测试台架，包括：本体、上配合端面、下配合端面、预留槽、落地支架,所述本体呈楔形状，且本体后端敞口设计，位于其楔形状底部固定有下配合端面，位于所述本体上固定设有上配合端面，上配合端面通过螺装或是焊接方式连接于本体上，所述本体内为中空结构，且位于本体内设有格挡，位于所述本体设有落地支架，所述落地支架与本体之间通过连接杆螺装或是焊接方式连接，位于所述下配合端面上第三配合槽及第四配合槽，所述第四配合槽位于第三配合槽下方靠近下配合端面边缘位置，且第四配合槽左右对称设置，位于所述下配合端面上靠近本体楔形状末端固定开有u型槽，u型槽与第三配合槽相对。
",G06F11/36