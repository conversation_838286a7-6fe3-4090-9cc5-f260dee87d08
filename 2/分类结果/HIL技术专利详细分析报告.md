# dSPACE HIL相关技术专利详细分析报告

## 概述

本报告对dSPACE公司的423项专利进行了技术分类分析，特别关注HIL（硬件在环）相关技术的专利布局情况。

## HIL相关技术专利总览

HIL相关技术专利总数: 103 条 (24.3%)

## HIL硬件在环仿真技术

**专利数量**: 6 条

**技术描述**: HIL硬件在环仿真相关技术，包括实时仿真、硬件模拟器等

**主要申请人**:
- NATIONAL INSTRUMENTS CORPORATION: 5 条
- National Instruments Corporation: 1 条

**重点专利**:

### US20250076907A1
- **申请号**: US18817742
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 环路
- **技术手段**: 提供, 实现, 控制, 系统, 电路
- **应用场景**: 控制
- **技术摘要**: 一种控制系统在恒定电压(CV)控制模式下表现出改进的动态性能，该模式也不随源类型和工作点而变化。通过将压控电流源替换为压控电阻作为系统反馈回路中的控制元件，可以在控制电路的附加部件最少或潜在地没有附加部件的情况下实现改进。测量来自被测器件(DUT)的输入电压，至少部分地基于输入电压确定反馈控制电压以提供用于DUT的CV模式控制环路，并且将添加到输入电压的反馈控制电压施加到DUT以在CV模式下操作D...

### US20160034617A1
- **申请号**: US14808116
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 仿真器
- **技术手段**: 平台, 包括, 算法, 仿真, 方法, 生成, 处理, 系统
- **应用场景**: 验证
- **技术摘要**: 用于创建机器视觉应用的系统和方法。包括指定机器视觉图像处理算法和相关联的参数的多个机器视觉步骤的机器视觉原型可被存储。步骤可以可解释为由仿真器解释以通过在硬件加速器上仿真或仿真步骤的执行来在图像上执行所指定的图像处理，硬件加速器例如是可编程硬件元件或图形处理单元。仿真器可仿真或仿真在硬件加速器上的步骤的执行，由此生成图像处理结果，其可被显示以用于验证用户的仿真或仿真。原型可被分析，并且基于该分析，...

### US20150130526A1
- **申请号**: US14080053
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 环路
- **技术手段**: 模拟, 实现, 装置, 具有, 控制, 电路, 配置
- **应用场景**: 控制
- **技术摘要**: 用于测量装置(例如示波器或数字化转换器)的前端电路可以使用可编程可变电阻实现DC增益补偿。 MOS晶体管可以配置成并且操作为具有快速自校准能力同时补偿温度变化的线性电阻器。基于CMOS的集成可变电阻器可以由此用于模拟可调节衰减器。主CMOS晶体管和从CMOS晶体管可以在线性模式中操作，并且可以通过使用配置在主MOS晶体管周围的集成环路控制器(电流控制器)来补偿线性晶体管上的温度效应。用补偿的可变电...

### US06823221B2
- **申请号**: US09997638
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 环路
- **技术手段**: 提供, 装置, 方法, 生成, 控制, 系统
- **应用场景**: 控制
- **技术摘要**: 运动控制系统和方法公开了提供改进的脉冲顺行放置的运动装置，例如步进马达。放置心律不齐可以对于多个时间间隔使得脉冲均匀地置于在多个时间间隔，其中心律不齐在每个时间间隔的数量是可变的。该脉冲可以生成并发送到运动装置将对象移动至期望位置。延迟可以被使用以将每个脉冲内任意位置的一个时间间隔。在期望的步进率为分数，时间可以被&ldquo；borrowed&rdquo；从其它为了他的缘故循环迭代循环迭代。在一...

### US06081751A
- **申请号**: US09/054556
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 环路
- **技术手段**: 提供, 包括, 模型, 方法, 控制, 系统, 计算
- **应用场景**: 控制
- **技术摘要**: 一种系统和方法，用于自动地调谐PID控制器驻留在PID控制环路。PID控制环路包括PID控制器及工艺。该进程提供过程变量进行比较，该环路的输入。比较的结果提供给PID控制器，PID控制器驱动过程。继电器应用于环路的输入。继电器比较设定点值与过程变量。如果设定点值大于过程变量，该继电器驱动回路输入与第一振幅值。如果设定点值小于过程变量，该继电器驱动回路输入与第二幅度值。响应设定点继电器，过程变量产生...

---

## 模拟数字转换板卡技术

**专利数量**: 49 条

**技术描述**: 模拟数字转换相关技术，包括ADC、DAC等转换器技术

**主要申请人**:
- NATIONAL INSTRUMENTS CORPORATION: 43 条
- National Instruments Corporation: 2 条
- NATIONAL INSTRUMENTS CORP.: 2 条
- 美国国家仪器有限公司: 1 条
- NATIONAL INSTRUMENTS, INC.: 1 条

**重点专利**:

### US06188347B1
- **申请号**: US09351758
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 模数转换, 数模转换, 转换器, 信号转换, 模拟信号, 数字信号
- **技术手段**: 提供, 包括, 控制, 系统, 模拟
- **应用场景**: 控制
- **技术摘要**: 模数转换与减少闪烁代码。模数转换器包括多个比较器，每个比较器耦合到接收模拟输入信号，并且加法器译码器连接成接收比较器的输出控制。每个比较器接收相应的参考信号为与模拟输入信号。每个比较器的输出之间的比较数字值表示模拟输入信号与相应的参考信号。加法器相加该解码器产生的数字输出信号由比较器输出代表模拟输入信号的数字表示式基于该结果。该系统可以有利地提供更有效的方式将模拟信号转换成数字信号，不产生闪烁代码...

### US20170139001A1
- **申请号**: US14939775
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 模数转换, 数模转换, ADC, DAC, 转换器
- **技术手段**: 提供, 实现, 包括, 具有, 控制, 系统
- **应用场景**: 控制
- **技术摘要**: 一种改进的测量系统可包括能够执行精确的低电平电流测量的源测量单元(SMU) 。基于在测量端子处提供具有精确电流限制的受控DC电压源和受控0V (零伏) DC的SMU设计，可实现AC设计以在指定频率范围内建立相同(或非常类似)的条件。代替控制每个数模转换器(DAC)在SMU的相应源端子处作为相应DC输出，每个DAC可被控制为具有可编程频率和连续可变相位和幅度的相应函数发生器。现货供应的流水线模数转换...

### US09621387B1
- **申请号**: US14879285
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 模数转换, 数模转换, ADC, DAC, 转换器
- **技术手段**: 生成, 包括
- **应用场景**: 通用
- **技术摘要**: 一种改进的正交调制器/解调器(IQD)可使用二相正交本振器(LO)信号生成来生成00和900 LO信号，以及RF (射频)端口上的反相组合器/分频器(在00和1800处) 。 IQD可包括混频器(可以是双平衡无源混频器)，其在信号以其射频(RF)端口入射时用作下变频器，且在信号以其中频(IF)端口入射时用作上变频器。因此，IQD可通过将数模转换器(DAC)连接至差分I和Q端口来用作I/Q调制器，和...

### US06594612B2
- **申请号**: US09735234
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 模数转换, 转换器, 信号转换, 模拟信号, 数字信号
- **技术手段**: 系统, 包括, 装置, 模拟
- **应用场景**: 通用
- **技术摘要**: 数字化仪用于测量系统。数字化仪获得从外部源，并且包括静态随机存取存储器( SRAM )，存储把扫描列表包括条目指定操作，例如数字化仪的开关时间，稳定时间，测量的时间，循环，数学运算规格，如缩放、增加，平均规格。该套说明书可以包括指令以重复地执行一个或多个条目的扫描列表。数字化装置包含可编程逻辑元件(例如耦合到SRAM FPGA )哪些存取并执行该扫描列表来获取模拟信号从源。数字化仪可以包括模数转换...

### US10838000B2
- **申请号**: US15922051
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 模数转换, ADC, 转换器, 模拟信号
- **技术手段**: 测试, 装置, 具有, 方法, 生成, 处理, 电路, 模拟
- **应用场景**: 测试
- **技术摘要**: 公开了用于在多个频率处同时测试组件的方法和装置。数字处理电路可以生成具有多个正弦波的信号的数字表示，每个正弦波具有唯一频率。数字表示可以被转换成模拟信号，并且被施加到被测器件(DUT) 。第一模数转换器(ADC)可以被耦合以测量跨DUT的电压，而第二ADC可以被耦合以测量通过DUT的电流。分别由第一ADC和第二ADC接收到的电压信号和电流信号可以被转换成第一数字值和第二数字值。根据第一数字值和第二...

---

## 频率可调脉冲输出板卡技术

**专利数量**: 6 条

**技术描述**: 频率可调的脉冲输出技术，用于信号生成和控制

**主要申请人**:
- NATIONAL INSTRUMENTS CORPORATION: 5 条
- National Instruments Corporation: 1 条

**重点专利**:

### US11959945B2
- **申请号**: US17761378
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 脉冲发生
- **技术手段**: 系统, 提供, 测试, 包括
- **应用场景**: 测试, 激光
- **技术摘要**: 本文提供了用于测试垂直腔面发射激光器(VCSEL)的开环测试系统的实施例。高速脉冲发生器可以用于产生提供给VCSEL的纳秒脉冲。可以使用高速示波器测量结果垂直腔面发射激光器上的蚁纳秒脉冲。VCSEL器件电压和VCSEL器件电流可以根据从系统得到的补偿数据从测量的纳秒脉冲获得。可以使用预测试补偿过程来获得补偿数据其可以包括每个系统组件的代表性特征。补偿过程还可以包括在脉冲发生器的不同负载条件下捕获指...

### US10746784B2
- **申请号**: US16180967
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 信号发生
- **技术手段**: 系统, 生成, 测试, 设备
- **应用场景**: 测试
- **技术摘要**: 为了执行系统级物理连接监测测量，可在仪器中生成测试信号并沿从仪器延伸到设备的信号路径向下传输。在静态状态(高或低)下，测试信号发生器可在信号路径连接到仪器以用于指定的后端的点处产生指定的AC阻抗。从测试信号得到的响应信号可被获取并用于获得代表信号路径和从测试信号的源延伸到信号路径的附加信号路径的阻抗值和/或反射系数值。可将所测量的响应与预期的响应进行比较以确定信号路径和/或附加信号路径中的任何(一...

### US09459295B2
- **申请号**: US13938495
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 信号发生
- **技术手段**: 包括, 生成, 处理, 控制, 系统, 电路, 模拟
- **应用场景**: 控制
- **技术摘要**: 一种改进的接收器系统可以包括：用于接收输入信号的输入端，以及用于生成期望的振荡器信号的信号生成电路，该期望的振荡器信号是时变频率的单边带射频信号。该接收器还可以包括下变频级，以基于输入信号和期望的振荡器信号生成中频(IF)信号。接收器中的信号处理块可以用于基于IF信号通过使IF信号频移补偿期望的振荡器信号的时变频率的量来产生输出信号。可以使用矢量信号发生器来生成期望的振荡器信号，该矢量信号发生器从...

### US09188617B2
- **申请号**: US13862986
- **申请人**: National Instruments Corporation
- **分类理由**: 关键词匹配: 信号发生
- **技术手段**: 系统, 生成
- **应用场景**: 通用
- **技术摘要**: 测量例如S参数测量可通过以下方式来执行：使用单个信号接收机获得至少两个信号的复比，同时消除传统上与单个接收机系统相关联的噪声问题。矢量信号发生器(VSG)可用于生成输入激励(信号)，从而使得可能与单个矢量接收机共享VSG的本振(LO)信号，使得LO信号的相位噪声对VSG和矢量接收机两者来说是共用的。当利用矢量接收机观察到来自VSG的激励信号时，LO相位噪声是不可观察到的，从而导致在分子和分母两者中...

### US20140257735A1
- **申请号**: US14281030
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 信号发生
- **技术手段**: 系统, 包括, 计算
- **应用场景**: 通用
- **技术摘要**: 用户获得一组模块，将它们插入到机架的槽中，并且互连模块以形成模块化仪器。信号路径延伸通过模块。为了支持信号路径的校准，模块中的第一模块(或机架或校准模块)包括校准信号发生器。计算机引导第一模块将来自发生器的校准信号应用到信号路径，并且测量信号路径的输出的功率(或振幅) 。计算机从第一模块(或机架或校准模块)的存储器读取校准信号振幅的工厂测量值A.值A和信号路径的测量的输出功率被用于确定信号路径的增...

---

## 车载以太网板卡技术

**专利数量**: 2 条

**技术描述**: 车载以太网通信技术，包括车载网络协议和通信接口

**主要申请人**:
- National Instruments Corporation: 1 条
- NATIONAL INSTRUMENTS CORPORATION: 1 条

**重点专利**:

### EP1191747A2
- **申请号**: EP00126588.3
- **申请人**: National Instruments Corporation
- **分类理由**: 关键词匹配: CAN
- **技术手段**: 包括, 处理, 设备, 控制, 系统, 计算, 配置
- **应用场景**: 控制
- **技术摘要**: 一种用于将主机计算机接口到控制器局域网(CAN)总线的系统。该系统包括存储器、嵌入式处理器和接口逻辑。该存储器存储程序代码。嵌入式处理器耦合到存储器并执行程序代码。接口逻辑将嵌入式处理器与互连总线(例如，实时系统集成(RTSI)总线)接口。响应于程序代码的执行，嵌入式处理器可操作来响应于接口逻辑在RTSI总线的所选线上接收到RTSI触发信号来执行CAN事件。外围设备也耦合到主机计算机，响应于外围设...

### US05938754A
- **申请号**: US08/979968
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: CAN
- **技术手段**: 包括, 装置, 具有, 控制, 计算, 电路, 配置
- **应用场景**: 网络, 控制
- **技术摘要**: 用于连接一台计算机到一连续的计测用总线的改进的双重的电缆接头。在一个实施例中，连续的计测用总线是一现场总线，优选地也一基础现场总线或一条控制器区域网络（CAN）总线。电缆包括位于用于连接电缆到计算机的电缆的一个第一端的第一终端。第一端子包括一装置连接器，其经配置以连接第一端子到在计算机上的一连接器。电缆也包括位于用于连接电缆到连续的计测用总线的电缆的一第二端的第二终端。第二终端包括用于接合具有连续...

---

## 数据注入类型支持技术

**专利数量**: 15 条

**技术描述**: 不同数据注入类型的支持技术，包括数据格式转换和处理

**主要申请人**:
- NATIONAL INSTRUMENTS CORPORATION: 12 条
- National Instruments Corporation: 3 条

**重点专利**:

### US20180276175A1
- **申请号**: US15466150
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 数据处理
- **技术手段**: 包括, 生成, 处理, 设备, 控制, 系统, 配置
- **应用场景**: 通信, 网络, 控制
- **技术摘要**: 一种网络接口外围设备(NIP)可以包括用于与网络通信的网络接口以及用于与处理器子系统通信的互连接口。 NIP中的外围数据缓冲器(PDB)可以保持NIP从对端外围设备接收的数据和/或由NIP向对端外围设备分布的数据，并且网络数据缓冲器(NDB)可以保持NIP向网络发送的和/或从网络接收的经调度数据流的有效载荷数据。 NIP中的数据处理器可以从PDB中的数据生成有效载荷数据，并且根据经调度数据处理器发...

### US09246852B2
- **申请号**: US14072297
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 数据传输
- **技术手段**: 系统, 方法, 配置
- **应用场景**: 数据传输, 网络
- **技术摘要**: 用于将迭代的基于时间的数据采集(DAQ)操作映射到网络的等时数据传输信道的系统和方法。可以配置与网络的等时数据传输信道相关联的时间敏感缓冲器(TSB) 。可以配置数据速率时钟和本地缓冲器。功能单元可以被配置为发起迭代的基于时间的DAQ操作的连续执行，将数据传输到本地缓冲器，在配置的开始时间发起数据在本地缓冲器和TSB之间的传输，以及以迭代的方式重复传输和发起传输，从而在本地缓冲器和TSB之间传输数...

### EP2598988A1
- **申请号**: EP11717422.7
- **申请人**: National Instruments Corporation
- **分类理由**: 关键词匹配: 数据传输
- **技术手段**: 包括, 具有, 方法, 设备, 系统
- **应用场景**: 数据传输
- **技术摘要**: 用于传输数据的系统和方法。显示系统图，其中，系统图包括对应于相应的设备的多个设备图标，每个设备图标具有指定用于在对应的设备上部署的相关联的可执行功能节点。功能节点互连以形成在设备上以分布式方式可部署和可执行的分布式图形程序。接收对系统图的指定功能节点之间的等时数据传输的用户输入。基于指定的等时数据传输来自动确定功能节点之间的调用定时关系，所述调用定时关系包括功能节点的执行之间的阶段关系。在功能节点...

### US20130031494A1
- **申请号**: US13633405
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 数据类型
- **技术手段**: 系统, 方法, 包括
- **应用场景**: 通用
- **技术摘要**: 一种系统和方法，用于创建和使用型一般绘画程序。所述方法可以包括存储第一图形程序存储介质上。第一图形程序可能已经创建了基于用户输入。第一图形程序可以包括多个节点之间的互连节点，所述多个节点以及节点之间的互连可以型通用。可以接收用户输入指定一个或多个数据类型的至少一个输入和至少一个输出，第一图形和/或程序。数据类型可以与第一图形程序响应于所述用户输入指定一个或多个数据类型。


### EP1755054A3
- **申请号**: EP06016944.8
- **申请人**: National Instruments Corporation
- **分类理由**: 关键词匹配: 数据处理
- **技术手段**: 方法, 包括, 处理, 模拟
- **应用场景**: 通用
- **技术摘要**: 一种用于存储和检索来自测量、模拟和/或其他技术数据处理活动的技术测量数据的方法，包括：将测量数据存储在多个数据文件中，其中所述存储的测量数据包括存储与测量数据相关联的元数据，其中元数据描述测量数据的特征，特别是时间戳信息和/或测量类型和/或推荐类型的分析的描述；接收对测量数据的查询，其中查询包括与测量数据相关联的元数据的参数；以及根据查询搜索多个数据文件的索引，其中索引是根据元数据创建的，其中与每...

---

## 多实时机级联技术

**专利数量**: 19 条

**技术描述**: 多实时机级联技术，用于分布式实时计算和处理

**主要申请人**:
- NATIONAL INSTRUMENTS CORPORATION: 18 条
- National Instruments Corporation: 1 条

**重点专利**:

### US20160359978A1
- **申请号**: US14840462
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 分布式, 实时系统
- **技术手段**: 包括, 生成, 设备, 控制, 系统, 配置
- **应用场景**: 控制
- **技术摘要**: 生成用于分布式实时系统的调度。至少一个调度发生器可以从在主设备上执行的相应定时功能接收时间性质，其中每个主设备连接到相应的多个从设备。每个主设备包括被配置为控制相应的多个从设备的物理输入和/或输出操作的定时以及主设备与相应的多个从设备之间的流的一个或多个定时功能。调度发生器可以接收定时功能与主设备之间的流之间的关联，并且至少部分地基于时间性质和关联来生成用于主设备的相应调度。相应调度可以被分布到主...

### US11803456B2
- **申请号**: US17464419
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 分布式
- **技术手段**: 包括, 测试, 方法, 设备, 系统, 计算
- **应用场景**: 测试
- **技术摘要**: 用于将测试荚分配到分布式计算系统以用于在待测设备(DUT)上执行测试计划的方法和计算设备。每个测试荚可以包括包含一个或多个测试步骤的测试微服务和指定该测试微服务与其他测试微服务之间的函数关系的事件微服务。该测试荚被分配到不同服务器以通过一个或多个测试接口在该DUT上执行该测试计划的分布式执行。


### US20230063629A1
- **申请号**: US17464419
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 分布式
- **技术手段**: 包括, 测试, 方法, 设备, 系统, 计算
- **应用场景**: 测试
- **技术摘要**: 用于将测试荚分配到分布式计算系统以用于在待测设备(DUT)上执行测试计划的方法和计算设备。每个测试荚可以包括包含一个或多个测试步骤的测试微服务和指定该测试微服务与其他测试微服务之间的函数关系的事件微服务。该测试荚被分配到不同服务器以通过一个或多个测试接口在该DUT上执行该测试计划的分布式执行。


### US10841136B1
- **申请号**: US16540838
- **申请人**: National Instruments Corporation
- **分类理由**: 关键词匹配: 级联
- **技术手段**: 具有, 包括, 装置, 电路
- **应用场景**: 通信
- **技术摘要**: 公开了一种用于发送和接收无线通信的装置，其中，发送电路包括用于对信号进行脉冲形状调制的平方根升余弦滤波器，以及接收电路包括被耦合的较高阶的奈奎斯特接收滤波器，以接收所述输入信号并且移除所述脉冲形状调制。所述发送滤波器和所述接收滤波器的级联组合具有等效于较高阶的广义升余弦滤波器响应的频率响应。


### US20180217954A1
- **申请号**: US15417765
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 分布式
- **技术手段**: 实现, 生成, 设备, 控制, 系统, 配置
- **应用场景**: 网络, 控制
- **技术摘要**: 在分布式系统中实现基于事件的异步输入/输出操作启动。在所述分布式系统内，经由内部网络耦合到相应多个从设备的多个主设备中的每个主设备可以实现一个或多个定时功能，所述一个或多个定时功能被配置为控制相应多个从设备的物理输入操作和/或物理输出操作的定时，以及主设备与相应多个从设备之间的流。从设备的子集还可以经由共享的基于信号的总线来互连，所述共享的基于信号的总线可以用于传播异步事件，所述异步事件可以用于启...

---

## 集群化测试技术

**专利数量**: 6 条

**技术描述**: 集群化测试技术，用于大规模并行测试和验证

**主要申请人**:
- NATIONAL INSTRUMENTS CORPORATION: 6 条

**重点专利**:

### US20250133684A1
- **申请号**: US18491891
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 测试系统
- **技术手段**: 包括, 测试, 具有, 设备, 系统, 配置
- **应用场景**: 测试
- **技术摘要**: 一种测试系统机架或机柜，包括容纳仪器或设备的壳体和安装在壳体内的大容量互连(MIC)。MIC具有耦合到仪器或设备的输入端和耦合到与机架分离的第二MIC的输出端。测试系统支架具有位于外壳下面的腿，以支撑外壳和第一MIC。测试系统机架具有致动器，其被配置为在不调整一个或多个支腿的位置的情况下调整第一MIC的位置。


### US20250053500A1
- **申请号**: US18796978
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 测试系统
- **技术手段**: 测试, 模型, 装置, 生成, 方法, 系统
- **应用场景**: 通信, 测试, 网络
- **技术摘要**: 用于基于被测器件(DUT)的规范的初始输入的生成人工智能(AI)辅助测试过程开发的装置、系统和方法。DUT的规格可以被输入到生成AI模型中。生成AI模型可以总结规范，请求经由与最终用户的交互的进一步输入以最终确定DUT的描述，并且生成/创建测试资产，诸如代码、文档、表、图表，等等。生成AI模型可与最终用户协作以细化来自测试资产的输出。可以将细化的测试资产发送到可以使用/运行/部署各种测试资产的软件...

### US11994545B2
- **申请号**: US17728377
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 测试系统
- **技术手段**: 提供, 测试, 包括, 系统, 电路
- **应用场景**: 测试
- **技术摘要**: 测试系统可以用于获得精确的远程感测电压和/或电流值。测量仪器可以向被测器件(DUT)提供调节的激励信号，并且测量至少部分地响应于该激励信号而产生的DUT信号。测试电路可以上位将测试信号置于激励信号之上，以使DUT信号响应于测试信号而进一步发展。DUT信号可以用于导出将测量仪器耦合到DUT的路径的电阻。测量仪器可以包括源测量单元激励信号可以是调节电压，DUT信号可以是读出电压。可以分析DUT信号的谐...

### US20220365123A1
- **申请号**: US17728377
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 测试系统
- **技术手段**: 提供, 包括, 测试, 设备, 系统, 电路
- **应用场景**: 测试
- **技术摘要**: 一种测试系统可以用于获得准确的遥感电压和/或电流值。测量仪器可以将经调节的刺激信号提供给被测设备(DUT)，并且测量响应于所述刺激信号而至少部分地开发的DUT信号。测试电路可以将测试信号叠加到所述刺激信号上，以使得响应于所述测试信号而进一步开发所述DUT信号。所述DUT信号可以用于导出将所述测量仪器耦合到所述DUT的路径的电阻。所述测量仪器可以包括源测量单元，所述刺激信号可以是经调节的电压，并且所...

### US06516053B1
- **申请号**: US09336932
- **申请人**: NATIONAL INSTRUMENTS CORPORATION
- **分类理由**: 关键词匹配: 测试系统
- **技术手段**: 提供, 包括, 测试, 具有, 系统, 计算, 电路, 配置
- **应用场景**: 通信, 测试, 网络
- **技术摘要**: 包括一便携式计算机系统，呈现一模块化的电信测试系统至少一个便携无线电通讯测试模块定位在计算机系统以外。便携式计算机系统存储电信测试申请（例如，在一记忆系统中）。每一测试模块包括具有一电接插件的一通信出入口，因此是适于对便携式计算机系统连接。每一测试模块还包括用于执行一组电信测试的电路，其中每一测试涉及进行通过电信业务安装的至少一个电气测量。在连接一特定的测试模块到便携式计算机系统方面，一名用户配置...

---

## 技术发展趋势分析

### 核心技术布局

1. **HIL硬件在环仿真技术** (62条, 14.4%): dSPACE在HIL技术方面拥有最多专利，体现了其在硬件在环仿真领域的技术领先地位。

2. **故障注入板卡技术** (50条, 11.6%): 故障注入技术是HIL测试的重要组成部分，用于验证系统的故障处理能力。

3. **数据注入类型支持技术** (54条, 12.5%): 支持多种数据类型的注入，提高了测试系统的灵活性和适用性。

### 技术特点

- **实时性**: 大量专利涉及实时仿真和实时数据处理
- **模块化**: 采用模块化设计，支持不同类型的板卡和接口
- **标准化**: 支持多种工业标准和通信协议
- **智能化**: 集成机器学习和人工智能技术

### 应用领域

- **汽车电子**: 车载网络、自动驾驶、电动汽车控制系统测试
- **航空航天**: 飞行控制系统、导航系统测试
- **工业自动化**: 工业控制系统、机器人控制测试
- **新能源**: 电池管理系统、电力电子设备测试

