﻿公开(公告)号,申请号,摘要(中文),原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
US20250231255A1,US18853791,"用于连接至大型互连的仪器的有效连续性测试。数字输入和输出能力可以用在大容量互连的每个引脚上，以测试被测器件上的各种输入/输出(I/O)类型。互连的每个引脚可以连接到测试器中的相应的数字输入和数字输出，其中数字输入电阻性地耦合到数字输出。引脚到数字输入端和数字输出端的连接性以及数字输入端和数字输出端之间的连接性可以分别用移位寄存器和缓冲级来实现。在一些实施例中，该结构可以通过并行I/O块来实现，如在复杂可编程逻辑器件(CPLD)、现场可编程门阵列(FPGA)或微控制器中。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 测试, 控制","测试, 控制"
US20250123306A1,US18911546,"与目前的仪器探针(例如，与示波器一起使用的有源探针)相关联的缺点可以通过将有源探针完全实现为封装集成电路(IC)来克服。探针IC可以在小的、低引脚数的封装中实现，以便于在小面积中安装许多探针IC。探针IC可以包括用于配置的接口以及定制软件，以控制探针IC和测量仪器，例如示波器，用于各种应用。探针IC可以被实现为不同类型的探针中的任何一种，包括有源探针和无源探针、电压探针和电流探针、或单端探针和差分探针。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 包括, 控制, 电路, 配置",控制
EP4010806B1,EP20857140.6,"一种可虚拟化自动化测试设备体系结构包括电路组件。该电路组件包括在前面板和背板之间延伸的多个信号路径。信号路径可以是连续的，并且与多个信号路径中的其它信号路径隔离。电路组件还包括包括沿着多个信号路径中的一个信号路径设置的阻抗。多个软件可配置的物理断开器可以布置在电路组件内以形成开关矩阵。多个信号路径可与多个软件可配置的物理断开相关联其可以被配置为基于预定的测试要求来打开和关闭多个信号路径中的信号路径。该电路组件还包括多个外部设备连接，其中至少一个可以被配置为与被测单元(UUT)接口。软件可配置物理断开可以在运行时配置。由于系统是可虚拟化的，所以可以根据不同的要求同时测试倍增的UUT，并且可以对UUT透明的方式在共享硬件上执行测试。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"包括, 测试, 设置, 设备, 系统, 电路, 配置",测试
US12177068B2,US18177611,"一种在测量系统中编排测量的方法包括：利用第一配置来配置第一服务，以便编排器获取测量数据。该方法还包括接收由第一服务响应于被配置而生成的监视符T表示第一配置。该名称包括第一服务的位置和第一配置的标识符。该方法还包括将该监控器转移到第二服务，该第二服务被配置为基于该位置与第一服务建立通信使用由第一服务使用第一配置获取的并且响应于从第二服务接收到标识符而发送的测量数据，并且响应于从第一服务接收到测量数据而生成结果。该方法还包括从第二服务接收结果。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"包括, 生成, 方法, 系统, 配置",通信
US12149176B2,US17535022,"多相电流共享配置可以包括在电流共享配置中提供相应输出电流的至少两个电源。一个或多个电源本身可以是多相电源。电流共享配置的第一电源可以检测提供给第一电源以控制第一电源的输出电压的外部控制信号与由第一电源的VCO提供的内部控制信号之间的相位差。可以将相位差提供给积分器以使内部控制信号在外部控制信号可用时跟踪外部控制信号，并且在外部控制信号丢失的情况下保持内部控制信号的当前工作频率，在这种情况下，内部控制信号可以用于不间断地控制第一电源的输出电压。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 控制, 检测, 配置","检测, 控制"
US20240372633A1,US18311335,"一种用于测试被测天线(AUT)的系统和方法。多探头天线阵列发射机移动到扫描区域内的多个位置。在每个位置，发射机的每个探头天线元件向AUT发射近场(NF)空中(OTA)信号。执行对准过程以对准由多个探头天线元件中的不同探头天线元件发射的信号的发射位置。确定表征发射机的探头天线元件之间的幅度和相位差异的校正因子。将校正因子应用于所述信号，在每个发射位置将校正后的信号合并，得到平均信号。基于平均信号的离散傅立叶变换确定AUT的远场(FF)接收模式，并将其存储在非瞬态计算机可读存储介质中。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 测试, 计算",测试
US20240369608A1,US18311329,"一种用于测试被测天线(AUT)的系统和方法。多探针天线阵列接收器被移动到扫描区域内的多个位置。在每个位置，接收机的每个探头天线元件从AUT接收近场(NF)空中(OTA)信号。执行对准过程以对准由多个探头天线元件中的不同探头天线元件接收的信号的接收位置。确定表征接收机的探头天线元件之间的幅度和相位差异的校正因子。将校正因子应用于所接收的信号，并且在每个接收位置处组合校正信号以获得平均信号。基于平均信号的离散傅立叶变换确定AUT的远场(FF)传输模式，并将其存储在非暂时性计算机可读存储介质中。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 测试, 计算",测试
US12114462B2,US17820112,"各种类型的电子设备可以安装在底架中，以便于与包含设备的设备对接，提供可以从电子设备移除热量的冷却系统等。将足够的冷却气流输送至机箱中的每个电子设备对于机箱中包含的电子设备的正常功能、寿命或其它特性可能是重要的问题。由于各种设计特性，一些电子装置冷却可能特别具有挑战性。其它电子设备可能具有现有机箱设计不能很好地服务的其它要求。例如，可以是，一些电子设备可以受益于附加的电和/或热连接。本文呈现的实施例描述了用于模块化插件架附件的新颖设计，其可以被配置为修改气流和/或满足各种可能性中的机箱中的电子设备的特定要求。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 装置, 具有, 设备, 系统, 配置",通用
US20240313276A1,US18183755,"用于执行动态控制的电池单元形成的系统、方法和装置。对多个电池单元执行形成工艺。通过多个电池单元中的每一个和循环装置建立串联连接。EacH电池单元耦合到相应的监测装置以监测单元形成期间的性能。监测装置在它们的监测的电池单元经历状态变化时向控制器提供指示。响应于该指示提供指令以用于同步地修改通过第一电池单元的串联连接和修改循环装置处的电压幅度。修改通过第一电池单元的串联连接可以包括切换第一电池单元两端的电压极性，从而使第一电池单元周围的串联连接短路，而不修改通过其他电池单元的串联连接。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 装置, 方法, 控制, 系统",控制
US12066971B2,US17669708,"一种网络接口外围设备(NIP)可以包括用于与网络通信的网络接口，以及用于与处理器子系统通信的互连接口。NIP中的第一缓冲器可以保存从对等外围设备接收的和/或由NIP分配给对等外围设备的数据，第二缓冲器可保存由NIP发送到网络和/或从网络接收的调度数据流的有效载荷数据。来自第一缓冲器中的数据的有效载荷数据可以存储在第二缓冲器中，并且根据基于接收到的调度生成的发送事件而被发送到网络。可以根据基于所接收的调度生成的接收事件从网络接收数据，并且将数据从第二缓冲器分发到第一缓冲器。集中式系统配置实体可生成调度表、管理NIP的配置、以及协调NIP的内部配置与网络配置流。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 生成, 处理, 设备, 系统, 配置","通信, 网络"
US20240230779A1,US18408132,"用于表征被测器件(DUT)的缺陷的系统、方法和器件。在对DUT执行第一操作之前，对DUT执行第一数量的第一测量，产生第一结果。对DUT执行第一操作，并且随后对DUT执行第一量的第二测量，产生第二结果。基于第一和第二结果之间的差，从多个缺陷类中为DUT表征缺陷类。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法",通用
US20240235699A1,US18151426,"一种用于确定来自被测器件(DUT)的极化传输的误差向量幅度(EVM)的系统和方法。由DUT发送的第一信号经由水平极化接收器天线接收，并且由DUT发送的第二信号经由垂直极化接收器天线接收。第二信号与第一信号相干。EVM至少部分地基于第一信号和第二信号以及参考信号来计算。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 计算",通用
US20240160542A1,US17985099,"描述了用于将基于场景的测试分配给测试资产的技术。在一个示例中，基于场景的测试可操作来测试被测系统(SUT)的关键性能指标(KPI)，接收SUT的第一组件所表现的组件行为和场景特性。基于组件行为和场景特性，标识与组件行为相关联的第一多个行为模型。基于场景特性，从基于场景的测试中提取特性值。使用所述特征值执行所述第一多个行为模型中的每个行为模型，以生成第一多个预测行为结果。基于第一多个预测行为结果，从多个测试资产类型中选择第一测试资产类型，并将基于场景的测试传送到第一测试资产类型的测试资产。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"系统, 生成, 测试, 模型",测试
US11982699B2,US17532532,"一种用于测试诸如集成电路(IC)的器件的系统和方法，该集成电路具有被配置用于无线信号接收的集成天线阵列。该方法对被测参考器件(DUT)执行校准操作。在校准操作期间，DUT使用不同的波束形成设置从第一远场(FF)位置接收一系列第一信号并且从第二近场(NF)位置接收一系列阵列传输，并由此确定一组校准参数。校准参数可以由探针天线系统(PAS)使用，以将阵列传输从第二NF位置传输到DUT，以仿真来自第一FF位置的单探针或多探针传输。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"测试, 具有, 仿真, 方法, 设置, 系统, 电路, 配置",测试
US20240125855A1,US18489535,"用于在形成过程期间构造用于电池单元的电化学过程歧管(EPM)的系统、方法和装置。通过电池的电流可控地调节以对电池充电或放电。温度和/或压力可以与电流一起可控地调节。在多个时间步长中的每一个处，当可控地调节电流时，测量电池两端的电压并随时间积分，以获得每个时间步长的电压-小时值。每一时间步的数据点被存储在存储器中，该数据点包括测量的电压，电压-小时值，以及在相应的时间步长通过电池的电流。每个时间步的数据点被映射到EPM上，并且EPM被存储在非暂时性计算机可读存储介质中。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 方法, 系统, 计算",通用
US11901636B2,US17089464,"呈现了用于验证紧凑型天线测试范围(CATR)的对准的方法、装置和系统。可以基于由参考天线在多个取向处接收的测试信号来生成射频(RF)简档。可以使用该RF简档的相位和幅度数据来确定CATR是否正确对准。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"测试, 装置, 生成, 方法, 系统","测试, 验证"
CN112077239B,CN202010535109.0,"本发明公开了一种用于将印刷电路板与电子元器件组装的剪脚机构及方法、组装机。剪脚机构包括：被配置为沿竖向轴线移动的驱动轴，被配置为在驱动轴的运动期间保持固定的固定砧座，具有剪切尖的切刀，以及被配置为绕肘节旋转轴旋转的肘节，该肘节包括被配置为在切刀的渐开线梯形槽滚动以使切刀相对于固定砧座运动的渐开线齿轮状齿。驱动轴沿线性轴线的运动被配置为使切刀相对于固定砧座运动，并且使剪切尖在固定砧座上运动，以剪切位于剪切尖和固定砧座之间的电子引线，并且使肘节绕该轴旋转。
",美国国家仪器有限公司,其他技术,0.0,未匹配到特定技术领域,"包括, 具有, 方法, 电路, 配置",通用
US20230393180A1,US18452335,"反射计可以包括两个定向耦合器，所述两个定向耦合器通过彼此跨布置在信号线的共享部分的相对侧上而被并行配置。耦合器中的一个耦合器可以将从共享通路的第一端流向共享通路的第二端的第一信号的信号功率的一部分耦合到反射计的第一端口，并且另一个耦合器可以将从共享通路的第二端流到共享通路的第一端的第二信号的信号功率的一部分耦合到反射计的第二端口。反射计受益于相对于串行耦合器配置的减小的尺寸和信号损耗。当在矢量网络分析器(VNA)系统中使用时，这导致VNA的更高的输出功率和更高的动态范围。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 包括, 配置",网络
US11796621B2,US17524850,"公开了涉及确定被测器件(DUT)的调制质量测量的技术。从源接收调制信号多次，并且将每个接收的调制信号传送到第一矢量信号分析器(VSA)和第二VSA中的每一个。第一VSA和第二VSA对接收的调制信号进行解调，以分别产生第一误差矢量和第二误差矢量。对相应接收的调制信号的第一误差矢量和第二误差矢量执行互相关计算，以产生复值的互相关测量，并且在多个接收的调制信号上对互相关测量的实部分量进行平均。基于平均的互相关测量确定调制质量测量。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,计算,通用
US11789074B2,US17500639,"本文中描述用于识别冗余参数并缩减用于测试装置的参数的系统、方法和其它技术。接收参数集合的测试值集合和极限。基于所述参数集合的一或多个概率表示确定所述参数集合的模拟测试值集合。基于所述测试值集合构造所述一或多个概率表示。基于所述模拟测试值集合和所述极限计算所述参数集合的通过的累积概率集合。基于所述通过的累积概率集合从所述参数集合确定缩减的参数集合。部署所述缩减的参数集合以用于测试所述装置。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"测试, 装置, 方法, 系统, 计算, 模拟",测试
US11778768B2,US17408312,"可以通过将模块强制推(或拉动)到背板和/或将背板强制推(或拉动)到模块的机构来消除在模块连接器配合面和底盘的背板连接器之间的连接器间隙。可以使用弹簧加载或弹性元件以有效填充连接器接口中的任何设计的插入和公差引起的间隙的方式来紧固模块，从而允许连接器完全安置。此外，可以在连接器配合面处包括垫圈或其他可压缩构件。可以通过引入能够在使用特殊的对准固定装置的组装或制造过程期间被设置的可调节卡笼构件来减小连接器接口中的间隙。还可以通过向卡笼子组件引入诸如机械加工的能够更高公差的制造过程来减小连接器接口中的间隙。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 设置, 装置",通用
US11774475B2,US17374697,"反射计可以包括两个处于并行配置的定向耦合器，共享信号线或通孔的相同部分。例如，两个定向耦合器可以在共享通孔的相对侧上彼此跨越地设置。定向耦合器之一可以将从共享通孔的第一端流向共享通孔的第二端的第一信号的信号功率的一部分耦合到反射计的第一端口，并且另一个定向耦合器可以将从共享通孔的第二端流向共享通孔的第一端的第二信号的信号功率的一部分耦合到反射计的第二端口。相对于具有串行配置的反射计，反射计从减小的尺寸和信号损耗获益。当在矢量网络分析仪(VNA)系统中使用时，这导致VNA的更高的输出功率和更高的动态范围。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 具有, 设置, 系统, 配置",网络
US20230266390A1,US18172754,"用于将仪器与用于执行测试过程的被测装置匹配的方法和计算装置。基于仪器的数据片构造第一数据结构。第一数据结构包括属性、要测量的现象和测试交互以测量相应现象。基于要对DUT执行的测试过程构造测试案例。测试案例包括属性、要测量的现象和测试交互以测量相应现象。比较第一数据结构和测试案例的属性、现象和测试交互以确定匹配条件，并且基于匹配条件输出指令。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 测试, 装置, 方法, 计算",测试
US11736129B2,US16667064,"用于选择本地振荡器频率以用于进行正交频分复用(OFDM)通信的方法和无线设备。对于多个本地振荡器频率中的每一个，无线设备确定针对多个子载波中的每一个的从本地振荡器频率得到的相应干扰功率，并且通过对与多个子载波中的每一个相关联的干扰功率执行求和来确定代价函数。无线设备选择具有最小代价函数的第一本地振荡器频率以用于无线通信。无线设备使用第一本地振荡器频率通过多个子载波来执行无线通信。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"具有, 方法, 设备",通信
US20230160936A1,US17532532,"一种用于测试例如具有经配置以用于无线信号接收的集成天线阵列的集成电路(IC)的装置的系统和方法。所述方法对参考受测装置(DUT)执行校准操作。在所述校准操作期间，所述DUT使用不同的波束成形设置从第一远场(FF)位置接收一系列第一信号并且从第二近场(NF)位置接收一系列阵列发射，并且从所述第一近场(NF)位置确定一组校准参数。所述校准参数可由探针天线系统(PAS)使用以从所述第二NF位置向所述DUT发射阵列发射，以仿真从所述第一FF位置的单个探针或多探针发射。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"测试, 装置, 具有, 仿真, 方法, 设置, 系统, 电路, 配置",测试
CN116076039A,CN202180054421.0,"一种用于(例如，快速地且便宜地)测试装置(诸如具有被配置用于毫米波发送和/或接收的集成的天线的集成电路(IC))的系统和方法。所述方法可以首先对参考受测试装置(DUT)执行校准操作。校准操作可以确定参考DUT FF基本函数集合，并且还可以产生校准系数集合。在使用参考DUT的校准步骤之后，可以使用所得的参考DUT FF基本函数和校准系数(或重构矩阵)来基于其他的场测量(例如，在DUT的近场中进行的测量)确定DUT的远场图案。
",美国国家仪器有限公司,其他技术,0.0,未匹配到特定技术领域,"测试, 装置, 具有, 方法, 系统, 电路, 配置",测试
US20230123930A1,US18045354,"用于仿真用于发射(TX)被测系统(SUT)与接收(RX) SUT之间的无线通信的信道的系统和方法。所述TX和RXSUT包括用于发射和接收无线信号的集成天线阵列。针对所述仿真信道的多个路径，并且针对所述TXSUT的每个天线元件，将相应相移和增益修改应用于所述相应天线元件发射的无线信号。所述相移和增益修改仿真不同天线元件之间的路径长度差。对每个天线元件的所述信号进行求，并且针对每个路径将路径特定的修改应用于每个集合信号。针对每个RX天线元件，应用相移和增益修改来仿真所述RX天线元件的路径长度差，针对每个路径对所得信号进行求，并且将所述仿真无线信号输出至所述RX天线元件。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括, 仿真",通信
US20230114555A1,US17500639,"本文中描述用于识别冗余参数并缩减用于测试装置的参数的系统、方法和其它技术。接收参数集合的测试值集合和极限。基于所述参数集合的一或多个概率表示确定所述参数集合的模拟测试值集合。基于所述测试值集合构造所述一或多个概率表示。基于所述模拟测试值集合和所述极限计算所述参数集合的通过的累积概率集合。基于所述通过的累积概率集合从所述参数集合确定缩减的参数集合。部署所述缩减的参数集合以用于测试所述装置。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"测试, 装置, 方法, 系统, 计算, 模拟",测试
US11604747B2,US17034726,"用于经由经由可编程硬件和一个或多个总线实现的虚拟网络接口在异构处理器之间进行通信的系统和方法。可编程硬件可以被配置有多功能总线，使得可编程硬件对于主机系统呈现为网络设备和可编程设备两者。另外，可编程硬件可以被配置有第二总线以对于嵌入式系统呈现为网络设备。每个系统可以实现网络驱动器以允许访问在可编程硬件上配置的直接存储器访问引擎。所配置的可编程硬件和网络驱动器可以实现系统之间的虚拟网络连接以允许经由一个或多个网络通信协议的信息传递。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 方法, 处理, 设备, 系统, 配置","通信, 网络"
US20230019522A1,US17374697,"反射计可以包括在并行配置中的两个定向耦合器，共享信号线或通路的相同部分。例如，两个定向耦合器可以在共享通路的相对侧上彼此跨越地设置。定向耦合器中的一个可以将从共享通路的第一端流到共享通路的第二端的第一信号的信号功率的一部分耦合到反射计的第一端口，并且另一个定向耦合器可以将从共享通路的第二端流到共享通路的第一端的第二信号的信号功率的一部分耦合到反射计的第二端口。相对于具有串行配置的反射计，反射计从减小的尺寸和信号损耗受益。当在矢量网络分析器(VNA)系统中使用时，这导致VNA的更高的输出功率和更高的动态范围。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 具有, 设置, 系统, 配置",网络
CN115552276A,CN202180034529.3,"一种用于模拟用于测试被测光检测和测距(LiDAR)单元(UUT)的无线环境的系统。该系统可以包括从LiDAR UUT接收光的透镜系统和多个光学处理链。该系统可以基于每个链处理的光信号将光生成到自由空间中。该系统可以对接收到的光进行光学处理以维持与从被测LiDAR单元接收到的光的相干性，并且可以同时处理LiDAR图像中的所有点。该系统可以操作以模拟飞行时间LiDAR UUT、调频连续波(FMCW)LiDAR UUT和/或闪光LiDAR UUT的空中环境。
",美国国家仪器有限公司,其他技术,0.0,未匹配到特定技术领域,"测试, 包括, 生成, 处理, 系统, 检测, 模拟","检测, 测试"
US11452231B2,US16901829,"各种类型的电子设备可以安装在机箱中以便于与包含设备的设备对接，提供可以从电子设备移除热量等的冷却系统。向机箱中的每个电子设备传递足够的冷却气流可能对于机箱中包含的电子设备的正常功能、寿命或其他特性是重要的问题。一些电子设备由于各种设计特性而对冷却可能是特别有挑战的。其他电子设备可能具有由现有机箱设计不能很好地提供服务的其他要求。例如，一些电子设备可以受益于额外的电和/或热连接。本文呈现的实施例描述了一种用于模块化卡笼附件的新颖设计，该模块化卡笼附件可以被配置为修改气流和/或满足机箱中的电子设备的特定要求，以及各种可能性。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 具有, 设备, 系统, 配置",通用
US11408916B2,US16811455,"一种新型模块化探头可包括可互换的(可连接/可断开的)探头尖端适配器，其具有用于耦合到被测装置的尖端连接器，并且还具有用于耦合到电缆组件的第一组件连接器的探头尖端端子，其还具有用于耦合到外建适配器的第一外建端子的第二组件连接器，该第二外建端子也具有用于耦合到可互换仪器连接器电缆组件的组件连接器的第二外建端子，该可互换仪器连接器电缆组件也具有用于耦合到测量仪器的仪器端连接器。外建适配器可包括用于针对变化的系统电容补偿探头的补偿调节电路。探头可包括在可互换探头尖端适配器和/或外建适配器中的一个或多个校正电路，用于利用电缆组件中的电缆的特性阻抗来至少部分地端接电缆组件的每个端部以衰减反射。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 具有, 系统, 电路",通用
US11381297B2,US16600495,"UE基于由UE测量的一组波束相干性间隔来确定作为波束对随时间的稳定性的度量的波束相干性间隔度量。波束对包括UE接收波束和基站发送波束。波束相干性间隔包括在其内在UE接收波束上接收的信号的质量保持在多个信号质量仓之一内的持续时间。 UE向基站报告度量。基站可以基于度量来更新波束管理资源并向UE报告配置。 UE还可以使用度量来确定滞后值，该滞后值可由UE用于决定从活跃接收波束切换到具有较高信号质量的不同接收波束至少滞后值。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"具有, 包括, 配置",通用
US20220140497A1,US17089464,"呈现了用于验证紧凑型天线测试范围(CATR)的对准的方法、装置和系统。可以基于由参考天线在多个取向处接收到的测试信号来生成射频(RF)简档。可以使用该RF简档的相位和幅度数据来确定CATR是否正确对准。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"测试, 装置, 生成, 方法, 系统","测试, 验证"
US11321341B2,US16875669,"一种用于在接收测量数据并将其移动到数据仓库时动态地分析包括测量数据集的测量数据的方法。程序指令可接收测量数据并且可从测量数据提取第一元数据。程序指令然后可提取并分析测量数据中的测量数据点，以确定测量数据点是否满足第一准则，并且响应于确定测量数据点满足第一准则而生成第二元数据。程序指令然后可将测量数据点、第一元数据和第二元数据提供到数据仓库以供存储。可在数据被采集并存储在数据仓库中时动态地执行对测量数据的分析和对新元数据的创建。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"生成, 提供, 方法, 包括",通用
CN110771078B,CN201880023935.8,"本公开提供了具有多个天线且用于发送多个空间流的基站(BS)。多个用户设备(UE)中的每个用户设备(UE)均估计多个空间流中的两个或更多个中的每一个的公共相位误差(CPE)，测量多个空间流中的两个或更多个中的估计的CPE的相关性，以及提供关于的CPE相关性的反馈至BS。BS使用CPE相关反馈来分配相位跟踪参考信号(PTRS)端口并将分配的PTRS端口映射到与多个空间流相对应的解调参考信号(DMRS)端口。
",美国国家仪器有限公司,其他技术,0.0,未匹配到特定技术领域,"具有, 提供, 设备",通用
US11288281B2,US16875566,"一种非暂态计算机可读存储介质可以存储包括行的第一表，其中每一行包括第一数据集标识(ID)字段和一个或多个字段，该第一数据集标识(ID)字段存储标识测量数据集的测量数据集标识符值，该一个或多个字段用于存储与所标识的数据集相关联的测量数据元数据。该介质还可以存储包括行的第二表，其中每一行包括第二数据集标识(ID)字段，该第二数据集标识(ID)字段存储存在于第一数据集ID字段中的测量数据集标识符值。第二表还可以存储用于存储个体数据集数据点的数据点字段和与个体数据集数据点的排序相对应的数据集索引字段。第一表和第二表两者的字段中的每一个的至少一部分可以以列格式存储在连续存储器中。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 计算",通用
US20220061181A1,US17408312,"可以通过将模块强制推(或拉)到背板和/或将背板强制推(或拉)到模块的机构来消除在模块连接器配合表面和底盘的背板连接器之间的连接器间隙。可以使用弹簧加载的或弹性的元件以有效地填充在连接器接口中的任何设计的插入的和公差引起的间隙的方式来紧固模块，从而允许连接器完全安置。此外，可以在连接器配合接口处包括垫圈或其他可压缩构件。可以通过引入能够在使用特殊的对准固定装置的组装或制造过程期间被设置的可调节卡笼件来减小在连接器接口中的间隙。也可以通过将能够更高公差的制造过程(诸如，机加工)引入卡笼件子组件来减小在连接器接口中的间隙。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 设置, 装置",通用
US11255891B1,US17064814,"本发明提供了包括用于测试(例如，快速和廉价)相控阵列天线和被配置用于射频(RF)发射和/或接收的其他设备的对准夹具的系统的各种实施例。待测设备(例如，被测设备(DUT))可以通过对准夹具被定位在测试位置。对准夹具可以提供可配置的摩擦级以将DUT保持在测试位置。对准夹具可以在处于测试位置时提供与针对DUT的电磁干扰的隔离。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 测试, 设备, 系统, 配置",测试
US11211869B2,US15923438,"一种多相电流共享配置可以包括在电流共享配置中提供相应的输出电流的至少两个电源。一个或多个电源本身可以是多相电源。电流共享配置的第一电源可以检测在提供到第一电源以控制第一电源的输出电压的外部控制信号和由第一电源的VCO提供的内部控制信号之间的相位差。该相位差可以被提供给积分器，以当外部控制信号可用时使内部控制信号跟踪外部控制信号，并且在外部控制信号丢失的情况下保持内部控制信号的当前操作频率，在该情况下，内部控制信号可以用于不间断地控制第一电源的输出电压。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 控制, 检测, 配置","检测, 控制"
US11178628B2,US16671101,"用户设备设备(UE)减少了接收波束选择时间。天线阵列形成接收波束以接收由基站(BS)发送的同步信号块(SSB) 。每个SSB包括OFDM符号。每个SSB包括BS分配的索引。及时地切换接收波束，使得对于每个SSB，接收波束中的两个或更多个接收波束用于接收对应的两个或更多个相互排斥的集合，每个集合具有SSB的至少一个但少于全部的OFDM符号。处理器被编程为对于每个接收波束/ SSB索引对，基于该对的接收波束所接收的索引SSB的至少一个但少于全部的OFDM符号来测量信号质量。处理器使用所测量的信号质量来选择接收波束中的一个接收波束以用于从BS接收后续通信。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"具有, 包括, 处理, 设备",通信
US11146439B2,US16703807,"一种用于确定所接收的M-QAM符号的初始块的粗略载波相位和频率偏移的方法，包括：创建离散的候选相位偏移值的网格，并且对于每个候选值：将所述候选值应用于每个符号，将相应的硬判决应用于每个所应用的符号，以及基于所述硬判决来计算优值。选择具有最佳优值的候选值作为初始相位偏移估计。使用利用所述初始相位偏移估计更新的符号、它们的相应的硬判决以及复指数函数的近似来计算初始频率偏移估计。为了跟踪与一系列符号块相关联的载波相位和频率偏移，对于当前块的每个符号，基于所计算的参数与阈值的比较来设置二进制信任权重，并且使用所述二进制信任权重来计算当前块的相位偏移误差和频率偏移误差。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"包括, 具有, 方法, 设置, 计算",通用
CN109716720B,CN201780053695.1,"公开了使用单个接收设备(诸如单个VSA)来在不同中心频率处捕获并数字化重复的信号的多个时域采集以创建带宽大于接收设备的实时瞬时带宽的单个时域波形的方法和系统。具体而言，一个或多个信号处理路径可以顺序地或者并行地处理重复的信号的所述多个经数字化的采集，使得经处理的采集可以被聚合成对重复的信号的一次或多次重复的表示。
",美国国家仪器有限公司,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 处理, 设备",通用
US11050496B2,US16168650,"测试诸如具有被配置用于毫米波(mmW)发射和/或接收的集成天线的集成电路(IC)之类的设备。可将DUT安装到测量固定装置(例如，插座、消声室等)中的接口。可在该接口上测试DUT的功率和数据连接，其还可提供用于输入/输出信号、功率、和控制的连接(例如，有线)，并且还可提供定位。可使用天线阵列或DUT天线的辐射菲涅耳区中的探针空中测试DUT的射频(RF)特性。阵列的天线或探针中的每个可并入功率检测器(例如，二极管)，以使得可使用DC电压测量来测量RF辐射模式。可将测得的电压测量与理想签名(例如，预期自理想或模型DUT的电压测量)进行比较。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 测试, 模型, 装置, 具有, 设备, 控制, 检测, 电路, 配置","检测, 测试, 控制"
US11050402B2,US16575850,"用于电子地调节电路中的一个或多个初级电感器的有效电感的电路和方法。电路可以包括并联连接在电路的输入和输出之间的多个子电路。每个子电路可以包括初级电感器和电感地耦合到初级电感器的辅助电感器。电路还可以包括耦合到初级电感器的第一电路，其中第一电路被配置为在初级电感器两端引入振荡第一电压；以及耦合到辅助电感器的第二电路，其中第二电路被配置为在辅助电感器两端引入振荡第二电压。第二电压的幅度可以被选择以减小初级电感器的有效电感之间的差异。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"方法, 包括, 电路, 配置",通用
US20210143817A1,US17069041,"低反射率固态开关电路包括被配置为发送电子信号的输入端口以及被配置为接收所述电子信号的第一和第二输出端口。所述开关电路还包括连接在所述输入端口和所述第一输出端口之间的第一开关元件、连接在所述输入端口和所述第二输出端口之间的第二开关元件、连接到第一开关元件和第一输出端口之间的第一导电路径的第三开关元件以及连接到第二开关元件和第二输出端口之间的第二导电路径的第四开关元件。所述第三和第四开关元件可用于在相应的导电路径被配置为关断配置时分流来自它们的连接的导电路径的电流反射。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 电路, 配置",通用
US10996268B2,US15945143,"本发明揭示涉及通过多个软件接口进行动态测量优先化的方法和测量系统。具有低优先级的第一软件接口可经由连接到测量装置的驱动器在待测装置DUT上进行第一测量。具有较高优先级的第二软件接口可起始在所述DUT上进行第二测量的请求。作为响应，所述驱动器可自动确定所述第二软件接口具有比所述第一软件接口高的优先级，并且可停止所述第一测量并进行所述第二测量。所述驱动器可通知所述第一软件接口其对所述测量硬件的存取已被撤销，并且所述第一软件接口可进入监视模式以监视所述第二测量的结果。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 装置, 具有",通用
US20210091669A1,US16575758,"用于操作可编程负载电路的电路和方法，可编程负载电路包括在输入和输出之间并联连接的多个子电路。每个子电路可以包括电感器、负载和耦合到电感器的开关。每个开关可以在第一状态和第二状态中配置，其中电感器通过负载连接到输出或者通过旁路负载的连接来连接到输出。多个第一子电路的开关可以是可编程的，以根据占空比在第一状态和第二状态之间周期性地切换，并且开关可以彼此异相预定量。占空比可以是可编程的，以调谐可编程负载电路的负载。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"方法, 包括, 电路, 配置",通用
US10917144B2,US15918083,"描述了用于使用单个宽带导频信号以减小多输入多输出(MIMO)无线电系统中的接收机之间的定时未对准的系统和方法。 MIMO无线电系统的多个发生器可以在执行接收机对准之后使用第二宽带导频信号来对准。 MIMO无线电系统的校准套件可以在执行接收机对准之前使用第三宽带导频信号来对准。可以通过根据宽带导频信号的相移的变化率来确定时间延迟，从而实现对子样本的对准。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 实现",通用
US20210004342A1,US16751934,"交换机结构总线的总线枚举可在不将总线号分配给未使用的交换机端口和/或未使用的交换机端口被路由到的对应槽的情况下执行。因此，耦合到底盘中的交换机结构总线的交换机可与底盘中的对应槽链接训练，以尝试与耦合到槽的设备建立活动连接。从交换机延伸到未使用槽的未使用的交换机结构总线通道可被标识，并且对应于未使用的交换机结构总线通道的未使用的交换机端口可被禁用。在用于交换机结构总线的后续总线枚举过程期间，总线号可被分配给所标识的使用过的交换机端口(或对应的使用过的槽)，但不被分配给所标识的未使用的交换机端口(或对应的未使用过的槽) 。链接训练、使用过的/未使用过的交换机端口标识和总线枚举可在底盘每次被重置时都被执行。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,设备,通用
US10841019B1,US16568527,"本发明公开了涉及确定被测器件(DUT)的调制质量测量的技术。调制信号从源接收多次，并且每个接收的调制信号被传输到第一矢量信号分析器(VSA)和第二VSA中的每一个。第一VSA和第二VSA解调接收的调制信号，以分别产生第一误差矢量和第二误差矢量。对相应接收的调制信号的第一误差矢量和第二误差矢量执行交叉相关计算，以产生交叉相关测量，并且在多个接收的调制信号上平均化所述交叉相关测量。基于平均化的交叉相关测量确定调制质量测量。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,计算,通用
US10708086B2,US15409261,"公开了涉及信道探测的技术。在一些实施例中，发射机传送从对应于定时信号(例如，每秒脉冲信号)的时间点开始的周期性CAZAC序列。在一些实施例中，接收机等待在对应于CAZAC序列的长度的时间间隔内开始处理所接收的序列，其中，时间间隔与定时信号同时开始。这可以避免处理之前对定时同步的需要，减少接收机实现中的处理和等待时间，并且可以通过将所接收的循环移位的CAZAC序列与所传送的CAZAC序列的本地版本相关而允许确定TOA以及信道脉冲响应估计。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 处理",通用
US10629161B2,US15688196,"可以利用用于系统的不同组件的不同时钟速度来创建硬件。对硬件组件的接口电路的时钟和吞吐量要求可以设置限制，这些限制是对功能组件的较低要求。使用或多或少的一些功能块或接口电路来降低成本、提高性能或可靠性、降低对附加零件的要求或其他有益因素可能是有利的。因此，利用多于单个时钟频率来生成硬件可能是有利的。生成指示用于单独组件的不同时钟频率的指令可能是困难的或耗时的；自动生成这些指令可以在时间节省、提高生产率、提高硬件性能或其他益处中提供显著益处。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 生成, 设置, 系统, 电路",通用
US20200096554A1,US16141697,"描述了用于具有被配置成用于空中发射和/或接收的集成天线的集成电路(IC)的硬件定时测试的天线表征系统和方法。待测试IC (例如，被测器件(DUT))可被安装到无回声室中的可调节定位器。可在使可调节定位器通过多个取向连续转变的同时使用无回声室内的天线或探针阵列来空中测试IC的射频(RF)特性(例如，包括发射特性、接收特性和/或波束成形特性) 。可采用计数器和参考触发智能来将测量结果与DUT的取向相关。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 测试, 具有, 方法, 系统, 电路, 配置",测试
US20200096547A1,US16141733,"描述了用于具有被配置用于空中传输和/或接收的集成天线的集成电路(IC)的硬件定时测试的天线表征系统和方法。要测试的IC (例如，被测器件(DUT))可被安装到无回声腔室中的可调节定位器。 IC的射频(RF)特性(例如，包括传输特性、接收特性和/或波束成形特性)可在可调节定位器通过多个取向持续转变的同时使用无回声腔室内的天线或探针阵列在空中测试。可采用计数器和参考触发智能来将测量结果与DUT的取向相关。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 测试, 具有, 方法, 系统, 电路, 配置",测试
EP2598989B1,EP11744154.3,"用于指定和实现程序的技术。在图形规范和约束语言中创建图形程序，该图形规范和约束语言允许规范计算模型和响应于用户输入的显式的约束声明。该图形程序包括指定的计算模型、多个互连的功能块、以及用于图形程序或图形程序中的功能块中的至少一个的规范或约束，所述互连的功能块根据指定的计算模型在视觉上指示图形程序的功能。指定的计算模型和规范或约束模型可用于分析图形程序或生成程序或模拟。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"实现, 包括, 模型, 生成, 计算, 模拟",通用
US10567065B2,US16100854,"UE中的处理器针对阈值评估射频波束质量度量，响应于确定该度量下降到阈值以下而从第一波束切换到第二波束，并且向基站(BS)发送包括波束测量的报告。该报告指示UE已经执行了切换并且波束测量是针对第二波束的。 UE/BS中的处理器将较窄波束与较宽波束相关联，使用较窄波束而不是较宽波束来在BS与UE之间传送用户数据，针对阈值评估较窄波束的波束质量度量，并且响应于确定较窄波束的波束质量度量下降到阈值以下而切换到使用较宽波束而不是较窄波束来在BS与UE之间传送用户数据。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"包括, 处理",通用
CN110720181A,CN201880029725.X,"基站(BS)从用户设备(UE)接收信道状态信息(CSI)计算能力的报告；基于所报告的计算能力用X和Y值配置用户设备(UE)；通过发送方向唯一的波束来执行波束扫描；以及从UE接收波束测量报告，该波束测量报告包括所发送波束中的Y个最强波束的参考信号接收功率(RSRP)和Y个波束中的X个最强波束的CSI的至少一部分。所述方法还包括基于所述波束测量报告从所述X个波束中选择其中一个，以配置所述UE用于随后的数据和控制信道传输。X和Y是正整数，Y大于或等于X，Y至少为1。
",美国国家仪器有限公司,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 设备, 控制, 计算, 配置",控制
US10447352B2,US15674282,"无线蜂窝基站(BS)发射器发射下行链路校准导频符号。接收器从用户设备(UE)接收由UE发射的上行链路校准导频符号和有效下行链路信道估计。有效下行链路信道估计由UE使用从BS接收的下行链路校准导频符号来计算。处理装置使用从UE接收的上行链路校准导频符号来计算有效上行链路信道估计，并且使用从UE接收的有效下行链路信道估计和由BS计算的有效上行链路信道估计来计算信道互易性校准系数。 BS包括多个天线，并且BS为每个天线计算信道互易性校准系数。可替换地，由BS接收的上行链路信道估计是处理装置用于信道互易性补偿的有效下行链路信道估计的逆版本。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 处理, 设备, 计算",通用
US10396867B2,**********,"一种用于降低多用户(MU)多输入多输出(MIMO)无线通信系统中的下行链路信号解调的复杂度的方法，包括：基站获取基站与用户设备(UE)之间的MIMO信道的上行链路(UL)信道状态信息(CSI)，从ULCSI推导下行链路(DLCSI)，以及使用基于DLCSI的MIMO预均衡来传输正交频分复用(OFDM)无线子帧。 UE使用单个复相量估计来执行从基站接收的OFDM子帧的下行链路互易性校正，并且执行下行链路互易性校正的OFDM子帧的下行链路数据解调，而无需执行另外的MIMO均衡。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括, 设备",通信
US10371733B2,**********,"射频设备的噪声系数可以通过功率测量获得。可以基于整个RF系统的S参数信息构建信号流图。 S参数信息可以表示微波终端、设备、测量仪器以及由于诸如连接电缆/衰减器/开关之类的附加部件所导致的任何损耗。该信号流图包括与以上列举的每个RF子系统相对应的源节点的适当布置和值。噪声系数测量可以包括校准步骤和测量步骤。在校准步骤期间，可以获得用于测量的测量仪器的噪声系数和噪声温度。在测量步骤期间，可以至少基于在校准步骤期间获得的测量仪器的噪声系数和噪声温度获得设备的噪声系数和噪声温度。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 包括, 设备",通用
US10367525B2,US14725914,"公开了涉及对通信进行编码的技术。在一些实施例中，对于编码矩阵的不同行，执行以下操作：针对行中的条目生成操作的集合，其中操作的集合包括要对条目执行的用于将矩阵与向量相乘的相应操作；将编码矩阵中的条目的值传播到操作的集合中；以及基于传播的值简化操作的集合中的一些，以生成操作的输出集合。在一些实施例中，操作的输出集合可用于对输入数据进行编码以用于通过介质进行通信。在一些实施例中，所公开的技术促进在编译器存储器约束内的循环展开。在一些实施例中，装置(例如，移动设备)被配置有操作的输出集合。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 生成, 设备, 配置",通信
US10338110B2,US15359462,"可以利用数字控制回路和电路来实现源测量单元(SMU)，以便数字地补偿输入偏置电流对电流测量的影响。可以在其中测量跨越电流感测元件产生的分流电压的SMU的输出信号路径中使用一个或多个缓冲器，所述一个或多个缓冲器具有关于可能影响电流测量的某些参数的明确定义的特性。例如，缓冲器可以传导/产生相应的输入偏置电流，所述输入偏置电流随着温度可感知地且可预测地改变。通过测量温度并且根据温度测量来调整用于产生分流电压—的控制电压—，可以将输入偏置电流对电流测量的影响降低到可忽略不计的水平和/或可以消除。可以通过调整表示所测量的分流电压的电压反馈值并且/或者通过调整用于生成控制电压的电流设定点来调整控制电压。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 具有, 生成, 控制, 电路",控制
US10312978B2,US16036372,"在具有M个天线的无线收发器站中，将一个天线指定为目标参考天线(TRA)，并且对于不是TRA的每个天线：通过零个或更多中间参考天线定义从天线M到TRA的N个不同的路径，N是两个或更多，每个不同的路径具有一个或多个天线对的不同的相关集合；对于这些集合中的每个天线对，通过在天线对之间来回发送校准导频并计算天线对的互易系数，估计有效的前向和反向信道响应，并且使用估计的信道响应计算天线对的互易系数；对于N个不同的路径中的每一个，使用对于与路径相关的天线对集合计算的互易系数计算互易系数估计；并且组合N个计算的互易系数估计，以产生天线对(M，目标参考天线)的最终互易系数估计。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"具有, 计算",通用
US20190137542A1,US16180561,"一种新型耦合系统可以包括头端电路，用于经由电缆将探测器耦合至仪器，在电缆将信号从探测器传送到仪器的同时，通过电缆将电力传递到探测器。头端电路可以包括用于经由电缆耦合至探测器的第一端子，并且还可以包括用于耦合至仪器的第二端子。头端电路可以将直流(DC)电力施加至电缆，并且可以在来自探测器的信号到达仪器之前移除由所施加的DC电力引起的DC电压偏移。头端电路可以包括耦合至第一端子的公共节点、将公共节点耦合至供电电压的电流源、以及将公共节点耦合至耦合至仪器的第二端子的电压源。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 包括, 电路",通用
US10277734B2,US15681772,"一种装置包括蜂窝频率无线电，该蜂窝频率无线电耦合至或被配置为耦合至设置在射频(RF)屏蔽室中的天线。蜂窝设备包括嵌入式用户识别模块(eSIM) 。当设备处于屏蔽室中时，处理器控制无线电使用天线来向设备传送用户识别模块(SIM)配置文件，并且随后与设备通信以当设备使用加载到eSIM中的SIM配置文件时测试设备的操作。一种蜂窝设备包括无SIM配置文件的eSIM、蜂窝频率无线电、以及处理器，该处理器控制无线电在eSIM仍无SIM配置文件时无线地接收SIM配置文件、将接收到的SIM配置文件加载到eSIM中、以及控制无线电使用加载到eSIM中的SIM配置文件来与蜂窝网络无线地通信。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 测试, 装置, 设置, 处理, 设备, 控制, 配置","通信, 测试, 网络, 控制"
US10257005B2,US15674059,"一种装置包括将子帧处理为用于射频通信的时域信号帧结构的一部分的电路。该子帧包括多个块符号，每个块符号具有相关联的时域保护时段和一个或多个射频(RF)切换保护时段。多个块符号中的每个块符号是公共块符号或特殊块符号。公共块符号与子帧的所有其他公共块符号具有公共时域保护时段。特殊块符号具有与公共时域保护时段不同的时域保护时段。子帧中的所有特殊块符号被放置到一个或多个RF切换保护时段中。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 具有, 处理, 电路",通信
EP2057541B1,EP07799492.9,"在图中配置线条/图标。所述图可以是可执行图，诸如图形程序或系统图。所述图可包括通过线条连接的多个图标，并且所述图标可在视觉上表示所述图的功能。所述图可执行以执行所述功能。显示所述图可包括在所述图中显示第一线条，其中所述第一线条连接第一图标和第二图标。数据传递功能可针对所述图中的所述第一线条和/或所述第一或第二图标指定。所述数据传递功能可在所述图中在视觉上例如通过所述第一图标、所述第二图标、所述第一线条和/或在接近所述图的这些组件处显示的图标的外观来指示。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"系统, 包括, 配置",通用
US10243715B2,US15270181,"公开了针对用于5G (5 <Sup>th </Sup>生成)移动电信标准和相关无线电接入技术(RAT)的新的统一且灵活的帧结构的实施例。所公开的实施例使用具有多个分区类型的通信帧并且能够以灵活且可扩展的方式跨越宽范围的5G部署场景。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"具有, 生成",通信
US10241764B2,US15588298,"用于编译程序的系统和方法，包括确定在每个程序结构的入口和出口处包含一个或多个变量的一个或多个程序结构，其中每个变量指定一个或多个源变量到程序结构外部与程序结构内部之间的目的地变量的值传递操作。可以确定目的地变量的子集，对于该子集，将目的地变量指派给对应源变量的存储器资源不破坏程序的功能。值传递操作的实施可以被执行以将目的地变量的所确定的子集中的每个映射到相应存储器资源。映射可以动态地改变，由此将值从第一源变量传递到目的地变量，而不在存储器资源之间复制该值。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括",通用
US20190082040A1,US15700050,"无线通信装置包括分别用于向/从存储单元传送的源/宿PDCP SDU和MACPDU的第一/第二数据源/宿、以及由控制处理器(CP)控制的硬件加速器。响应于来源用于向存储单元传送的传送PDCP SDU，CP控制硬件加速器以生成并向存储单元写入PDCP RLC MAC报头，并且将所生成的报头和来自存储单元的传送PDCP SDU组装到传送MACPDU以供提供给第二数据宿。响应于来源用于向存储单元传送的接收MACPDU，CP控制硬件加速器以解码存储单元中的接收MACPDU的PDCP、RLCSDU报头，以确定存储单元中的接收PDCP SDU的位置并且从所确定的位置获取接收PDCP SDU以供提供给第一数据宿。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 装置, 生成, 处理, 控制","通信, 控制"
US10218549B1,US15879369,"一种后均衡相位跟踪单元，对于接收序列的每个信号块：使用已均衡的前导频符号来计算开始绝对相位旋转；将块细分成已均衡符号的组的时间序列；利用绝对相位旋转来初始化与在时间中的第一组相关联的累积相位。对于每个组，该单元：使用用于盲目地估计剩余组相位的先前组的累积相位来计算每个符号的去旋转版本；向该组的累积相位分配该组的剩余相位与先前组的累积相位之和；通过至少使用该组的累积相位来计算相位补偿信号，来估计该组内的相位漂移。前均衡相位跟踪单元计算在相同的传输的初始/终端序列部分之间的自相关的相位；使用自相关相位和先前信号块开始相位来估计开始相位；对开始相位进行内插以估计相位漂移。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,计算,通用
US10219405B2,US15645267,"各种类型的电子设备可安装在机架中以便于与包含这些设备的设备对接，提供可从这些电子设备等移除热量的冷却系统。向机架中的每个电子设备传送足够的冷却气流可能对于包含在机架中的电子设备的适当功能、使用寿命或其他特性是重要的问题。本文呈现的实施例描述了一种气流矫直机的新颖设计，该气流矫直机被配置成用于插入机架内以矫直气流。在一些实施例中，格栅由主要彼此垂直取向的长分隔件和较短分隔件组成，从而导致矩形格栅。将这样的格栅包括在机架中可改善冷却系统的均匀性和性能。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 设备, 系统, 配置",通用
US10218548B1,US15879318,"一种预均衡相位跟踪单元，对于接收的系列的每个信号块：计算与发送的相同的初始和终端序列的部分之间的自相关，并且计算自相关的相位；使用自相关相位和串联的先前信号块的开始相位来估计块处理窗口的开始相位；通过使用至少串联的信号块和下一个信号块的估计的开始相位进行内插来估计窗口内的相位偏移；以及使用估计的相位偏移来计算相位补偿信号。后均衡相位跟踪单元将块细分为均衡符号的组的时间序列。对于每个组：使用先前的组的累积相位来计算每个符号的去旋转版本，以盲估计剩余组相位；利用组的剩余相位和先前的组的累积相位的和来分配组的累积相位；通过使用至少组的累积相位来估计组内的相位偏移以计算相位补偿信号。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"处理, 计算",通用
US20190056982A1,US15678929,"用于自动计算由测量设备执行的测量的不确定性的系统和方法。测量设备发起一个或多个测量。响应于测量发起，测量设备的驱动器向测量涉及的多个硬件模块中的每一个发送误差规范请求。多个硬件模块中的每一个基于相应硬件模块的当前配置来确定所请求的误差规范。测量设备然后基于误差规范来计算与一个或多个测量相关联的不确定性。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"方法, 设备, 系统, 计算, 配置",通用
US10201020B2,US15708664,"一种用于在移动电信网络中在基站与具有多个天线的用户设备(UE)之间执行多用户随机接入过程的系统和方法，包括：使用多个UE天线中的一个或多个天线来发送随机接入信号集(RASS)消息。响应于接收到RASS消息，基站发送随机接入响应物理下行链路控制信道(RAR-PDCCH)消息。响应于接收到RAR-PDCCH消息，使用多个UE天线来发送互易性参考信号集(RRSS)信号。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"包括, 具有, 方法, 设备, 控制, 系统","网络, 控制"
US10125706B2,US13935282,"升压电源可以由多个较小开关电源构成，每个开关电源向负载提供由升压电源提供的组合输出电流的相应部分。可以向每个开关电源提供不同的相应控制信号，以调节由开关电源提供的组合输出电流的相应部分。可以向对应的开关电源提供相对于彼此不同相的每个不同相的相应控制信号，以防止组合输出电流超过指定的阈值电流值。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 控制",控制
US10120405B2,US14245693,"单半导体基结可用于创建电压参考，并通过在不同电流驱动电平之间时分多路复用所述电压参考来温度补偿所述电压参考。也就是说，通过所述单结驱动的电流的值可以递归方式重复地变化。在所述结为齐纳二极管的情况下，所述电流可在正向与反向之间重复地切换。只要响应于所述结上的不同电流而产生的不同电压的温度系数(以ppm/0C计)不同，所述不同电压值的加权就产生零温度系数电压参考值。为了实施带隙参考，单二极管连接双极结晶体管可交替地使用第一电流和至少第二电流来正向偏置。所述(至少)两个所得Vbe (基极-发射极电压)下降的加权可产生零温度系数带隙电压。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,未明确,通用
US10103928B2,US15486537,"公开了用于无线通信中的脉冲成形的奈奎斯特滤波器和相关匹配滤波器，其提供改进的性能。所公开的实施例认识到，升余弦函数的二阶导数在频域中是不连续的，并且方根升余弦的一阶导数在频域中是不连续的。这样，应用了用于升余弦滤波器的泛化，并且可以通过向频率响应的较高阶导数引入平滑性，来对升余弦函数时间-频率定位进行改进，并且最终对符号间干扰与相邻信道干扰之间的权衡进行改进。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,提供,通信
US10069400B1,US15827588,"残留电流(例如，共模电流)可以存在于隔离子系统中。隔离子系统可以包括变压器的次级绕组，而第一子系统可以包括变压器的初级绕组。第一子系统还可以包括补偿电路。驱动器电路可以生成提供给变压器的初级绕组以及还提供给补偿电路的驱动信号。补偿电路可以包括可变电容器网络(例如，可变电容器二极管网络)，其接收驱动信号并且还接收偏置电压，并且根据驱动信号和偏置电压生成消除信号。补偿电路可以通过将可变电容器二极管网络耦合到接地平面的电容器将消除信号提供给隔离子系统的接地平面，以便减少或消除存在于隔离子系统中的残留电流。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 生成, 系统, 电路",网络
US20180198651A1,US15855148,"无线设备接收组成均衡的FDM数据子载波的频分复用(FDM)符号。调制方案星座图被细分成两个或更多个区域。对于每个区域，提取落入该区域内的子载波的子集，并且在其上计算相应区域特定的CPE估计。对该相应区域特定的CPE估计进行平均，以产生用于补偿子载波的总体CPE估计。此外，使用嵌入第一接收FDM符号中的导频符号来计算第一CPE估计，并且该第一CPE估计被用于补偿子载波。使用第一CPE估计来补偿不具有嵌入的导频符号的后续第二FDM符号，并且在使用第二CPE估计进行补偿的第二FDM符号补偿子载波上使用盲估计方法来计算第二CPE估计。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"具有, 方法, 计算, 设备",通用
US20180191168A1,US15861898,"一种多相功率变换器可以包括以并联交错的电流共享配置耦合的多个LLC变换器级。由该多相功率变换器提供的总电流可以通过以下方式在不同LLC变换器级之间平衡：感测每个LLC变换器级中的相应输出电流，其中所述LLC变换器级中的一个LLC变换器级的感测的输出电流用作参考电流，并且基于感测的输出电流对每个LLC变换器级(不同于参考LLC变换器级)执行一个或多个调整。该调整可以包括调整提供给该LLC变换器级的输入电压、该LLC变换器级的谐振频率和/或该LLC变换器级的有效谐振阻抗。感测相电流或功率的能力使得有可能在多相LLC级电流共享配置中的不同LLC变换器级之间实现平衡。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 实现, 包括, 配置",通用
EP1971944B1,EP06846471.8,"用于实现包括处理器的可编程硬件元件(PHE)的设计流程的系统和方法。接收图形程序(GP)，其中所述GP指定性能标准。映射所述GP以用于部署，其中第一部分被针对由所述处理器执行，并且第二部分被针对在所述PHE中实现。做出关于所述图形程序是否满足所述性能标准的确定。如果不满足，则重新映射所述GP以用于部署，包括：标识并指定用于在所述PHE中实现的子部分，从而将所述子部分从所述第一部分移动到所述第二部分；和/或标识并指定用于在所述处理器上执行的所述子部分，从而将所述子部分从所述第二部分移动到所述第一部分。将所述确定和重新映射重复一次或多次，直到满足所述性能标准为止。将所述第一部分和所述第二部分部署到所述PHE.
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"实现, 包括, 方法, 处理, 系统",通用
US20180164752A1,US15372649,"改进的过程控制系统可以包括共享的安全控制和过程控制部件/元件，以便于在系统的安全功能和系统的一般操作(即，正常)功能之间共享传感器、致动器以及输入/输出(I/O)接口电路。对应于系统的安全操作的部件和/或电路可以被设计成允许输入在正常操作期间被一直监视，因为通常没有与监视相关联的安全风险。对应于(或专用于)系统的安全操作的部件和/或电路可以操作以在需要激活安全功能时防止对各种指定的输出/致动器的正常操作控制。当不需要激活安全功能时，这些相同的部件和/或电路可以允许对指定的输出/致动器的正常操作控制。系统的安全部分或区段可以具有对输出/致动器的优先控制，并且可以在致动器/输出是安全的时允许正常功能和信号传递到致动器/输出。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 具有, 控制, 系统, 电路","传感器, 控制"
US20180159637A1,US15821473,"公开了涉及校准和操作多输入多输出(MIMO)无线电系统的技术。在一些实施例中，双模式校准可被用来校准远程发射机(RT) 。在第一稀疏全系统校准(SFSC)模式期间，RT可物理地连接到MIMO无线电系统。在一些实施例中，可分别为RT和本地发射机(LT)中的每一个导出第一和第二均衡器。在随后的实时校准(RTC)模式期间，RT可位于远离MIMO无线电系统的位置，并且RT可被配置成经由天线通过空中与MIMO无线电系统通信。在RTC模式中，第三均衡器可为LT导出。然后，可基于从第一、第二和第三均衡器中的每一个导出的均衡器来校准RT.作为一个非限制性示例，本文所描述的技术可实现用于RT的实时校准，即使在RT位于远离MIMO无线电系统的位置时也是如此。在不同实施例中，校准可通过导出分序间隔的频域均衡器或时域均衡器来实现。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 实现, 配置",通信
US09990250B2,US14725812,"公开了涉及单个集成电路(IC)上的LDPC编码电路的实现的技术。在一些实施例中，单个IC上的电路包括：消息电路，被配置为接收或生成要被编码的消息；编码电路，被配置为对所述消息执行低密度奇偶校验(LDPC)编码；噪声电路，被配置为向所编码的消息施加噪声；以及解码电路，被配置为执行对所述消息的LDPC解码。在一些实施例中，所公开的技术可以降低生产成本(例如，通过降低总芯片面积)、促进LDPC测试、和/或提供涉及单个芯片上的消息传输的多个不同功能。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 实现, 包括, 测试, 生成, 电路, 配置",测试
US09965371B2,US14861006,"用于确定和传送有线计算机外围设备到用户的连接性的系统和方法。可以存储关于连接到总线联网系统的系统分级结构中的计算机系统的多个设备中的每一个的特性信息，所述特性信息包括与每个设备相关联的设备分级结构，所述设备分级结构标识包括在所述设备中的相应硬件节点以及所述设备的一个或多个可视属性。可以基于所述设备分级结构为所述设备中的至少一些自动确定相应系统位置。可以基于所述设备中的一个或多个的特性信息确定至少一个设备的相应参考点。所述计算机系统可以生成指示所述至少一个设备相对于所述设备的相应参考点的相应系统位置的信息，所述信息可用于在所述总线联网系统中可视地标识所述设备。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 生成, 设备, 系统, 计算",通用
US09935637B2,US14619888,"用于FPGA应用的设计环境使得FPGA平台能够包括用户设计和一个或多个接口单元，用户设计可以使用所述接口单元来访问一个或多个外部模块/设备，而不需要对这样的模块/设备的结构和操作的任何特定知识。在操作环境的控制下，与外部设备/模块相对应的接口单元可以建立用户设计和外部模块/设备之间的通信。外部处理模块可以使用接口单元来监控和/或控制用户设计。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"平台, 包括, 处理, 设备, 控制","通信, 控制"
US09935757B2,US14941158,"公开了涉及用于全双工(FD)无线通信的信道质量报告的技术。在一些实施例中，一种装置(例如，移动设备)被配置成在无线通信中接收参考信号，并且基于所测量的参考信号的SINR和一个或多个自干扰消除电平来确定用于FD通信的有效信号与干扰加噪声比(SINR) 。该装置可基于该装置传送的信号的传送功率和SIC后的残余功率来确定这一个或多个自干扰消除电平。 SIC电平可包括可被单独确定的模拟和数字SIC电平。一个或多个调制和编码方案可基于有效SINR来确定。在一些实施例中，为该装置所使用的多个不同传送调制阶次确定多个有效SINR.
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 配置, 装置, 设备, 模拟",通信
US20180090988A1,US15716108,"一种嵌入式系统可使用确定性通信链路，例如在与主电网耦合的点处，将与电力输送设备的一个或多个操作参数(例如，相位角、频率、幅度等)对应的信息分发到其它电力输送设备。可在未来或过去的时间点，例如以限定的时间间隔，传输对一些或全部信息的更新。可使用例如锁定瞬时内插机制的时间同步方法，来创建在所有电力输送设备之间共享的协调时间。由此，可将各种操作参数失配，例如向电网输送电力的电力输送设备之间的相位失配，降低到小于规定的可忽略的值。这在电力输送设备之间创建紧密的时间同步，并允许它们以稳定而不使电网不稳定的方式互操作。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 设备",通信
US09917755B1,US15457413,"本发明揭示与确定经配置以执行包络跟踪的射频RF通信装置中的延迟有关的技术。所述RF通信装置可包括功率放大器和包络跟踪器。可分别将第一和第二输入刺激信号发射到所述功率放大器和所述包络跟踪器中的每一者。所述RF通信装置可通过所述功率放大器将输出信号输出到向量信号分析器VSA.所述VSA可通过使所述输出信号与参考信号互相关来确定第一延迟偏移，且所述VSA可基于所述输出信号的振幅失真来确定第二延迟偏移。可基于所述第一和第二延迟偏移之间的差来确定所述第一和第二输入刺激信号之间的相对延迟。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 配置",通信
US20180031609A1,US15664396,"公开了涉及使用软前面板(SFP)来显示测量功能的结果的方法和测量系统。测量设备可以并行地执行两个或更多个测量功能，以产生两个或更多个相应的信号测量，其中每个测量功能包括获取功能和处理功能。计算机可以被配置为在显示设备上的软前面板上显示与两个或更多个信号测量相对应的信息。计算机可以从用户接收对更新选项的选择，其中该更新选项指示使所显示的信息更新的触发。响应于接收到该触发已发生的指示，计算机可以更新在该软前面板上所显示的信息。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 处理, 设备, 系统, 计算, 配置",通用
US09880030B2,US14295443,"用于在保留装置驱动器IP和驱动器IP的功能的同时扩展可编程装置功能的系统和方法。可接收指定具有标准驱动器IP的可编程测量装置的定制IP的功能的用户输入。定制IP可相应地生成，并且可部署到可编程测量装置。在操作期间，定制IP可直接与标准驱动器IP通信，并且可提供可编程测量装置的定制功能，同时保留可编程测量装置和标准装置驱动器上的标准驱动器IP的功能。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 装置, 具有, 方法, 生成, 系统",通信
US09871649B2,US15194800,"用于子样本时间分辨率信号对准的系统和方法。可以通过迭代地执行以下操作直到满足终止条件来对准第一信号和第二信号：可以获取第一信号和第二信号的当前样本，可以生成第一信号的当前样本的延迟的副本并且可以从第一信号的当前样本中减去第一信号的当前样本的延迟的副本以生成第三信号，可以利用当前子样本延迟来生成第二信号的当前样本的延迟的副本并且可以从第一信号的当前样本中减去第二信号的当前样本的延迟的副本以生成第四信号，并且可以基于第三信号和第四信号来生成对准误差并且相应地延迟调整当前子样本。迭代地调整可以生成将第二信号与第一信号对准的子样本分辨率延迟。随后样本第一信号和第二信号可以根据子样本分辨率延迟来对准和输出。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 生成, 方法",通用
US09852036B2,US14878449,"一种新型诊断和可验证输入/输出(DVIO)信道可减少固定诊断电路并且允许标准输入/输出信道被重新用作针对具体部署的诊断。 DVIO信道可包括数字输入子信道和数字输出子信道，其中每个子信道包括用于执行基本诊断的基本保护和诊断电路。两个子信道可彼此独立地使用，并且它们还可耦合在一起以创建增强的数字输入或数字输出信道，其能够执行更高级的诊断，例如输出回读或测试脉冲生成。多个DVIO信道可耦合在一起以创建具有冗余信号路径的多信道数字输入或数字输出。以此方式，输入/输出资源可被配置为满足给定应用的具体需要，并且最小化传统实现中所需的测试和诊断电路。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 包括, 测试, 具有, 生成, 电路, 配置","测试, 验证, 诊断"
US09854552B2,US14740425,"公开了与无线信号的检测有关的技术。在一些实施例中，一种方法包括：生成针对接收到的无线消息中的训练字段的自相关结果，基于该自相关结果来生成差分信息，以及确定满足了一个或多个信号识别准则。在一些实施例中，该信号识别准则包括关于差分信息中的第一峰在至少第一时间区间内满足第一阈值的第一准则。在一些实施例中，该信号识别准则包括一个或多个附加准则，该一个或多个附加准则包括关于差分信息中的第二峰在至少第二时间区间内满足第二阈值的第二准则，其中，第一和第二峰具有不同的极性，和/或关于第一峰对应于在特定自相关阈值之下的自相关结果值的第三准则。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 具有, 方法, 生成, 检测",检测
CN104620120B,CN201380031762.1,"一种开关元件，至少部分地实现在一个或多个印制配线板(PWB)中。第一输入和多个输出可以集成到PWB中。在一些实施例中，多个接触部也可以集成到PWB中。开关元件选择性地可操作在第一状态和第二状态下，在第一状态下，第一输入耦合到第一输出，并且在第二状态下，第一输入耦合到第二输出。
",美国国家仪器有限公司,其他技术,0.0,未匹配到特定技术领域,实现,通用
US09800229B2,US14267274,"提供了一种用于处理来自多个数据通道的数据样本的方法。该方法可包括从多个数据通道获得多个数据样本。获得多个数据样本可涉及从多个数据通道中的每个数据通道连续地获得数据样本。可在指定时间段期间执行多次从每个数据通道连续地获得数据样本。多个数据样本中的每个数据样本可与相应样本时间相关联，并且每个相应样本时间可以相对于单个指定参考时间点。该方法还可包括：针对多个数据样本中的每个数据样本，确定可对应于与数据样本相关联的样本时间的时间相关系数值，以及将所确定的时间相关系数值应用于所述数据样本。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 包括, 处理",通用
US09797936B2,US14639432,"一种改进的计数器可实现动态频率测量，同时还保持与传统频率测量方法完全向后兼容。计数器可根据低频、大范围和/或高频操作模式来操作。它可用与大范围操作模式相关联的除数值以及与高频操作模式相关联的测量时间来编程。除数和测量时间的设置可被允许或禁止，并且当任一设置被禁止时，计数器变为与传统频率测量方法向后兼容。计数器还可被提供有表示所需测量类型以及要测量的信号的最小和最大期望值的输入。计数器可根据操作模式中的任何一个或多个来执行频率测量，并且返回在首先完成测量的操作模式中获得的测量结果。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 设置, 实现",通用
US20170300303A1,US15490280,"描述了用于图形程序中的图的增量布局的线性编程制定的系统和方法的各种实施例。图形编程开发环境或其他软件应用可操作用于自动分析图形程序的框图，以例如以便确定呈现在框图中的对象以及它们在框图内的初始位置。图形编程开发环境然后可以自动地重新定位框图中的各种对象。在各种实施例中，对象可以被重新定位以便更好地组织框图或使得用户能够更容易地查看或理解框图。图形编程开发环境可以对重新定位施加一个或多个约束以便确保所得到的经修改的框图类似于原始框图。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法",通用
US09785415B2,US14500652,"用于控制定制模块化测量系统的系统和方法。编辑器可以接收指定一个或多个系统定义的用户输入，每个基于映射消息的命令、参数、变量和/或元数据(""信息"")符合用于独立仪器的用于功能和编程语言的数据的控制协议，并且相应地生成定义，每个定义可以由客户端应用程序使用以经由基于消息的信息与包括多个逻辑仪器的定制模块化测量系统对接。定义中的至少一个可以被部署在测量系统上。测量系统的运行时引擎可以从应用程序接收基于消息的命令，并且调用可以调用逻辑仪器中的至少一个的操作的对应功能。逻辑仪器可以同时操作，包括由逻辑仪器中的至少两个共享单个物理测量设备的使用。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 生成, 设备, 控制, 系统",控制
US09784279B2,US15045723,"风扇噪声抑制电路可以耦合在电源和到至少一个风扇的功率输入之间。风扇噪声抑制电路可以包括耦合到电源的可调电流源。可调电流源可以基于电源的功率输出来提供电压输出和电流输出。风扇噪声抑制电路可以包括耦合到可调电流源的输出的反馈控制器。反馈控制器可以被配置为将电压输出与参考电压进行比较并且向可调电流源提供误差值，其中可调电流源可以基于误差值来调整电流输出。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 控制, 电路, 配置",控制
US09768805B2,US14725706,"涉及LDPC编码的技术。产生可用于基于输入消息而生成经编码消息的操作集合。操作集合对应于较小矩阵表示中的条目的操作，其指定LDPC编码矩阵中的非零条目的位置。利用操作集合配置移动装置以执行LDPC编码。利用操作集合配置的电路以高性能、相对小的面积和/或低功耗来执行LDPC编码。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"生成, 装置, 电路, 配置",通用
US09755289B2,US14739474,"一种系统包括导电板、同轴传输线、平行于导电板的电路以及从同轴传输线到电路的直角过渡。传输线包括突出穿过板中的孔的中心引脚、由孔的导电表面形成的外导体以及两者之间的空气电介质。电路包括顶部导电层(TCL)、具有切口的接地平面以及TCL与接地平面之间的邻接引脚的绝缘衬底。过渡包括引脚、将中心引脚连接到TCL的导电元件、外导体、空气电介质、衬底抵靠引脚的邻接以及切口。邻接和切口使得与引脚和接地平面之间的距离有关的制造变化最小化。过渡将通过将引脚接合到TCL而引入的电感调谐。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 包括, 电路, 具有",通用
US09753881B2,US14213476,"一种计算平台，包括互连的现场可编程门阵列(FPGA)阵列、存储器、以及外部输入/输出接口。该平台是与高级电信计算架构(ATCA)标准兼容的刀片的形式。该平台特别用于电信和联网应用。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"平台, 包括, 计算",通用
US09702313B2,US13934947,"一种发动机系统可包括喷射器和发动机控制单元(ECU)，所述发动机控制单元具有耦合到喷射器的多个引脚。 ECU可包括耦合到每个引脚的单独测量电路，其中每个单独测量电路提供对应于耦合到测量电路的引脚的相应单独输出。 ECU还可包括减法电路，每个减法电路具有相应的一对输入并且提供表示出现在该对输入处的相应输入值之间的差的输出。 ECU还可包括耦合在测量电路和减法电路之间的交叉点开关，并且可通过将耦合到两个引脚的两个单独测量电路中的每个单独测量电路的相应输出选择性地耦合到任何减法电路的该对输入中的相应输入，来确定任何两个引脚之间的电压差。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 具有, 控制, 系统, 电路",控制
US09692586B2,US14691339,"一种用于无线通信节点的灵活实时调度器，使得该节点能够使用动态可变帧结构与远程节点进行通信。该调度器连续地接收定义帧序列中的帧的帧结构的映射信息。每个帧包括多个时隙(例如，时隙或频率时隙) 。该映射信息为每个帧的每个时隙指定该时隙是发送时隙还是接收时隙。该调度器驱动发送器在被指派用于发送的时隙期间进行发送，并驱动接收器在被指派用于接收的时隙期间进行接收。 (每帧的时隙数量和每个时隙的大小也是可配置的)
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 配置",通信
EP1849069B1,EP06718414.3,"用于确定和/或合并配置图之间的差异的系统和方法。接收关于包括第一多个节点的第一配置图并且以图形方式表示第一系统的第一信息，并且接收关于包括第一多个节点的第二配置图并且以图形方式表示第二系统的第二信息。节点的至少一部分可以对应于相应系统的硬件设备、程序和/或配置数据，并且可以互连。分析第一信息和第二信息，以例如通过遍历配置图或表示该图的数据结构来确定和/或合并第一配置图与第二配置图之间的差异，例如硬件、软件、配置和/或连接性之间的差异。可以例如在以图形方式指示差异的情况下例如经由突出显示在显示设备上显示差异和/或合并配置图的指示。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 设备, 系统, 配置",通用
US20170131984A1,US14938649,"用于创建程序的系统和方法。可以编译程序，包括确定该程序中的一个或多个值传送操作。每个值传送操作可以指定相应的一个或多个源变量和目的地变量之间的值传送。对于一个或多个值传送操作中的每个，可以实现值传送操作，其中值传送操作的实现可以是可执行的以将值传送操作的每个变量指派到相应的存储器资源，从而将变量映射到存储器资源，并且动态地改变映射，包括将目的地变量指派到一个或多个源变量中的第一源变量的存储器资源，从而将值从第一源变量传送到目的地变量而不在存储器资源之间复制该值。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括, 实现",通用
US20170131976A1,US14937732,"用于创建图形程序的系统和方法。第一复制结构可以被包括在图形程序中，其中，第一复制结构指定第一复制结构内的任何图形程序代码的复制。第一图形程序代码可以被包括在第一复制结构内。图形程序的实现可以被自动生成，包括生成在图形程序的实现内的第一图形程序代码的实现的多个实例。执行图形程序可以包括例如同时地和/或串行地执行多个实例。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 包括, 生成, 方法, 系统",通用
US20170132108A1,US14937602,"用于调试图形程序的系统和方法。可以例如从存储装置、从另一进程或设备等接收图形程序。所述程序包括并行的图形程序部分，每个部分包括图形程序结构和/或图形程序中的执行路径。并行的图形程序部分的第一图形程序部分可以是单步调试的，包括在第一图形程序部分中执行单步步骤，以及在后台中执行其他图形程序部分中的每一个中的代码，所述代码被调度为在第一图形程序部分中的单步步骤的开始和结束之间执行。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 方法, 设备, 系统",通用
US09626233B2,US14260420,"公开了一种便于生产者程序和消费者程序之间的通信的图形程序执行环境。生产者程序可将数据存储在由生产者程序分配的存储器块中。图形程序可与生产者程序通信以获得对存储器块的引用。图形程序可异步地将该引用传递给消费者程序，例如，可在消费者程序访问存储器块中的数据时不用阻塞或等待来传递该引用。在消费者程序完成对数据的访问之后，消费者程序可异步地通知图形程序执行环境以释放存储器块。图形程序执行环境可随后通知生产者程序存储器块不再在使用中，使得生产者程序可解除分配或重新使用存储器块。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,未明确,通信
US09611797B2,US13934431,"发动机系统可包括指定数量的喷射器和发动机控制单元(ECU)，发动机控制单元具有喷射器可耦接到其的引脚。 ECU可包括控制器，控制器以硬件、软件或两者的组合实现，并且能够在不同的多路复用配置之间切换，其中每个多路复用配置包括跨相应引脚对耦接的指定数量的单独喷射器。控制器可从多个指定多路复用配置中选择一个多路复用配置，而不需要对喷射器和/或引脚进行任何硬件调整。控制器还可在由控制器选择的主动多路复用配置中通过引脚对来操作单独喷射器。在至少一个多路复用配置中，控制器可控制相应引脚对中的某些引脚处的低侧开关，以及控制相应引脚对中的其余引脚处的独立高侧开关。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 包括, 具有, 控制, 系统, 配置",控制
US09581630B2,US14180425,"一种用于校准矢量网络分析器的方法可以包括：对多个端口中的第一端口执行第一测量集合，以及确定针对第一端口的误差系数。误差系数可以被用来获得第一校准端口。对于多个端口中的未校准端口，可以建立经由已校准端口与未校准端口之间的已知直通的连接，并且来自第一信号源的第一信号可以被应用到校准端口，并且来自第二信号源的第二信号可以被应用到未校准端口。可以执行关于未校准端口的进一步测量集合，并且可以基于该进一步测量集合和与校准端口的误差系数的关系来确定针对未校准端口的误差系数。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"方法, 包括",网络
US09582342B2,US14694566,"一种通信设备和相关联的方法，其被配置为利用API约束语言来通信通信栈的不同层之间的资源约束。在第一通信设备中执行的通信栈的第一层可以从也在第一通信设备中执行的通信栈的第二层接收应用编程接口(API)消息。此外，第一层可以接收具有一个或多个API消息的资源约束。这些一个或多个资源约束可以由第二层或在通信设备中执行的其他软件生成。然后第一层可以基于API消息并服从资源约束来执行通信功能。资源约束可以影响在执行通信功能期间对第一通信设备的硬件和/或软件资源的使用。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"具有, 生成, 方法, 设备, 配置",通信
US20170033915A1,US14878645,"本发明公开了涉及在全双工无线电的情境中使用数字预失真的技术。在一些实施例中，一种装置包括一个或多个天线，并且被配置为使用所述一个或多个天线经由至少部分重叠的频率资源同时发送和接收无线信号。在一些实施例中，所述装置包括接收链电路，所述接收链电路被配置为处理由所述装置经由所述一个或多个天线发送的无线信号以及来自一个或多个其他计算设备的空中无线信号两者。在一些实施例中，所述装置包括一个或多个处理元件，所述一个或多个处理元件被配置为基于由所述装置经由所述一个或多个天线发送并且由所述接收链电路处理的无线信号来确定一个或多个数字预失真参数，并且基于所述一个或多个数字预失真参数将预失真应用于发送的无线信号。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 处理, 设备, 计算, 电路, 配置",通用
US09489181B2,US14510441,"用于执行相关性分析的系统和方法。存储包括多个程序结构和一个或多个数据对象的程序。每个数据对象由至少两个程序结构共享。对于每个程序结构，分析由于应用于该程序结构的相应的一个或多个优化变换中的每个而导致的对由该程序结构共享的每个数据对象的分解效应。基于该分析确定一个或多个相关结构组。每个组包括共享至少一个数据对象的两个或更多个程序结构，以及关于该两个或更多个程序结构和共享的数据对象兼容的至少一个优化变换。对于至少一个组，该至少一个优化变换可用于变换该两个或更多个程序结构以满足指定的优化目标。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括",通用
US20160320250A1,US14871041,"热电偶(TC)仪器的输入端子。输入端子可以包括印刷电路板(PCB)，该印刷电路板(PCB)包括：输入部分，被配置为从热电偶接收信号；以及输出部分，被配置为通信地连接到仪器。输入端子还可以包括传感器，该传感器被安装在PCB上，被配置为测量在输入端子的冷接点处或附近的温度。 PCB可以包括：第一迹线，将PCB的输入部分连接到PCB的输出部分，并且被配置为将TC信号发送到TC仪器；以及第二迹线，将传感器连接到PCB的输出部分，并且被配置为将温度信号发送到仪器。迹线可以被配置为在不使用金属引脚的情况下将TC信号和温度信号提供到TC仪器。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 电路, 配置","通信, 传感器"
US09477566B2,US14206272,"一种被测系统(SUT)的功率调平。在初始功率水平处将输入信号提供给所述SUT.执行多次迭代，每个迭代包括在指定的测量间隔上测量由所述SUT响应于所述输入信号产生的信号的功率，以及响应于所述输入信号动态调整所述输入信号的功率。在迭代期间增加测量间隔，从而在将信号收敛到指定的功率水平的同时提高迭代期间的测量精度。可以对SUT执行初始功率调平操作以建立指定的功率水平，此后测试SUT，在此期间执行多次功率调平操作，每个操作包括在指定的测量间隔上测量来自SUT的信号的功率，以及响应于输入信号调整，从而在校正热下降的同时在测试期间保持指定的功率水平。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 提供, 测试, 包括",测试
US09477624B2,US14248463,"在实时应用中，一个或多个计算任务根据时间调度并使用来自输入设备的输入数据和/或来自输出设备的输出数据来执行。一个或多个输入设备或输出设备可以是在非调度时间尝试访问外围总线的非调度设备。这样的非调度总线访问可以使得时间调度变得被包括。描述了用于仲裁对总线的访问以更好地将总线访问与应用之后的时间调度集成的各种方法。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"方法, 包括, 计算, 设备",通用
US09413107B2,US14672427,"用于将串行通信插头通信地耦合到串行通信总线的系统和方法。所述系统可包括壳体。所述壳体可包括被配置成通信地耦合到总线的插座。所述插座可包括位于所述插座内的一个或多个内部固位弹簧。所述一个或多个内部固位弹簧可被配置成当阳插头被插入到所述插座中时用固位力抓持所述阳插头。所述壳体可包括或可耦合到夹具，其中所述夹具在所述插座的外部。当所述阳插头被插入到所述插座中时，所述夹具可经由夹具调节机构调节以约束所述一个或多个内部固位弹簧，从而增大所述固位力并进一步将所述阳插头固定在所述插座中。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括, 配置",通信
US09413165B2,US13935049,"输入保护电路可包括接收输入信号的输入节点，并且可进一步包括基于输入信号提供受保护输出信号的输出节点。保护电路可耦合在输入节点和输出节点之间以建立绕过输入节点并在输入节点处发生瞬态的情况下将输出引脚拉到指定的参考电压电平的电流路径。推挽电源可用于将参考电压提供给电流路径，并通过烧断任何过多电压来将其消散在推挽电源电路中包括的半导体器件中。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 电路",通用
US09411920B2,US14107476,"一种用于以高级编程语言指定和实现相对硬件计时的系统和方法。可接收指定程序的用户输入。所述程序被指定用于部署到可编程硬件元件(PHE)，并且包括被配置为在执行期间彼此通信的第一和第二代码部分。所述用户输入可进一步指定所述第一和第二代码部分的相应执行速率的有理比率。自动地生成实现所指定程序的硬件配置程序(HCP)，所述硬件配置程序包括基于所述有理比率自动地确定所述第一和第二代码部分中的至少一个的相应时钟速率。所述HCP可被部署到所述PHE，包括实现用于根据所述有理比率和针对所述至少一个代码部分的自动确定的相应时钟速率来控制所述第一和第二代码部分的执行的第一和第二时钟。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 包括, 方法, 生成, 控制, 系统, 配置","通信, 控制"
US09391818B1,US14856736,"本发明公开了涉及生成用于信道估计和/或均衡的导频序列的技术。在一些实施例中，所生成的导频序列具有平频响应、空部分和低自相关。在一些实施例中，一种用于生成导频序列的方法包括：从恒幅零自相关(CAZAC)序列开始，并且反复执行以下步骤直到结果具有平幅：用零填充序列，确定零填充序列的频率变换(FT)是否具有平幅，调整第二序列(其具有期望的频率响应)的相位以匹配FT的相位，确定经调整的第二序列的逆FT，并且将逆FT的结果用作下一次反复的序列。所公开的技术可以允许高效地产生例如用于蜂窝网络中的导频序列。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"具有, 生成, 方法, 包括",网络
US09383967B2,US14026167,"用于硬件实现的波形数据的累积的系统和方法。提供了一种数字化仪，其包括：电路以及耦合到该电路的第一存储体和第二存储体。该电路可被配置成：将波形的第一子集存储在第一存储体中，以分块方式累积每个波形，其中每个块具有指定大小，由此生成包括波形集合的第一部分累积的第一存储体，与该累积同时地将波形的第二子集存储在第二存储体中，以及以分块方式累积波形的第二子集的每个波形，由此生成包括波形集合的第二部分累积的第二存储体，其中波形集合的第一部分累积和第二部分累积可用于生成波形集合的累积记录。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 实现, 包括, 具有, 方法, 生成, 系统, 电路, 配置",通用
US20160164198A1,US14559497,"一种被配置用于附接到印刷电路板(PCB)的连接器基座。所述连接器基座可以包括第一附接构件，所述第一附接构件被配置用于在所述PCB的至少一个迹线上面的表面安装件附接。所述PCB的所述至少一个迹线可以被配置用于联接到所述连接器基座。所述第一附接构件可以包括连接器插座，所述连接器插座被配置用于接纳多个可能的连接器中的第一连接器。所述连接器基座还可以包括连接器基座引脚，所述连接器基座引脚被配置用于联接到所述第一附接构件内的所述PCB的所述至少一个迹线并且延伸到所述连接器插座中。当所述第一连接器连接到所述连接器插座时，所述连接器基座引脚可以联接到所述第一连接器的连接器引脚。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 电路, 配置",通用
US20160139968A1,US14940370,"自主并行管理(ACM)子系统使得测试仪器(作为服务器操作)能够可靠且高效地处理各种无缝多器件被测(多DUT)场景，并利用来自原始设备制造商(OEM)客户端软件(例如，测试计划、硬件脱落层等)的最小协作。并行能力被直接构建到测试仪器中。使得基于仪器的并行自主手段，OEM软件代码库不需要针对并行性具体实现，潜在地保存OEM软件代码的数千条线。为了支持其中客户端异步地共享仪器的基本并行场景以及诸如广播场景之类的先进并行场景，ACM包括软件锁、客户端分离器、客户端会面和客户端观察器功能。仪器ACM子系统通过将复杂度移动到最低软件层、RF (测试)仪器来从客户端的视角简化问题。
",NATIONAL INSTRUMENTS COPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 包括, 测试, 处理, 设备, 系统",测试
CN104364766B,CN201380019995.X,"定制测试仪器。可以提供多对代码模块。每对代码模块可以包括第一代码模块和第二代码模块，第一代码模块具有用于由测试仪器的处理器执行的程序指令，第二代码模块用于在测试仪器的可编程硬件元件上实现。对于每对代码模块，第一代码模块和第二代码模块可以共同地实现测试仪器中的功能。可以接收指定所述多对代码模块的至少一对中的第二代码模块的修改的用户输入。相应地，可以基于经修改的第二代码模块来生成用于测试仪器的可编程硬件元件的硬件描述。
",美国国家仪器有限公司,其他技术,0.0,未匹配到特定技术领域,"提供, 实现, 包括, 测试, 具有, 生成, 处理",测试
US09326174B1,US14515144,"描述了用于使用多个矢量信号分析器(VSA)来处理信号的设备和相关联的方法的各种实施例。输入信号可被分割并且被提供给多个VSA，所述多个VSA中的每一个可处理所述信号的相应频带，其中所述相应频带具有重叠区域。每个VSA可调整其相应信号的增益和相位，使得穿所述重叠区域保留相位和量值的连续性。增益和相位的校正可通过与复数校准常数的复数相乘来完成。可通过比较由所述VSA中的每一个所测量的利用每个重叠区域生成的一个或多个校准音调的所述增益和相位来确定每个VSA的复数校准常数。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 具有, 方法, 生成, 处理, 设备",通用
US09323699B2,US14149389,"一种系统可以包括执行程序指令(SW)的处理单元、用于采集样本数据和/或生成控制信号的数据采集(DAQ)硬件设备以及被配置为存储与DAQ和处理器操作相关联的数据样本和各种数据的主机存储器。 DAQ设备可以在被发生在DAQ设备中的预定事件(例如，定时事件或中断)触发时将HW状态信息推送到主机存储器，以避免或减少对DAQ设备的SW读取。 DAQ设备可以利用这些事件中的任何事件的状态数据来更新主机存储器中的专用缓冲器。被推送到存储器的状态信息可以以允许检测竞争条件的方式被读取。可以类似地处理由DAQ设备生成的中断。在生成中断时，DAQ设备可以收集处理中断所需的信息，并且将信息连同识别中断的信息推送到系统存储器中。 SW可以读取针对该信息的系统存储器，并且根据需要处理中断，而不必查询DAQ设备。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 生成, 处理, 设备, 控制, 系统, 检测, 配置","检测, 控制"
US09310832B2,US13664139,"用于经由底板来同步时钟的技术和系统。一种装置包括底板、耦合到或包括在底板中的时钟、同步接口以及经由底板耦合到时钟并且耦合到或包括同步接口的至少一个处理元件。该至少一个处理元件可以配置成比较经由底板从时钟接收的第一时间信息与从同步接口接收的第二时间信息。第二时间信息可以与外部时钟相关联。该至少一个处理元件可以基于该比较来确定调整信息，并且经由底板使用该调整信息来将时钟与外部时钟同步。该装置可以是PX底板。时钟输出可以发送到插入底板中的模块，以便例如将它们与外部底板时钟同步。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 处理, 系统, 配置",通用
US09311266B2,US13918308,"可以在PCIe子系统的虚拟拓扑和物理拓扑之间建立映射和对应，并且主机可以被呈现有虚拟拓扑而不是实际的物理拓扑。半透明桥可以将上游主机耦合到包括中间桥和耦合在中间桥下游的相应的PCIe端点的PCIe子系统。中间桥可以从主机隐藏，而相应的PCIe端点可以对于主机是可见的。配置块可以在设置模式期间响应于上游主机预期与相应的PCIe端点相对应的第二存储器分配信息而向上游主机提供与中间交换机相对应的第一存储器分配信息。配置块然后可以在运行时模式期间响应于上游主机预期第二存储器分配信息而向上游主机提供第二存储器分配信息。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 设置, 系统, 配置",通用
US09304810B2,US14011366,"用于经由处理元件中的时间监视电路控制线程执行的系统和方法。响应于接收到的挂起线程指令，可以经由包括在处理元件中的线程挂起/恢复逻辑块挂起线程的执行。可以向包括在处理元件中的时间监视电路(TMC)接收唤醒时间的指示。可以使用包括在处理元件中的时钟经由TMC监视时间，直到唤醒时间获得。包括在处理元件中的线程挂起/恢复逻辑块可以响应于唤醒时间获得而由TMC调用，由此恢复线程的执行。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 处理, 控制, 系统, 电路",控制
US20160091876A1,US14500781,"用于控制定制模块化测量系统的系统和方法。编辑器可以接收指定一个或多个系统定义的用户输入，每个基于映射消息的命令、参数、变量和/或元数据(""信息"")符合用于独立仪器的控制协议以功能和编程语言的数据，并且相应地生成定义，每个定义可以由客户端应用使用以经由基于消息的信息与包括多个逻辑仪器的定制模块化测量系统对接。定义中的至少一个可以部署在测量系统上。测量系统的运行时引擎可以接收来自应用的基于消息的命令，并且调用可以调用逻辑仪器中的至少一个的操作的对应功能。逻辑仪器可以同时操作，包括由逻辑仪器中的至少两个共享单个物理测量设备的使用。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 生成, 设备, 控制, 系统",控制
US09286258B2,US13918611,"主机系统可以耦合到PCIe子系统。在PCIe子系统的设置期间，主机系统中的BIOS可以首先被通知：要被耦合的设备不是PCIe设备，并且这些设备需要一定量的存储器。 BIOS因此可以不尝试配置设备，并且可以替代地分配所需的存储器空间。当操作系统引导时，它可能不尝试配置设备，加载定制驱动程序而不是现有的PCI驱动程序来配置总线。一旦被加载，定制驱动程序可以配置设备，然后通知OS在指定的地址处在系统中有PCIe设备，这些PCIe设备可以使OS加载并执行现有的PCIe设备驱动程序操作/使用这些设备。专有的驱动程序也可以被用于处理PCIe驱动程序与OS之间的业务量。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"设置, 处理, 设备, 系统, 配置",通用
US09288157B2,US14054566,"用于使用时间敏感(TS)网络交换机调度数据排出的系统和方法。该TS网络交换机可以包括功能单元、多个端口和多个队列。每个端口可以与TS分组的一组网络地址相关联，并且可以配置有一组排出周期。每个队列可以与TS分组类型和端口相关联。功能单元可以配置成经由第一端口从网络节点异步地接收TS分组，确定用于排出TS分组的第二端口，确定用于排出TS分组的排出周期，确定TS分组当前不能从第二端口排出，将TS分组排队到第一队列中，其中第一队列与第二端口相关联，以及将TS分组从第二端口排出到相应时间窗中。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括, 配置",网络
US20160070499A1,US14523039,"公开了涉及配置互锁存储器系统的技术。在一个实施例中，一种方法包括确定程序的存储器访问请求序列，以及基于存储器访问序列生成指定存储器访问约束的信息，其中该信息可用于避免存储器访问序列的存储器访问危险。在该实施例中，该方法还包括使用该信息来配置第一电路，其中第一电路包括在存储器中或耦合到存储器。在该实施例中，在配置之后，第一电路可操作以执行对与存储器访问序列相对应的存储器的存储器访问请求，同时避免存储器访问危险，而不接收指示存储器访问危险的其它信息。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 生成, 系统, 电路, 配置",通用
US09252470B2,US14029294,"本发明揭示一种混合双工器组合平面传输线和波导。在一个实施例中，双工器包括第一、第二和第三端口。所述双工器还包括第一信号路径和第二信号路径。所述第一信号路径可用于传达较低频率，且可使用平面传输线来实施。所述第二信号路径可用于传达较高频率，且可至少部分使用波导来实施。所述第一信号路径可耦合在所述第一端口与所述第二端口之间，而所述第二信号路径可耦合在所述第一端口与所述第三端口之间。在一个实施例中，所述第一信号路径可实施低通滤波器，而所述第二信号路径可实施高通滤波器。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,包括,通用
US09252734B2,US13772874,"在一个实施例中，高频模块可包括基板。该基板可包括第一表面和与第一表面基本上相对的第二表面。高频模块可包括耦合到第二表面的部件。可使用基板向部件提供直流电流。高频模块可包括耦合到基板的第二表面的芯。在一些实施例中，芯可包括延伸通过芯的至少一个开口。部件可被定位在开口中的至少一个开口中。在一些实施例中，高频模块可包括耦合到芯的盖。部件可被定位在基板和盖之间的开口中的至少一个开口中。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括",通用
US09250272B2,US14179300,"一种电流测量连接器可以包括第一部件和第二部件。每个部件可以包括底座和接头。第一和第二部件可以通过置于第一和第二部件之间的电流互感器经由相应的接头来连接。相应的底座可以被配置为从电流源接收电流并且经由第一和第二部件使所接收的电流通过电流互感器，从而在电流互感器中感应出电流。所感应出的电流可以用于测量来自电流源的电流。用于制造电流测量连接器的方法可以包括压铸第一和第二部件并且通过电流互感器将第一和第二部件压配合在相应的接头处。使用的方法可以包括经得起故障电流脉冲并且经由第一和第二部件耗散与脉冲相关联的热量。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"方法, 包括, 配置",通用
US09250894B2,US14019963,"用于在指定的计算模型下验证程序的系统和方法。计算模型可以与同步状态图计算模型相关。可以接收使用逻辑节拍内的变量指定多个操作的程序，使得变量在逻辑节拍内具有多个值。可以根据指定的计算模型静态地分析程序，该指定的计算模型基于逻辑节拍指定程序执行，该程序可以包括确定程序在程序执行期间具有为每个逻辑节拍指定确定性结果的确定性语义，包括指定在逻辑节拍内执行的多个操作的确定性结果。响应于该确定，可以根据指定的计算模型验证程序。这些技术可以允许比常规模型验证更大的程序集合，同时维持确定性结果。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 模型, 具有, 方法, 系统, 计算",验证
US09244874B2,US13918685,"一种选择性透明桥接器促进PCI设备将其本身作为PCI到PCI桥接器呈现给主机但选择性地将硬件与主机总线隐藏和隔离。 PCI配置可以通过标准PCI快速配置机制来实现，但不是直接配置设备。选择性透明桥接器中的配置处理器可以拦截来自主机的配置分组，并创建虚拟配置以改变总线拓扑如何出现于主机。设备被配置处理器选择性地隐藏和管理，从而导致简化的复杂性和总线深度。因为选择性透明桥接器对主机而言出现为透明桥接器，所以不需要特殊驱动程序或资源预置负载，尽管选择性透明桥接器完全支持特殊驱动程序和/或资源预置负载。位于桥接器的下游/连接的设备因此可以与未修改的驱动程序一起工作。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 处理, 设备, 配置",通用
US20150381139A1,US14318401,"用于校准阶跃衰减器的系统和方法。可以接收阶跃衰减器的N个衰减测量，其中阶跃衰减器包括M个串联连接的衰减段。每个衰减段可被配置为可切换地提供相应的衰减水平，其中N大于M，并且其中可以经由M+1系数对阶跃衰减器进行建模，所述M+1系数包括针对无衰减状态的系数和针对衰减段的相应系数。可以使用N个衰减测量经由最小平方估计来确定系数的值，从而校准阶跃衰减器。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 方法, 系统, 配置",通用
US09201849B2,US13865357,"一种用于计算QR矩阵分解和反矩阵R <Sup>−1</Sup>的系统和方法。电路配置为使用修改的克里德(MGS)处理来实现将矩阵A分解为两个矩阵Q和R.所述电路包括专用于计算矩阵Q的指定部分。使用MGS处理经由指定部分基于第一输入来计算矩阵Q，其中第一输入包括矩阵A和可能的缩放因子σ。单位矩阵可以按缩放因子σ来缩放，由此生成缩放的单位矩阵σI.缩放的矩阵σR <Sup>−1 </Sup> (或未缩放的R <Sup>−1</Sup>)可以使用MGS处理基于提供给该部分的第二输入经由指定部分来计算，其中第二输入包括(可能缩放的)单位矩阵。如果缩放，则缩放的矩阵σR <Sup>−1 </Sup>可以未缩放，由此计算矩阵R <Sup>−1</Sup> 。存储和/或输出矩阵R <Sup>−1 </Sup> 。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"提供, 实现, 包括, 方法, 生成, 处理, 系统, 计算, 电路, 配置",通用
US09201633B2,US13958138,"用于生成web服务的方法和存储器介质。可提供多个图形数据流程序，并且可接收选择多个图形数据流程序中的一个或多个图形数据流程序以包括在web服务中的用户输入，可基于一个或多个图形数据流程序来生成web服务。每个图形数据流程序可实现相应的web方法，其中每个web方法可实现或请求相应的动作。 Web服务可部署到服务器以用于托管，其中web服务通过网络来调用以执行相应的一个或多个web方法。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 实现, 包括, 生成, 方法",网络
US20150339127A1,US14286149,"用于配置测量设备的系统和方法。测量设备的初始配置可以存储在存储介质上。响应于测量设备的配置的改变，指示配置中的改变的信息可以存储在存储介质上，其中配置中的改变导致修改的配置。存储指示配置中的改变的信息可以针对相应的改变重复一次或多次。存储的初始配置和指示配置中的改变的存储的信息可以包括测量设备的配置改变的历史，其可以用于执行以下中的一个或多个：生成关于测量设备的报告、显示测量设备的配置中的改变的历史、或者将测量设备恢复到先前的配置。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 生成, 设备, 系统, 配置",通用
US09189215B1,US14468935,"用于汇聚分析的系统和方法。第一程序的一个或多个状态变量可以基于第一程序中的变量的依赖性来确定。基于状态变量及其依赖性来创建与第一程序相对应的第二程序，并且将其执行多次。每个执行可以包括记录状态变量的值，确定执行计数，将该值与来自第二程序的先前执行的对应值进行比较，以及响应于该值与来自第二程序的至少一个先前执行的对应值相匹配而终止该执行。基于执行计数来确定第一程序的汇聚属性，并且指示生成一个或多个变量的所有可能值所需的第一程序的执行的数目。汇聚属性被存储，并且可以用于优化第一程序。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 生成, 方法, 包括",通用
US20150301085A1,US14258368,"从数据采集数据流捕获感兴趣数据集。可以从测量设备接收采集数据集。所述采集数据集可以包括来自由所述测量设备采集的一个或多个物理现象的测量的测量数据，并且可以是由所述测量设备采集的数据集的序列中的当前数据集。缓冲所述采集数据集，从而产生缓冲数据集。可以基于所述缓冲数据集或一个或多个先前采集的数据集，自动确定指定感兴趣数据集的一个或多个阈值。可以相对于所述一个或多个阈值，自动分析所述缓冲数据集，并且可以基于所述自动分析，做出关于所述缓冲数据集是否是感兴趣数据集的确定。响应于确定所述缓冲数据集是感兴趣数据集，可以将所述缓冲数据集存储在存储介质中。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 设备",通用
US20150304075A1,US14255046,"一种用于确定由受测装置(DUT)传输的信号的误差矢量幅度EVM <Sub>TD </Sub>的机制。接收器(通常是RF信号分析器)响应于所述信号传输而产生基带信号。从存储器访问(从所述基带信号导出) OFDM输入信号。所述OFDM输入信号包括时域OFDM输入符号序列。从所述存储器访问参考信号。所述参考信号包括时域OFDM参考符号序列。基于时域差信号，即所述时域OFDM输入符号序列与所述时域OFDM参考符号序列之间的时域差，来计算时域中的EVM <Sub>TD </Sub> 。确定所述误差矢量幅度EVM <Sub>TD </Sub>，而不会将所述时域OFDM输入符号序列转换到频域。误差矢量幅度EVM <Sub>TD </Sub>通过标量倍数与标准定义的复合EVM相关。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 计算, 装置",通用
US09158826B2,US13958871,"本发明公开了用于以指定约束渲染数据的系统和方法。可以接收对来自数据集的数据的请求。所述数据集可以包括带时间戳的历史数据，所述带时间戳的历史数据包括多个精简数据集，每个精简数据集具有相应的分辨率。所述请求可以指定时间帧。可以基于所指定的时间帧来确定精简数据集中的第一精简数据集。可以检索来自第一精简数据集的与所指定的时间帧相对应的第一数据，并且第一数据可用于显示在显示设备上。可以从接收到的原始数据生成数据集，其中，原始数据包括第一分辨率处的带时间戳的历史数据。可以经由多个级精简原始数据，从而以其相应的分辨率生成精简数据集。以视觉上令人愉悦且技术上精确的方式生成并表示精简数据。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 具有, 方法, 生成, 设备, 系统",通用
US20150286239A1,US14245693,"单半导体基结可用于创建电压参考，并通过在不同电流驱动电平之间时分多路复用所述电压参考来温度补偿所述电压参考。也就是说，通过所述单结驱动的电流的值可以递归方式重复地变化。在所述结为齐纳二极管的情况下，所述电流可以在正向与反向之间重复地切换。只要响应于所述结上的不同电流而产生的不同电压的温度系数(以ppm/0C计)不同，所述不同电压值的加权就产生零温度系数电压参考值。为了实施带隙参考，单二极管连接双极结晶体管可以交替地使用第一电流和至少第二电流来正向偏置。所述(至少)两个所得Vbe (基极-发射极电压)下降的加权可以产生零温度系数带隙电压。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,未明确,通用
US09134895B2,US13679725,"描述了一种用于将数据流布线连接到图形程序中的节点的输入/输出端子的触摸手势布线方法。该方法可以由在包括被配置为接收用户输入作为触摸手势的触敏屏幕的移动设备上执行的图形编程应用来实现。该方法可以通过显示使得用户更容易(相对于图形程序的默认视图)看到输入/输出端子和/或更容易选择输入/输出端子中的所需一个输入/输出端子的输入/输出端子的放大视图来辅助用户。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 包括, 方法, 设备, 配置",通用
US09137044B2,US14054227,"用于在时间敏感(TS)网络和非时间敏感(NTS)网络之间互操作的系统和方法。该系统可包括TS网络交换机和TS网络接口控制器(NIC) 。每个可具有功能单元。 TS交换机的第一端口可耦合到NTS网络的NTS节点，并且其功能单元可被配置成管理将从NTS网络接收的分组与NTS网络关联的标签的插入和移除。可经由第二端口将加标签的分组转发到TS NIC. TS NIC的功能单元可被配置成将从TS网络交换机接收的加标签的分组排队，并将经由TS网络交换机去往NTS网络的分组排队和加标签。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 具有, 方法, 控制, 系统, 配置","网络, 控制"
US09135143B2,US13646905,"当将高级图形代码(例如，LabVIEW ™代码)编译为不同表示(例如，不同的软件代码或硬件FPGA)时，与设计的特性相关的信息可以从编译过程收集/捕获，并自动地提供给编译过程的所有较早阶段以获得更优化的结果。在没有该信息的自动反馈的情况下，用户必须手动识别、产生和提供反馈信息，或事先推论该过程，当工具不能是这种情况时，必须假设工具已经产生了最好的可能结果。为了校正定时，可以解析出故障的约束路径并将其与先前编译期间获得的延迟进行比较，并且可以撤消先前的可以撤消可以产生期望结果的调整。然后可以识别和调整来自撤消的路径的可以撤消结果的最长延迟，并且可以重复该过程直到预测所有路径都通过。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 工具",通用
US09135062B2,US13859030,"一种用于调度时间关键任务的方法和系统。该系统可以包括处理单元、硬件辅助调度器以及耦合到处理单元和硬件辅助调度器的存储器。该方法可以包括接收用于执行时间关键任务的定时信息，时间关键任务经由处理单元的核上的线程执行程序指令，以及基于接收的定时信息调度时间关键任务。该方法还可以包括对延迟定时器编程，等待唤醒时间以获得调度并向处理单元通知调度。另外，该方法可以包括根据调度在处理单元的核上执行时间关键任务，监控延迟定时器，以及响应于延迟定时器到期断言线程执行中断，从而暂停时间关键任务的执行。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括, 处理",通用
US20150244399A1,US14192732,"用于对频谱进行分区以用于多个功能电路的系统和方法，包括步进衰减、相位调制和增益放大功能电路。系统可以包括可编程步进衰减器，该步进衰减器包括选择电路和功能电路。第一选择电路可以包括多个输出并且可以接收信号并且基于信号的频率选择性地将信号提供给输出。第一电路可以耦合到输出中的一个并且可以作为第一步进衰减器用于频谱的第一部分中的信号。第二电路可以耦合到另一个输出并且作为第二步进衰减器用于频谱的第二部分中的信号。第二选择电路可以耦合到第一和第二电路并且可以提供来自频谱的第一或第二部分的步进衰减信号。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 方法, 系统, 电路",通用
US20150242193A1,US14704689,"当编译表示设计的高级图形代码(例如LabVIEW ™代码)时，可以在编译过程期间执行代码的不依赖于外部输入数据的部分。可以记录程序中的特定变量的特定变量和/或值踪迹，例如常数值和/或重复模式，然后分析程序中的特定变量的特定变量和/或值踪迹，并且可以根据分析的结果在编译过程中应用某些变换，从而优化设计。在一种方法中，可以每次通过一个节点动态步进图形，并且可以确定到步进节点的所有输入是否都已知。如果那些输入已知，则可以动态执行类型转换和与步进节点对应的操作。在另一方法中，可以编译并执行不依赖于外部数据的图形代码的子集，从而获得如上所述的相同结果。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,方法,通用
US09083594B2,US14081712,"一种用于执行盲均衡的系统、方法和存储介质。接收基带样本的块{ u <Sub>n</Sub> } 。使向量f的函数J最小化以确定最小化器f <Sub>MIN</Sub> 。该函数J取决于根据J (f) =Σ(|y <Sub>n</Sub> | <Sup>2</Sup> - γ) <Sup>2</Sup>的向量f.求和Σ对应于均衡样本的序列{ y <Sub>n</Sub> } 。均衡样本的序列{ y <Sub>n</Sub> }根据卷积关系{ y <Sub>n</Sub> } = { u <Sub>n</Sub> } * f.参数γ与块{ u <Sub>n</Sub> }相关。当前模量值γ被更新成等于序列{ y <Sub>n</Sub> }的四个时刻与序列{ y <Sub>n</Sub> }的二个时刻的比率。对基带样本的一系列接收的块重复最小化和参数更新操作。来自最后的重复的最小化器f <Sub>MIN </Sub>用于确定最终均衡样本。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法",通用
US09065609B2,US13942065,"一种用于对校正I/Q损伤的参数进行盲估计的机制。从接收机接收复基带信号的复样本。计算复样本的I分量与Q分量之间的互相关。为复样本的I分量计算均方值；以及为复样本的Q分量计算均方值。交叉信道增益估计：基于互相关值以及均方值中的一个或两个来计算；并且用于向复样本应用交叉信道增益校正。 I/Q增益不平衡的估计基于均方值来计算。增益不平衡估计可用于校正存在于复样本中的I/Q增益不平衡。参数可以被提供给接收机，使得接收机能够应用在线校正。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"提供, 计算",通用
US20150171793A1,US14133317,"一种混合放大器包括与开关放大器串联耦合的线性放大器。所述线性放大器可根据输入信号生成中间放大信号。所述开关放大器可根据所述中间放大信号生成输出信号，所述输出信号具有相对于在参考节点处提供的参考电压的幅度。所述线性放大器可响应于所述输出信号的瞬态变化驱动所述参考节点来调整所述参考电压。耦合到所述线性放大器和所述开关放大器的高通滤波器可使得所述开关放大器能够从实际接地提供大部分稳态电流，其可驱动负载。所述线性放大器和开关放大器可例如从具有分别向所述线性放大器和所述开关放大器提供功率的初级绕组和两个电隔离的次级绕组的电源被独立供电。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"具有, 提供, 生成, 包括",通用
US20150168529A1,US14105999,"用于源测量单元(SMU)的校准和操作的系统和方法。该系统可以包括功能单元和耦合到该功能单元的输出端子。可以通过SMU将激励信号施加到电容器。可以将电容器包括在校准电路中。该方法可以包括获得电流校准系数(CCC)或电压校准系数(VCC)中的一个或多个。 CCC可以对应于电流范围设置，并且VCC可以对应于电压范围设置。 CCC可以响应于激励信号从电容器中形成的第一电流的值和第二电流的值获得。可以响应于激励信号从电容器两端形成的第一电压的值和第二电压的值获得VCC.
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 设置, 系统, 电路",通用
US20150172943A1,US14630923,"本发明公开了用于测试过程的方法和系统。该方法可包括在一个或多个待测单元(UT)上执行测试。可在一个或多个UT上执行至少一个测试。可从UUT获取信号。可检索参考信号。该参考信号可从UUT的传输信号特性中导出。可相对于参考信号分析该信号。可存储因在一个或多个UT上执行至少一个测试而能用于表征一个或多个UT的结果。该参考信号可从初始测试导出并且可被存储以供后续检索。针对相应测试，可针对一个或多个UT中的所有UT检索相应的参考信号。该信号可以是射频信号。 UUT可以是无线移动设备。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 测试, 方法, 设备, 系统",测试
US20150168463A1,US14133167,"用于印刷电路板(PCB)的使用和制造的系统和方法。 PCB可以包括节点和多行通孔，多行通孔可以被配置为建立远离节点的多个电流路径。节点可以是敏感节点，并且多个电流路径可以响应于施加到节点的信号而减少节点处的泄漏电流。多行通孔中的每一行通孔可以相对于PCB的水平平面中的相邻多行通孔偏移。 PCB可以具有多个层，并且节点可以在外部表面层或内部层上。通孔可以是微通孔、埋入通孔或通孔。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 具有, 方法, 系统, 电路, 配置",通用
US20150161068A1,US14097924,"本发明提供智能桥和使用。所述智能桥包括功能单元、存储器和用于使用路由表在主机与多个装置之间路由数据的交换机。所述桥存储转发地址范围FAR作为所述装置所需的硬件存储器资源的桥表示。所述FAR为第一指定最小大小的整数倍且与所述第一指定最小大小对准。基于所述FAR的起始地址将所述桥表示转换为包括多个虚拟存储器资源的端点表示。每个虚拟存储器资源具有相应子地址范围，所述相应子地址范围具有小于所述第一指定最小大小的第二指定最小大小的2倍的功率大小，且相应地对准。所述端点表示可由所述交换机或所述主机使用以将所述虚拟存储器资源分配给所述装置。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"具有, 提供, 包括, 装置",通用
US09048775B2,US13934811,"发动机系统可包括指定数量的单独喷射器，以及具有指定数量的联接至单独喷射器的引脚的发动机控制单元(ECU) 。 ECU可包括能够在不同的多路复用配置之间动态切换的控制器，其中每个多路复用配置联接跨对应引脚的单独喷射器。每个引脚可内部地联接至H桥结构的一半，其中喷射器联接跨两个引脚，从而完成完全H桥结构，提供灵活性以实现组合的螺线管和压电喷射控制。具体地，每个引脚可内部地联接至低侧开关和一组高侧开关，并且开关可根据动态选择的多路复用配置和喷射控制的类型来操作以根据所使用的喷射器类型执行单极螺线管、单极压电、双极螺线管和双极压电喷射控制中的任一个或多个。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"提供, 实现, 包括, 具有, 控制, 系统, 配置",控制
US20150142723A1,US14568829,"用于计算噪声信号的功率谱密度估计的系统/方法。在噪声信号出现在两个信道(单信道)中的情况下，使用来自两个信道(单信道)的n个连续数据获取来计算n个相应的交叉(功率)谱密度，然后对其进行平均。然后可以在谱域中平滑平均的交叉(功率)谱密度。平滑后的交叉(功率)谱密度的幅度包括噪声功率谱密度的估计。可以基于数量n、应用于获取的样本集的时域窗口、连续样本集之间的重叠量以及频域平滑函数的形状来计算有效数量的独立平均值。可以基于有效数量的平均值以及平均的单信道和交叉信道谱估计来确定功率谱密度估计的统计误差绑定(或不确定度测量) 。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括, 计算",通用
US20150103848A1,US14054443,"用于使用时敏(TS)网络接口控制器(NIC)来同步跨网络的时钟的系统和方法。所述TS NIC可以包括功能单元、端口、时钟、多个输入/输出队列对和时间戳单元(TSU) 。所述功能单元可以被配置为生成能够由相应的NTSC网络的NTSC网络计时器使用的同步分组以将NTSC网络与主时钟同步，包括使用TSU根据与主时钟同步的时钟来生成同步分组的时间戳，以及使用对应的输入/输出队列对经由端口与相应的NTSC网络通信，包括将同步分组发送到相应的NTSC网络的NTSC网络计时器。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 生成, 控制, 系统, 配置","通信, 网络, 控制"
US08995513B1,US14076865,"用于输出可用于触发被测设备(DUT)的泄漏的射频(RF)信号的设备和方法。该设备可以包括矢量信号分析器(VSA)，该矢量信号分析器还可执行用于触发DUT的方法。 VSA可以包括：第一组件，该第一组件被配置为生成RF信号；输入，该输入被配置为接收从DUT发送的RF信号；以及接收的RF信号调节部分，每个RF信号调节部分耦合至内部切换部分。 VSA可以被配置为：经由第一组件生成RF信号；将RF信号从第一组件泄漏到内部切换部分；生成泄漏的RF信号；将泄漏的RF信号路由至输入；绕过接收的RF信号调节部分；以及输出可用于经由输入触发DUT的泄漏的RF信号。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"包括, 生成, 方法, 设备, 配置",通用
US08942300B1,US13955523,"数字化仪系统(DS)可以包括用于接收样本数据的一个或多个输入通道以及用于根据感兴趣事件将样本数据组织成一个或多个采集记录并且生成与该一个或多个采集记录相对应的成帧信息的采集状态机(ASM) 。感兴趣事件可以由DS中的触发电路来标识并且被中继到ASM以组织样本数据。 DS还可以包括数据接口，该数据接口能够接收该一个或多个采集记录和成帧信息、将该一个或多个采集记录和成帧信息编码成编码数据、以及将编码数据传送到扩展模块。扩展模块可以接收编码数据、解码编码数据、以及根据成帧信息和该一个或多个采集记录来从解码数据中恢复样本数据。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"系统, 生成, 包括, 电路",通用
US08924949B2,US14304412,"定制目标系统。目标系统可以包括具有第一可编程硬件元件(PHE)的第一设备和具有第二PHE的第二设备。可以提供同步模块以便在第一和第二PHE上实现。同步模块可以提供用于与其他代码交互的标准接口。用户可以指定针对利用同步模块的第一和第二PHE的用户创建的代码。用户创建的代码可以使用标准接口与同步模块交互。因此，可以针对目标系统的第一和第二PHE生成硬件描述。可以针对不同的互连件使用不同的模块。另外，可以在目标系统的操作期间(例如，动态地)使用多个同步模块。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"提供, 实现, 包括, 具有, 生成, 设备, 系统",通用
US20140372660A1,US13918435,"一种PCIe子系统可以由系统扩展器耦接到主机，所述系统扩展器适于基于分组类型来执行PCIe分组路由。第一TLP (传输层分组)类型路由器可以接收PCIe分组，并且根据分组的类型选择性地将PCIe分组路由通过至少两个交替路径中的相应路径。如果PCIe分组被路由通过第一路径，则第二TLP类型路由器可以通过第一路径接收被路由的分组，并且如果被路由的分组被路由通过第二路径，则第二TLP类型路由器可以通过第二路径接收被路由的分组。沿着第二路径可以在第一TLP类型路由器块和第二TLP类型路由器块之间耦接非透明桥，而第一路径可以是从第一TLP类型路由器块到第二TLP类型路由器块的直通路径。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,系统,通用
US20140358469A1,US14295443,"用于在保留装置驱动器IP和驱动器IP的功能的同时扩展可编程装置功能的系统和方法。可接收指定具有标准驱动器IP的可编程测量装置的定制IP的功能的用户输入。定制IP可相应地生成，并且可部署到可编程测量装置。在操作期间，定制IP可直接与标准驱动器IP通信，并且可提供可编程测量装置的定制功能，同时保留可编程测量装置和标准装置驱动器上的标准驱动器IP的功能。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 装置, 具有, 方法, 生成, 系统",通信
US20140285180A1,US13849761,"一种改进的测量电路，包括电流互感器和工作为负电阻的有源反馈电路，该负电阻匹配电流互感器的绕组电阻的值。反馈电路中的放大器提供功率来驱动次级电流通过感测电阻和互感器绕组电阻，通过向电流互感器提供负阻抗来减少电流互感器电路中最重要的误差源。与互感器绕组的正电阻相结合，负阻抗在电流互感器上产生净零负担，这消除了必须变压器提供功率来驱动次级电流的需要。这有助于使用较小的互感器，同时实现减少的测量误差。因此，单个紧凑的测量设备可以用于具有高测量性能的广泛的应用中。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 实现, 包括, 具有, 设备, 电路",通用
CN102160309B,CN200980136929.4,"测试多个通信设备。可以从多个通信设备中接收多个信号。多个信号可以包括来自所述多个通信设备中的每个的信号，其中，所述多个信号的第一子集具有与所述多个信号的第二子集不同的频率。可以将所接收的信号组合成组合信号。例如，通过将所述组合信号与来自至少一个本地振荡器的输出相混合，可以将所述组合信号下变频为组合信号。所述下变频可以产生多个较低频率信号，每个较低频率信号对应于多个所接收的信号中的一个。可对所述多个较低频率信号中的每个执行测试。
",美国国家仪器有限公司,其他技术,0.0,未匹配到特定技术领域,"具有, 测试, 包括, 设备","通信, 测试"
US20140121846A1,US13935178,"发动机控制系统可用多层或多层软件层次结构或模块化算法(算法模块)来实施以控制喷射事件。在第一层次结构中，可执行程序指令以界定在此期间发生发动机的一系列燃料喷射的时间周期。在第二层次结构中，可执行所述程序指令以在所述所界定的时间周期期间产生控制命令。在第三层次结构中，可执行所述程序指令以使所述控制命令适应于指定喷射器类型。最后，在第四层次结构中，可执行所述程序指令以将所述经适应控制命令映射到经配置以执行所述一系列燃料喷射的物理硬件。所述程序指令的执行可以由一或多个处理元件执行的软件的任何所要组合来执行、以FPGA来实施和/或以硬件来译码。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"算法, 处理, 控制, 系统, 配置",控制
US20140059854A1,US14073084,"公开了一种连接器。该连接器包括导电壳体。导电壳体包括包围用于接收适配器的空间的壁区域。导电壳体还包括环形端件，该环形端件从壁区域的第一端径向地向内延伸并且终止空间。环形端件包括平坦的环形表面、以及安装在平坦的环形表面上的凸起的可变形环。凸起的可变形环具有一定高度，使得适配器插入到空间中使凸起的可变形环变形，以在平坦的环形表面与适配器之间产生物理接触连接。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"具有, 包括",通用
US20140040792A1,US13783859,"一种编辑图形图的系统和方法。在显示设备上显示诸如图形程序之类的图形图。可以接收编辑图形图的用户输入，从而生成编辑的图形图。响应于该编辑，可以基于确定的基于所述编辑向编辑的图形图中的一个或多个元件施加的力，调整图形图中的所述一个或多个元件的布置，从而产生调整后的编辑的图形图。可以在显示设备上显示调整后的编辑的图形图，其可以包括显示动画，该动画图示出元件到平衡状态的移动，在平衡状态中，力平衡并且移动停止。可以根据需要顺序地和/或同时地执行编辑、调整和显示。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 生成, 方法, 设备, 系统",通用
US20140019794A1,US13953412,"用于同步设备的系统和方法。设备读取耦合到主时钟并与主时钟相关联的第一计数器以及耦合到该设备并与该设备相关联的第二计数器，其中该设备是耦合到主时钟并经由交换结构彼此耦合的一个或多个设备中的一个，其中每个设备包括相应的时钟，并且耦合到相应的第二计数器并与相应的第二计数器相关联。第一计数器和第二计数器中的每一个可由一个或多个设备中的每一个访问。该设备确定该设备的相关联的第二计数器和第一计数器之间的差异，并且基于所确定的差异来确定并存储针对该设备的相对于主时钟的时间参考，其中该时间参考可用于对事件进行时间戳或者同步将来的事件。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括, 设备",通用
US20140006867A1,US13930265,"本发明公开了测试执行系统和使用方法。所述系统包括测试执行引擎，所述测试执行引擎被配置为执行至少一个测试执行序列以测试至少一个被测单元(UUT)；过程模型，所述过程模型指定用于所述测试执行序列的测试前或测试后功能性的一个或多个功能序列；以及插件框架，所述插件框架被配置为将一个或多个过程模型插件实例选择性地并入所述过程模型中。每个过程模型插件实例指定用于所述测试执行序列的测试前或测试后功能性的至少一个相应功能序列。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 模型, 测试, 方法, 系统, 配置",测试
US20130283237A1,US13853724,"用于在目标设备上调试状态图的系统和方法。状态图可以被创建或显示在显示器上，并且可以包括通过导线连接的多个状态图标。可以为状态图指定一个或多个调试操作。ST可以在目标设备上执行Atechart。目标设备可以在状态图的执行期间向计算机提供调试信息。调试信息可以显示在计算机上，并且可以用于帮助调试状态图。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 方法, 设备, 系统, 计算",通用
US20130262695A1,US13709197,"系统和方法用于流送数据。主机设备包括一服务器可以获取来自数据源。服务器可以在接收对于从至少一个客户端设备通过网络经由无损传输协议，其中该请求可指定数据的范围与流的至少一个客户端设备。服务器可以流，该数据经由网络向至少一个客户端设备经由无损的传输协议，根据该请求。该至少一个客户端设备可以接收和处理的数据。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 处理, 设备, 系统",网络
US20130215986A1,US13852291,"一种用于联合地校正解调信号中的载波相位和载波频率误差的机制。计算机系统可以接收基带输入信号(由QAM解调产生)的样本。该计算机系统可以在2D角度-频率空间中的网格上计算代价函数J的值。通过以下步骤来针对网格中的每个点(θ，ω)计算代价函数值J (θ，ω)：(A)对输入信号应用角度θ的相位调整和频率ω的频率调整；(b)对调整后的信号的样本执行K-means算法的一次或多次迭代；(c)在每个K-means簇上生成和；以及(D)将该和相加。最小化代价函数J的2D角度-频率空间中的点(θ<Sub>e</Sub>，ω<Sub>e</Sub>)用作对载波相位误差和载波频率误差的估计。可以使用估计出的误差来校正输入信号。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"系统, 生成, 计算, 算法",通用
EP2545444A1,EP11708643.9,"用于编辑图形程序的系统和方法。在显示设备上显示图形程序。接收对多点触摸界面的多点触摸输入，其中该多点触摸输入指定该图形程序中的编辑操作。响应于该多点触摸输入在该图形程序中执行该编辑操作，并且在该显示设备上显示所编辑的图形程序。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 设备",通用
EP2433213A1,EP10723859.4,"经由web浏览器在网络上执行程序相关操作的系统和方法。在网络上在服务器计算机和客户端计算机之间建立网络连接。通过网络从客户端计算机向服务器计算机发送通用资源标识符(URI)，其中URI指示程序，例如图形程序(GP)，或者图形程序交互式开发环境(GPDE)的至少一部分，例如图形程序编辑器、执行引擎、静态或动态分析器和/或编译器。响应于URI，通过网络从服务器计算机接收GPDE的至少一部分，并且在客户端计算机的web浏览器中执行该GPDE的至少一部分以执行关于GP的一些指定功能。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 计算",网络
EP1991938A1,EP07717615.4,"一种用于执行系统的仿真的方法。该系统包括被配置为实现诸如通用求解器的仿真逻辑的FPGA.例如，FPGA设备可以实现通用时间域求解器或通用频率域求解器。 FPGA设备还被配置有表示处于仿真下的系统的系统模型的信息。该系统还包括用于向FPGA设备提供用于仿真的输入信号的输入硬件，以及用于从FPGA设备接收由仿真计算的输出信号的输出硬件。可以重新配置系统以通过改变系统模型来仿真不同的系统，而不需要改变仿真逻辑(例如，通用求解器) 。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"提供, 实现, 包括, 模型, 仿真, 方法, 设备, 系统, 计算, 配置",通用
EP1987403A1,EP07710221.8,"用于自动更新耦合到可编程控制器(PrC)或包括在可编程控制器中的存储器映射的系统和方法。 PrC可以自动检测耦合到PrC的至少一个可编程硬件元件(PHE) 。 PHE可以在一个或多个设备与PrC之间提供可定制接口，即输入和输出(I/O)接口。这些设备可以包括一个或多个测量、数据采集、信号生成、自动化、运动控制和/或分析设备等。 PHE可以对在设备与PrC之间传输的数据执行一个或多个功能。一个或多个PHE和/或设备可以是本地的或远离PrC. PrC可以基于至少一个可编程硬件元件的硬件I/O接口自动地更新存储器映射，其中存储器映射促进在PrC和可编程硬件元件上执行的程序之间的通信。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 方法, 生成, 设备, 控制, 系统, 检测","检测, 通信, 控制"
EP1941358A2,EP06817201.4,"用于在各个设备上执行的图形程序之间进行通信的系统和方法，所述各个设备例如是可编程硬件元件(PHE)和控制器。所述系统包括表示第一先进先出(FIFO)结构的第一节点，以及向所述FIFO结构提供控制器接口的第二节点。所述FIFO的第一部分在所述PHE上实现，并且所述FIFO的第二部分在所述控制器的存储器中实现。所述第一和第二节点可操作以被分别包括在第一和第二图形程序中，其中所述第一图形程序可部署到所述PHE，其中所述第二图形程序可部署到所述控制器，并且其中所述图形程序经由所述FIFO通信以协作地执行指定任务。所述FIFO可以实现直接存储器存取(DMA) FIFO，其中DMA控制器的至少部分在所述PHE上实现或耦合到所述PHE.
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"提供, 实现, 包括, 方法, 设备, 控制, 系统","通信, 控制"
EP1917582A2,EP06813489.9,"一种用于开发执行测量功能的测量程序的系统和方法。响应于第一用户输入在开发环境中创建测量程序，可能包括用一个或多个参数值配置被测物理单元(UUT) 。测量程序包括视觉地指示测量功能并且可执行以接收来自物理的信号(UUT)的图标。接收指定测量程序将接收模拟数据而不是接收来自物理UUT的信号的第二用户输入。作为响应，开发环境被配置为向测量程序提供模拟数据，可能包括根据参数值配置模拟数据。配置不改变测量程序。在配置之后，执行测量程序，其中在执行期间，测量程序接收模拟数据并且对模拟数据执行测量功能。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"提供, 模拟, 包括, 方法, 系统, 配置",通用
EP1872206A2,EP06718413.5,"用于合并图形程序之间的差异的系统和方法。接收关于第一和第二图形程序之间的匹配和差异(例如在硬件、软件、配置和/或连接中)的信息，每个图形程序包括相应的多个互连节点。分析该信息以合并第一和第二图形程序之间的差异，例如针对每个差异：从第二图形程序移除第二子图中的所有非共同节点和到其的连接，将第一子图中的所有共同节点和到其的连接添加到第二图形程序，确定第一子图中将共同节点连接到非共同节点的所有边；以及针对每个所确定的边，将将第二子图中的对应共同节点与从第一子图添加的对应非共同节点连接的边添加到第二图形程序。在显示器上显示合并的图形程序，例如，以图形方式指示合并的匹配和/或差异。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括, 配置",通用
EP1849068A2,EP06718412.7,"用于确定配置图与实际系统之间的差异和/或匹配的系统和方法。接收关于包括第一多个节点并以图形表示第一系统的配置图的第一信息，并且接收关于包括多个组件的实际系统的第二信息。节点的至少一部分可以对应于第一系统的硬件设备、程序和/或配置数据，并且可以互连。例如通过遍历配置图或表示该图的数据结构和/或遍历实际系统或表示该实际系统的数据结构，分析第一和第二信息以确定配置图与实际系统之间例如硬件、软件、配置和/或连接性之间的差异和/或匹配。例如经由合并的配置图在显示设备上以文本或以图形显示差异和/或匹配的指示。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 设备, 系统, 配置",通用
EP1745370A2,EP05750094.4,"用于创建使用多个计算模型(MoC)的图形程序的系统和方法。响应于第一输入在图形程序中组装第一多个图形程序元素，其中所组装的第一多个图形程序元素具有第一MoC.在图形程序中显示结构，该结构指示对包括在该结构内部的图形程序元素使用第二MoC.响应于第二输入在该结构内组装第二多个图形程序元素，其中所组装的第二多个图形程序元素具有第二MoC.图形程序可执行以执行功能，例如通过根据第一计算模型执行所组装的第一多个图形程序元素，以及根据第二计算模型执行所组装的第二多个图形程序元素。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"包括, 模型, 具有, 方法, 系统, 计算",通用
EP1664951A1,EP04809648.1,"一种运动控制系统和用于配置运动控制器驱动器的方法。该系统可以包括通过数字通信链路耦合到运动控制器驱动器的运动控制器。运动控制器驱动器包括可重配置部分，其可配置有例如用于与不同运动控制器通信的不同通信协议。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 控制, 系统, 配置","通信, 控制"
EP1660994A2,EP04757092.4,"一种用于指定图形程序中节点之间的定时关系的系统和方法。可接收指定第一节点相对于第二节点的定时的期望定时的用户输入。在各种实施例中，可指定第一节点和第二节点之间的任何种类的定时关系或定时约束。可在显示器上显示定时信息以可视地指示第一节点相对于第二节点的定时的定时。在一个实施例中，显示定时信息可包括显示第一节点和第二节点之间的定时线。图形程序可以满足可视地指示的第一节点相对于第二节点的定时的定时的方式来执行。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括",通用
EP1639457A2,EP04755293.0,"用于配置图形程序元件(GPE)和图形用户接口元件(GUIE)之间的通信的系统和方法，其中GPE和GUIE来自不同的相应的图形程序开发环境。响应于接收到指定GPE和GUIE的用户输入以及其间的数据源/数据目标关联，GUIE和GPE中的至少一个被编程地修改以配置GPE和GUIE之间的通信。然后图形程序被执行，其中GPE执行功能以生成数据，向GUIE提供数据，并且GUIE显示接收到的数据；或者GUIE元件从用户接收数据，向GPE提供数据，并且GPE接收数据并基于数据执行功能。可以通过拖放用户接口技术、线绘制、命令行、菜单、对话框或向导等执行用户输入。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 生成, 系统, 配置",通信
EP1535138A1,EP03739289.1,"用于访问可操作地执行第一功能的图形程序(GP)中的预定义对象的属性的系统和方法。节点图标在GP中显示并响应于用户输入而耦合到预定义对象，并且与可执行以仅提供对与对象对应的多个属性的访问并且指定对象的配置的程序指令相关联，其中，对象与第一功能的子集相关联。显示对象的可用属性，并且用户输入指示接收到的多个属性。 GP被执行，包括执行属性节点以：接收指定属性的修改的输入，并且修改属性以配置对象执行第一功能的子集；和/或从预定义对象读取属性，并且向GP的图形程序元素提供属性，例如，用于显示。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 方法, 系统, 配置",通用
EP1530756A2,EP03763354.2,"用于以无线方式部署或执行图形程序到设备的系统和方法。创建实现测量功能的图形程序(GP) 。部分或全部GP通过网络被发送到集线器。集线器执行所发送的GP并且根据无线通信协议经由无线装置将对应命令发送到测量设备。测量设备执行命令以执行测量功能，由此生成被无线地发送回到集线器或计算机系统的结果数据。 GP可以包括在测量设备上执行的框图、以及由第一计算机系统显示的用户接口部分。将GP发送到集线器可以包括基于GP生成机器可执行程序并且将机器可执行程序发送到集线器以供执行。可替换地，在主机计算机上存储多个执行系统部件。程序以编程方式被分析以确定执行程序所需的部件的子集，并且子集和程序组合成文件，保留程序的执行顺序。文件被发送到设备并且用于构建包括用于程序和部件的子集的可执行代码的组合程序。设备包括执行部件的子集以执行程序的最小执行引擎。文件可以被流传送到设备以供流传送执行，其中存储执行程序的所接收部分所需的部件的子集的所接收部分，直到不再需要。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"实现, 包括, 装置, 方法, 生成, 设备, 系统, 计算","通信, 网络"
US06823283B2,US10194952,"用于测量的系统与方法，DAQ ，对照操作。一测量模块包括用于执行信号调节和/或信号变换的测量电路，提供用于测量电路的一个接口的接口电路元件。一自行式修井机连接到组件的接口电路元件。一计算机系统连接到自行式修井机并存储一个或多个硬件配置程序。接口电路元件传送描述接口的一接口协议，例如，对自行式修井机或计算机系统。计算机系统提供一硬件配置程序响应于传送的接口协议，在具有硬件配置程序的自行式修井机上编程一个或多个可编程硬件元件。在被配置之后，可编程硬件元件与根据连通的接口协议的测量模块接合。测量模块和程序的自行式修井机可以作为一DAQ 设备一起执行，测量设备，和/或控制设备。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 具有, 方法, 设备, 控制, 系统, 计算, 电路, 配置",控制
US06820032B2,US09877314,"一种系统和方法，用于扫描物体在一个区域内，采用共形扫描方案。该系统可以包括计算机，包括一CPU和存储器用于存储一个或多个程序的哪个可由CPU执行该方法。该方法可以：1 )确定特征几何形状的区域；2 )生成形曲线扫描基于特征几何形状的区域，通过执行共形映象的特性之间的几何和一第一扫描曲线以产生形曲线扫描(即，第一扫描曲线图上定位点对特征几何形状的区域；以及3 )使用该形曲线扫描扫描区域。这些测量区域出示数据指示一个或多个特性的物体。该方法还产生指示一个或多个特性的物体。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 生成, 方法, 系统, 计算",通用
US06807232B2,US09746861,"一种系统和方法，用于复用同步数字数据并行流具有不同时钟频率转换为指令串-单数据串方式stream's定时，同时保持各数据的完整性。多个数字数据对应的输入和时钟输入耦合到相应的FIFOs (第一先入先出缓冲器)，其耦合到数据多路转接器( MUX ) 。每个时钟输入耦合到时钟MUX哪个耦合到每个FIFO与数据MUX 。最终，转换状态机连接到时钟MUX，数据MUX，与FIFOs 。每个输入的数字数据从源接收数据流，诸如数字摄像机，而相应的时钟输入端并行地接收相应的时钟信号。过渡状态控制主机选择数据流从MUXed数据流与选择相应的时钟信号从MUXed时钟信号。过渡状态机器可从外部源接收选择信号发送选择信号给数据MUX与时钟MUX，以及FIFOs 。每个时钟输入发送其时钟信号提供给时钟MUX选择时钟信号基于选择信号，并将所选择的信号发送到数据MUX，FIFOs上，过渡态的机器。每个FIFO发送其数字数据流提供给数据MUX其选择了数据流对应于所选择的时钟信号，并输出所选择的流，例如图像采集装置。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 装置, 具有, 方法, 控制, 系统","摄像, 控制"
US06807305B2,US09832912,"一种系统和方法，用于执行模式匹配，以定位一个或多个多个模板图像的实例在目标图像。在预处理相统一的信号变换( UST )来确定从模板图像。该UST转换每个模板图像与广义频率域。该UST施加广义频率到每个模板图像来计算对应的广义频率分量值( GFCVs )分别对模板图像。在运行时间，接收到目标图像，与UST施加在广义频率到目标图像，计算相应的GFCV 。该UST可以应用于像素子集的模板和目标图像。好的匹配确定在GFCV与目标图像GFCVs各个模板图像。最后,信息指示最佳匹配的模板图像从一组模板图像为输出。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 处理, 计算",通用
US06807631B2,US09991770,"一种系统和方法，用于部署硬件配置与计算机程序。该程序可执行仪器，测量/控制，工业自动化，或机器视觉功能，或其它类型的函数利用硬件设备上，所述程序可以取决于各种硬件配置的计算机系统。安装捆相关的配置信息包括多个硬件设备的相互作用与程序可以自动地创建和部署程序在新计算机系统上。安装捆可包括程序指令可操作安装该程序新计算机系统和自动更改硬件配置的新计算机系统以便将执行该程序正确。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 设备, 控制, 系统, 计算, 配置",控制
US06784902B1,US09648927,"一种用于开发图形程序开发系统图形程序，其中该开发系统，利用配置矩阵用于表示该图形编程。该矩阵可以图形化地表示或指定操作的计算机程序。该矩阵可以包括列(或行)在显示设备上显示包括一个或多个多个列，包括符号用于功能元件和一个或多个互连列包括多个互连的两个或更多符号之间用于功能元件。一旦矩阵(计算机)被创建，该计算机程序可以被执行，其中该计算机程序执行互连功能元件根据符号显示在矩阵。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 设备, 系统, 计算, 配置",通用
US06757428B1,US09375453,"彩色表征手段操作来分析每个相应像素的至少一个子集的图像对象。获取所述图像HSI格式中，或者被从另一格式转换到HSI 。对于各个像素，该方法确定颜色种类或仓的相应像素的值基于各个像素。颜色类别是一个多个可能的颜色种类或箱柜的HSI颜色空间。当像素分析并分配了来着色类别，该方法储存信息，即使计算机中关于该数量或百分比或像素中的每一个颜色种类。一种配色法使用颜色表征方法。该配色法确定相似度之间的颜色模板图像和感兴趣对象( ROI ) 。该配色法首先执行上述颜色表征技术分别在模板图像对象感兴趣区域ROI (匹配)，然后生成基于颜色信息的目标颜色信息与模板图像的ROI，其中本次比赛信息指示颜色模板图像的相似性在物体与ROI 。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"生成, 方法, 计算",通用
US20040122557A1,US10434414,"一种系统和方法，用于执行轨迹生成，内插，并且控制用于运动控制的应用，在轨迹生成，内插，并且执行彼此并联。在一个实施方案中，FPGA在运动控制装置可以被配置成执行轨迹生成，内插，以及控制并联连接。执行轨迹生成，插值，控制并联一个FPGA可提高效率的运动控制的应用。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"装置, 生成, 方法, 控制, 系统, 配置",控制
US06754850B2,US09798363,"一种创建可由计算机程序执行的多个线程，其中该方法使用用于执行同步的技术在此称为一批Synchronization部分。根据该技术，多个线程可以彼此相关联，作为&ldquo；batch&rdquo；中的线程。每个线程在多个(批量)的线程可以执行计算机程序同时发生。批量Synchronization部分可指定一计算机程序在执行时，该部分由多个线程同步。在一个实施例中，不同类型的批量Synchronization部分可以被指定，其中每种批Synchronization部分执行不同的类型的执行同步。在一个实施例中，该方法可以使得执行同步行为并行执行的多个测试执行程序测试顺序将被指定。测试序列可以包括一个或多个批量Synchronization部分。多个线程可以各自执行测试序列的实例，以同时测试一组在测单元，配料Synchronization部分可以协调执行多个线程的必要地方。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"方法, 包括, 计算, 测试",测试
US06745175B2,US09920982,"一种改进的方法和系统，用于高速测井和潮流的数据。记录的数据可以包括:接收数据；并存储数据，和一个索引数据，在共享存储器位置连接到数据库。潮流的数据可以包括:检索所请求的数据的共享存储器位置使用该回转性指数被请求的数据，并显示检索的数据。用户可配置频率可以用于各种行为数据上。数据可以是测量数据，包括数据流包括多个条目。记录和趋势可以是：( 1 )基本并发地执行；( 2 )实现使用共享存储器模板。该数据库可以位于第一计算机系统，与测井和趋势可以被执行第一计算机系统上或在第二计算机系统上耦合到第一计算机系统经由网络。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 包括, 方法, 系统, 计算, 配置",网络
US06720968B1,US09210636,"视频捕捉系统及方法，由此视频帧或图像，接收在多个可能的格式，取得并存储到单板存储器的图像格式。图像数据可以被传送到系统存储器在最优速率。视频捕获系统，包括主机，包括视频捕获板，其连接到视频源，例如摄像机。视频源提供数字化视频数据以第一格式的多个不同的可能的格式。该视频捕获板包括存储器控制器，该电路接收第一格式的数字视频数据选择性地提供视频数据至缓冲存储器的图像格式。该存储器控制器包括地址逻辑，用于生成用于存储视频数据的缓存地址给该缓冲存储器的图像格式。地址产生逻辑是可编程的以第一格式，并使用对应的配置信息，该配置信息，产生地址。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 方法, 生成, 控制, 系统, 电路, 配置","摄像, 控制"
US06697766B2,US09832508,"系统和方法用于表征高斯脉冲的信号。该系统包括计算机可操作以接收该信号，确定一组估计的高斯脉冲的参数使用&ldquo；zoom-in&rdquo；方法，为改变已估计参数组产生一个或多个置换参数集合，在估计和置换参数集合，每个代表相应的波形，产生闭形之间的内积，接收信号和每个波形，产生线性方程从上述内积，每个线性方程函数的一个参数集合和对应的参数变量的高斯脉冲，确定参数的值的变量，通过求解线性方程组。所确定的参数来表征高斯脉冲。如果有N个参数，以确定置换生成M，其中M大于或等于N，M&plus；1求解线性方程对由多种因素决定N参数。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 生成, 方法, 系统, 计算",通用
US06690390B1,US09695803,"用于在从一在线帮助信息显示的内部的应用之内执行任务的一计算机系统和方法。从而通过计算机系统的在线帮助信息显示可以包括用户可选元素，其使用户能够从在线帮助窗直接地完成部分或所有任务，例如而不需要用户为在应用之内的菜单或工具栏中的此功能搜索。用户可以发起应用然后选择与在应用之内执行任务相关联的在线帮助信息。响应于此用户输入，计算机可以展示与应用相关联的在线帮助信息。所显示的在线帮助信息可以包括各种帮助信息，其指定用于执行任务，例如信息的一方案通过在应用中执行任务的一系列的步骤可以引导用户。所显示的在线帮助信息可以包括一个或多个用户可选元素。响应于选择一用户可选元素的用户，应用在应用之内可以执行至少一部分任务。从而用户可以执行一部分或所有任务，通过从在线帮助信息窗直接地选择一元素或项目。从而用户不要求手动地搜索为适当的选择，例如一菜单项或工具栏项目，从应用本身的内部。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 计算, 系统, 工具",通用
US06690981B1,US09565610,"系统和方法，用来使用户界面代码到封装在子程序的图形编程。节点参考图形用户界面元素程序可以连接到节点称为&ldquo；属性node&rdquo；用户可以配置节点与该属性信息指定哪些属性或属性组的引用的用户界面元素，以设定或收回。当执行时，资产节点可以利用提供的基准节点以便与该用户界面元素相关联的元件。节点参考用户界面元素还可连接到子程序节点。该子程序可操作以接收该用户界面元素参考通过引用到性质节点为了设定或检索特性的用户界面元素。该子程序可具有相关的接口面板包括&ldquo；参考control&rdquo；用于接收用户界面元素的参考。打印信息的涉及用户界面元素的参考通过子程序可以被指定。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 具有, 方法, 系统, 配置",通用
US06681057B1,US09510290,"一种系统和方法，用于提高精度和收敛速度，在确定仿射变换的一个图像相对于另一个图像或值阵列或阵列的值。本发明的具体实施例包括参考和输入图像。一种梯度矩阵，&lgr；可以构造包含梯度信息位置值的参考图像。另外，估计矩阵，&rgr；可以构造包含至少一个初始位置、角度和缩放输入图像。该输入图像然后从参考图像逐像素地减去生产误差矩阵，e 。该误差矩阵，e相乘，然后,利用矩阵，&lgr；和&rgr；结果新变化位置、角度和缩放输入图像。新的位置、角度和规模的变化，根据计算的位置、角度和刻度值的输入图像。这些新的值代替先前的值的输入图像。该输入图像然后被变换。然后该经变换的输入图像中减去从参考图像逐像素地形成新的误差矩阵，e 。新矩阵，&rgr；从先前的估计，构成矩阵，&rgr；误差矩阵，e，与梯度矩阵。该过程被重复直到，e小于给定值。当发生之间的对应关系，都输入和参考图像已经被确定。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括, 计算",通用
US06681284B1,US09723552,"一种系统和方法，用于与装置通过外部传输介质。该系统包括主机系统，包括用户应用程序，串行总线接口驱动器，第一部分接口驱动器。主机可包括串行总线东道主控制器。串行总线可以耦合到串行总线东道主控制器串行总线接口，其包括一接口连接器适于连接到该设备，其中,串行总线接口可以用于存储和执行第二部分接口驱动器。当用户生成API函数调用，第一部分的接口驱动器可以生成帧的接口命令那个转移到第二部分接口驱动器，用于执行，从而使要传输的信号经由接口总线到该设备。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 方法, 生成, 设备, 控制, 系统",控制
US06665335B1,US09553200,"一种系统和方法，用于估计之间的偏移两个信号。换档评估体系的方法：(一)接收第一信号，其中第一信号可表示为矢量g具有N个组分；(二)向空间投影向量g与维数K小于N，得到一种投影矢量X具有K组分；(三)计算之间的距离的测量投影向量X的每个矢量中存储的一组向量；(步骤d)中确定存储的矢量p所存储的该组矢量具有最小距离与投影向量X 。所述存储的向量来生成模板信号f，也表示为向量，具有N个成分，通过投影移位版本的模板信号f到空间尺寸K 。该移位版本的模板信号f可以被称为移位模板向量，或简称为移位向量。位移估计方法可对应于提供移位值移位模板向量，产生一个存储的矢量p作为估计之间的转换接收到的信号与模板信号f 。移位值限定了量由模板信号f必须转入获得移位模板向量。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 具有, 方法, 生成, 系统, 计算",通用
US06665066B2,US09844754,"在机器视觉系统中进行照射线分析和方法上的缺陷的图像来检测物体。该方法可以包括在投影线的图案的表面上，然后生成物体图像的物体的表面。该分析方法跟踪每个照射的左边缘和右边缘线的宽度和曲率，以确定每个线，优选使用双向边缘检测技术应用于路径垂直于电流方向的线。信息关于左和右边缘的线可以与确定使用本地宽度和局部取向线条。该信息可以用于对确定减薄或开花线路发生，或者如果线曲率发生变化的，这可指示可能的缺陷的目的。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 生成, 系统, 检测",检测
US06640312B1,US09629349,"传送数据的系统和方法通过通信介质。主机与装置通过串行总线缺少错误处理能力，例如IEEE 1394总线，该主机可控制设备发送请求的访问它的存储器寄存器) 。该主机产生第一请求至该装置访问内存地址位置的装置，其包括地址和状态信息指示预先请求至内存地址位置返回成功。该装置检查消息以确定其是否重试的前一个，如果是，则确定先前的请求成功完成到内存地址位置通过比较地址和数据传送字节大小的第一请求与那些事先请求。如果相同，则预先请求成功完成到内存地址位置，并请求被忽略。否则,该设备重试事先请求。如果第一请求不是重新审理设备执行它，并返回确认向主机指示成功完成。如果主机未收到有效应答，它重试第一请求，否则完成事务相关联的第一请求。于是，新的事务请求可被接收，这样导致新的请求产生由主机访问内存地址位置。主机可操控所述状态信息的新请求，以指示第一请求给内存地址位置返回成功。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 方法, 处理, 设备, 控制, 系统","通信, 控制"
US20030194135A1,US10430546,"估计系统和方法旋转偏移在第一曲线和第二曲线离散不连续的，其中第二离散曲线旋转地移位版本的第一离散曲线。第一和第二离散的曲线被接收。旋转偏移在第一离散曲线与第二离散曲线是基于估计第一离散曲线与第二离散曲线。累积旋转偏移被基于估算的旋转偏移。旋转移位的版本的第二离散曲线是基于所累积的旋转偏移。估计，更新，并以迭代方式执行使用相应的转动移位离散曲线每次迭代，直到停止状况发生，从而确定结算的旋转之间的第一离散曲线与第二离散曲线。该预算可以用于向进行曲线匹配。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法",通用
US06633937B2,US09746734,"一种系统和方法，用于GPIB自动轮询中的GPIB设备。一台计算机与一个或多个GPIB仪器通过GPIB能够分析、测量或控制工艺或被测单元。该计算机包括一GPIB控制器的活动进行监视，GPIB总线上的装置，并生产了启发式信息关于过去行为GPIB设备，诸如一长列一个或多个GPIB设备如何IDs排序依据最近每个设备被访问。该启发信息产生存储在存储器和/或硬盘驱动器的计算机。服务请求( SRQ )线坚持已见触发由一个或多个GPIB设备可以被接收。该GPIB控制器执行自动轮询的GPIB设备作为排序队列，以确定哪个设备声明该SRQ 。如果没有断言排队装置，剩余部分的装置轮询以任意顺序。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 方法, 设备, 控制, 系统, 计算",控制
US06615158B2,US09891566,"一种系统和方法，用于分析表面。该系统包括计算机，该计算机包括一CPU和存储器存储程序，可操作的可由CPU执行该方法。该方法可包括:1 )接收描述N维定义的表面n维空间中的有界，在表面嵌入m维实空间通过嵌入功能的x ( )，并且在m>n；2 )确定微分同胚f n维空间；3 )计算逆变换f&minus；1的微分同胚f；4 )选取点，如低校正序列，N维空间；5 )点映射到表面使用x ( f&minus；1 )，从而产生图上定位点表面；6 )取样表面使用至少一个子集的样本生成图上定位点的表面；和7 )分析，以确定样本表面的表面特征。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 生成, 方法, 系统, 计算",通用
US06608755B2,US10084246,"偶联装置耦合到两张牌的第一大小合适以向底盘具有狭槽的第二尺寸。偶联装置可以连接两个3U卡一起通过夹紧所述第一卡和第二卡的第一板和第二板之间有效地形成一个联合梳麻机第二尺寸。因此，两个3U卡到槽6U中，共同连接6U的底盘。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"具有, 装置",通用
US06608516B1,US10061007,"可变时间常数积分器包括放大器，被配置为生成输出信号，电容器耦合到反馈提供到放大器的可变增益元件耦接到放大器的输出端和电容器。该可变增益元件被配置为产物的增益与输出信号的电容器。该可变增益元件还被配置成接收指示新的增益和相应地设定增益等于新的增益。调整增益的可变增益元件的调节integrator's时间常数。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 生成, 包括, 配置",通用
US06584601B1,US09500187,"一种计算机实现的系统和方法，用于产生硬件实现的图形代码。该方法可以用于配置器械来执行测量功能，其中该器械包括可编程硬件元件。该方法包括首先创建图形化程序，其中该图形程序可以实现测量功能。图形程序的一部分可以任选地被编译成机器代码的执行由CPU，而另一部分的图形程序可以被转换成硬件实现上的可编程硬件元件。可编程硬件元件被配置为利用硬件描述以产生配置硬件元件。该配置硬件元件从而实现硬件实现的第二部分的图形编程。在产生硬件实现，计算机系统可以工作来估计和/或显示一个或多个尺寸和成本的硬件实现的图形编程。在一个实施例中，图形程序来操纵一个或多个硬件资源的器械和使用率指示一个或多个硬件资源的创建期间显示的图形编程。探针还可插入图形程序，其中相应的探测器元件被置于硬件实现来实现探头的功能。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 包括, 方法, 系统, 计算, 配置",通用
US06572403B2,US09767572,"一种系统和方法，用于将多个接线板。一种膨胀塞，包括第一连接器和第二连接器进行电连接。每个接线板包括至少一个插头连接器，以耦合到该两个连接器的膨胀塞子，可以包括顶塞连接器和底部连接器，以与邻端块通过膨胀塞使用任一顶塞连接器或底塞连接器。每个所述多个接线盒连接到一对应的开关模块，以形成多个交换机矩阵。通过对连续的接线盒连接顶和底插头连接器以交替方式因此偶合了许多终端区块/模块配对交易经由膨胀塞方式，从而使得多个交换矩阵被集成到单个集成交换机矩阵。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括",通用
US20030071842A1,US10218701,"本发明的一实施例包括系统和方法，用来使图形程序动态注册一个或多个事件期间执行图形编程。事件登记节点可以显示在该框图图形的程序。一个或多个事件可以与事件注册节点。事件登记节点可操作以动态地注册一个或多个事件期间执行图形编程。本发明的另一个实施例包括系统和方法，用于使绘画程序来编程地生成响应用户定义事件。用户可能希望定义定制(即，用户定义的事件，用于图形编程。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 生成, 方法, 包括",通用
US06542166B1,US08644120,"一种集装箱独立编辑的方法控制，其中改变可以查看并评估了应用改变之前控制。该方法包括用户跌落控制到容器的形式，如可视Basic 。当用户停止控制，容器构建控制。该控制器包括标准接口，优选Active X，柔性部分和内部统制物体部分。当容器构建控制其构造标准接口兼容的控制。在正在构造的标准接口兼容控制构建第一内部统制引用的一个原始指针。第一个内部统制对象包括数据的控制。标准接口兼容的控制提供一种方法，调用重定向方法在控制通过标准接口与内部统制引用的原有的指针。因此控制选择性地重定向方法调用时，在内部统制对象通过修改所述原始指针。当用户选择控制一个编辑交易标准接口兼容控制创建第二内部统制对象副本的第一内部统制对象，创建地产对话页，产生预览窗显示第二控制对象在预览窗口。用户改变控制，经由该属性对话页或通过直接与拷贝的图形交互控件显示在预览窗口。这些改变被反映在控件显示在预览窗口，但从形式上说窗口。用户编辑控制如在预览窗口，施加改变或取消改变。如果使用者将改变，标准接口兼容控制创建第三内部统制对象副本的第二内部统制对象，即这些反映了用户改变，并将原有的指针引用第三种内部统制对象。标准接口兼容控制然后丢弃第一内部统制对象。如果用户取消改变，第二内部统制对象，预览窗口和地产对话页被丢弃。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 包括, 控制",控制
US06542838B1,US09569862,"一种系统与方法，配置测量设备到正确的范围内。该系统和方法实现自动换挡技术，优选地在软件中，那些基于自动确定测量范围的测量。一旦初始测量，接收到初始测量与电流测量范围。如果当前范围正确测量对于初始测量，则测量设备已经配置的正确测量范围。然而，如果当前范围被发现不是正确的测量范围内，则确定新的测量范围是基于该测量结果。然后,采用新的测量，使用新的测量范围，以确保新的测量范围正确的测量范围内。该过程不断重复，通过比较新测量与新的测量范围，以查看是否新量程，为正确范围。如果新的测量范围不能正确测量范围内，则该过程继续，直到正确的范围内被找到。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 方法, 设备, 系统, 配置",通用
US06535640B1,US09560695,"信号分析系统和方法，用于识别最接近的向量的向量集合对给定输入信号向量，包括输入端、存储器和处理单元。存储器存储一批载体，和一台之间的相互距离对矢量集合。该处理单元可用于接收输入矢量对应于输入信号。处理单元可进一步配置成：(一)选择矢量从电流收集；(二)计算该距离的输入矢量到所选择的向量；(三)确定计算的距离小于边界半径值；(步骤d)进行环形过滤响应于所计算的距离不能小于围绕半径值，其中环形过滤保留集电要不是矢量谁的制表从所选择的向量距离大于所计算的距离减去半径值，并小于所计算的距离加上半径值；以及迭代地执行( )、( )，(三) d，直到计算的距离与所选择的点小于半径值，于是，处理器可以识别所选矢量作为解向量(也就是最接近矢量的矢量集合对输入向量)，并且可以提供输出指示给用户作为回答这一标识。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 方法, 处理, 系统, 计算, 配置",通用
US20030043175A1,US10100559,"一种系统和方法，用于指定机器视觉过程利用两个或更多不同的程序建立方法。在一个实施例中，不同的程序建立方法学可以包括指定操作或步骤过程图形输入板的使用，例如，在基于向导的方式，并利用图形程序设计技术用于其它部分，例如指定决策操作。因此，最优方案的方法学建立用于指定各个不同的部分过程。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括",通用
US06529355B1,US09605139,"一种电路，保护分流器或任何模拟测量电路没有实现熔断器。在特定实施例中，该电路包括分流器和多个晶体管，此可配置成用作熔丝。其中一个晶体管可被停用，一旦输入电压超过负阈值电压。另一个晶体管可被停用，一旦输入电压超过正阈值电压。通过去激活晶体管一旦输入电压超过负的或正的阈值电压，则晶体管被配置成熔断器的作用，并提供用于输入保护分流器或任何模拟测量电路。在另一特定实施例中，一种电路提供保护第一和第二分流器在第二分流器阻力小比第一分流器。该电路还包括多个晶体管，三个可配置成用作熔丝。该电路还包括限流器保护第二分流器在第二范围和电压限制器，以保护该限流器在第一范围，其中第二范围小于第一范围。限压器用于去激活晶体管一旦输入电压超过负电压或正电压阈值的第一范围内。限流器限制流过晶体管一旦输入电压超过负电压或正电压阈值的第二范围。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 模拟, 实现, 包括, 电路, 配置",通用
US06515682B1,US08644119,"一种编辑一个OLE控件包括用户拖动图标表示的控制，该控制而且滴下了到容器的形式，其含有OLE控件，诸如可视Basic 。当用户停止控制，容器构建控制和显示控制形式中，当用户选择控制一个编辑交易控制创建地产对话页，产生预览窗显示控件在预览窗口。用户改变控制经由该属性对话页。该属性对话页调用一种方法在显示器控制的控制与该变化反映在预览窗口，从而使用者可看见改变的效果的控制。候补显示方法，该显示方法方法致敬号之间的界面所限定的容器与控制。优选备用显示器方法接收附加输入参数哪个包含描述该变化。该control's数据未被修改，因此改变未反映在控制显示器从形式上说窗口，直到使用者施加改变。用户编辑控制并在预览窗口根据需要改变或取消改变。如果使用者将改变，control's数据被更新以反映该变化。如果用户取消改变，预览窗口和地产对话页被丢弃。因此，改进的容器独立编辑的方法控制，其中改变可以查看并评估了应用改变之前控制进行描述。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"方法, 包括, 控制",控制
US06513086B1,US09447514,"包括一低电压模拟母线和一条高压模拟总线的一信号调节系统。信号调节系统包括具有多个插槽的一底盘，其中每个插槽适合接收一组件。底盘包括一低电压模拟母线并且适于传输低压电气信号。底盘还包括一条高压模拟总线并且适于传输高电压电气信号。组件可以被放置在底盘的插槽里，其中每一组件可以通过一连接器连接到低电压模拟母线和高压模拟总线的一个或者全部。高压模拟总线允许高电压电气信号的调节（包括转换）。在替代实施例中，信号调节系统可以包括仅仅包括一低电压模拟母线的一底盘，即底盘不包括一条内置式高压模拟总线。例如信号调节系统可以是不包括一条高压模拟总线的一现有的SCXI 底盘。在该实施例中，系统可以包括适于连接到包含在底盘中一个或多个组件的一个或多个背面连接器。一个或多个背面连接器共同地形成用于在一个或多个组件之间传输高电压电气信号的一条高压模拟总线。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 模拟, 包括, 具有",通用
US06507842B1,US09613341,"从或到一个数据库的用于进出口测试执行程序值的一种系统和方法。一测试执行程序可以提供用户称为变量和属性各种地方，其中数据值可以被储存。这些数据值可以影响一次测试执行程序序列的执行。变量可以是全局到一顺序文件或本地的到一特定序列。在一序列中的每一步骤可以具有属性。用户可以包括在一序列中的一属性装载器步骤，其动态地，即在运行时可被操作以便，来自一数据库的承载性能和/或变量值。属性装载器步骤可以被放置在序列的一设置组里，步骤谁的属性被配置可以被放置在序列的一主族里，以便当序列在主族被执行步骤在运行之前被配置成合适性质值时。测试执行程序对相互作用地，例如通过一用户接口菜单项目也可以能够进行一名用户，可变的请求和被进口从或出口到一数据库的与一个序列相关联的特性质。例如，在出口值至一数据库之后，值稍后可以用于动态地配置一测试执行程序序列。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 测试, 具有, 方法, 设置, 系统, 配置",测试
US06505247B1,US09243224,"该系统和方法工业自动化提供了改进的网络传送之间数据的节点。该系统包括多个计算机系统经由网络进行互连，其中,每个计算机系统执行工业自动化软件。一个或多个这些计算机系统联接于各种硬件输入/输出装置以进行数据采集。每个计算机系统是家庭到一个或多个客户机和/或服务器处理。在数据采集之前开始，每个服务器进程寄存器的多个时间/质量( TQ )组，其中每一TQ群组由一个时间/质量识别价值( TQID )，其中每个TQ组代表一组相关数据元素。当硬件产生数据输入输出装置，服务器进程哪个一条光纤连接在一起，装置通过设备驱动程序接收数据块从装置。驾驶员分配给块单个时间戳和单个质量值。使用实时协议，服务器添加一个或多个数据块到分组并将该分组到每个客户端其具备了订阅了该数据。用于提高网络和计算效率，实时协议包括压缩技术和基于例外的是：只改换了数据元件，改变时间戳，而且变更了质量值被发送给客户端。两个客户机和服务器存储TQID对于每个块，使客户端能够更有效地更新改变的时间戳而且变更了质量价值所有数据元素的TQ组。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 装置, 方法, 处理, 设备, 系统, 计算","驾驶, 网络"
US06493079B1,US09656571,"一种系统和方法，用于分析一个对象，使用减少数目的相机和/或减少数目的获取的图像。该系统和方法可用于在机器视觉应用程序，例如，为了检查制造物体。该方法可以用于获得图像，或者多个图像物体。为了减少图像数量，那些需要被获取，至少一个所获取的图像，该图像包括视觉信息的两个或多个侧面物体。这种图像可以由一个转播机位多种方式中的任何一种，以便照相机可捕获视觉信息的多个侧面物体。获取物体的图像然后可以由计算机系统接收的连接到摄像机，并利用图像处理软件。一种包括用于每个图像的视觉信息的多个侧面对象，分析该图像可包括确定图像中感兴趣区域，其中对应于每个感兴趣区域的一侧的物体。每一个感兴趣区可被分析分开。在机器视觉制造应用，图像分析的结果可以用于对确定所述对象是否满足所需生产标准。该图像或ROIs可被分析使用各种技术中的任一种或算法设计以检测任何各种特性或缺陷的物体。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 算法, 方法, 处理, 系统, 检测, 计算","检测, 摄像"
US06486893B1,US09504412,"一种系统和方法，用于允许浏览器显示属性的属性子对象的控制，例如ActiveX控件。该系统和方法进一步允许用户操纵属性子对象的控制。该方法确定顶层接口控制和显示顶层性质在顶部接口属性浏览器。浏览器还显示该属性指示特性，这些特性子对象，也就是，哪个属性本身的特性。该指示符通常是常规加号表示该单个另外的元件可以在层次树元素。当用户随后选择属性进行子对象，该方法操作读取接口从类型子属性库，然后进一步显示界面的顶层性质必需的。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 控制",控制
US06459707B1,US09219205,"继电器复用系统提供改进的安全从危险电压情况。继电器复用系统包括继电器多路复用器单元和接线盒哪个连接到继电器多工器模块。继电器多路复用器单元和相应的终端块包括根据本发明设计的由此电能只能给各个继电器，当对应接线盒连接到相应的继电器多工器模块。接线盒的需要之间形成电通路电源的继电器多路复用器单元与上述继电器。因此，当接线盒被从相应的继电器多路复用器单元，功率不能给继电器，确保安全，从危险电压另外可以出现在暴露的销的连接器。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 提供, 包括",通用
US06425033B1,US09092342,"用于连接外围设备到一台计算机的一种广域连续的PCI 系统。WASP 系统包括通过一条串行总线至一远程设备连接的一主机系统。串行总线可以位于范围从几仪表到几公里或更多。主机系统包括一CPU 和存储器，以及包括第一外围设备元件间相互连接（PCI）总线，也称为本地的外围部件互连总线。根据本发明的主镜梁被连接到第一外围部件互连总线。主镜梁包括用于对第一外围部件互连总线接合的PCI 接口电路元件。远程设备远离计算机系统并包括第二或远程PCI 总线和耦合到第二个外围部件互连总线的一个或多个外围设备。远程设备还包括耦合到第二外围部件互连总线的次级桥接。次级桥接包括用于到第二个外围部件互连总线接合的PCI 接口电路元件。串行总线耦合在主镜梁和次级桥接。每个主镜梁和次级桥接包括并行/串行收发器，其用于转换并行数据，其对用于在串行总线上的传输的串行数据和用于把接收自串行总线转化成用于分别的生成在第一外围部件互连总线和第二个外围部件互连总线上的并行数据的串行数据分别在第一外围部件互连总线和第二外围部件互连总线上生成。主镜梁和次级桥接共同地实现一PCI&mdash ；PCI 桥寄存器组。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 包括, 生成, 设备, 系统, 计算, 电路",通用
US20020080157A1,US09894660,"一种系统和方法，用于搜索条目层级调色板窗口和包括位于物品进入程序。调色板窗口可包括一个或多个调色板窗口选择项的列表，当被选择时，打开儿童调色板窗口当前调色板窗口。调色板窗口可包括一个或多个项目中进行分级。搜索机制用于定位调色板窗口和/或调色板窗口内容可以提供。从上述搜索机制，使用者可以定位并打开了调色板窗口一个或多个分层结构。用户还可以包括调色板窗口内容到某物，例如，程序是编辑在一个图形编程环境。在一个实施例中，用户可以拖放调色板窗口内容到窗口被编辑。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 提供, 方法, 包括",通用
US06370569B1,US09185161,"一种客户数据插座和相关联的应用程序和/或工具，这些程序提供访问来自各种源的数据和具有各种类型的或格式，其中提供接入对用户不可见。此数据套接字主机允许用户或程序来访问任何数据源可用的user's机以及数据在网络上的任何地方，例如LAN，WAN或因特网。在该优选实施例中，数据套接字主机地址数据源I/O源使用URL (统一资源定位符)，许多情况用于寻址一个URL的网页世界上的任何地方。本发明也包括新资料插座URLs，这样允许用户访问I/O资源。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"具有, 提供, 包括, 工具",网络
US06366686B1,US09233400,"数据采集( DAQ )系统和方法，包括一种改进的事件的体系结构。该DAQ设备包括事件逻辑耦合以接收来自一个或多个外部或内部事件事件源。事件逻辑包括一个或多个事件选择器和一个或多个事件测绘仪。事件及事件映射器选择器接收事件信号从事件源并选择性地发送事件信号到一个或多个目的地，也称为目的地硬件元件。目的地可操作以执行动作响应于接收所选事件信号。事件的本发明的结构被设计以利用两个柔性的传统计算机中断体系结构的速度与直接硬件的映射。气密其精华，硬件结构包括迷你、硬件，中断服务程序。硬件事件或触发映射到一组板级别事件哪些与传统观念的中断。这些硬件事件可以被映射到引起各种各样的直接硬件或软件行为，类似于传统的中断服务程序，除了硬件事件可以是好像要引起各种各样的实时或确定性响应，因为不需要主进程是必需的。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 设备, 系统, 计算",通用
US06332116B1,US09551924,"用于分析从一力学系统获取的一输入信号的一信号分析系统与方法。力学系统可以至少包括一旋转装置。信号分析系统可以经配置以：（a）接收输入信号的样品，在生产取决于时间和频率的系数的第一数组的输入信号的样品上（b）进行可逆的联合时频变换（例如一加长生长），从第一数组（c）选择第一系数，其对应于在输入信号中的一个或多个订单组件的第一子集，从第一系数（d）生成一时域信号，（e）提供时域信号到在一提示设备上的一名用户。通过执行在第一系数上的反的联合时频变换，信号分析系统从第一系数生成时域信号。通过所有的系数而出除那些对应于一个或多个组件的之外掩蔽的寄出给用户的信号分析系统提取物一个或多个订单组件。相反地，信号分析系统可以抑制一个或多个订单组件，即对用户提供输入信号减去一个或多个订单组件，通过对应于一个或多个组件的系数而出掩蔽并保持剩余的系数。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 装置, 方法, 生成, 设备, 系统, 配置",通用
US06317637B1,US09177210,"PID控制其保持了连续性的系统和方法的控制输出，尽管改变控制器参数。控制器包括比例增益，积分增益，微分增益，和一个手动/自动模态参数。控制输出，在参数被设置成等于先前的控制器输出，集成错误设置为值与前一控制器输出根据PID控制器方程。该值用于集成错误有助于确保持续连续性控制输出用这样的方式控制程序参数变化。可选地，控制输出，在参数设定为等于第一值连续相对于一个或多个先前的控制输出值，以及综合误差的值设置为符合第一值根据PID控制器方程。而且，该系统和方法使用积分器抗积分饱和。如果控制输出集中轰炸，也就是落在控制区由假想的下级单位绑定和上位结合，本发明的系统和方法规定设置控制输出值控制结合的哪个被超过，(二)设置集成误差等于值与超过控制结合的根据PD控制器方程。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 设置, 控制, 系统",控制
US20010033196A1,US09839454,"提供了一种主动状态可变滤波器，其包括求和电路、第一和第二积分器电路以及放大器电路。求和电路以及第一和第二积分器电路每个均包括用于可编程地改变状态可变滤波器的滤波特性的可编程可变电阻器。每个可编程可变电阻器接收数字值并且在一对端子之间提供与数字值相对应的电阻。每个可编程可变电阻器可以包括在该对端子之间延伸的多个分支电路，每个分支电路包括串联连接的电阻器和电开关。可替换地，每个可编程可变电阻器可以包括在该对端子之间串联连接的多个分支电路，每个分支电路包括并联连接的电阻器和电开关。每个可编程可变电阻器还可以包括用于存储数字值的存储器单元以及用于根据数字值来控制电开关的控制逻辑。电开关可以是诸如微机电系统(MEMS)开关的双边开关。还提供了一种仪表系统，其包括包含状态可变滤波器的信号调节子系统。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 控制, 系统, 电路",控制
US06294945B1,US09496566,"一种系统和方法，其中介质吸收电容器被取消，通过补偿电路。一个实施例使用补偿电路包括补偿电容具有基本上相同，因为电容器待补偿的积分仪回路。介电吸收的效应的电容器的积分器电路被减小或消除由于介质吸收补偿电容抵消了介电吸收的电容器积分仪回路。另一个实施例使用补偿电路，以减少或消除介电吸收的效应在任何特定电容器。补偿电容的电介质吸收补偿电路具有一个较高的和较低的电容值比电容器谁的介质吸收效应来降低或消除。在另一个实施方案中，介电吸收的效应的电容被减小或消除通过选择补偿电容器中具有相同电介质吸收补偿电路作为电容进行补偿。该介质吸收补偿电容由定标电阻器中的哪一确定补偿电路中放大器的增益补偿电路。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 具有, 方法, 系统, 电路",通用
US06285095B1,US09488163,"一种AC衰减器系统包括用于为AC衰减器提供可选择微调阻抗的补偿电路。补偿电路包括优选地与衰减器的一部分并联耦合的多个电容器，其中电容器具有二进制加权值。补偿电路还包括多个开关，其中每个开关耦合到多个电容器中的每个电容器的输入端并且还耦合到AC衰减器电路。开关优选地是微电子机械系统(MEMS)开关。补偿电路还包括耦合到多个开关的存储器元件。存储器元件存储可操作来断开/闭合所选开关的值，优选地是数字或二进制值。存储在存储器元件中的值在接通期间提供给开关，从而断开/闭合所选开关并且配置多个电容器之中的电容值以用于AC衰减器系统的补偿。补偿电路因此为AC衰减器产生可选择微调阻抗。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 具有, 系统, 电路, 配置",通用
US06282699B1,US09256806,"一种系统和方法，用于创建图形化程序，其中该图形程序可操作以调用执行文本代码。用户选择典节点包括在图形程序，其中该典节点显示在屏幕上。然后,用户或进入文本代码，即包括在或显示在典节点。文本代码可以是代码从本文的语言，例如Perl，Mathematica，Java等,或可以是脚本从脚本语言。用户可以手动地输入文本代码到典节点，进口书籍文本代码从文件中。文本代码包括在典节点看得见的并且是用户可编辑的。在执行图形程序，典节点可操作以调用执行文本代码包括在典节点。文本代码优选地被执行的实例服务器程序。在执行图形程序，图解程序设计系统提供文本代码给服务器程序，以及任何由典节点接收的输入数据。服务器程序随后执行该文本代码，使用任何由典节点接收的输入数据，并产生一个输出，被提供回到图形编程。因此，本发明使用户图解程序设计系统更易于结合、查看、编辑和调试正文底码从在图解程序设计系统。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 提供, 方法, 包括",通用
US06259428B1,US09071318,"一种系统和方法，用于动态地中替换颜色图形图像。该颜色置换法接收颜色到内替换该图形图像(铬)和新的颜色(航向) 。该方法还色调偏差%值，其指示色调变化的程度对颜色替换来执行的。该方法然后转换为颜色将被替换(铬)对色调、亮度和饱和度成分(海港，反应限量，Sr )，并将新的颜色(航向)对色调、亮度和饱和度成分( Hn、Ln，锡) 。该方法还计算亮度差Ld&equals；ln&minus；Lr 。然后，对于每个彩色像素，每个像素或彩色指令，该方法执行颜色代替操作代替一个或多个当前颜色的图形图像与新颜色。该颜色代替操作对每个色元比较色彩成份Hc卷积云(当前)中当前像素的图形图像，并确定是否计算高度火柴岩石在规定的色调差异。该方法保持了的当前颜色当前颜色元件不变如果计算高度不干扰匹配岩石在规定的色调差异，因为颜色当前颜色元件没有充分接近所期望的颜色改变。如果计算高度火柴岩石在规定的色调差异，则该方法值设为Hc价值达港口设置值到Lc&plus光电流；装货应对确定是否计算高度火柴港口转移部分文件值Hc和光电流是适当地改变，该方法转换成新的计算高度和光电流值与钪值，回RGB格式产生新的颜色用于当前元件。该新的颜色则存储了各自的色素，从而实现颜色替换相应的颜色基元。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 方法, 设置, 系统, 计算",通用
US06256625B1,US09153383,"包括图像采集处理的改进的软件控制的一视频捕获系统和方法。所述方法包含：使用一个或多个目的首先创建一应用影像。已获取的数据都被优选地存储在一图像对象，以及一个或多个目的，例如一观众目标或图像过程目的，经配置以共享图像对象，即在程序执行期间动态绑定到或与图像对象通信。当应用开始执行时，计算机系统获取图像数据并存储在一图像对象中的图像数据。然后一第一目标即动态绑定到图像对象与图像对象通信，存取包含在图像对象的图像数据并处理图像数据响应于捆绑。出现图像数据的此动态绑定或通信，以及后续过程，没有用户干预。当包含在图像对象中的图像数据改变时，图像对象自动地告知一个或多个第一目标其被绑定到图像对象，每一第一目标自动地存取改变图像数据并处理改变图像数据。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 处理, 控制, 系统, 计算, 配置","通信, 控制"
US06243738B1,US09055589,"一种用于实现对数据获取(DAQ)系统中的远程设备的访问和控制的远程设备访问(RDA)特征。所述数据获取系统包括至少一个执行客户端DAQ应用的客户端计算机系统。所述数据获取系统还包括一个或多个通过网络耦合到所述客户端计算机系统的服务器计算机系统，其中每个服务器计算机包括可由所述客户端DAQ应用控制的数据获取设备。所述RDA方法包括所述客户端DAQ应用做出用以访问所述服务器计算机中的所述远程DAQ设备的呼叫。响应于确定所述呼叫是意图用于远程DAQ设备(相同的呼叫可以意图用于本地DAQ设备)，所述呼叫随后被封装到一个或多个包中，并且被传输到所述服务器计算机。所述包优选地使用远程过程调用(RPC)来传输。所述服务器计算机接收所述一个或多个包，并且对所述一个或多个包进行解包以产生所述呼叫。所述呼叫随后被提供给在所述服务器计算机中执行的DAQ驱动器软件。在所述服务器计算机中执行的所述DAQ驱动器软件响应于所述呼叫来执行所述呼叫，即使用所述DAQ设备执行DAQ功能，并且生成结果数据。所述结果数据随后被传输到所述客户端计算机，并且所述客户端应用接收所述结果数据。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 实现, 包括, 方法, 生成, 设备, 控制, 系统, 计算","网络, 控制"
US06237136B1,US08982592,"一种系统和方法，其用于生成源代码示例文件应用程序的计算机系统中。在优选实施例中，该系统和方法用于创建源代码示例文件NI-DAQ驱动软件的从国家仪器。用户首先产生代码流程描述文件。代码流程描述文件是以书面提出代码流程描述语言，其独立于多个编程语言(即，包括仅通用程序元件从多个编程语言。用户然后指定一个或多个钢要目标法语言。本发明包括代码产生器，产生一个或更多个目标示例源码文件中指定的钢要目标法语言响应于代码流程描述文件。代码产生器操作以分析代码流描述文件以产生代码流描述数据结构，然后生成一个或多个目标示例源码文件中指定的钢要目标法语言响应于代码流描述数据结构。因此用户可以容易地创建多个源代码示例文件用于多个不同编程语言和/或用于多个不同操作系统。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 生成, 方法, 系统, 计算",通用
US06232831B1,US09454256,"电源，描述了一种包括浮动电流源。该浮动电流源包括电流源提供选定量的电流给第一终端，和一电流吸收器接收选定量的电流从第二终端。电流源适于耦合至第一电压的电平哪个呈阳性相对于参考电位。电流吸收适于连接到第二电压为负等级，第二参考电位。所选量的电流可以正比于参考电流流经两个与电流源和电流宿。电流源和电流宿可各自包括一对晶体管连接在一起，以便电流流动通过一个晶体管对的产生比例电流流动通过另一个晶体管。该电源还可以包括两个线路供电的电压源产生第一和第二电压水平。在操作期间，第一和第二电压水平可通过改变第一和第二电压源，和期望的电压可以被显影第一和第二终端之间。电源，因此能提供偏压和电流以放大的换能器(例如，放大压电传感器) 。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括",传感器
US06219628B1,US08912427,"一种系统和方法，用于配置一种器械来执行测量功能，其中该器械包括可编程硬件元件。图形程序首先创建，其中该图形程序实现测量功能。图形程序可以包括前面板与框图。该方法产生硬件描述基于至少一部分图形编程。硬件描述描述了硬件实现的至少一部分图形编程。该方法接着配置可编程硬件元仪器利用硬件描述以产生配置硬件元件。该配置硬件元件从而实现硬件实现的至少一部分图形编程。接着该器具从外部源获取信号，与可编程硬件元仪器执行执行测量功能的信号。前板可以由用户来控制仪器在测量。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 包括, 方法, 控制, 系统, 配置",控制
US06161154A,US08/979589,"一种系统和方法，用于动态地重复程序设计获取循环视频采集系统。视频采集系统，包括主机和视频源。主机包括CPU，系统存储器和一个视频捕获板。该视频捕获板接收来自视频源的视频信号。视频信号包括一序列帧。该视频捕获板包括DMA控制器哪些写入帧到系统存储器的控制下循环链接表的命令。该链接列表包含多个子表，每个DMA传送调度帧到相应的视频缓冲器系统存储器中。通过去除子列表从链表，视频缓存器被从第二组缓冲器目标由DMA数据传送的控制器。通过加入子表对链表，视频缓存器恢复到目标组。当前或最近完成的视频缓冲器被从目标根据响应请求断言由应用程序。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括, 控制",控制
US06148438A,US09/003391,"一种系统和方法，用于创建对象的组合类具有虚拟功能，其中组合类使得避免用户模式/内核模式过渡操作系统中。该方法首先包括定义A级，空类，组合类A级与上述空类。这些类在软件程序中定义的时刻。该组合类继承从上述组合类与上述空类。该组合类包括第一模式和第二模式界面接口，所述第一接口和第二接口模式的模式具有相对的分序作业基类。在执行期间，软件实例化复合材料物品与组合类。该方法然后修改组合对象以使复合对象可直接在第一模式和第二模式之间共享具有降低的模式过渡。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括, 具有",通用
US06140859A,US09/148264,"模拟开关构造成使用两个双极结晶体管。该第一晶体管的射极耦合到所述第二晶体管的发射极，优选该第一晶体管的基极耦接至第二晶体管的基极。晶体管的集电极形成端子的模拟开关。电流源耦合到基座的两个晶体管。该电流源产生驱动电流足以正向偏置基极-射极结的两个晶体管。在这一点上正向偏压状态时，第一晶体管的集电极所述第二晶体管的集电极与电连接。偏置电压源被耦合通过开关底座的两个晶体管。偏置电压源的电压产生足以使基极-集电极结反向偏置，两个晶体管。在这一点上反向偏置的状态，该第一晶体管的集电极之间的电连接，所述第二晶体管的集电极与断开。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,模拟,通用
US06122713A,US09/088542,"一种系统和方法，用于控制存取双端口共享存储器的系统包括主机系统和通信设备包括或耦合到主计算机系统。该通信设备包括共享存储器，并且还包括本地处理器执行通信应用。共享存储器是可由两个主机与局部处理器通信设备上。该板或局部处理器具有确定性和/或实时要求因此高优先级请求者，同时或主计算机的主CPU的低优先级要求。如果高优先级侧(板)获得臂板信号第一然后访问由低优先级侧(主机)被阻止，直到该写入结束。对于主机读取/写板，如果低优先级侧向补给量的第一，则重要优先项目侧写入可以预先empt低优先级侧读取。在这种情况下，以避免数据完整性问题，低优先级侧需要，以验证其仍然拥有臂板信号在它完成其读访问，访问失败如果低优先级侧没有自己的信号量在那时所读过的完成。对于主机写入/板读取，如果低优先级侧向补给量，则该高优先级的第一侧到干不单单抢先低优先级侧与高至低位数据传送方向。相反，在本例中，当重要优先项目侧确定了那个低优先级侧拥有臂板信号，重要优先项目侧读取先前读取数据从本地缓冲器。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 具有, 方法, 处理, 设备, 控制, 系统, 计算","通信, 验证, 控制"
US06108609A,US08/903550,"用于设计一基小波的一种改进系统和方法。系统与方法首先在屏幕上展示一过滤器P （z），其中一基小波与过滤器P （z）有关。过滤器P （z）是二低通滤波片G.sub.0 （z）和H.sub.0 （z）的乘积，其共同地包括建模小波的一完全重构条件。然后方法在屏幕上展示一图，其示出了P （z），以及其他信息，例如母子波函数，关联，频率响应等等的零分布。然后用户可以用图形化的方式调整在图上的P （z）的零分布，为了获得所需的基小波。这涉及因式分解P （z）进入G.sub.0 （z）和H.sub.0 （z），其中因式分解包括分配图对功能G.sub.0 （z）和H.sub.0 （z）响应于用户输入中所示的一个或多个零。调整在图上的P （z）的零分布的步骤还包括选择某种过滤器P （z）。调整P （z）的零分布的操作操作自动地调整一个或多个基小波，关联，以及被显示在显示屏上的频率响应，从而提供即时反馈关于基小波的性能。这使用户能够使用于基小波的最佳的选择。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 提供, 方法, 包括",通用
US06098124A,US09/058090,"用于在一条串行总线上的转移数据的一种改进系统和方法。输入数据被存入具有一个动态地变量大小的数据缓冲器。每一数据缓冲器的尺寸可以被调整为进来的新建数据。具有相同的始发地址的数据储存在相同的数据缓冲器。耦合到数据缓冲器的和对串行总线仲裁人监控每个数据缓冲器和串行总线的可用性。当序列是可以获取的时，从数据缓冲器之一根据一些预定优先权的仲裁人传输数据。例如，最大缓冲器可以具有最高的优先级。优先权的一这样的分配作串行总线的极高效使用，因为大量的数据较少地在头顶上具有并因此是更有效的转移。此外，尽管数据正在调离数据缓冲器之一，数据正在聚集在所有其他的缓冲。这使另一个缓冲更大和更有效的根据串行总线的随后的可用性转移。仲裁人可能还包括逻辑，其用于决定多久这因为上次数据被转移出不确保在数据缓冲器中的原料供应不足数据的每一数据缓冲器。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括, 具有",通用
US06096094A,US08/943784,"数据采集系统包括组态管理员用于智能地管理对DAQ配置信息。数据采集系统包括计算机系统耦合到数据采集装置，采集应用(用户应用程序)在计算机系统上执行，DAQ驱动器级软件执行在计算机系统上。该计算机系统的存储器存储包括关于DAQ硬件数据库哪些对象DAQ系统上，且哪些存储库配置文件包括期望构型的DAQ系统。本发明的该组态管理员执行在计算机系统来控制访问硬件数据库和配置文件存储在计算机系统中。本发明也包括一种用于提供对有关对象( DAQ )数据采集DAQ系统。该组态管理员接入配置信息从系统存储器如果配置信息包括修改的参数值存储在存储器中，并且与组态管理员否则接入配置信息从硬件数据库。本发明也包括一种用于提供对附加的或新信息( DAQ )对象数据采集DAQ系统。用户的DAQ系统接收更新的硬件数据库，现有的配置文件可以自动地访问附加出力DAQ的物体从已安装的更新硬件数据库。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 装置, 控制, 系统, 计算, 配置",控制
US06076952A,US08/932274,"一种系统和方法，用于显示并配置了参数总线组态系统。该公用总线组态首先确定设备且堵塞了系统中存在的参数，然后确定每一块。在参数被显示时，显示对象到接近某些参数。对象包括颜色和/或形状，这种表示类型或类别的参数。用户还可以配置和使用改进的警报趋势图形界面上。用户配置告警和趋势通过装配图形程序或布线图上包括选择一个或多个块图标和图标代表设备接收趋势或警报。该系统也包括用于获得周期性的更新窗口，这种周期性更新的值选择的参数。
","NATIONAL INSTRUMENTS, CORP.",其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 设备, 系统, 配置",通用
US06075690A,US09/248647,"一种系统和方法，用于控制继电器。该方法可包括接收一个或多个输入指令用于多个继电器，继电器包括与多个第1继电器，以及一继电器，启动致动第一中继在接收到输入继电器命令，并最后继电器致动之后开始致动第一中继。最后启动致动中继不等待反跳的第一中继。该方法等待防反跳时间段之后启动致动最后一个继电器。该防反跳时间段用于防反跳最后一个中继与第一。包括一个或多个第二继电器也可以是在反跳序列。该继电器可锁存或闩锁继电器。该方法可以确定是否需要另一继电器致动之后开始致动各继电器。一个输入命令可以包括反弹模式输入。延迟防反跳模式防反跳多个继电器仅在最后的继电器已被致动。立即反跳模式防反跳每个继电器在继电器已被致动。该系统包括一个或多个继电器驱动耦合到继电器控制逻辑，具有可选的继电器耦合于其间。继电器控制逻辑可操作来接收输入命令的继电器，该继电器来发起动作，而不等待反跳前继电器。继电器控制逻辑等待防反跳时间段之后启动致动最后一个继电器。该防反跳时间段用于防反跳最后一个中继与第一和第二继电器。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 具有, 方法, 控制, 系统",控制
US06073205A,US08/891150,"写记入设备和方法在通用串行总线( USB )系统包括主计算机与USB设备通过USB 。主机生成请求将数据写入到存储器，在该USB设备。主机包括队列发送写请求在发电中。该写请求记入队列中直到主机传送单个数据分组从所述投寄写入请求。据分组产生响应于主机读取数据的请求，该USB装置时，主机确定那个最近发送的写请求针对存储器位置在USB设备进行nonpostable，指示队列缺乏存储空间用于随后的写请求。USB设备接收从主机发送的数据包，并将数据写入内部存储器位置根据接收到的数据分组。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 方法, 生成, 设备, 系统, 计算",通用
US06053951A,US09/170329,"一种计算机实现的系统和方法自动生成图形代码的图解程序设计系统。计算机存储器中存储多个图形代码模板。该图解程序设计系统计算机系统上执行也包括多个面板对象或控制这些代表用户接口。一个或多个相关联的图形代码的部分或模板可以与某些控制。根据本发明，用户首先选择控制，然后优选地启动图形代码生成向导的控制。当图形代码生成向导被调用，该向导屏幕上的显示配置板或对话时，提示用户配置该控制或对象。然后,用户参数值配置特定方面的图形代码被创建。图形代码生成向导选择图形代码模板响应于控制并配置图形代码模板与参数值。图形代码生成向导接着之间创建关联的控制与配置图形码。用户可以编辑向导创建的代码或者可以使用图形代码生成向导或通过解锁该控制与代码之间的关联，并直接改变的框图。本发明还包括图形代码生成向导特别设计成用于工业自动化应用，称为MMI G向导。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 包括, 方法, 生成, 控制, 系统, 计算, 配置",控制
US06038617A,US09/027982,"装置配置串行ROM 。按照于此，指令被串行传送到输入串行ROM 。此后，全部为零的x位宽的地址被串行传送到输入串行ROM 。每个比特的指令和地址传送到x位宽的输入上，该串行ROM在时钟的上升沿又输入了到串行ROM 。串行ROM的输出连接到预充电感测节点。串行ROM输出数据输入之前响应于x位宽的地址，该串行ROM将放电感测节点以接地如果串行ROM是第一类型的串行ROMs 。然而，如果串行ROM是第二类型的，传感节点不予排到地面输出之前该数据。该地址的传输之后立即X位，配置寄存器被设置为指示串行ROM的类型。在这方面，配置寄存器的设置是根据是否感测节点是否完全被排出，在发射x位宽的地址。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"设置, 装置, 配置",通用
US05995376A,US08/859028,"一种VXI底盘，具有用户可选择或可配置的狭槽0的位置。该VXI机箱包括个性模块VXI接口耦合到底板包括在底盘。该个性模块接口或槽是适应到接收个性模块。该个性模块插入个性模块接口或槽在多个方位。基于方位插入的个性模块，个性模块可操作以选择性地进路信号机底板以使多个位置中的一个以作为槽0的位置。在优选实施例中，VXI机箱可以容纳多种C尺寸和/或B尺寸VXI/VME卡，与VXI机箱之间配置不同的槽0的位置对于不同尺寸的卡，即在一个C型槽或B-尺寸的槽0的控制器。因此，本发明的VXI机箱提供一创新的解决方案使用两个C型槽VXI和bSize VXI/VME资源单个系统中。选择C型槽或bSize槽0控制器提供增加的选项提供给用户，并降低了系统成本。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 具有, 控制, 系统, 配置",控制
US05990906A,US08/914554,"一种Undo/Redo特征图解程序设计系统哪个最小化所需的存储。该图解程序设计系统包括多个相互连接的物体，包括固定大小的对象和大小可变的对象。可变对象物体一般尺寸变化(即，增长或收缩时，响应于用户修改，而固定大小的对象不会改变大小。当对象被用户修改，本发明的方法存储副本用于撤消目的。用于对象的修改，备份不同地执行大小取决于对象物体或固定大小的对象。用于可变对象，方法存储原始对象的备份列表前一对象的方法，将原始数据的复本存储在对象作为当前对象备份列表。对于固定大小的对象，该方法存储原始对象的副本与前一对象备份列表方法，存储原始对象的当前对象的备份列表。对于固定大小的对象，从而保持原始对象的当前或前景物体在图形编程。由于原始对象的停留在前景、其它对象，该对象引用参考这些保持它们，因此不被支持也需要为不合格，因此，本发明提供一种独特的系统和方法用于执行撤销/重复图形，这种系统减少了所需的存储器量撤消目的。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 提供, 方法, 包括",通用
US05987246A,US08/799955,"一种图解程序设计系统和方法，包括3维节点彼此有线连接至从图形程序的框图。每一个节点包括三维的多个侧设计成接收预定义的输入。在一个实施方案中，每个节点包括用于接收数据输入的左侧和右侧，用于产生输出数据。上部顶面的每个节点设计为接收输入关于错误状态和/或初始化信息。在每一节点保留显示该节点的名称或执行的功能由该节点。后侧的每个节点保留定时和同步的输入。底部一侧的每个节点设计为接收基座配置信息和/或类型说明信息。用户仅被允许以连接指定类型的数据的输入与指定每个节点。如果用户尝试连接该错误类型的数据与相应一侧的节点，连接被断开，因此向用户警告该问题。因此，本发明的图解程序设计系统简化了装配过程，并增加错误检查。用户还操作或旋转图形程序的框图视图，因此与不同侧面观察不同类别的输入。
",NATIONAL INSTRUMENTS CORP.,其他技术,0.0,未匹配到特定技术领域,"系统, 方法, 包括, 配置",通用
US05987530A,US08/889996,"提供一种装置和方法，用于缓存数据的通用串行总线( USB )系统。在一个实施例中，本发明使用主计算机耦合到I/O装置通过USB 。主机包括用于存储数据的数据高速缓冲存储器检索自I/O设备。数据高速缓冲存储器允许将数据返回给主机按要求而不访问I/O设备通过USB事务。一种可缓存性查找表和高速缓存表被提供以确保数据的完整性被返回给主机。如果数据被返回到请求从I/O设备可高速缓存性查找表表示被请求的数据是不可高速缓存的。如果数据被返回到从数据高速缓存高速缓存表指示被请求的数据在高速缓存中可用作为有效数据。如果高速缓存表指示被请求的数据不能以高速缓存作为有效数据，从I/O设备返回被请求数据以及数据存储在预定输入输出设备的地址。该附加数据存储在高速缓存中，以随后由主计算机。速告来自高速缓存的数据，本发明降低了响应时间并减少了上的业务在主计算机和USB装置。
",NATIONAL INSTRUMENTS COPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 装置, 方法, 设备, 系统, 计算",通用
US05980298A,US08/935641,"应变减轻装置用于一对电缆连接器端子电气装置。该应变减轻装置包括细长的基底构件具有平的表面之间延伸的第一和第二端部。卡扣件固接至平坦表面，在第一端部分，用于可释放地紧固到基部外表面上的壳体的电子装置。夹紧装置垂直地延伸，在第二端处从基部部分。该夹紧装置可调槽限定了构造成接收电缆连接器。另外，该槽可以与若干纵剖线图相对于基部旋转。当该基座的外表面而紧固至该电子装置，该槽对准容纳在其中的可移至电缆连接器与端子的电子装置。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"具有, 包括, 装置",通用
US05978850A,US08/886909,"一种系统和方法，用于对象的存取参数，或使用标签参数界面在现场总线系统中。本发明能够接入参数或目的使用标签参数构造不需要用户执行任何预先配置连接或收报人名址索引映象。这显著地简化了用户拓展应用中现场总线网络，其中用户完全被屏蔽从所有现场总线基金会礼宾细节。根据本发明，用户不需要具有任何关于该VCR的细节，如何来架起或建立VCR，甚至一个实体被称为""VCR""存在于系统中。另外，本发明自动映射参数名称为索引，因此用户被保护不受构造细节的装置。而且，如果新的块实例化或现有的功能块去实例化时，本发明的方法处理配置管理，无需任何用户介入。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"装置, 具有, 方法, 处理, 系统, 配置",网络
US05974254A,US08/870262,"一种检测互差两个图形程序被公开。图形程序包括对象，优选布置成用户界面板包括控制装置和指示器，和方框图，包括图形代码功能块在一起作为数据流程序。有向图数据结构创建来表示图形程序，其中该顶点的图形对象的图形节目的边缘与该图形数据流的信号和/或框图的层次关系用户界面板对象。然后两个图形对象的程序使用启发式配装在一起的计分方法。该得分存储在矩阵之间的相似度匹配指示物体在第一绘画程序和物体在第二绘画程序根据一个或多个准则。匹配的标准包括对象类型、对象，连通性和对象属性。该匹配矩阵分解生成1:1或1:0之间的对应关系的对象在第一和第二绘画程序基于室内联赛让步优待分。该匹配信息被用于确定差异后两个图形节目。第一，通过使用匹配信息和比较引擎，将对象分组为精确匹配子图，然后进入不精确的匹配子图。非精确匹配子图匹配的并且合并了在可能的地方使用传递性。对非准确匹配子图使用该比较引擎比较，以检测另外的差别。均检测到差异存储和显示的用户。该差异可能显示在不同的方式，例如周围画圈差异，突出与众不同之处通过颜色、和/或显示文本描述差异。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 方法, 生成, 控制, 检测","检测, 控制"
US05974541A,US08/898050,"一种GPIB系统，包括异步事件的通知。该GPIB应用至上提供通知请求到GPIB驱动器级软件，优选ibnotify呼叫的通知或GPIB OLE控件。NOTIFY请求包含单元描述符唯一识别GPIB设备，事件信息关于多个GPIB事件来监视，参考回调函数的GPIB的应用，和用户自定义的参考数据回调函数。响应于通知请求、GPIB驱动器级软件开始监控事件由所述事件信息。当事件发生时正在监控，GPIB驱动器级软件识别该事件并且调用回调函数。调用回调函数异步地执行对GPIB的应用。回调函数可包括一个或多个功能的一个或多个调用GPIB驱动器软件。回调函数还使用用户自定义的参考数据，以帮助处理该事件。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 处理, 设备, 系统",通用
US05974257A,US08/995948,"一种系统和方法，用于显示响应于程序建立DAQ设备配置信息。本发明优选以数据采集( DAQ )系统，其中DAQ系统包括计算机系统和至少一个DAQ设备耦合到该计算机系统。该计算机存储器包括一数据库，存储关于DAQ设备。数据库优选包括硬件数据库哪些商店硬件功能DAQ设备有关的信息，配置哪个存储当前DAQ结构。计算机也包括一个或多个DAQ软件成分本发明。当DAQ软件成分是包括根据本发明的应用程序，DAQ软件成分查询该数据库以获得关于DAQ设备(们) 。响应于这个获取的信息，第一DAQ软件成分的硬件框图，显示在显示屏上。包括一个或多个功能块的硬件方框图包含在DAQ设备采集数据，这些用于执行的任务以及在连接和信号路由在功能块。当用户编辑一个或多个特性DAQ软件成分，该组件再次询问该数据库以获得关于DAQ设备和显示更新的硬件框图，响应于用户改变。本发明因此提供配置信息发送给用户在程序建立，简化了应用开发和系统配置。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 方法, 设备, 系统, 计算, 配置",通用
US05971581A,US08/932356,"一种系统和方法，用于创建总线组态计算机系统上。使用者装配图形程序或布线图上包括多个选定的功能块进行链接的图标与一个或多个连接功能块图标。当使用者组装总线组态接线图，系统自动地创建和显示调度哪个可视地呈现调度被创建。该调度包括一个或多个附表酒吧用于图形化地或可视表明的顺序功能块。用户可以在调度的顺序表示通过图形操纵附表酒吧调度中。用户还可以设置一个或多个循环结构编辑器窗口中的哪个封装一组功能块图标。环形结构用于指定对功能块图标包含在环结构。
",NATIONAL INSTRUMENTS CORP.,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 设置, 系统, 计算",通用
US05966532A,US08/907247,"一种计算机实现的系统和方法自动生成图形代码的图解程序设计系统。计算机存储器中存储多个图形代码模板。该图解程序设计系统计算机系统上执行也包括多个面板对象或控制这些代表用户接口。一个或多个相关联的图形代码的部分或模板可以与某些控制。根据本发明，用户首先选择控制，然后优选地启动图形代码生成向导的控制。当图形代码生成向导被调用，该向导屏幕上的显示配置板或对话时，提示用户配置该控制或对象。然后,用户参数值配置特定方面的图形代码被创建。图形代码生成向导选择图形代码模板响应于控制并配置图形代码模板与参数值。图形代码生成向导接着之间创建关联的控制与配置图形码。用户可以编辑向导创建的代码或者可以使用图形代码生成向导或通过解锁该控制与代码之间的关联，并直接改变的框图。本发明还包括图形代码生成向导特别设计成用于工业自动化应用，称为MMI G向导。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"实现, 包括, 方法, 生成, 控制, 系统, 计算, 配置",控制
US05964892A,US08/920479,"本发明公开了一种软件监控器效用捕获和显示通用目的接口总线的GPIB好像要( GPIB )调用驱动软件的实时监控。该GPIB驱动软件控制通用接口总线的界面。该通用接口总线的界面耦合到一个或多个GPIB仪器。监视器应用被配置为记录和显示两个开始和结束时间的GPIB操作，在结束时间的时间被返回至GPIB的应用。另外，对于异步GPIB的呼叫，以监视公用设施记录和显示结束时间对应的异步操作。此外,监视器应用提供了选择替代监控启用模块的版本卸键器非启用版本在入口到效用。另外，提供一个选项的恢复版本non-monitor-使能在离开效用。
",NATIONAL INSTRUMENTS CORP.,其他技术,0.0,未匹配到特定技术领域,"提供, 控制, 配置",控制
US05963726A,US09/138311,"一种系统和方法，用于控制测量系统，其中本发明包括改进的仪器驱动软件体系结构。该仪器驱动软件本发明的结构提供多个特征，包括仪器可互换性，也就是，使用可互换的虚拟仪器或可互换的仪器驱动器，改进的性能，改进的属性模型，增大距离检查，提高仿真特征，等等。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 模型, 仿真, 方法, 控制, 系统",控制
US05958028A,US08/898051,"一种系统和方法，其能够使多个线程的多线程应用程序中的每个访问每个拷贝的GPIB全局变量的GPIB系统。根据本发明，多个线程使用一组特殊的线程本地GPIB呼叫执行每个线程访问。每当一个线程首先使GPIB呼叫，GPIB驱动软件开始保持私有拷贝的所有GPIB全局变量的各线程。驱动器优选地单独专用副本用于每一个线程。随后，当线程希望访问每个拷贝的一个或多个全局变量，线程使用线程本地GPIB呼叫执行全线程访问。该线程本地功用GPIB全局变量访问这些线程是ThreadIbsta，ThreadIberr，ThreadIbcnt，ThreadIbcntl 。当GPIB驱动器级软件接收调用线程本地功能来访问线程全球变量，软件确定该值的相应线程的线程的值，并与相应的线程使用。在一个实施例中，线程指定存储区，在每线程全局变量被保持。以这种方式，线程可以访问线程的全局变量直接上述指定内存区域，而无需进行线程本地函数调用。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 方法",通用
US05953511A,US08/835527,"一外围部件互连总线，其对用于经由一IEEE 1394 总线连接一PCI 设备到一主机的IEEE 1394 总线翻译者。通过使用翻译了的1394 存储地址请求与响应具有主机的分组，翻译者把起源于PCI 设备外围部件互连总线循环的地址译为1394 存储地址并通过交换1394 执行在PCI 设备和主机之间的数据传送。翻译者也把接收自主机的1394 请求包的1394 存储地址译为PCI 循环地址并在PCI 设备和主机之间执行数据传送，通过使用翻译了的外围部件互连总线循环地址在PCI 设备启动外围部件互连总线周期目标。来自顺序的外围部件互连总线写操作周期起源于PCI 设备进入一个写-递交FIFO 的翻译者传递数据，直到同意1394 总线的所有权。翻译者结合PCI 写周期数据进入一单个IEEE 1394 写请求分组并在1394 总线到主机上传输分组。翻译者，如果配置成一第一模态，通过邮寄用管道输送随后的外围部件互连总线写操作周期PCI 写周期数据进入写-递交FIFO 一旦第一1394 写请求分组的接收被主机承认但是在报以指示写交易的完成的状态的主机之前，特别地不管出现一资源冲突。响应于起源于PCI 设备PCI 读周期，翻译者预取信号通知更大量的数据，其比从主机进入一指令缓存为了满足处于具有以前的PCI 读周期的地址序列的随后的PCI 读周期在PCI 读周期里指定。来自主机的翻译者预取信号通知更多数据，一旦指令缓存变成空预定数量，为了用管道输送通过PCI 设备的数据预取的消耗和通过主机至翻译者的数据预取的传输。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"具有, 设备, 配置",通用
US05926775A,US08/946804,"一种改进的用于数据采集的软件结构( DAQ )驱动器级软件执DAQ系统。该DAQ驱动器级软件接收来电，并从上述DAQ用户应用程序执行的控制DAQ设备。该DAQ驱动器级软件包括一个或多个解释器哪个接收来电从上述DAQ应用。每一个解释器执行功能性进行共同用于多个DAQ设备，每个解释器优选地可用于多个不同DAQ设备。该DAQ驱动器级软件也包括多个小型驱动程序基元，这些基元每个执行一部分控制DAQ设备(即，每个控制硬件资源的DAQ设备。在优选实施例中，每个小型驱动器原语呈现相同的应用编程接口。本发明因此包括用于DAQ的改进架构的驱动器级软件。因此，本发明的软件体系结构提供了极大的代码重用和简化各个董事会司机。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 设备, 控制, 系统",控制
US05925109A,US08/630387,"该系统和方法简化了计算机之间的通信的计算机系统上执行的控制程序和输入/输出设备耦合到计算机系统和独立于优惠模型，其中该计算机程序的方式执行，以及输入/输出设备耦合到计算机系统，即直接例如经由扩展总线与间接地例如通过并行轻便安I/O管理器提供输入/输出操作宏，其包括第一和第二条件执行部分，连同其它相关功能。该计算机程序编译来自一个或多个源编码文件，其中该源代码文件采用输入/输出操作的宏而且拨打了其它I/O管理器的功能。该系统和方法包括一个预处理器解析第一条件执行部分输入/输出操作宏成直接输入/输出指令如果编译执行环境具有足够的特权级别和解析第一条件执行部分宏函数的调用情况。该函数使计算机系统来改用内核模式执行直接输入/输出指令。该系统和方法还包含执行编译的计算机程序包括分配存储器和居住数据结构的一个实例，该I/O管理器确定是否输入/输出设备耦合到计算机系统使得输入/输出设备寄存器直接可存取的计算机程序经由直接输入/输出指令。接着，将输入/输出操作到输入/输出设备进行是通过执行第一条件执行部分输入/输出操作宏到输入/输出设备如果输入/输出设备直接耦合到计算机系统或执行第二条件执行部分输入/输出操作宏，是一种功能，如果输入/输出设备不直接与计算机系统，其中该函数执行输入/输出操作的直接输入/输出通过执行多条指令。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 模型, 具有, 方法, 处理, 设备, 控制, 系统, 计算","通信, 控制"
US05920479A,US09/092673,"一种系统和方法，用于产生控制器械独立于接口类型的仪器，图形编程环境。该系统包括计算机系统，包括显示屏和输入设备，仪器连接到计算机系统，用于图形编程环境创建和执行程序来控制该仪器。程序设计环境包括一个对话控制，功能节点和属性节点，对象管理员框图和前面板编辑器，用于创建虚拟仪器。控制该器械包括显示在屏幕会议图标，功能节点，属性节点和布线将它们一起创建虚拟仪器。虚拟仪器，或绘画程序可以被创建，这些可跨不同的可能的输出入接口类型，例如GPIB，VXI，异步串行接口，将所述器械连接至计算机系统。对象管理器解析类程定义文件以确定可能的类与仪器与哪个有效期为班班的属性和功能。该环境中执行式传播校验以确保那个程序元素是未接线的一起无效的方式由用户为了避免程序错误。特别是，环境察看固定在那个归因于与待执行的功能与仪器有效期为类会话与仪器。进一步执行类传播环境中的对象程序，以避免编程错误。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 设备, 控制, 系统, 计算",控制
US05910905A,US08/744435,"一种系统和方法，用于检测存在分散宽带信号的实时监控。本发明使用一排匹配滤波器用于检测所接收的分散的宽带信号。每个匹配滤波器使用相应的鲁棒时间模板那个已被设计了近似于分散的宽带信号所关注的，并且每次模板改变通过任何可能的分散宽带信号时间模板。所接收的分散的宽带信号x方向(Δt )接收由每个匹配的滤波器，并且如果一个或多个火柴发生，则确定该接收到的数据是具有信号数据，所关注的位置。该信号数据然后被分析和/或传输到地球进行分析，根据需要。本发明的系统和方法将证明极有用处许多领域，包括卫星通信，等离子物理学，星际研究。不同时间的存储体中使用的模板匹配的滤波器确定如下。鲁棒时域模板的假设采取形成w (Δt ) =A cos{2&phgr (Δt )；(Δt ) } 。由于瞬时频率f t已知等于相位的导数&phgr；t，轨迹的联合时频表示的x (Δt )用作&phgr的近似值；(Δt ) 。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 具有, 方法, 系统, 检测","检测, 通信"
US05905649A,US08/717772,"一种系统和方法，用于产生控制器械独立于接口类型的仪器，图形编程环境。该系统包括计算机系统，包括显示屏和输入设备，仪器连接到计算机系统，用于图形编程环境创建和执行程序来控制该仪器。程序设计环境包括一个VISA会话控制，VISA功能节点和VISA属性节点，对象管理员框图和前面板编辑器，用于创建VISA虚拟仪器。控制该器械包括VISA会话显示屏幕上的图标，VISA功能节点，VISA属性节点和布线将它们一起产生VISA虚拟仪器。虚拟仪器，或绘画程序可以被创建，这些可跨不同的可能的VISA输出入接口类型，例如GPIB，VXI，异步串行接口，将所述器械连接至计算机系统。对象管理器解析类程定义文件以确定可能的VISA类与仪器与哪个有效期为班班的属性和功能。该环境中执行式传播校验以确保那个程序元素是未接线的一起无效的方式由用户为了避免程序错误。特别是，环境察看固定在那个归因于与待执行的功能与仪器有效期为VISA的类别相关联的会话仪器。进一步执行类传播环境中的对象程序，以避免编程错误。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 设备, 控制, 系统, 计算",控制
US05887164A,US08/870966,"一种系统和方法，用于目标来使用存储装置并且引导其主机计算机的操作系统从存储设备到目标计算机。该计算机系统包括主机连接到目标计算机，经由I/O事务的一个实施例中，在目标计算机是嵌入式系统包括智能数据采集装置。主机包括磁盘驱动器具有文件系统和一个文件作为虚拟磁盘驱动器的目标计算机，即文件基本图象，否则这些磁盘驱动器连接到目标计算机。目标计算机可以是BIOS级IBM-兼容的个人计算机。目标计算机包括选项ROM与int13H处理程序这些钩磁盘服务例程软件中断矢量的目标计算机而且转发了INT 13H请求给设备驱动器在主机计算机上执行的经由共享存储器目标计算机上。设备驱动器扇区地址转换成INT 13H请求中所指定的输入参数转换为偏移到虚拟盘文件从文件读取数据到共享存储器。该int13H处理器将数据从共享存储器到其本地存储器，如果是读请求，或从其局部存储器到共享存储器，如果是写请求。操作系统引导到目标计算机上执行的操作系统可以不同于主机。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 具有, 方法, 处理, 设备, 系统, 计算",通用
US05870088A,US08/647363,"一种编辑一个OLE控件其中做出改变控制直接图形化地互动方式。用户拖动图标表示的控制，该控制而且滴下了上形成一种容器，支持OLE控件，诸如可视Basic 。当用户停止控制，容器构建控制。当用户选择控制一个编辑交易控制创建地产对话页和显示器控制器性能对话页。用户改变直接向控件显示在该属性对话页与输入装置，例如鼠标。这些改变被应用到控制器的数据，并因此反映在控件显示在预览窗口形式的窗口。该方法特别可用于设置初始运行期值控制系统控制。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"装置, 方法, 设置, 控制, 系统",控制
US05854890A,US08/730909,"一种访问现场总线设备的数据在现场总线网络，其具有用户可选型号的数据谱系关系。现场总线设备包括上执行的用户应用程序的现场总线设备和一种壳牌功能块执行现场总线设备。该方法包括接收请求以访问数据上的现场总线设备。响应于该请求，壳牌功能块确定所有权数据的属性，其中可能的所有权属性包括用户所拥有或外壳拥有。壳体访问数据应对确定是否所有权数据的属性是外壳拥有。用户访问数据应对确定是否所有权数据的属性是用户拥有。该方法进一步包括以通知用户指针所有权壳体和该模型提供额外的灵活性。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 模型, 具有, 方法, 设备",网络
US05850571A,US08/635688,"一种系统和方法，用于在多个读周期仪器系统中具有多个中间联接总线通过转换读出周期进入写周期并利用该写记入和先进先出缓冲区能力的每一个之间的中间联接总线总线接口逻辑。当请求者设备希望发起的读取存储器位于同一或不同的总线，请求者设备首先创建数据传送分组包含关于期望的数据。请求者设备然后写入到存储器DMA逻辑位于接近期望被读取。该DMA逻辑使用32位地址提供从请求者设备获取信息关于传输，然后启动DMA写周期的执行所需的数据传送。由于每个总线接口桥包括写记入能力，写操作能够更加高效地执行比读取操作。该写记入能力要求仅一个)总线拔不出腿给定时间，因此写周期执行更加有效。本发明也能够在任一方向。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 具有, 方法, 设备, 系统",通用
US05850523A,US08/666116,"一种改进系统和方法，用于监控现场网络。该改进的方法和监视利用多个具有能够同时捕获数据分组从多个现场总线与能施加多重滤波器对任何单个总线。过滤包捕获为缴获文件并存储在监视器的存储器。滤波的分组可以被显示、实时，监视器的显示屏。改进的监视器被配置为执行捕获后过滤捕获的数据包。捕获后滤波不干扰纵火烧毁数据。改进的监视器允许动态改变滤波器设置。利用该特征，用户可通过捕捉使用第一滤波器设置，改变过滤器设置同时分组正在捕获，并改变过滤器设置现场总线而不终止捕获。改变过滤设置应用于现场总线基本上瞬时地改变与上述数据包捕获在过滤器设置显示。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"具有, 方法, 设置, 系统, 配置",网络
US05847953A,US08/717771,"一种系统和方法，用于产生控制器械独立于接口类型的仪器，图形编程环境。该系统包括计算机系统，包括显示屏和输入设备，仪器连接到计算机系统，用于图形编程环境创建和执行程序来控制该仪器。程序设计环境包括一个VISA会话控制，VISA功能节点和VISA属性节点，对象管理员框图和前面板编辑器，用于创建VISA虚拟仪器。控制该器械包括VISA会话显示屏幕上的图标，VISA功能节点，VISA属性节点和布线将它们一起产生VISA虚拟仪器。虚拟仪器，或绘画程序可以被创建，这些可跨不同的可能的VISA输出入接口类型，例如GPIB，VXI，异步串行接口，将所述器械连接至计算机系统。对象管理器解析类程定义文件以确定可能的VISA类与仪器与哪个有效期为班班的属性和功能。该环境中执行式传播校验以确保那个程序元素是未接线的一起无效的方式由用户为了避免程序错误。特别是，环境察看固定在那个归因于与待执行的功能与仪器有效期为VISA的类别相关联的会话仪器。进一步执行类传播环境中的对象程序，以避免编程错误。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 设备, 控制, 系统, 计算",控制
US05847955A,US08/880340,"一种系统和方法，用于控制基于计算机的仪表系统提供用于仪器系统简化应用开发和改进的性能。本发明提供了一种系统，包括软件架构，其限定了控制和管理的测量系统。本发明包括基本对象类，对象管理器，会话和资源类，和一个或多个资源的模板。本发明的测量系统提供多个仪器控制资源进行被用作构件块，以创建仪器驱动器和高级应用。本发明还使用面向对象技术的一种允许设备资源可以容易地串成高级应用。本发明独立于输出入接口型，操作系统和编程语言，同时还提供公共的外观和感觉一致的API提供给用户。本发明包括新颖的方法用于访问控制，事件处理，资源管理，并且资源寻址，等等。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 方法, 处理, 设备, 控制, 系统, 计算",控制
US05842006A,US08/524068,"计数器电路具有多个注册消除重编程序延迟之间的无缝切换和提供定时信号。在第一实施例中，两个寄存器预加载有分在值和控制逻辑和寄存器加载计数器。计数器断言计数终端信号以输出逻辑，这相应地断言转换成脉冲到模拟测量电路。该信号与接收到计数终端信号启停控制逻辑，控制逻辑控制操作作相应调整。这种方式，延迟值是初始载入到计数器提供初始延迟时间段在接收到启动信号，然后扫描速率值不断加载到计数器从另一寄存器用于定义该扫描速率，此后直到接收到START信号。替代地，第一扫描速率值被预先装载到第一和第二寄存器，并分别选择信号作为信号来识别哪个扫描速率来使用。这种方式，扫描速率几乎立即切换到新的速率，当栅极信号拨动以期达成无缝切换。在另一实施例中，多双寄存器和适当的控制和选择逻辑的时序信号的定义每个寄存器组。在每个存储体的两个寄存器定义低和高持续时间的相应定时信号。因此，每一个定时信号的频率和占空比全面投产开工预编程而且切换了从某时信号到另一个无缝地发生延迟。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 具有, 控制, 电路, 模拟",控制
US05841383A,US08/874064,"一条当前模式轨道和控制（顺/逆）电路提供具有一高精度。当前模式顺/逆电路包括一输入节点，以及输出节点，一电压控制电源，以及一转换的integretor 电路。输入节点被配置为接收由电压控制电源所提供的一输入模拟电流信号和一回授电流。输出节点被配置为接收由电压控制电源产生的输出量模拟电流信号。回授电流等于输出电流，都与一由开关积分器电路产生的控制电压成正比。在根据一被接收的二元的顺/逆控制信号的也一跟踪方式或一握持方式中，开关积分器电路运转。在跟踪方式中，由在回授电流和输入模拟电流之间的一个差异产生的电荷进行综合开关积分器电路。电荷的集成导致一在由开关积分器电路产生的控制电压。在控制电压反过来导致一在回授电流直到一稳定速率被达到，其中回授电流那里通过输出电流，等于输入电流。在握持方式中，电压控制电源在开关电路从跟踪方式至握持方式转换的一时间点维持在一等于输入电流常数值的输出量。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 模拟, 包括, 具有, 控制, 电路, 配置",控制
US05822554A,US08/892166,"一种系统和方法，用于复用多个数据输出到数据总线使用大小可变的多路复用器，多路复用器不能需要甚至2 。用晚餐。 N头数输入。这使分组相同的寄存器可以更容易地和有效地复用在一起。其中该复用系统包括多个寄存器，每个寄存器提供八位数据到8比特地址译码逻辑母线注册从总线接收并输出多个寄存器选择信号选择在那个寄存器的写入。该寄存器选择信号也与每个相应寄存器的输出，以形成内部9-bit总线输出从每个寄存器。该9-bit总线内部输出从多个寄存器耦合穿过一层或多层多路复用逻辑提供输出到八位数据总线的代替使用标准2 。用晚餐。 N复用器具有N个选择线和2 。用晚餐N输入，本发明使用复用器的任何大小不一定是2的倍数。该复用器的尺寸最好基于分组相同的寄存器。因此，本发明使用复用器，其接收许多X个输入的，其中X未必倍数。这允许大小可变复用器系统中使用的，减少系统部件，并简化了设计过程中。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 具有, 方法, 系统",通用
US05815690A,US08/916500,"抗尖峰脉冲电路用于滤波输入信号的错误转换基于时钟信号的转换。多个存储器装置作为被提供用于检测该输入信号是测定23和五个时钟信号的转换在该优选实施例。可编程逻辑责还提供用于选择在这三种情况。在优选实施例中，六个单独的倒装芯片弗洛普包括由时钟信号和时钟，四个触发器的复位是在假否定输入信号君主统治这种转变，并且在两个四个触发器记录上班的时间上升沿而另外两个记录上班的时间下降沿的时钟信号。该可编程逻辑责优选包括一多路复用器，用于选择在三个案例。在优选实施例中，输入信号是数据有效( DAV ) GPIB的信号，在抗尖峰脉冲电路保证有效数据的GPIB取样。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 装置, 检测, 电路",检测
US05796963A,US08/648880,"一种系统和方法，用以转换VXI块传送循环到PCI突发循环。本发明还推广之间转换这些使用任何各种总线块传送循环和突发循环。总线桥或接口单元，耦接于VXI总线的PCI汇流排，该总线桥包括VXI到PCI适配器变换成VXI模块循环到PCI突发循环。总线桥包括缓冲器，接收一VXI块传送循环并VXI循环。总线桥还包含控制电路，哪些标签数据的接收VXI块传送循环，以便标识且隔开了不同VXI块传送。一旦所有周期的给定传输具有被标记，它们被沿着PCI总线作为PCI爆裂周期。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 具有, 方法, 控制, 系统, 电路",控制
US05796721A,US08/666117,"一种改进系统和方法，用于监控现场网络。该改进的方法和监视利用多个具有能够同时捕获数据分组从多个现场总线与能施加多重滤波器对任何单个总线。过滤包捕获为缴获文件并存储在监视器的存储器。滤波的分组可以被显示、实时，监视器的显示屏。改进的监视器被配置为执行捕获后过滤捕获的数据包。捕获后滤波不干扰纵火烧毁数据。改进的监视器允许动态改变滤波器设置。利用该特征，用户可通过捕捉使用第一滤波器设置，改变过滤器设置同时分组正在捕获，并改变过滤器设置现场总线而不终止捕获。改变过滤设置应用于现场总线基本上瞬时地改变与上述数据包捕获在过滤器设置显示。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"具有, 方法, 设置, 系统, 配置",网络
US05784275A,US08/716725,"一种系统和方法，用于产生控制器械独立于接口类型的仪器，图形编程环境。该系统包括计算机系统，包括显示屏和输入设备，仪器连接到计算机系统，用于图形编程环境创建和执行程序来控制该仪器。程序设计环境包括一个VISA会话控制，VISA功能节点和VISA属性节点，对象管理员框图和前面板编辑器，用于创建VISA虚拟仪器。控制该器械包括VISA会话显示屏幕上的图标，VISA功能节点，VISA属性节点和布线将它们一起产生VISA虚拟仪器。虚拟仪器，或绘画程序可以被创建，这些可跨不同的可能的VISA输出入接口类型，例如GPIB，VXI，异步串行接口，将所述器械连接至计算机系统。对象管理器解析类程定义文件以确定可能的VISA类与仪器与哪个有效期为班班的属性和功能。该环境中执行式传播校验以确保那个程序元素是未接线的一起无效的方式由用户为了避免程序错误。特别是，环境察看固定在那个归因于与待执行的功能与仪器有效期为VISA的类别相关联的会话仪器。进一步执行类传播环境中的对象程序，以避免编程错误。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 设备, 控制, 系统, 计算",控制
US05765949A,US08/770009,"一种方法和装置，用于减少其影响地面回路热电偶测量装置利用其的差分放大器信号放大。电阻器耦合在输入差分放大器以提供DC接地参考。电容器并联耦合到电阻以补偿交流噪声防止输入从通用模式范围中漂浮出来。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 装置",通用
US05764546A,US08/756401,"数据采集配置系统和方法便于创建数据的应用。本发明使用户信道配置信息来创建这些包括用于各自的DAQ硬件频道，例如物理现象是测量和如何情事正在被测量。用户然后分配收报人名址各自信道配置。该路配置名称然后可用于数据采集应用指定该信道配置，简化应用程序开发。在指定声道指派名称，该用户然后构建一个程序，控制数据采集系统。该程序构建包括提供赋值名字指定信道配置所选信道的数据获得设备。该程序可以被执行以执行数据采集操作，其中执行包括使用信道配置参考由赋值名字执行数据采集操作。因此，用户不需要以创建代码来指定信道配置。此外,该程序能够执行单位被测量的物理量/产生根据声道，并且用户不需要创建代码以物理量的单位被测量/产生。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 方法, 设备, 控制, 系统, 配置",控制
US05751536A,US08/609942,"隔离系统允许单个电路测量两个低和高电压信号从分离的低和高的电压总线，分别保持基本上隔离保护人或动物可能暴露于低压总线上中心分割偶合到测量电路之间选择低和高的电压总线，因此提供一个级别的隔离。两个势不两立继电器在中继站提供另一级隔离。由于中心与两个势不两立继电器均为额定值为该基本绝缘标准，低和高电压总线相隔至少两个中继级，从而满足双重绝缘或高压验电标准在正常操作。用于额外的保护，将一个或多个继电器失效，双火花隙限制最大电压到中间电压电平。箝位电路来进一步限制该电压出现在低压总线低于危险电压水平。火花隙保护出现的最大电压穿过箝位电路与上述钳位电路来进一步限制两端的最大电压低压总线上，而且串联熔断器隔离高电压从低压总线如果高压危险情况存在相当长的时间。因此，在至少三个保护水平被提供。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 提供, 电路",通用
US05748916A,US08/529692,"一VXI 设备，其智能地监控在VXI 总线上的交易并在可能的地方在总线上早开始循环，因此提高系统性能。VXI 0device 包括一VXI 总线主控器装置和一VME总线请求者。当总线主控希望总线时，VXI 总线主控对其VME 请求者发出一个设备需求总线信号，其引导请求者控制住用于设备或主的VXI 总线。然后VME 请求者试图控制住用于总线主控的总线。根据本发明，VME 请求者也监控VXI 总线并向通知主是否它将失去总线的主生成总线释放信号。总线释放信号提供指示到总线主控，无论总线主控可以早开始在总线上的循环。因此，本发明提供了耦合到VXI 总线的VXI 控制器和设备的改良性能。这提供仪表的改良性能和测验测量申请，以及使用一个VXI 或VME 的总线的其他应用程序。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 装置, 生成, 设备, 控制, 系统",控制
US05734876A,US08/473200,"时间戳定时器的GPIB系统，包括计数器提供流逝时间值而且捉到了逻辑捕获GPIB样本在预定的条件下和用于捕获最大中央处理机的在倾翻的计数器。该GPIB样品和时间值的优选被捕获到先入先出( FIFO )缓冲器。捕获逻辑优选地复位计数器之后GPIB信号被捕获，由于预定的条件，从而每个值代表之间的经过时间GPIB样本。如果计数器到达翻倒状态时，捕获逻辑使得另一捕获进入缓冲器用于捕获最大时间值。因此，任何连续最大经过时间的值相加，以确定最终中央处理机的经过时间两两之间有效GPIB捕获。在一个实施例中，标记来标记每一个逻辑首先出现最大中央处理机的来通知软件计时器的倾翻。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"系统, 提供, 包括, 处理",通用
US05734261A,US08/740963,"一种用于仪表系统输入保护电路接收一输入信号以及哪些是适于提供电流和/或电压限度版本的输入信号与测量系统。本发明的输入保护电路使用光耦合器，并且还包括光耦合器电路保护免于损坏，如果过压状况进行检测。输入保护电路包括一比较器，可操作用于禁用或关闭光耦合器当电流限制版本的输入信号超过预设值。输入保护电路最好包括并联连接的电阻器光耦合器。其提供了备选路径输入信号当光耦合器已经被切割了进攻，本发明因此提供改进的输入保护电路哪个保护光耦合器不受损坏，如果过压状况被检测到，但仍允许待测量的输入信号期间。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 系统, 检测, 电路",检测
US05724272A,US08/238480,"一种方法和装置用于控制系统和设备，用于向用户提供该能力来开发应用软件仪器驱动器和用于控制仪器操作系统。本发明提供了一种系统，包括软件架构，其限定了控制和管理的测量系统。本发明的方法利用与设备资源无关的方法由此个人能力的设备分解为多个对象被称为资源和这些资源是则使用了来开发仪器驱动器或仪器控制的应用。本发明的方法允许进行也有采用面向对象技术的设备资源可以容易地串成高级应用。本发明独立于输出入接口型，操作系统和编程语言，同时还提供公共的外观和感觉一致的API提供给用户。本发明包括新颖的方法用于访问控制，事件处理，资源管理，并且资源寻址，等等。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 装置, 方法, 处理, 设备, 控制, 系统",控制
USD0387750S1,US00/020282,"FIG. 1壳体结构的透视图图标的显示屏编程计算机系统显示我的新型设计；
FIG. 2是其正视立体图；
FIG. 3是放大的前视图，其与剖线的公开被省略。
虚线公开在计算机屏幕上FIGS 。1和2仅仅用于说明性的目的，并且不形成一部分要求的设计。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"系统, 计算",通用
US05694333A,US08/425765,"一种系统和方法，用于执行更有效的硬件关联开关计算机控制的测试设备系统，包括一计算机系统控制多个仪器。该测量系统包括直接存储器存取传送装置的进行各种数据的传送在计算机系统与各种器械。该系统还包括多个计算机系统中执行的进程运行的窗口通过公共以映射循环到该计测用总线到各种器械。每个进程或线程执行CPU需要特定的上下文，与DMA传送装置自动地对自身进行配置给不同的上下文平行于操作系统上下文改变。每个过程包括对应的上下文信息存储在存储器中。当操作系统调用切换进程的CPU，操作系统写入地址的上下文信息到DMA传送装置。该DMA传送装置读取该上下文凭记忆和自动配置本身平行于上下文执行的改变操作系统。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 测试, 装置, 方法, 设备, 控制, 系统, 计算, 配置","测试, 控制"
US05678063A,US08/423469,"一种系统和方法，用于快速地传送大量的小尺寸基准对非时序址instumentation系统。根据优选实施例，主机包括多个测试矢量存储在存储器控制设备运行的附加仪器。主处理器产生一块地址/数据对响应于所述测试矢量，每对包括数据和目的地址。该块存储在存储器与该块的地址提供给随机写引擎。随机写引擎随后分配该数据到各自的器械在适当的地址具有最小的处理器的发明。因此，大量小型数据可迅速转移到非时序地址而不配料处理器。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 测试, 具有, 方法, 处理, 设备, 控制, 系统","测试, 控制"
USD0384052S1,US00/020281,"FIG. 1透视图一会儿回路图标的显示屏编程计算机系统显示我的新型设计；
FIG. 2是其正视立体图；
FIG. 3是放大的前视图，其与剖线的公开被省略。
虚线公开在计算机屏幕上FIGS 。1和2仅仅用于说明性的目的，并且不形成一部分要求的设计。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"系统, 计算",通用
USD0384051S1,US00/020025,"FIG. 1透视图层序结构图标的显示屏编程计算机系统显示我的新型设计；
FIG. 2是其正视立体图；
FIG. 3是放大的前视图，其与剖线的公开被省略。
虚线公开在计算机屏幕上FIGS 。1和2仅仅用于说明性的目的，并且不形成一部分要求的设计。
",National Instruments Corporation,其他技术,0.0,未匹配到特定技术领域,"系统, 计算",通用
US05664092A,US08/531291,"一种系统和方法，用于执行加料循环试验并设定了循环的系统具有多个中间联接总线，其中,一个或多个总线没有支承锁定机构测试并设定了循环。一种CPU执行一个或多个过程的一个或多个首车连接到那些不支承锁定机构测试并设定了循环。这一个或更多首车通过测试与置位本发明的装置向一个或多个第二总线测试哪个支承锁定机构并设定了循环。存储器耦合到一个或者多个第二总线，包括一臂板信号位共享执行的多个线程或进程的CPU 。该测试与置位设备执行加料循环试验和设置操作在信号量(一个或多个)位在该存储器中，在该CPU上执行的线程。进程或线程开始测试与置位操作通过设置寄存器的一位在该测试与置位装置然后读取至测试与置位那映射到目标存储器装置在相应的臂板信号比特的位置。响应于读取，测试与置位设备执行读/写操作的锁定至目标使用原子读/写协议访问哪个锁定从其它过程。该测试与置位装置然后返回所读取的数据获得来自目标存储器臂板信号位到CPU 。第一总线读取循环被维持直到测试与置位装置完成了测试与置位操作发送到相应的臂板信号咬破该存储器。在这种方式中，一个或多个首车锁定测试与置位操作期间，甚至在一个或多个首车不会固有地支持测试与置位操作。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 测试, 装置, 具有, 方法, 设置, 设备, 系统",测试
US05659749A,US08/437025,"一种系统和方法，用于执行更有效的硬件关联开关计算机控制的测试设备系统，包括一计算机系统控制多个仪器。该测量系统包括直接存储器存取传送装置的进行各种数据的传送在计算机系统与各种器械。该设备包括用于不同硬件关联直接存储器存取传送差别迁移，与DMA传送装置自动地对自身进行配置给不同的上下文与最小CPU的参与。对于每个进程或线程需要不同的DMA传送装置的情况下，CPU准备上下文信息存储在上下文存储器中。该CPU然后请求列表到DMA传送装置包括交错上下文指针和数据传送分组。当DMA传送装置进行传送和需要切换到新的上下文，该上下文凭记忆读取DMA传送装置和自动配置本身。因为，上下文被存储在存储器中，因此容易获得，该DMA传送装置干不必中断CPU接收新的上下文。一旦CPU具有准备和存储，并提供该上下文信息，该上下文信息的地址向DMA传送装置，该传送装置可在DMA传送上下文对应于不同进程或线程许多次而不CPU的参与，提高了系统效率。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 测试, 装置, 具有, 方法, 设备, 控制, 系统, 计算, 配置","测试, 控制"
US05649129A,US08/475067,"一种GPIB电路卡包括GPIB控制器和GPIB分析仪对同一电路板上。组合控制器并剖析了允许单个连接与GPIB和仅使用单个I/O插槽的主计算机。总线接口电路系统卡允许两个控制器并剖析了传达到主机使用该相同总线连接器。该GPIB控制器优选地独立于同时与GPIB分析器。因此，根据本发明的GPIB电路卡节省计算机资源。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"包括, 控制, 系统, 计算, 电路",控制
US05648918A,US08/458974,"校准系统用于校准多个激发源的测量系统。该校准系统允许单个校准调节校准所有的激发源同时而不是必须独立地调整每个激励源。一种可调式齐纳二极管连接到电位计形成可调节的校准电路将参考电压提供至多个电流激励源。每个电流源优选包括缓冲器和三极管控制电路接收参考电压，用于控制电流流经的精度电阻器。在这种方式中，只有精密电阻针对每个信道，在校准调节机构设置于单回路尽管通道。这显著减少了电路和成本，同时还简化校准过程到单个调整。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 设置, 控制, 系统, 电路",控制
US05619702A,US08/695968,"编程硬件寄存器的方法和设备，使用数据库界定每一硬件寄存器而且关联了比特域的寄存器，输入代码包括位字段写入( BFW )命令识别比特字段的值与相应的进位的字段，并且限定了一系列软件拷贝的硬件寄存器，预处理器用于写入适当的代码来编程该硬件寄存器。数据库提供名称、大小和地址的每个寄存器与名称和大小字段相关联的比特在每个寄存器。源代码预处理器发电容量通过更换BFW与代码命令来操纵软件拷贝的硬件寄存器和写入软件拷贝到硬件寄存器。输出源代码中识别每个受影响的寄存器而且书写了适当的代码访问注册一次，每BFW命令。该数据库定义与上述BFW命令允许优良的组织和简化，以减少编程时间，成本和潜在的差错。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 方法, 处理, 设备",通用
US05583988A,US08/208559,"一种方法和装置用于在程序执行期间执行运行期检查编译环境中使用全ANSI-C编程语言。本发明检测多个错误在运行时期间发现的那些不能由编译器正在那个时刻一个相应的C语言约束被违反。本发明还提供用户直接指示问题，节约了调试时间。该运行期检查本发明的特征，进一步检测当用户正在使用库函数不正确。当C源代码被编译时，本发明分配特别数据结构的每个指针，阵列和结构对象程序中。关联这些对象之间进行，其特殊数据结构在编译器符号表中。在运行时间，这些数据结构包含状态信息，关于其相关联的对象。本发明还插置了专用机语言教学的C表达在编译期间修改中的任一值或数据结构的根据本发明的内部运行期检查呼叫功能，使用这个信息相应的数据结构，以确定是否表达为非法和报告错误，如果需要的话。该运行期检查本发明的特征包括用于指定精确的对自变量那个可以被传送到库函数。这些限制用于判断自变量到库函数符合其各自报告任何违反限制向用户在运行时，指示自变量引起错误和哪些约束被违反。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 装置, 方法, 检测",检测
US05572525A,US08/455507,"一种GPIB总线扩展器哪个智能地支持IEEE 488 。2发现听众协议。本发明的该GPIB总线扩展器监视总线，并在总线上存在一个谈话者在GPIB扩展器检测说话者出现在总线，扩展器配置本身作为收听者接收任何消息，用于设备的远程扩展器在GPIB母线不干扰检测说话者出现在总线，扩张件不会干扰将其自身配置为收听者，而准备将参与发现监听协议。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"检测, 设备, 配置",检测
US05313622A,US08/087965,"提供一种设备结合使用的控制系统，其能提供一个或多个相应的输出指令和一个或多个相应的时序指令，该设备包括计数器用于计数序列提供用于基本连续地提供有点经过时间，在规定的时间间隔；存储装置，响应于匹配指示，用于存储多个相应的输出指令和相应的多个相应的时序指令，以及用于声明各自的输出指令和相应的时序指令，按照序列以便在接收到匹配指示，下一个相应的输出指令和相应的下一相应时序指令序列的断言；以及一比较器，比较每个相应声明指令到时序计数的计数序列和提供匹配指示在发生匹配。
",NATIONAL INSTRUMENTS,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 装置, 设备, 控制, 系统",控制
US05287528A,US07/548293,"IEEE 488接口，连接微处理器与一个或多个设备与IEEE 488总线的消息传输时的速度可由重叠的部分T1总线稳定时间与微处理器的写入脉冲，是什么时候已知那个数据的微处理器的总线有效的预定的时间段之前写入的后缘。改进的握手同步是通过产生中断信号以通知微处理器，此微处理器的最后一个字节消息已被接收。该计数终端信号由微处理器产生的的DMA控制器用以检测多个字节一个人所抱的目标发送的消息，该微处理器。界面自动生成EOI信号IEEE 488总线当最后一个字节消息声明的IEEE 488总线数据线。这大大简化了传输多个字节消息由微处理器相比现有技术所使用的协议的接口。该DMA控制器的计数终端信号也用来防止由设备发送的数据的IEEE 488总线到微处理器，其通常发生在当微处理器必须服务于另一设备当中接收长报文原来设备。丢失被阻止，通过检测中断点的消息传输，也就是，当DMA计数终端信号变为活动，然后拖延第一设备断定""data valid"" 。
",NATIONAL INSTRUMENTS CORPORATION,其他技术,0.0,未匹配到特定技术领域,"生成, 处理, 设备, 控制, 检测","检测, 控制"
US04901221A,US06/851569,"用于编程计算机系统具有显示控制台显示控制至少一个虚拟仪器和器械的步骤在屏幕上显示至少一个第一功能图标的那个参考至少一个第一控制模块，用于控制至少一个第一功能；在屏幕上显示至少一个迭代图标那个参考文献迭代控制模块，用于控制多个迭代数据流；在屏幕上显示至少一个第一输入变量的引用图标那至少一个第一输入变量；在屏幕上显示至少一个第一输出可变图标那个参考至少一个第一输出变量；而且集结了屏幕上第一无环的数据流图包括至少一个第一功能的图标与至少一个迭代图标与至少一个第一输入可变图标与至少一个第一输出可变图标，以便图示显示第一过程产生至少一个用于至少一个第一输出可变图标与至少一个用于至少一个第一输入可变图标，由此，该至少一个迭代图标图表示的多次迭代至少一个第一功能在第一程序。
","NATIONAL INSTRUMENTS, INC.",其他技术,0.0,未匹配到特定技术领域,"包括, 具有, 控制, 系统, 计算",控制
