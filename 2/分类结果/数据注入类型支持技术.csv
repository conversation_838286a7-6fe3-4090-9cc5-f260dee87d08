﻿公开(公告)号,申请号,摘要(中文),原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
US20180276175A1,US15466150,"一种网络接口外围设备(NIP)可以包括用于与网络通信的网络接口以及用于与处理器子系统通信的互连接口。 NIP中的外围数据缓冲器(PDB)可以保持NIP从对端外围设备接收的数据和/或由NIP向对端外围设备分布的数据，并且网络数据缓冲器(NDB)可以保持NIP向网络发送的和/或从网络接收的经调度数据流的有效载荷数据。 NIP中的数据处理器可以从PDB中的数据生成有效载荷数据，并且根据经调度数据处理器发送事件将有效载荷数据存储在NDB中。数据处理器可以从NDB中的有效载荷数据获得数据，并且根据经调度数据处理器接收事件将所获得的数据存储在PDB中。 NIP可以包括在设备级(可以包括NIP的设备的)操作并由集中式系统配置实体控制的镜像有限状态机，以管理NIP的配置并将NIP的内部配置与网络配置流协调。
",NATIONAL INSTRUMENTS CORPORATION,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"包括, 生成, 处理, 设备, 控制, 系统, 配置","通信, 网络, 控制"
US09246852B2,US14072297,"用于将迭代的基于时间的数据采集(DAQ)操作映射到网络的等时数据传输信道的系统和方法。可以配置与网络的等时数据传输信道相关联的时间敏感缓冲器(TSB) 。可以配置数据速率时钟和本地缓冲器。功能单元可以被配置为发起迭代的基于时间的DAQ操作的连续执行，将数据传输到本地缓冲器，在配置的开始时间发起数据在本地缓冲器和TSB之间的传输，以及以迭代的方式重复传输和发起传输，从而在本地缓冲器和TSB之间传输数据。 TSB可以被配置为通过网络的等时数据传输信道传递数据，从而将迭代的基于时间的DAQ操作映射到网络的等时数据传输信道。
",NATIONAL INSTRUMENTS CORPORATION,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"系统, 方法, 配置","数据传输, 网络"
EP2598988A1,EP11717422.7,"用于传输数据的系统和方法。显示系统图，其中，系统图包括对应于相应的设备的多个设备图标，每个设备图标具有指定用于在对应的设备上部署的相关联的可执行功能节点。功能节点互连以形成在设备上以分布式方式可部署和可执行的分布式图形程序。接收对系统图的指定功能节点之间的等时数据传输的用户输入。基于指定的等时数据传输来自动确定功能节点之间的调用定时关系，所述调用定时关系包括功能节点的执行之间的阶段关系。在功能节点之间显示确定的调用定时关系。根据确定的调用定时关系，图形程序在设备上以分布式方式可部署和可执行，其中，在图形程序的执行期间，数据在功能节点之间等时传输。
",National Instruments Corporation,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"包括, 具有, 方法, 设备, 系统",数据传输
US20130031494A1,US13633405,"一种系统和方法，用于创建和使用型一般绘画程序。所述方法可以包括存储第一图形程序存储介质上。第一图形程序可能已经创建了基于用户输入。第一图形程序可以包括多个节点之间的互连节点，所述多个节点以及节点之间的互连可以型通用。可以接收用户输入指定一个或多个数据类型的至少一个输入和至少一个输出，第一图形和/或程序。数据类型可以与第一图形程序响应于所述用户输入指定一个或多个数据类型。
",NATIONAL INSTRUMENTS CORPORATION,数据注入类型支持技术,1.0,关键词匹配: 数据类型,"系统, 方法, 包括",通用
EP1755054A3,EP06016944.8,"一种用于存储和检索来自测量、模拟和/或其他技术数据处理活动的技术测量数据的方法，包括：将测量数据存储在多个数据文件中，其中所述存储的测量数据包括存储与测量数据相关联的元数据，其中元数据描述测量数据的特征，特别是时间戳信息和/或测量类型和/或推荐类型的分析的描述；接收对测量数据的查询，其中查询包括与测量数据相关联的元数据的参数；以及根据查询搜索多个数据文件的索引，其中索引是根据元数据创建的，其中与每个数据文件的文件类型相关联的插件用于从每个数据文件检索元数据。
",National Instruments Corporation,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"方法, 包括, 处理, 模拟",通用
EP1665034A2,EP04780980.1,"本发明提供了一种用于为图形程序(例如模型)的选定参数提供图形用户界面(GUI)的系统和方法。分析所述程序以确定多个参数，所述多个参数例如被显示在列表、树线图、调色板等中。接收选择所述多个参数中的一个或多个参数的用户输入。生成用于所述一个或多个参数的GUI，所述GUI包括分别与所述一个或多个参数对应的一个或多个GUI元素，例如控件和/或指示符，例如相对于数据类型分析所述一个或多个参数，并且基于所述分析确定所述一个或多个GUI元素，例如通过用户选择从响应于所述分析呈现的多个GUI元素中选择所述一个或多个GUI元素，所述一个或多个GUI元素被添加到所述GUI中并与所述一个或多个参数相关联。在所述图形程序的执行期间，所述一个或多个GUI元素访问对应的参数。
",National Instruments Corporation,数据注入类型支持技术,1.0,关键词匹配: 数据类型,"提供, 包括, 模型, 方法, 生成, 系统",通用
US06829733B2,US09851033,"一种改进的方法和系统，用于检测第一和第二测试执行顺序文件互差在计算机系统中。每一个测试执行顺序文件可以包括多个相互关联的对象。该物体可以是比较和区别对象可以被显示。该物体可以包括以下中的一个或多个：一种序列；一种全球变量；和/或数据类型。序列可以包括:一个步骤，参数，和/或局部变量。步骤的序列可包括树状结构的步骤的性质。每个步骤的特性可以包括以下的一个或多个：一属性值，属性标记，和/或属性评论。对象可以包括对象的层次(例如，父对象和子对象的儿童) 。差分级可以检测物体。差异可以是导航。每个显示的差异可以称为插入或删除。
",NATIONAL INSTRUMENTS CORPORATION,数据注入类型支持技术,1.0,关键词匹配: 数据类型,"包括, 测试, 方法, 系统, 检测, 计算","检测, 测试, 导航"
US06721677B2,US09920614,"一种方法和系统，用于模块化存储测量流的流使用分层结构的对象。第一应用程序可以测井测量多个数据类型的数据到共享存储器位置在第一计算机中，独立于数据的类型。两类数据和索引提供给数据可被记录。第二应用可以基本上同时趋势此数据作为数据被记录。应用都可以包括多个过滤器的物体，包括分级流水式处理对象，它可以工作到日志/趋势数据。趋势可以包括:( 1 )产生查询；( 2 )来确定位置使用该索引；( 3 )访问该数据从共享存储器中；和( 4 )显示该数据。可以将数据复制到一个存档数据库，独立于数据的类型。复制的数据可以被传送到多个计算机。
",NATIONAL INSTRUMENTS CORPORATION,数据注入类型支持技术,1.0,关键词匹配: 数据类型,"提供, 包括, 方法, 处理, 系统, 计算",通用
US06412028B1,US09286701,"一种USB-based数据采集系统，包括虚拟DMA软件，其增加了USB数据传输的速率以最小的改变到DAQ驱动器级软件。虚拟DMA软件运行接收或拦截DMA提供的指令DAQ驱动器级软件来编程DMA控制到获得数据的装置。虚拟DMA软件拦截DMA指令而且仿真了DMA操作的控制器的软件。虚拟DMA请求该数据的软件运行大量分组从装置。虚拟DMA软件存储所接收的数据包中指定的地址的DMA指令。虚拟DMA软件因此仿真操作DMA装置，因此&ldquo；fooling&rdquo；该DAQ驱动器级软件进入思维数据正在被获取的使用真实DMA传送。因此，本发明允许更快了数据的传送，同时需要最小的或改变现有DAQ驱动器级软件。
",NATIONAL INSTRUMENTS CORPORATION,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"提供, 包括, 装置, 仿真, 控制, 系统","数据传输, 控制"
US06067584A,US08/711128,"数据采集系统包括计算机系统，耦合到数据采集装置，数据采集计算机系统上执行的应用，设备接口软件具有属性-基于API执行在计算机系统上。该API使得基于属性-数据采集应用程序执行台阶式密封控制采集数据的装置。其步骤为:创建一个任务，用于控制数据获取设备的设定值的属性的任务和设备，启动该任务获取数据，并且破坏了所述任务。创建包括分配给该任务的属性包括多个属性对象的属性相对应的数据采集仪和属性表引用多个属性对象。属性对象包括环境包含数据的属性的值和设定值的函数的属性。属性表包括属性表条目。每一个属性表入口引用各自的属性对象。属性表进行索引，由一部分唯一属性号码相关联的每个属性。查找相关的时间调用属性集功能无关的常数，多个属性。每个属性具有一个相关联的独特数子的多个部分指定数据类型和I/O类型的属性，并且当属性可以是组，上述指标属性到适当的属性表。新的属性可被添加到该API和现有的应用可以链接到或调用新的API的功能，而无需修改。
",NATIONAL INSTRUMENTS CORPORATION,数据注入类型支持技术,1.0,关键词匹配: 数据类型,"包括, 装置, 具有, 设备, 控制, 系统, 计算",控制
US06052743A,US08/968221,"一种物体缓冲进行智能缓冲作用在数据采集( DAQ )系统。该物体缓冲存储关于待传数据迭代器，并包括一个或多个用于执行突发传输。当DAQ用户应用程序产生呼叫执行数据传送操作的DAQ系统，驱动器级软件创建或实例化物体缓冲。该物体缓冲包括数据格式信息指定正在传送的数据的格式，包括数据的大小，样本数目每扫描，扫描数目的数据。该驱动器级软件产生前景迭代器执行的之间传递数据的客户机部分系统存储器和缓冲部分的系统存储器。该驱动器级软件产生背景迭代器执行的之间传递数据的系统存储器的缓冲部分与上述单板存储器包含在该DAQ设备。前景与背景迭代器迭代器执行响应于DAQ用户应用程序进行数据的传送从DAQ设备。前景和背景迭代器也有采用信息存储在缓冲器对象执行数据传送。在优选实施例中，前景迭代器的CPU执行用户模式中，背景迭代器执行在内核模式的CPU 。
",NATIONAL INSTRUMENTS CORPORATION,数据注入类型支持技术,1.0,关键词匹配: 数据格式,"系统, 包括, 设备",通用
US05937175A,US08/826925,"PCI总线IEEE 1394到总线译者耦合到PCI设备与主机通过IEEE 1394总线，该翻译器翻译地址周期中通过PCI总线的PCI设备为1394，并进行数据的存储器地址之间的传输PCI设备和主机通过交换请求和响应分组1394与主机1394利用翻译后的存储器地址。翻译器1394 1394请求分组又翻译了存储地址从所述主计算机接收的循环进入PCI地址并进行数据传输的PCI设备和主机之间通过PCI总线循环发起针对该PCI设备使用已翻译的PCI总线周期地址。翻译器柱数据来源于顺序PCI总线写周期由PCI装置进入写入发送FIFO的所有权直到准予该翻译器1394总线与PCI写周期数据组合成单个IEEE 1394写请求分组并将分组1394总线到主计算机。翻译器，如果配置第一模式，随后的PCI总线写入周期流水线邮寄该PCI写周期数据在写入发送FIFO一旦接收到第一写请求分组1394已经确认主机之后但在主机的响应是状态指示完成事务，尤其是资源冲突是否发生。响应于发起的PCI读周期PCI设备，翻译器预取比较大量的数据中所指定的PCI读周期从主机到PCI预取FIFO为了满足随后的读周期中哪些处于地址序列与先前的PCI读取周期。翻译器预取更多来自主机一旦预取FIFO变为预定量空，以便管道消耗该预取的数据通过PCI设备与传输该预取的数据由主计算机提供给翻译器。
",NATIONAL INSTRUMENTS CORPORATION,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"提供, 装置, 设备, 计算, 配置",数据传输
US05875313A,US08/826920,"PCI总线IEEE 1394到总线译者耦合到PCI设备与主机通过IEEE 1394总线，该翻译器翻译地址周期中通过PCI总线的PCI设备为1394，并进行数据的存储器地址之间的传输PCI设备和主机通过交换请求和响应分组1394与主机1394利用翻译后的存储器地址。翻译器1394 1394请求分组又翻译了存储地址从所述主计算机接收的循环进入PCI地址并进行数据传输的PCI设备和主机之间通过PCI总线循环发起针对该PCI设备使用已翻译的PCI总线周期地址。翻译器柱数据来源于顺序PCI总线写周期由PCI装置进入写入发送FIFO的所有权直到准予该翻译器1394总线与PCI写周期数据组合成单个IEEE 1394写请求分组并将分组1394总线到主计算机。翻译器，如果配置第一模式，随后的PCI总线写入周期流水线邮寄该PCI写周期数据在写入发送FIFO一旦接收到第一写请求分组1394已经确认主机之后但在主机的响应是状态指示完成事务，尤其是资源冲突是否发生。响应于发起的PCI读周期PCI设备，翻译器预取比较大量的数据中所指定的PCI读周期从主机到PCI预取FIFO为了满足随后的读周期中哪些处于地址序列与先前的PCI读取周期。翻译器预取更多来自主机一旦预取FIFO变为预定量空，以便管道消耗该预取的数据通过PCI设备与传输该预取的数据由主计算机提供给翻译器。
",NATIONAL INSTRUMENTS CORPORATION,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"提供, 装置, 设备, 计算, 配置",数据传输
US05315706A,US07/889596,"一种改性IEEE 488.1总线接口的增加，通过为大量为八的因子，进行葬埋仪数据的传送可以被实施。总线接口状态机呈现ANSI/IEEE Std 488.1-1987已作修改，以便如果所涉及的设备特定数据传送是配备成处理，再将修改的数据传输高速数据传输的方法用于以使较高速度地传输多路信息比否则将是可能的。如果所涉及的设备特定数据传送干没有接口装备来处理高速数据的传输，该条件被自动地检测通过该接口具有高速能力，再将标准数据传输的方法学使用。高速数据传输是完全透明的方式对控制器软件，因为它不要求任何对控制器软件也不向设备驱动程序和设备应用程序。
",NATIONAL INSTRUMENTS CORPORATION,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"具有, 方法, 处理, 设备, 控制, 检测","检测, 数据传输, 控制"
US05301301A,US07/647785,"计算机系统被编程为计算由用户执行数据流的数据流框图，通过构造一种利用功能的图标。至少一个子集的功能图标的多态相对于数据类型和相对于数据聚合。多态功能的执行图标是通过执行单一数学运算，当其输入纯量场，当该图标上执行的元件当其输入为阵列或标量及阵列。具有群集输入，多态功能的图标上执行的组件部件的效率。输出类型多态功能的图标是由它们输入类型之前执行该图。系统自动构建连接图包括图标与连接点的一个标记与该名称相关联的面板控件和指示器。前面板控制可以被隐藏，从而使相关的值恒定。以方便使用了图标，二维调色板用户构建图标自动构建用户从目录构建的图标。另外，虚拟仪器的名称相关联的图标被显示当鼠标光标最接近的图标。该系统还层次示图，其中图标被排列成使得每一图标具有连接到与它在其所有图标的框图。数据流框图可以被锁定以防止其修改，同时仍然允许数据流框图来执行的。
",NATIONAL INSTRUMENTS CORPORATION,数据注入类型支持技术,1.0,关键词匹配: 数据类型,"包括, 具有, 控制, 系统, 计算",控制
