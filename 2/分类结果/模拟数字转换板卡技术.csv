﻿公开(公告)号,申请号,摘要(中文),原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
US20250165553A1,US18514448,"用于对包括第一多个值的输入信号进行重采样的方法和系统。执行第一再取样以获得中间信号，其包含将所述第一多个值划分为多个值群组，根据第一滤波器抽头集合对每一群组中的值进行再取样以获得中间值群组。每个组的第一值在时间上与每个中间组的第一值对准。然后对中间信号执行第二次重新采样以获得输出信号，该输出信号执行每个中间值组的相移以在时间上与输出信号的相应值组对准。输出信号通过有线或无线装置输出。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"系统, 方法, 包括, 装置",通用
US20250164621A1,US18513058,"一种用于仿真空中环境以测试调频连续波(FMCW)光检测和测距(LiDAR)被测单元(UUT)的系统。该系统包括光学透镜系统，该光学透镜系统从LiDAR UUT接收FMCW激光信号，并且将该信号提供给一个或多个光纤。使用数字信号处理来确定FMCW激光信号的斜率、啁啾定时和强度，并且至少部分地基于斜率、啁啾定时和强度来确定调制波形以仿真空中(OTA)环境。同相正交相位(IQ)调制器使用调制波形调制FMCW激光信号，并通过光学透镜系统将调制的激光信号提供回LiDAR UUT。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 数字信号,"提供, 测试, 包括, 仿真, 处理, 系统, 检测","检测, 测试, 激光"
US20250164620A1,US18513023,"一种用于仿真空中环境以测试调频连续波(FMCW)光检测和测距(LiDAR)被测单元(UUT)的系统。该系统包括光学透镜系统，该光学透镜系统从LiDAR UUT接收FMCW激光信号，并且将该信号提供给一个或多个光纤。使用数字信号处理来确定FMCW激光信号的斜率、啁啾定时和强度，并且至少部分地基于斜率、啁啾定时和强度来确定调制波形以仿真空中(OTA)环境。同相正交相位(IQ)调制器使用调制波形调制FMCW激光信号，并通过光学透镜系统将调制的激光信号提供回LiDAR UUT。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 数字信号,"提供, 测试, 包括, 仿真, 处理, 系统, 检测","检测, 测试, 激光"
US20250115274A1,US18506483,"用于在实时驾驶数据中识别驾驶场景的方法、计算设备和软件程序。驾驶场景被接收并被转换为与驾驶场景相对应的事件的有序序列。为序列中的每个事件确定信号计算函数，其量化与相应事件的接近度。接收多个时间步长的驾驶数据。确定每个信号计算函数的值，在多个时间步长中的每个时间步长处针对驱动数据对每个信号计算函数的值进行评估。基于信号计算函数的值确定驾驶数据中是否已经发生驾驶场景。基于驾驶场景是否已经发生的确定，修改、丢弃驾驶数据的第一部分，或将其存储在存储器中。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 量化,"方法, 计算, 设备",驾驶
US20230268947A1,US17713587,"可以经由多信道相量配置来改进射频发射器和接收器的动态范围，在所述多信道相量配置中，信道以将本机振荡器相位分布在π/2弧度上的方式被相位化。多信道相位接收器可以包括将输入信号分成多个信号的功率分配器，并且可以进一步包括提供中间信号的多个单信道接收器。每个单信道接收器可以具有接收多个信号中的相应信号的输入，并且可以进一步具有根据相应输入信号提供相应中间信号的输出、应用于相应输入信号的总增益、本机振荡器信号的信号频率、以及本机振荡器信号的相应相位。多信道接收器可以包括将多个中间信号组合成单个输出信号的数字信号处理器。可以类似地实施多信道发射器/收发器。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 数字信号,"提供, 包括, 具有, 处理, 配置",通用
US10838000B2,US15922051,"公开了用于在多个频率处同时测试组件的方法和装置。数字处理电路可以生成具有多个正弦波的信号的数字表示，每个正弦波具有唯一频率。数字表示可以被转换成模拟信号，并且被施加到被测器件(DUT) 。第一模数转换器(ADC)可以被耦合以测量跨DUT的电压，而第二ADC可以被耦合以测量通过DUT的电流。分别由第一ADC和第二ADC接收到的电压信号和电流信号可以被转换成第一数字值和第二数字值。根据第一数字值和第二数字值来确定每个唯一频率处的电压值和电流值。使用每个唯一频率的电压值和电流值，可以确定组件在一频率范围内的频率响应(例如，阻抗) 。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,4.0,"关键词匹配: 模数转换, ADC, 转换器, 模拟信号","测试, 装置, 具有, 方法, 生成, 处理, 电路, 模拟",测试
CN107579941B,CN201710967762.2,"本公开涉及用于I/Q减损校正的机制、以及利用偏移本地振荡器的发送器减损测量。公开了用于测量发送器和/或接收器I/Q减损的机制，包括利用共享的本地振荡器或者利用故意偏移的本地振荡器测量发送器I/Q减损的迭代方法，以及测量接收器I/Q减损的方法。还公开了用于根据采样了的复信号计算I/Q减损、用于计算发送器和接收器之间信号路径的DC属性以及用于通过线性系统变换I/Q减损的方法。
",美国国家仪器有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"系统, 方法, 包括, 计算",通用
US10181858B1,US15827824,"通过执行""自动零每个采样""过程，可以提高模拟输入信号采样期间的采样精度。可以基于采样频率确定在采样频率所定义的采样时间间隔内输入信号采样与零输入采样的比率。对于采样频率等于或小于模拟输入信号的信号调节路径的指定频率特性，该比率可以被设置为1 (一) 。对于采样频率高于指定频率，该比率可以被设置为大于1 (一)，并且可以是2的幂。数字信号处理块可以包括用于输入信号测量和零输入测量的独立数字信号处理路径。每个信号处理路径可以包括低通无限脉冲响应滤波器、平均抽取有限脉冲响应滤波器和二进制移位器以允许可调比率。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,2.0,"关键词匹配: 采样, 数字信号","包括, 设置, 处理, 模拟",通用
US10122495B2,US15357382,"公开了涉及被配置成交织数据(例如，用于处理用于无线数据传输的纠错码)的电路的技术。在一些实施例中，一种装置包括被配置成接收输入数据采样、多个多项式系数、开始索引、以及指示用于交织器索引的非顺序遍历的窗口大小的信息的一个或多个电路元件。多项式系数可包括用于至少三阶多项式的系数。在一些实施例中，一个或多个电路元件进一步被配置成基于多项式的阶数、代码块长度、开始索引、以及指示窗口大小的信息，生成用于将输入数据采样写入多个存储器块的交织存储体和地址信息。在一些实施例中，该装置还包括被配置成从存储器块提供交织数据采样的输出电路。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 包括, 装置, 生成, 处理, 电路, 配置",数据传输
US20170139001A1,US14939775,"一种改进的测量系统可包括能够执行精确的低电平电流测量的源测量单元(SMU) 。基于在测量端子处提供具有精确电流限制的受控DC电压源和受控0V (零伏) DC的SMU设计，可实现AC设计以在指定频率范围内建立相同(或非常类似)的条件。代替控制每个数模转换器(DAC)在SMU的相应源端子处作为相应DC输出，每个DAC可被控制为具有可编程频率和连续可变相位和幅度的相应函数发生器。现货供应的流水线模数转换器(ADC)可用于监测测量端子处的电压、电流和电压，并且傅立叶变换可用于获得幅度和相对相位测量两者以提供给相应控制环路。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,5.0,"关键词匹配: 模数转换, 数模转换, ADC, DAC, 转换器","提供, 实现, 包括, 具有, 控制, 系统",控制
US09654188B2,US14871997,"公开了涉及大规模MIMO通信的技术。在一些实施例中，基站被配置为动态地调整用于MIMO信号估计的处理单元的数量(例如，用于并行处理的MIMO RX链的数量) 。在一些实施例中，处理单元的数量可以基于当前使用的天线的数量、空间流的数量、互连吞吐量阈值、采样率等。在一些实施例中，基站包括可配置MIMO核心，其被配置为例如在每个符号的基础上在MIMO信号估计技术之间动态地切换。在一些实施例中，基站包括可配置线性解码器，其被配置为分别地将输入矩阵相乘，并且基于当前使用的天线和/或处理单元的数量来组合或抑制组合结果。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"包括, 处理, 配置",通信
US09621387B1,US14879285,"一种改进的正交调制器/解调器(IQD)可使用二相正交本振器(LO)信号生成来生成00和900 LO信号，以及RF (射频)端口上的反相组合器/分频器(在00和1800处) 。 IQD可包括混频器(可以是双平衡无源混频器)，其在信号以其射频(RF)端口入射时用作下变频器，且在信号以其中频(IF)端口入射时用作上变频器。因此，IQD可通过将数模转换器(DAC)连接至差分I和Q端口来用作I/Q调制器，和/或IQD还可通过将模数转换器(ADC)连接至差分I和Q端口来用作I/Q解调器。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,5.0,"关键词匹配: 模数转换, 数模转换, ADC, DAC, 转换器","生成, 包括",通用
US20160273957A1,US14940942,"本发明提供了使用相位调整向量平均进行机器状态监测的系统和方法。可获取来自测量机器参数的传感器的模拟信号，从而生成包括多个数据分析块的第一数字信号。对于每个分析块，可经由离散傅里叶变换(DFT)计算复值频谱(CVFS)，可指定至少一个参考频率，并且保留大小的复值相位补偿向量同时调整相位，所述复值相位补偿向量被构造为实现参考频率分量(RFC)与所选择的分析块之间的相干性。可通过将复值相位补偿向量乘以复值频谱来对CVFS进行相位补偿。可对分析块的复值频谱进行向量平均，从而改善指定频率处的信噪比。可识别平均频谱中的RFC，从而生成平均RFC以进行分析，以确定机器状态。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,2.0,"关键词匹配: 模拟信号, 数字信号","提供, 实现, 包括, 方法, 生成, 系统, 计算, 模拟",传感器
US09287789B2,US13957015,"一种功率转换器可以具有输入级，该输入级包括一对变压器线圈、开关电路以及限制电路，该开关电路用于根据由输入源提供的输入供应电压来为该对变压器线圈通电，该限制电路耦接在该对变压器线圈之间，以使得该输入级在从输入源汲取的电荷量达到特定值时停止从输入源汲取电流。该功率转换器的输出级可以从该对变压器线圈接收能量，并且将接收到的能量转换成输出供应电压。该对变压器线圈可以在至少一段时间内持续向输出级提供存储在该对变压器线圈的漏电感中的能量，一旦限制电路已经使得输入级停止从输入源汲取电流时。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 转换器,"具有, 提供, 包括, 电路",通用
US09285208B2,US13748702,"一种在现场可编程门阵列(FPGA)上实现的信号处理系统，其根据低频K时钟进行操作以对光学相干地形(OCT)信号进行采样，而不是依赖于高频K时钟来获得相同的信息。重新采样器用于在光频域中均匀地对OCT信号进行重新采样。可以通过以下步骤来执行重新采样：从低频数字化K时钟信号提取瞬时相位信息；对所提取的相位信息进行展开；将展开的所提取的相位信息与插值因子相乘以获得重新计算的相位信息；确定与重新计算的相位信息相对应的一个或多个整数交叉点；以及基于所述一个或多个整数交叉点对OCT信号的一个或多个值进行插值。整数交叉点可以表示在由重新计算的相位信息所限定的相位中的点的范围内可被360度整除的相位中的点。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"系统, 实现, 处理, 计算",通用
US09235395B2,US14063049,"用于配置异构硬件组件的系统的系统和方法，所述异构硬件组件包括至少一个：可编程硬件元件(PHE)、数字信号处理器(DSP)核心、以及可编程通信元件(PCE) 。创建包括浮点矩阵功能并且针对在系统上的分布式部署的程序，例如图形程序(GP) 。自动地确定程序的用于部署到各个硬件组件的各个部分。自动地生成程序代码，所述程序代码实现至少一个PHE与至少一个DSP核心之间的通信功能并且针对到至少一个PCE的部署。根据程序和代码来生成至少一个硬件配置程序(HCP)，所述至少一个硬件配置程序包括编译程序和程序代码的用于部署到各个硬件组件的各个部分。 HCP可部署到系统以用于程序的同时执行。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 数字信号,"实现, 包括, 方法, 生成, 处理, 系统, 配置",通信
US20150303936A1,US14257944,"公开了用于使用交织数据转换器来增加可编程硬件元件中的吞吐量的方法和相关联的设备的各种实施例。一种包括可编程硬件元件的设备可以被配置成包括多个N个处理部分。该设备可以接收输入信号，并且基于每个采样，以有效速率K来以交织方式对该信号进行采样，以产生N个并行数据流。 N个并行数据流可以由多个N个处理部分并行地处理。多个N个处理部分的输出可以被组合以产生输出数据。有效速率K和/或并行数据流的数目N可以由用户输入指定。替换地，可以自动地确定这些值。例如，可以基于输入信号的带宽来自动地确定有效速率K.
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,2.0,"关键词匹配: 转换器, 采样","包括, 方法, 处理, 设备, 配置",通用
US20150137840A1,US14086047,"前端转换器电路可以允许设备(例如示波器和数字转换器)接收具有宽范围的可能幅度的输入信号，同时维持高标准化输入阻抗。该转换器可以使用低电压开关将两个或更多个输入网络中的所选输入网络选择性地耦合至虚拟接地节点，并且将两个或更多个反馈网络中的所选反馈网络选择性地耦合至跨导级输入。所选输入网络和所选反馈网络一起定义相应的输入信号幅度范围。该转换器还可以可控地调整转换器的AC增益以匹配转换器的DC增益，并且选择性地将非所选输入网络耦合至信号接地。通过(通过输入电阻)降低跨转换器的输入和虚拟接地节点耦合的跨导级的值，可以将输出所提到的集成电阻器热噪声减小到期望值。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 转换器,"具有, 设备, 电路",网络
US20140300430A1,US13857481,"在一些实施例中，系统可以包括无源单平面单平衡毫米波混频器。在一些实施例中，形成混频器核心的三端口二极管三通IC耦合在槽线巴元件平衡-不平衡转换器的端部与第二共面平衡-不平衡转换器之间。通过优化混频器二极管三通核心与后短路之间的距离来增强混频器结构的操作带宽。 LO和IF信号的频率分离可以通过独立的三端口滤波器-双工器装置来实现。系统可以允许比频率转换器装置的频率倍频程操作带宽宽的全部进入毫米波频率，同时支持超过6个频率倍频程的基带IF信号的操作带宽。在一些实施例中，系统可以实现500 MHz至34.5 GHz连续IF带宽，其中从33 GHz扫描到67 GHz的RF信号以及在67.5 GHz固定频率的本地振荡器。
",National Instruments Corporation,模拟数字转换板卡技术,1.0,关键词匹配: 转换器,"系统, 实现, 包括, 装置",通用
US20140241410A1,US14186727,"信号生成系统的第一和第二输出经由相应的电导体耦合到信号数字化系统的第一和第二输入。控制器引导生成系统生成第一校准信号，并且数字化系统响应地捕获第一组矢量采样。然后重配置导体，使得导体将生成系统的第一和第二输出分别连接到数字化系统的第二和第一输入。控制器然后引导生成系统生成第二校准信号，并且数字化系统响应地捕获第二组矢量采样。控制器或其它处理剂使用第一和第二矢量采样集计算增益和/或相位损伤。数字滤波器参数可以基于计算的损伤来计算，并且用于校正生成系统和/或数字化系统的损伤。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"生成, 处理, 控制, 系统, 计算, 配置",控制
US08654903B2,US13866312,"一种用于操作接收机以便估计接收机的实际样本时钟速率1/T <Sub>S</Sub> '与预期样本时钟速率1/T <Sub>S</Sub>之间的偏移的低复杂度系统和方法。该接收机捕获以速率1/T <Sub>S</Sub> '的所接收的基带信号的样本，对捕获到的样本进行操作以生成对时钟速率偏移的估计，并使用时钟速率偏移对捕获到的样本进行部分重采样。重采样的数据表示发射机发送的基带符号的估计。对捕获到的样本进行操作的动作涉及计算误差向量信号，然后使用该误差向量信号来估计时钟速率偏移。可以根据载波频率偏移和载波相位偏移是否被假设存在于接收的基带信号中，以不同的方式来计算误差向量信号。
",National Instruments Corporation,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"系统, 生成, 方法, 计算",通用
US06801873B1,US10393963,"用于分析从具有一旋转元件的一物理系统获取的一个输入信号的系统与方法。一测速信号被接收包括用于旋转元件的转速信息。一时序使用测速信号来确定，在基本上等边角钢增量，例如通过边缘探测软件或定时/计数器包括用于旋转元件的时间价值。一基于软件的第一数字插值滤波器，例如一个级联-积分仪-梳状滤波器，被施加于时序，产生一修改时间序列。一数字化数据信号被接收包括用于在基本上均等机会增量的旋转元件的数据。第二基于软件的数字插值滤波器被施加于数据信号，产生一修改的数据信号，其使用修改时间序列被采样以生成一角度域数据信号，在基本上等边角钢增量包括数据信号值，其中角度域数据信号是可用的以分析物理系统。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"包括, 具有, 生成, 方法, 系统",通用
US06775629B2,US09880167,"系统和方法用于估计信号参数(例如，频率、振幅和/或相位)的一个或多个音调存在于输入信号。采样的输入信号被接收，以及频率变换的样本生成。幅度峰值在频率变换对应于音调被识别。两个或两个以上的频率段所选的近端到所识别的频率的变换。音调频率确定的值最小化的那个两个或两个以上表达的差异，每个相应的分子和分母术语对应于各个和每个频率仓谁的比率表示的复振幅音调在相应的箱。每个表达式包括音频变量一个代表正确的音频值的音调。正确的音频值通过计算差别表达各个不同音频变量值，并选择产生差分最小的那个值。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"包括, 生成, 方法, 系统, 计算",通用
US06594612B2,US09735234,"数字化仪用于测量系统。数字化仪获得从外部源，并且包括静态随机存取存储器( SRAM )，存储把扫描列表包括条目指定操作，例如数字化仪的开关时间，稳定时间，测量的时间，循环，数学运算规格，如缩放、增加，平均规格。该套说明书可以包括指令以重复地执行一个或多个条目的扫描列表。数字化装置包含可编程逻辑元件(例如耦合到SRAM FPGA )哪些存取并执行该扫描列表来获取模拟信号从源。数字化仪可以包括模数转换器以便将模拟信号转换成数字信号，以及多路复用器读取模拟信号从多个信道，信号调节器以改变模拟信号从复用器，和放大器来放大该模拟信号从信号调节装置。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,5.0,"关键词匹配: 模数转换, 转换器, 信号转换, 模拟信号, 数字信号","系统, 包括, 装置, 模拟",通用
US06434515B1,US09370310,"信号分析器、方法以及储存媒体用于产生时变光谱频率分量的输入信号，其特征在于，这种变化的时间。信号分析器包含源图像序列的数字信号表示输入信号，处理器耦合到源，和存储介质耦合到处理器。存储介质存储软件程序进行由该处理器执行的计算时变谱的输入信号。当处理器执行该软件程序，该处理器可操作第一计算Gabor变换(也就是，短时傅里叶变换)采样的数字信号，以生产加博尔系数。然后,处理器计算二维自相关加博尔系数来产生自相关结果。自相关结果随后被应用到二维快速内插滤波器产生时变谱，其中该时变谱的加博尔谱图。信号分析器可重复上述步骤n&plus；1倍，基于由用户确定的顺序，和n顺序时变谱的结果。该过程更可以被操作以处理和/或显示时变谱。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,2.0,"关键词匹配: 采样, 数字信号","方法, 处理, 计算",通用
US06393493B1,US09062893,"一种USB-based数据采集系统包括虚拟缓冲器软件，其增加了USB数据传输的速率以最小的改变到DAQ驱动器级软件。该虚拟缓冲器软件运行接收或拦截调用由所述DAQ驱动器级软件获取来自该设备，其中, DAQ驱动器级软件被设计成读取数据从装置每次一个采样从装置排出。该虚拟缓冲器软件拦截该调用并且请求了此数据大量分组从装置。缓冲器存储接收到的数据分组中的软件虚拟或虚拟FIFO保持在计算机系统存储器。该DAQ驱动器级软件而得出数据从电脑系统内存使用更快了传送。该虚拟缓冲器软件的仿真操作，包括产生中断和起反应起反应状态寄存器读取，因此&ldquo；fooling&rdquo；该DAQ驱动器级软件进入思维数据是被从获取的装置。因此，本发明允许更快了数据的传送，同时需要最小的或改变现有DAQ驱动器级软件。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"包括, 装置, 仿真, 设备, 系统, 计算",数据传输
US06373423B1,US09461173,"一瞬间模数转换系统和方法，使用减少数目的比较器。施加的电压通过比较器进行移动或调节，以提供A/D转换器具有大得多的电压范围。该系统包括减小的多个比较器，每个比较器被耦合以接收模拟输入信号，并且解码器耦合到接收比较器的输出控制。每个比较器还接收相应的比较器参考信号为了与模拟输入信号，并输出之间的比较数字值表示模拟输入信号与相应比较器参考信号。在一个实施例中，动态参考控制器动态输出一个或多个动态电压范围到多个比较器，其中每个比较器可以接收不同的比较器参考电压相比较的模拟输入信号。动态参考控制器因此可以提供滑动工作电压窗口用于模数转换处理，其中输入信号保持在该电压窗口。在另一个实施方案中，反馈信号用于减小电压范围的模拟输入信号，从而允许减少数目的比较器。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,2.0,"关键词匹配: 模数转换, 转换器","提供, 包括, 具有, 方法, 处理, 控制, 系统, 模拟",控制
US06359946B1,US09159269,"一种设备可以包括用于接收异步数据信号的时钟发生器，用于生成具有频率的时钟信号，约等于异步数据信号的比特率。边沿检测器可检测转变异步数据信号。一种迟缓率检测器可以检测当转变时钟信号用于对数据信号的预定时间内发生转变异步数据信号的采样点上，使得数据在时钟信号转换可能无效。时钟信号的相位可以被调节，如果在时钟信号转换发生时，在该时间量。时钟发生器可包括两个可编程计数器，一种可以编程比特率值，使得它产生一个信号大致匹配的比特率的异步数据信号，另一个编程有相位延迟值，使其产生采样定时信号在相位延迟从信号产生的第一计数器。该采样时钟的相位可以被调节，通过重启计数器响应于转换的异步数据信号。数据可被提供给串并转换器包括第一移位寄存器，被配置为移动数据字中输出串行和并行的数据字和第二移位寄存器，用于跟踪当数据字是完全移位到第一移位寄存器。
",NATIONAL INSTRUMENTS CORP.,模拟数字转换板卡技术,3.0,"关键词匹配: 转换器, 采样, 信号转换","提供, 包括, 具有, 生成, 设备, 检测, 配置",检测
US06359575B1,US09458539,"模数( A/D )转换器，包括一A/D转换器和D/A转换器的模式。A/D转换器包括内部数字到模拟转换器( DAC ) ( D/A )一个可用于反馈回路A/D操作期间(在该A/D模式)，并且可以用作独立的数模转换器D/A的模式。本发明还利用先进的校准技术可用于内部D/A转换器的A/D转换器。处理单元可以耦合到输出端的内部A/D转换器。处理单元或单独的计算机系统可以执行校准A/D的方式生成线性误差校正信息校正线性误差内部D/A转换器。线性误差修正信息可以用于配置线性误差校正装置实现通过处理单元。在该A/D模式时，处理单元可以实现线性误差修正和抽选功能在A/D转换。在该D/A模式时，处理单元还可以实现线性误差校正函数以及其它功能在D/A转换。A/D转换器还可以包括开关元件，用于配置A/D转换器或A/D或D/A模式，该模式。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,3.0,"关键词匹配: 数模转换, DAC, 转换器","模拟, 实现, 包括, 装置, 生成, 处理, 系统, 计算, 配置",通用
US06323792B1,US09650002,"一种校正由模数转换器输出码产生的( ADC )和装置，体现了该方法。该方法包括接收由ADC产生的数字输出码i，并随机地选择新的数字输出码码值范围内根据差边过渡实际代码的数字输出码i一个理想代码边过渡的数字输出码i 。一n位ADC产生数字输出码0通过( 2n&minus；1 ) 。1通过( 2n&minus数字输出码；1 )具有实际和理想代码边沿转换。边过渡实际代码的数字输出码i，在哪里1&lE；i&lE；( 2n&minus；1 )的数字输出码i&minus之间的转变；1与数字输出码i内的实际ADC的传递函数，并与理想代码边过渡的数字输出码i之间的转变i&minus数字输出码；1和代码i内的理想ADC的传递函数。当ADC是一个n位ADC，两个数字输出码i与新的数字输出码是n的值。产生n比特的值，方法，或者代码改正到ADC的概率方法，容易合并到测量与控制系统被设计成接收n比特从n位的ADC 。该方法校正ADC输出代码错误结果从若干误差源，包括偏移误差、增益误差，线性误差，或其任何组合。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,3.0,"关键词匹配: 模数转换, ADC, 转换器","包括, 装置, 具有, 方法, 控制, 系统",控制
US06243034B1,US09290218,"一个模数（模数转换）牵切纺纱方式和方法，其对于集成-类型ADCs 提供改良分辨率和减少的噪声，包括双斜面，多斜率，以及西格码-德尔他A型/模拟数字转换器。在也一个双斜面或多斜率积分型A/D转换器的上倾斜间隔之后，出现下倾斜间隔，其中一基准信号然后应用到积分元件返回积分元件到其原始价值。时钟循环时被计算，同时基准电压被施加于确定一主要的斜坡计数值。在下倾斜间隔期间，尽管施加基准电压，测量两个或多个集成电压。在一个实施例中，在原始价值和第二集成电压在原始价值之后测量，例如在跨越零值前后之前测量一第一积分器电压。然后方法确定一分数斜坡基于测得的两个或多个集成电压计算，即分数斜坡计算在其原始价值的积分元件的返回之前发生。通过由使用测得的两个或多个集成电压推断或插其原始价值的积分元件的返回决定，分数斜坡计算。使用主要的和分数斜坡计数，总的斜坡计算然后被计算，输出量数字值使用总的斜坡计数值来确定。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,3.0,"关键词匹配: 模数转换, ADC, 转换器","提供, 包括, 方法, 计算, 模拟",通用
US06232897B1,US09351759,"一种系统和方法，用于校准模数( A/D )转换器。A/D转换器包括内部D/A转换器，其中内部D/A转换器包括多个电流发生器，并且一个或多个电流发生器可产生线性误差的A/D转换器。A/D转换器包括一开关元件，连接到内部D/A转换器。在校准期间，开关元件操作来调整连接与电流发生器的内部D/A转换器分一次或多次根据不同的切换模式，导致不同的一个电流发生器与受输入到A/D转换器。这避免了需要使用复杂和昂贵的波形发生器输入校准期间，这将通常被要求确保所有的电流发生器的内部D/A转换器被刺激。相反，更简单的输入可用于校准A/D转换器，从而降低了成本。多个从A/D转换器输出的数字信号记录校准期间，其中这些记录信号格式线性误差信息包含与各个电流发生器。该线性误差信息可以被提取和用于校准A/D转换器。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,2.0,"关键词匹配: 转换器, 数字信号","系统, 方法, 包括",通用
US06222940B1,US09227507,"一种系统和方法，用于执行模式匹配，以定位零个或零个以上实例模板图像与目标图像。该方法首先包括采样模板图像用低校正序列，也称为似随机序列，确定多个样本中的哪些像素模板图像精确地表征了模板图像。低校正序列被设计为产生样本点哪个最大限度的避免彼此。用这样的方式模板图像采样或表征，然后,所述方法执行模式匹配使用样品像素与目标图像的位置以确定零个或零个以上模板图像目标图像。该方法还执行局部稳定分析围绕至少一个子集的样本像素以确定较小的第三样本组数像素，这些像素具有所期望的稳定程度，并随后执行模式匹配使用第三多个取样像素。在一个实施例中，本地稳定性分析确定多个组的像素具有不同稳定性邻区尺寸样品，与模式匹配进行多个迭代使用不同组的模式匹配采样像素，优选地执行粗略至细腻的雕刻风格，例如使用两套样品像素具有依次更小的稳定性邻区尺寸和/或步长。本发明还包括执行旋转不变性模式匹配对模板图像沿着一个或多个旋转不变的路径，优选圆形周长，生成一组或多组样本像素。样本是这些像素与圆形路径中则使用了模式匹配。该旋转不变的模式匹配还可以使用局部稳定分析和粗到精细搜索技术。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"包括, 具有, 生成, 方法, 系统",通用
US06188347B1,US09351758,"模数转换与减少闪烁代码。模数转换器包括多个比较器，每个比较器耦合到接收模拟输入信号，并且加法器译码器连接成接收比较器的输出控制。每个比较器接收相应的参考信号为与模拟输入信号。每个比较器的输出之间的比较数字值表示模拟输入信号与相应的参考信号。加法器相加该解码器产生的数字输出信号由比较器输出代表模拟输入信号的数字表示式基于该结果。该系统可以有利地提供更有效的方式将模拟信号转换成数字信号，不产生闪烁代码。加法器解码器可以是金字塔加法器。sigma-delta转换器可以包括比较器在模数部分反馈回路与加法器解码器在反馈环路的外部。温度计代码直接提供到数模转换器的西格马-德尔塔变换器，并向加法器解码器。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,6.0,"关键词匹配: 模数转换, 数模转换, 转换器, 信号转换, 模拟信号, 数字信号","提供, 包括, 控制, 系统, 模拟",控制
US06169501A,US09158994,"一种中心电钟可以包括两个可编程计数器，一种可以编程比特率值，使得它产生一个信号大致匹配的比特率的异步数据信号，另一个编程有相位延迟值，使其产生采样定时信号在相位延迟从信号产生的第一计数器。该采样时钟的相位可以被调节，通过重启计数器响应于转换的异步数据信号。数据可被提供给串并转换器包括第一移位寄存器，被配置为移动数据字中输出串行和并行的数据字和第二移位寄存器，用于跟踪当数据字是完全移位到第一移位寄存器和以引起将要输出的数据字并行地从第一个移位寄存器，使得新单词可以被移位到第一移位寄存器。状态值可以加载到第二移位寄存器，以便当最后一个比特被转换了第一移位寄存器、第二移位寄存器移出的转换完成指示。待转换的位长可以由加载不同的状态值为第二移位寄存器。同样的技术可以用于并行-串行数据转换器或在通用转换器转换成那个可以从串行-并行或并行-串行根据转换式信号。
",NATIONAL INSTRUMENTS CORP.,模拟数字转换板卡技术,2.0,"关键词匹配: 转换器, 采样","提供, 包括, 配置",通用
US06166673A,US09/162678,"改进的数据采集系统用于数字化和存储模拟数据在可选抽样率。模拟数据信号首先数字化定义的速率高频时钟信号。一种减速器的每N个样本收集数字数据，并输出样品到N个存储器分区的速率等于原始时钟频率除以N 。N存储器分区被进一步配置成接收N个储存使能信号的数字数据对应于N个样本。N个存储启用信号确定存储器分区将存储相应的数字资料样本。通过选择合适的图案N医疗的使能信号，只有一部分生成的数字数据信号被存储在存储器中。这导致可选择的有效采样速率的模拟数据。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"系统, 模拟, 生成, 配置",通用
US06012109A,US08/926374,"图像获取装置，用于获取视频信号的帧，并将这些进入屏幕计算机存储器被公开了。模拟视频输入信号，包括视频帧的序列，为数字化由A/D转换器。所得到的数字化帧选择性地选通到帧捕获缓冲器。该帧捕获缓冲器包括两个或更多个存储器段时，每个被配置为存储数字化视频帧。一种DMA控制转移图像帧从上述帧捕获缓冲器到计算机存储器经由外设总线帧采集控制逻辑(例如第二DMA控制器选择哪些视频帧将获取到帧捕获缓冲器。该帧捕获控制逻辑与DMA控制器协调存储器，其包含由状态的状态标志分别存储段。该帧捕获控制逻辑用于：检查状态标志确保对应的内存段之前有可用的指令存储器段为overvritten与新视频帧；改变状态标志以表明不可用性。该DMA控制器更新状态标志以表明其有效性时，结束数据传送内容对应的存储器段。该帧捕获控制逻辑与DMA控制器同时操作。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 转换器,"包括, 配置, 装置, 控制, 计算, 模拟",控制
US05955979A,US08/929707,"一种系统和方法，用于补偿假信号D/A转换器，尤其是在系统中假信号不是恒定的，在采样周期。在本发明的优选实施例中，该系统基于闪信号作为恒偏向采样周期期间的假信号。该系统使用补偿信号具有恒偏向与区域相对应的区域的假信号。这种近似在频域中变得更精确的较低频率使用，特别是在超出采样系统。通过对补偿闪信号作为恒偏向从理想信号在一个取样周期内，有可能消除光谱效应的假信号在低频上。这种近似使假信号补偿的采样信号。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,2.0,"关键词匹配: 转换器, 采样","系统, 方法, 具有",通用
US05935466A,US08/929500,"一种系统和方法，用于监测和调节D/A转换器的温度，减少了温度漂移而引起的不精确在D/A转换器包括加热装置，优选晶体管，热接触的D/A转换器。该系统还包括一温度感测控制电路耦合到数模转换器与上述加热装置。该温度感测控制电路接收到期望的信号葡萄酒一个表示温度数模转换器。该温度感测控制电路测量温度D/A转换器，通过计算之间的电压差的基极电压D/A转换器与上述电力输入信号转换为数模转换器。该温度感测控制电路随后提供控制输出提供给加热装置调节D/A转换器的温度，其中该控制输出端产生响应于所测量的温度D/A转换器与所述期望温度的D/A转换器。该温度感测控制电路用于重复地测量温度D/A转换器和反复地提供控制输出到加热装置，以调节空气温度的D/A转换器。这用于减小温度漂移造成的不准确性的D/A转换器。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,3.0,"关键词匹配: 数模转换, 转换器, 信号转换","提供, 包括, 装置, 方法, 控制, 系统, 计算, 电路",控制
US05909660A,US08/951808,"一种用于感测信号训练指令舱多形场信号及提供隔离数字信号适于处理系统。本发明允许场地感信号，这些信号可以是AC或DC，且这些量值范围从零到大于240伏的优选实施例。根据本发明的电路包括双向最大电流限制器耦合到双向隔离电流传感器。该电流检测器包括光耦。限流器优选包括交叉耦合耗尽模式装置和电流限制电阻。这种方式，流过电阻的电流产生电压关闭一个耗尽模式器件或其它取决于电流的极性，使输入电流限于预定最高水平宽电压范围而不管输入信号的电压的极性。光耦合器优选包括交叉耦合LEDs用于感测电流的任一方向。一种AC平流滤波器提供交流信号输出到滤波器提供平滑的数字输出信号。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 数字信号,"提供, 包括, 装置, 处理, 系统, 检测, 电路","检测, 传感器"
US05896552A,US08/916242,"一种GPIB系统捕获GPIB信号以预定的速度和在有效转变数据有效信号。第一采样电路采样GPIB预定速率以及第二采样电路采样GPIB与转变数据有效信号。捕获逻辑优选包括数据有效逻辑用于监控数据有效信号保证有效过渡。捕获逻辑还优选包括选择逻辑挑选用的GPIB信号样本之间的以预定速率和在数据有效信号的声明，在数据有效信号跃迁优选地具有较高优先级。捕获逻辑监测采样GPIB信号与数据有效逻辑以使先进先出缓冲器到捕获采样数据在预定俘获条件和转变数据有效信号。在这种方式中，数据信号转变为原本可能由脱漏捕动预定采样率采样捕获。另外，计数器电路被实现为提供时间戳值写入该缓冲器在每个截获的数据值。捕获逻辑断言选择信号时间戳的时间戳值，并且还使得缓冲器到捕获时间戳值到缓冲器中。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 实现, 包括, 具有, 系统, 电路",通用
US05886660A,US08/959199,"时间数字转换器开始匝道信号发生器触发信号时被接收。该匝道信号发生器输出的信号具有基本上不变的坡度。该输出采样匝道信号发生器是在两个或更多参考点。采样的幅度和时间进行样品采取的存储。一种外推计算信号的时刻，从该存储斜坡信号的幅度和时间样本。可选地，输出采样匝道信号发生器可以周期性速率，在这种情况下，只需要将采样幅度与时间的第一采样被存储。在这种方式中，时间触发，可准确地检测而不需要多个参考信号。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,2.0,"关键词匹配: 转换器, 采样","具有, 检测, 计算",检测
US05781137A,US08/771480,"一种系统和方法，用于降低线性误差的西格马-德耳塔转换器。该Δ-∑转换器的线性误差建模，通过产生一组数字信号表示输入的正弦波。该组数字信号是低通过滤经快速傅里叶变换算法生成频域表示的正弦波。此后，去除净线性误差频谱的频域表示和傅里叶反变换回到时域。该滤波的数字信号还被分为的子集的数字信号，在每个信号子集对应于特定输出德耳塔西格马调制器包含在Δ-Σ转换器。快速傅里叶变换算法施加到每个滤波后的数字信号的子集生成频域表示中。产生特定线性误差施加逆傅立叶变换算法对每个特定线性误差谱的频域表示的过滤子集的数字信号。此后，校正系数产生的线性误差函数净线性误差与特定线性误差。线性误差校正系数被用于生成查找表中的条目在条目可由数字输出的Δ-Σ调制器。查找表可以用于校正由该Δ-∑调制器输出的数字信号之前抽取和数字滤波器。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,2.0,"关键词匹配: 转换器, 数字信号","系统, 生成, 方法, 算法",通用
US05686917A,US08/423470,"一种根据本发明的测量系统进行自动化多路分离复用的数据从多个模拟通道。该系统包括多个N个模拟通道多路复用到A/D转换器。A/D转换器依次供给多路复用或交织的数字数据到外部计算机，在数据被存储在存储器中。外部电脑包括直接存储器存取( DMA )解复用逻辑根据本发明哪个自动读取数据改写多路复用的数据转换为非交错或多路解编格式。一旦所有数字数据复已经接收并存储在计算机系统中，本发明的解复用逻辑执行DMA转移到多路分解或消间插数据分成N个独立的缓冲器或存储空间中哪个不再交织。该DMA多路复用逻辑执行转去每个模拟通道中每个的数据分为模拟通道到单独的缓冲器中。该解复用逻辑执行DMA传送通过每个递增地址复用的数据来传输，仅将对应于各个频道。解复用逻辑增加由传送字节大小和一个地址排序法值进行数据的数量的模拟通道多路复用的数据。换句话说，DMA解复用逻辑序列多个N次传送字节大小，其中多个N数量的模拟通道交织的数据通过该计算机系统。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 转换器,"系统, 包括, 计算, 模拟",通用
US05654654A,US08/377455,"合成电压和测量电源系统包括单个放大器提供在前端，接收一参考电压的电平进行判定电压或电流源供给的信号在输出。CMOS开关或类似的逻辑选择电流和电压之间的部分组合电路。如果电压功能，选择参考放大器控制电压调节器，以保持输出电压等于参考电压施加到输入。如果当前被选择，该参考放大器被配置为电压-电流转换器建立电流流经的精度电阻器。电压跟随器电路提供电流源信号参考至电源电压提供正电流。在这种方式中，单参考使用放大器，在前端而不是两个单独的参考放大器目前提供可靠的开关电路的输入代替较昂贵且不可靠的继电器切换电流和电压之间的功能。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 转换器,"提供, 包括, 控制, 系统, 电路, 配置",控制
US05649123A,US08/472626,"一种GPIB系统包括平行登记通讯检测系统的GPIB采集信号响应于平行登记通讯命令。捕获逻辑包括用于连续地采样电路的采样GPIB信号，在捕获逻辑存储样本进入缓冲器在条件。一种平行登记通讯检测电路检测平行登记通讯宣称的命令并随后被取反的GPIB，相应地使GPIB被捕获到样品上，这两种现象。同样，计时器电路使GPIB发信号给被捕捉的列举每个时间段后的坚持已见平行登记通讯命令。在优选实施例中，过渡检测逻辑进一步使GPIB样本被捕获到如果有的话GPIB数据信号的转换时发生平行登记通讯命令断言的GPIB 。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"系统, 检测, 包括, 电路",检测
US05646620A,US08/460074,"DAC的抗尖峰脉冲电路包括开关电路，用于接地DAC输出在DAC的过渡期。该DAC优选是电流型，尽管电压式也是可预期的。开关电路最好包括被偏置晶体管电路接收更新或保持信号接地的输出DAC在保持周期。抗尖峰脉冲电路通常导致一致的假信号在每个过渡，这容易滤除滤波器电路。抗尖峰脉冲电路就大大简单了廉价，并且占用很小的空间和功率。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: DAC,"包括, 电路",通用
US05574639A,US08/322053,"一种系统和方法，用于构建一组滤波器这些检测信号的存在谁的频率内容随着时间而变化。本发明包括一种新颖的系统和方法，用于开发一个或多个时间模板设计成匹配所关心的信号与上述之滨匹配滤波器使用一个或多个时间模板检测接收的信号。每个匹配滤波器比较接收信号x (Δt )与各自独有的时间模板那个已被设计了来近似形式的目标信号。鲁棒时域模板的被认为有大约w (Δt ) =A cos{2&pgr (Δt )；phi. (Δt ) }与本发明使用轨迹的联合时间-频率表示的x (Δt )作为近似{&phgr瞬时频率的功能；(Δt ) 。首先，许多数据的采样的接收信号x (Δt )被收集。联合时间频率表示然后应用于表示信号，优选地使用时间-频率分布系列(也称为加博尔谱图) 。联合时频变换表示被分析的信号能量在时刻t和频率&fnof；P ( t，f，哪些是三维绘图的时间对频率对信号能量。则P (Δt，f )减少到一个多值函数f t，二维图的时间对频率，利用阈值化处理。曲线拟合步骤后,时间/频率暗算，优选使用Levenberg-马夸特曲线拟合技术，导出一般瞬时频率的功能。phi. ' (Δt )哪个最好地拟合多值函数f t，轨迹的联合时频域表示( x ) 。集成&phgr；(Δt )产生沿t &phgr；(Δt )，然后,插入形成当代模板方程。合适的振幅(Δt )也优选地确定。一旦时间模板已经被确定，一个或多个过滤器的开发，这些各自使用版本或形成当代模板。
",NATIONAL INSTRUMENTS CORPORATION,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"包括, 方法, 处理, 系统, 检测",检测
US05353233A,US07/851725,"信号分析器产生时变光谱频率分量的输入信号，其特征在于，这种变化的时间。信号分析器包含转换器产生一连串数字信号表示输入信号。该序列的数字信号被提供给数字信号处理器哪些计算正交类似离散Gabor变换的系数C.sub.m，n响应序列，和时变输入信号的频谱能响应于系数。最终，数据处理器处理频谱用于进一步分析或显示。一个具体的分析步骤对于时变谱有用场处于划分输入信号为分开的部件。正交类似离散Gabor变换来计算系数使用非周期性，局部离散窗函数h和离散辅助功能&ggr；类似的，h 。该时变谱计算利用交叉术语删除Wigner-Ville分布。
","NATIONAL INSTRUMENTS, INC.",模拟数字转换板卡技术,2.0,"关键词匹配: 转换器, 数字信号","提供, 处理, 计算",通用
