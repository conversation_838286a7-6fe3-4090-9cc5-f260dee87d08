﻿公开(公告)号,申请号,摘要(中文),原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
US20250076907A1,US18817742,"一种控制系统在恒定电压(CV)控制模式下表现出改进的动态性能，该模式也不随源类型和工作点而变化。通过将压控电流源替换为压控电阻作为系统反馈回路中的控制元件，可以在控制电路的附加部件最少或潜在地没有附加部件的情况下实现改进。测量来自被测器件(DUT)的输入电压，至少部分地基于输入电压确定反馈控制电压以提供用于DUT的CV模式控制环路，并且将添加到输入电压的反馈控制电压施加到DUT以在CV模式下操作DUT。
",NATIONAL INSTRUMENTS CORPORATION,HIL硬件在环仿真技术,1.0,关键词匹配: 环路,"提供, 实现, 控制, 系统, 电路",控制
US20160034617A1,US14808116,"用于创建机器视觉应用的系统和方法。包括指定机器视觉图像处理算法和相关联的参数的多个机器视觉步骤的机器视觉原型可被存储。步骤可以可解释为由仿真器解释以通过在硬件加速器上仿真或仿真步骤的执行来在图像上执行所指定的图像处理，硬件加速器例如是可编程硬件元件或图形处理单元。仿真器可仿真或仿真在硬件加速器上的步骤的执行，由此生成图像处理结果，其可被显示以用于验证用户的仿真或仿真。原型可被分析，并且基于该分析，针对硬件加速器的图像处理算法的资源使用或性能的估计可被确定和显示，并且可用于图像处理算法的目标平台选择或修改。
",NATIONAL INSTRUMENTS CORPORATION,HIL硬件在环仿真技术,1.0,关键词匹配: 仿真器,"平台, 包括, 算法, 仿真, 方法, 生成, 处理, 系统",验证
US20150130526A1,US14080053,"用于测量装置(例如示波器或数字化转换器)的前端电路可以使用可编程可变电阻实现DC增益补偿。 MOS晶体管可以配置成并且操作为具有快速自校准能力同时补偿温度变化的线性电阻器。基于CMOS的集成可变电阻器可以由此用于模拟可调节衰减器。主CMOS晶体管和从CMOS晶体管可以在线性模式中操作，并且可以通过使用配置在主MOS晶体管周围的集成环路控制器(电流控制器)来补偿线性晶体管上的温度效应。用补偿的可变电阻实现的电路具有用控制电压的宽调节范围，并且可以用在示波器或数字化转换器的前端(电路)中，或者从可调节衰减器受益的任何其他电路和/或仪表中。
",NATIONAL INSTRUMENTS CORPORATION,HIL硬件在环仿真技术,1.0,关键词匹配: 环路,"模拟, 实现, 装置, 具有, 控制, 电路, 配置",控制
US06823221B2,US09997638,"运动控制系统和方法公开了提供改进的脉冲顺行放置的运动装置，例如步进马达。放置心律不齐可以对于多个时间间隔使得脉冲均匀地置于在多个时间间隔，其中心律不齐在每个时间间隔的数量是可变的。该脉冲可以生成并发送到运动装置将对象移动至期望位置。延迟可以被使用以将每个脉冲内任意位置的一个时间间隔。在期望的步进率为分数，时间可以被&ldquo；borrowed&rdquo；从其它为了他的缘故循环迭代循环迭代。在一个实施例中，步进率可以从一个环路周期到下一个。
",NATIONAL INSTRUMENTS CORPORATION,HIL硬件在环仿真技术,1.0,关键词匹配: 环路,"提供, 装置, 方法, 生成, 控制, 系统",控制
US06081751A,US09/054556,"一种系统和方法，用于自动地调谐PID控制器驻留在PID控制环路。PID控制环路包括PID控制器及工艺。该进程提供过程变量进行比较，该环路的输入。比较的结果提供给PID控制器，PID控制器驱动过程。继电器应用于环路的输入。继电器比较设定点值与过程变量。如果设定点值大于过程变量，该继电器驱动回路输入与第一振幅值。如果设定点值小于过程变量，该继电器驱动回路输入与第二幅度值。响应设定点继电器，过程变量产生持续振荡。该周期与幅度的持续振荡进行测量。一套新的PID控制器参数，计算出与周期和振幅的持续振荡。特别是，振荡周期与振幅都用来计算的时间常数和空载时间标准过程模型。该时间常数和滞后时间被用来计算新的PID控制器参数(一)直接或者通过公式反应曲线与Ziegler-Nichols方法，或(二)通过中间步骤计算最终周期和频率从时间上看恒定和息止时间。
",NATIONAL INSTRUMENTS CORPORATION,HIL硬件在环仿真技术,1.0,关键词匹配: 环路,"提供, 包括, 模型, 方法, 控制, 系统, 计算",控制
USD0384050S1,US00/019991,"FIG. 1透视图环路图标的显示屏编程计算机系统显示我的新型设计；
FIG. 2是其正视立体图；
FIG. 3是放大的前视图，其与剖线的公开被省略。
虚线公开在计算机屏幕上FIGS 。1和2仅仅用于说明性的目的，并且不形成一部分要求的设计。
",National Instruments Corporation,HIL硬件在环仿真技术,1.0,关键词匹配: 环路,"系统, 计算",通用
