﻿公开(公告)号,申请号,摘要(中文),原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
US20250133684A1,US18491891,"一种测试系统机架或机柜，包括容纳仪器或设备的壳体和安装在壳体内的大容量互连(MIC)。MIC具有耦合到仪器或设备的输入端和耦合到与机架分离的第二MIC的输出端。测试系统支架具有位于外壳下面的腿，以支撑外壳和第一MIC。测试系统机架具有致动器，其被配置为在不调整一个或多个支腿的位置的情况下调整第一MIC的位置。
",NATIONAL INSTRUMENTS CORPORATION,集群化测试技术,1.0,关键词匹配: 测试系统,"包括, 测试, 具有, 设备, 系统, 配置",测试
US20250053500A1,US18796978,"用于基于被测器件(DUT)的规范的初始输入的生成人工智能(AI)辅助测试过程开发的装置、系统和方法。DUT的规格可以被输入到生成AI模型中。生成AI模型可以总结规范，请求经由与最终用户的交互的进一步输入以最终确定DUT的描述，并且生成/创建测试资产，诸如代码、文档、表、图表，等等。生成AI模型可与最终用户协作以细化来自测试资产的输出。可以将细化的测试资产发送到可以使用/运行/部署各种测试资产的软件应用。另外，生成AI可访问本地测试硬件，并经由网络/串行通信枚举其它系统上的测试硬件，以创建适合所标识的硬件的测试系统。
",NATIONAL INSTRUMENTS CORPORATION,集群化测试技术,1.0,关键词匹配: 测试系统,"测试, 模型, 装置, 生成, 方法, 系统","通信, 测试, 网络"
US11994545B2,US17728377,"测试系统可以用于获得精确的远程感测电压和/或电流值。测量仪器可以向被测器件(DUT)提供调节的激励信号，并且测量至少部分地响应于该激励信号而产生的DUT信号。测试电路可以上位将测试信号置于激励信号之上，以使DUT信号响应于测试信号而进一步发展。DUT信号可以用于导出将测量仪器耦合到DUT的路径的电阻。测量仪器可以包括源测量单元激励信号可以是调节电压，DUT信号可以是读出电压。可以分析DUT信号的谐波以确定DUT信号的测量基频的幅度与路径的电阻之间的相关性。
",NATIONAL INSTRUMENTS CORPORATION,集群化测试技术,1.0,关键词匹配: 测试系统,"提供, 测试, 包括, 系统, 电路",测试
US20220365123A1,US17728377,"一种测试系统可以用于获得准确的遥感电压和/或电流值。测量仪器可以将经调节的刺激信号提供给被测设备(DUT)，并且测量响应于所述刺激信号而至少部分地开发的DUT信号。测试电路可以将测试信号叠加到所述刺激信号上，以使得响应于所述测试信号而进一步开发所述DUT信号。所述DUT信号可以用于导出将所述测量仪器耦合到所述DUT的路径的电阻。所述测量仪器可以包括源测量单元，所述刺激信号可以是经调节的电压，并且所述DUT信号可以是感测电压。可以分析所述DUT信号的谐波以确定所述DUT信号的测量的基频的振幅与所述路径的电阻之间的相关性。
",NATIONAL INSTRUMENTS CORPORATION,集群化测试技术,1.0,关键词匹配: 测试系统,"提供, 包括, 测试, 设备, 系统, 电路",测试
US06516053B1,US09336932,"包括一便携式计算机系统，呈现一模块化的电信测试系统至少一个便携无线电通讯测试模块定位在计算机系统以外。便携式计算机系统存储电信测试申请（例如，在一记忆系统中）。每一测试模块包括具有一电接插件的一通信出入口，因此是适于对便携式计算机系统连接。每一测试模块还包括用于执行一组电信测试的电路，其中每一测试涉及进行通过电信业务安装的至少一个电气测量。在连接一特定的测试模块到便携式计算机系统方面，一名用户配置测试系统以执行该组与特定的测试模块相关联的电信测试。至少一个测试模块选自一组测试模块，各自配置成通过不同类型的电信业务安装执行电信测试。电信业务安装的合格的类型包括POTS 、T1/E1 、ISDN ，以及xDSL 。测试系统经由一通信网络可能还包括耦合到第一计算机系统的一第二计算机系统（例如，一台式计算机系统）。通信网络可以包括PSTN 和因特网。第一计算机系从每一测试模块可接收电信测试数据并经由通信网络提供试验资料到第二计算机系统。
",NATIONAL INSTRUMENTS CORPORATION,集群化测试技术,1.0,关键词匹配: 测试系统,"提供, 包括, 测试, 具有, 系统, 计算, 电路, 配置","通信, 测试, 网络"
US06249125B1,US09517935,"一种系统和方法能够减少测试蓄电池损坏失效系统成本。在特定实施例中，测试系统可构造成减小系统成本通过电报双线继电器从多个电池到多个多路复用器位于第一级。双线继电器数目等于：
B/2&plus；4.5如果B是古里古怪
B/2&plus；5如果B是即使
这里B是在电池。电池可以与特定通道的多路复用器位于第一级。多路复用器的输出位于第一级可以随后连接到至少一个多路复用器的输入端位于第二级。至少一个多路复用器的输出位于第二级可以被连接到至少一个器械，例如万用表测定。由于各只电池可与特定组合的输入声道多路复用器位于第一级，软件可以对选择使用的特定组合，输入待由该至少一个器械。该至少一个仪器比较该电压间的特定组合，输入通道多路复用器位于第一阶段和预期值。如果在特定组合的输入声道小于期望值，那么该特定电池是有缺陷的。
",NATIONAL INSTRUMENTS CORPORATION,集群化测试技术,1.0,关键词匹配: 测试系统,"系统, 方法, 测试",测试
