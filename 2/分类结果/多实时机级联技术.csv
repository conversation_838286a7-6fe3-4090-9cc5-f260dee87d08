﻿公开(公告)号,申请号,摘要(中文),原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
US11803456B2,US17464419,"用于将测试荚分配到分布式计算系统以用于在待测设备(DUT)上执行测试计划的方法和计算设备。每个测试荚可以包括包含一个或多个测试步骤的测试微服务和指定该测试微服务与其他测试微服务之间的函数关系的事件微服务。该测试荚被分配到不同服务器以通过一个或多个测试接口在该DUT上执行该测试计划的分布式执行。
",NATIONAL INSTRUMENTS CORPORATION,多实时机级联技术,1.0,关键词匹配: 分布式,"包括, 测试, 方法, 设备, 系统, 计算",测试
US20230063629A1,US17464419,"用于将测试荚分配到分布式计算系统以用于在待测设备(DUT)上执行测试计划的方法和计算设备。每个测试荚可以包括包含一个或多个测试步骤的测试微服务和指定该测试微服务与其他测试微服务之间的函数关系的事件微服务。该测试荚被分配到不同服务器以通过一个或多个测试接口在该DUT上执行该测试计划的分布式执行。
",NATIONAL INSTRUMENTS CORPORATION,多实时机级联技术,1.0,关键词匹配: 分布式,"包括, 测试, 方法, 设备, 系统, 计算",测试
US10841136B1,US16540838,"公开了一种用于发送和接收无线通信的装置，其中，发送电路包括用于对信号进行脉冲形状调制的平方根升余弦滤波器，以及接收电路包括被耦合的较高阶的奈奎斯特接收滤波器，以接收所述输入信号并且移除所述脉冲形状调制。所述发送滤波器和所述接收滤波器的级联组合具有等效于较高阶的广义升余弦滤波器响应的频率响应。
",National Instruments Corporation,多实时机级联技术,1.0,关键词匹配: 级联,"具有, 包括, 装置, 电路",通信
US20180217954A1,US15417765,"在分布式系统中实现基于事件的异步输入/输出操作启动。在所述分布式系统内，经由内部网络耦合到相应多个从设备的多个主设备中的每个主设备可以实现一个或多个定时功能，所述一个或多个定时功能被配置为控制相应多个从设备的物理输入操作和/或物理输出操作的定时，以及主设备与相应多个从设备之间的流。从设备的子集还可以经由共享的基于信号的总线来互连，所述共享的基于信号的总线可以用于传播异步事件，所述异步事件可以用于启动在耦合到从设备的子集中的至少一个从设备的主设备上实现的所述一个或多个定时功能中的至少一个定时功能。所述异步事件可以由所述从设备中的一个从设备生成。
",NATIONAL INSTRUMENTS CORPORATION,多实时机级联技术,1.0,关键词匹配: 分布式,"实现, 生成, 设备, 控制, 系统, 配置","网络, 控制"
US20170286169A1,US15470374,"公开了一种用于基于硬件属性和指定约束将程序功能自动映射到分布式异构平台的方法。该方法包括接收多个程序功能并且为每个程序功能确定约束信息。该方法进一步包括确定多个硬件处理元件的属性，其中多个硬件处理元件中的一些硬件处理元件具有相对于硬件处理元件中的其他硬件处理元件的不同属性。多个程序功能可以被自动映射以用于在硬件处理元件的至少子集上执行，其中映射基于约束信息和属性。
",NATIONAL INSTRUMENTS CORPORATION,多实时机级联技术,1.0,关键词匹配: 分布式,"平台, 包括, 具有, 方法, 处理",通用
US20160359978A1,US14840462,"生成用于分布式实时系统的调度。至少一个调度发生器可以从在主设备上执行的相应定时功能接收时间性质，其中每个主设备连接到相应的多个从设备。每个主设备包括被配置为控制相应的多个从设备的物理输入和/或输出操作的定时以及主设备与相应的多个从设备之间的流的一个或多个定时功能。调度发生器可以接收定时功能与主设备之间的流之间的关联，并且至少部分地基于时间性质和关联来生成用于主设备的相应调度。相应调度可以被分布到主设备，并且可由主设备用于以协调方式实时控制定时功能和主设备之间的流的执行。
",NATIONAL INSTRUMENTS CORPORATION,多实时机级联技术,2.0,"关键词匹配: 分布式, 实时系统","包括, 生成, 设备, 控制, 系统, 配置",控制
US20160117158A1,US14883876,"在异构硬件系统(HHS)上使用图形浮点数学功能的网络物理系统的全局优化和验证。程序包括控制程序(CP)、物理系统(MPS)的模型、目标函数、要求验证程序(RVP)和/或全局优化器的浮点实现。使用具有受信任模型的共仿真来仿真程序的HHS实现，包括仿真程序在HHS上的分布式执行的行为和定时，并且可以使用RVP来验证HHS实现。 HHS被配置成以分布式方式同时执行CP和MPS.在将程序部署到HHS之后，HHS被配置成经由全局优化器来全局优化(改进)在HHS上同时执行的CP和MPS.优化的MPS可以用于构建物理系统。优化的CP可以在HHS上执行以控制物理系统。
",NATIONAL INSTRUMENTS CORPORATION,多实时机级联技术,1.0,关键词匹配: 分布式,"实现, 包括, 模型, 具有, 仿真, 控制, 系统, 配置","网络, 验证, 控制"
US20150326286A1,US14703214,"公开了涉及大规模MIMO基站架构的技术。在一些实施例中，基站被配置为组合由多个天线接收的信号，并且对于包括在基站中的处理元件的至少子集，每个处理元件被配置为在组合的信号的不同部分上操作。在这些实施例中，每个部分包括来自多个天线的信号。在一些实施例中，这些部分是组合的信号的不同时间和/或频率部分。在一些实施例中，这种分布式处理可以允许基站的天线数量显著地缩放，提供动态可重配置性，促进实时基于互易性的预编码等。
",NATIONAL INSTRUMENTS CORPORATION,多实时机级联技术,1.0,关键词匹配: 分布式,"提供, 包括, 处理, 配置",通用
US20150118971A1,US14328898,"收发器系统的发射器(TX)和接收器(RX)信号链中的复杂高阶非线性的动态表征可被有效且准确地执行。环回连接可用于促进自表征。适当的RX和TX配置设置可被开发以促进个体RX和TX非线性从所测量的级联非线性解耦合。系统的对双音信号生成的高阶响应可被测量，并且复杂数学分析可被执行以识别和隔离通带非线性分量以提取用于系统的高阶无存储器模型。所提取的系统模型可用于所生成的信号的校正性和非迭代性预失真中以及接收的信号的后失真中以改善收发器的线性性能。无存储器模型和分析系统在改善在测试和测量系统以及信道模拟系统中常见的基于A类放大器的信号链的性能中是有效的。
",NATIONAL INSTRUMENTS CORPORATION,多实时机级联技术,1.0,关键词匹配: 级联,"模拟, 测试, 模型, 生成, 设置, 系统, 配置",测试
US20140101347A1,US13645864,"用于分布式系统中的不同存储器映射域之间的等时数据传送的技术。方法包括用等时时段配置等时引擎。该方法还包括在等时时段的周期的指定部分期间通过存储器映射结构将数据从第一存储器传送到第二存储器。第一存储器被包括在存储器映射结构的第一存储器映射域中的第一设备中，并且第二存储器被包括在存储器映射结构的第二存储器映射域中的第二设备中。该方法还可包括转换与传送相关的一个或多个地址。存储器映射结构可以是PCI Express结构。传送可由DMA控制器执行。非透明桥可分离第一存储器映射域和第二存储器映射域，并且可执行转换。
",NATIONAL INSTRUMENTS CORPORATION,多实时机级联技术,1.0,关键词匹配: 分布式,"包括, 方法, 设备, 控制, 系统, 配置",控制
US20030037316A1,US10176739,"一种系统和方法，用于创建和使用配置图配置分布式体系。此处描述的方法可用于各种类型的操作配置分发系统，包括创建节目，节目管理方法在分布式系统中，部署程序给各种分布式设备、配置或分布式互操作远程执行的程序，执行分布式应用。本发明的实施例利用基于图表的图像技术用于执行上述操作。该结构图可以包括代表本发明设备的设备图标的程序图标哪个表示程序。设备图标的程序图标可以彼此相关联，以完成各种程序建立和部署操作。设备图标的程序图标也可以与图形编程节点或图标。连接上下文有关的装置和/或程序之间的连接显示。异步数据流节点可以对促进异步数据流之间使用两个图形节目。分布式系统也支持分布式绘画的调试。
",NATIONAL INSTRUMENTS CORPORATION,多实时机级联技术,1.0,关键词匹配: 分布式,"包括, 装置, 方法, 设备, 系统, 配置",通用
US06473707B1,US09259165,"抽象试验执行程序其能见度大大提高可配置性和模块化，从而创建、修改和测试的执行序列。该试验执行程序包括过程模型用于改进的灵活性、模块化和可配置性。过程模型提供模块化和可配置实体封装相关联的操作和功能的一类测试序列。因此封装&ldquo过程模型；试验process&rdquo；用于多个测试序列。过程模型将令使用者写不同的测试序列，而不用重复标准测试操作在每个序列。该试验执行程序也囊括了跨步式改进的可配置性。台阶式是模块化，可识别的单元由用户配置和/或操作，其定义了共性与多个步骤。台阶式的实例包括该共同功能和/或性质从跨步式，因此用户不需要硬编码那些功能和/或特性在每个实例或步骤。该试验执行程序还提供了分布式存储和冲突解决。当用户加载文件，包括一储存式，方法执行冲突解决，包括，确定是否该负载型冲突与一个或多个预先装载/注册类型。如果类型进行冲突，则测试执行程序或用户选择型，优选使用版本数据相关联的每一型式。该结果也进行了测试执行程序系统自动采集自动收集在每个步骤。用户可以允许或禁止结果收集特定序列或整个测试台。
",NATIONAL INSTRUMENTS CORPORATION,多实时机级联技术,1.0,关键词匹配: 分布式,"提供, 包括, 模型, 测试, 方法, 系统, 配置",测试
US06226762B1,US09062677,"模块化分布式I/O系统包括计算机耦合到模块排通过网络总线上，一个模块排包括通讯模块、终端碱，和输入输出模块。该邻接的端子基部形成局部总线掌握通过通信模块。该输入输出模块连接到本地总线过线端子碱。输入输出模块是pluriform和可编程的。通信模块保持存储器映像的配置状态的每个I/O模块驻留在模块组。存储图像持续在输入输出模块被从其端子底座上。该存储器映像用于配置新输入输出模块哪个插入到同一端子底座上。通信模块监控通信故障对网络总线，并且被配置成捕获模块的状态自动银行和恢复该捕获的状态在掉电事件。终端碱实现本地总线，包括一并行总线、串行总线、和地址分配母线各基地码头接收值从前一基地码头，递增该值，而且断言了增量值与接续端子底座。每个基地码头自动地分配一个物理邻近性地址。一种输入输出模块控制读/写访问其登记空间由臂板信号机构，这种机构支持多线程处理。锁存模块而且假定了ungranted信号要求的自我排除状态在释放信号量其中信号量保留通信模块。
",NATIONAL INSTRUMENTS CORPORATION,多实时机级联技术,1.0,关键词匹配: 分布式,"实现, 包括, 处理, 控制, 系统, 计算, 配置","通信, 网络, 控制"
US6182179B1,US6284198,"一种模块化的分布式I/O 系统通过一网络母线包括耦合到模块集的计算机。一模块集包括一通信模块，接线座，以及输入输出模块。邻接的接线座通过通信模块形成一本地总线主设备。输入输出模块通过接线座连接到本地总线。输入输出模块是pluriform 并且可编程的。通信模块维持驻于模块集的每一输入输出模块的置状态的一存储图像。当一输入输出模块从其接线座移除时，一存储图像持续。存储图像用于配置被插入到相同的接线座的一新的输入输出模块。通信模块为在网络母线上的通信失效监控，经配置以捕获模块集的状态并在一个动力-损失事件之后自动地恢复这捕获的状态。接线座实现一本地总线，其包括一并行总线，一条串行总线，以及一条地址分配总线。每一接线座从一先前的接线座接收一值，增加此值，宣称递增值到一接连的接线座。每一接线座被自动分配物理邻近区地址。对通过支撑多线程的处理的一个信标机制的其寄存器空间的一个输入输出模块控制读写存取。输入输出模块闭锁一ungranted 信标请求并通过释放信号假定一独占状态，其中信号被预留为通信模块。
",NATIONAL INSTRUMENTS CORPORATION,多实时机级联技术,1.0,关键词匹配: 分布式,"实现, 包括, 处理, 设备, 控制, 系统, 计算, 配置","通信, 网络, 控制"
US06175932B1,US09062891,"模块化分布式I/O系统包括计算机耦合到模块排通过网络总线上，一个模块排包括通讯模块、终端碱，和输入输出模块。该邻接的端子基部形成局部总线掌握通过通信模块。该输入输出模块连接到本地总线过线端子碱。输入输出模块是pluriform和可编程的。通信模块保持存储器映像的配置状态的每个I/O模块驻留在模块组。存储图像持续在输入输出模块被从其端子底座上。该存储器映像用于配置新输入输出模块哪个插入到同一端子底座上。通信模块监控通信故障对网络总线，并且被配置成捕获模块的状态自动银行和恢复该捕获的状态在掉电事件。终端碱实现本地总线，包括一并行总线、串行总线、和地址分配母线各基地码头接收值从前一基地码头，递增该值，而且断言了增量值与接续端子底座。每个基地码头自动地分配一个物理邻近性地址。一种输入输出模块控制读/写访问其登记空间由臂板信号机构，这种机构支持多线程处理。锁存模块而且假定了ungranted信号要求的自我排除状态在释放信号量其中信号量保留通信模块。
",NATIONAL INSTRUMENTS CORPORATION,多实时机级联技术,1.0,关键词匹配: 分布式,"实现, 包括, 处理, 控制, 系统, 计算, 配置","通信, 网络, 控制"
US06173438B1,US08912445,"一种基于计算机的虚拟测量系统包括主计算机和嵌入式系统或装置，其中绘画程序创建使用计算机系统可以下载到嵌入式系统执行的实时至少确定性方式。本发明因此提供一种自动生成图形嵌入式应用程序创建响应于由用户。这为用户提供能够开发出功能性或定义仪器的使用图形程序设计技术，同时使所得到的程序以一个嵌入式实时系统。本发明包含一种配置嵌入式系统。在执行图形程序的嵌入式系统，该嵌入式系统执行在框图的部分，与主CPU执行的代码前面板显示器屏幕上显示图形的前面板图形编程。嵌入式系统与主机使用前协议交换数据以实现该操作。本发明也包括提高排障支持图形上执行的程序的嵌入式系统。主机图解程序设计系统因此提供用户界面图形上执行的程序的嵌入式系统，实质上作为前面板的嵌入式浏览器应用。主机LabVIEW可以用作独立申请相连通的嵌入LabVIEW通过共享存储器。主机图解程序设计系统提供无缝环境，其中用户发展嵌入式应用使用高级图形程序设计技术。
",NATIONAL INSTRUMENTS CORPORATION,多实时机级联技术,1.0,关键词匹配: 实时系统,"提供, 实现, 包括, 装置, 生成, 系统, 计算, 配置",通用
US06098117A,US09/063177,"模块化分布式I/O系统包括计算机耦合到模块排通过网络总线上，一个模块排包括通讯模块、终端碱，和输入输出模块。该邻接的端子基部形成局部总线掌握通过通信模块。该输入输出模块连接到本地总线过线端子碱。输入输出模块是pluriform和可编程的。通信模块保持存储器映像的配置状态的每个I/O模块驻留在模块组。存储图像持续在输入输出模块被从其端子底座上。该存储器映像用于配置新输入输出模块哪个插入到同一端子底座上。通信模块监控通信故障对网络总线，并且被配置成捕获模块的状态自动银行和恢复该捕获的状态在掉电事件。终端碱实现本地总线，包括一并行总线、串行总线、和地址分配母线各基地码头接收值从前一基地码头，递增该值，而且断言了增量值与接续端子底座。每个基地码头自动地分配一个物理邻近性地址。一种输入输出模块控制读/写访问其登记空间由臂板信号机构，这种机构支持多螺纹处理。锁存模块而且假定了ungranted信号要求的自我排除状态在释放信号量其中信号量保留通信模块。
",NATIONAL INSTRUMENTS CORPORATION,多实时机级联技术,1.0,关键词匹配: 分布式,"实现, 包括, 处理, 控制, 系统, 计算, 配置","通信, 网络, 控制"
US06065068A,US09/063179,"模块化分布式I/O系统包括计算机耦合到模块排通过网络总线上，一个模块排包括通讯模块、终端碱，和输入输出模块。该邻接的端子基部形成局部总线掌握通过通信模块。该输入输出模块连接到本地总线过线端子碱。输入输出模块是pluriform和可编程的。通信模块保持存储器映像的配置状态的每个I/O模块驻留在模块组。存储图像持续在输入输出模块被从其端子底座上。该存储器映像用于配置新输入输出模块哪个插入到同一端子底座上。通信模块监控通信故障对网络总线，并且被配置成捕获模块的状态自动银行和恢复该捕获的状态在掉电事件。终端碱实现本地总线，包括一并行总线、串行总线、和地址分配母线各基地码头接收值从前一基地码头，递增该值，而且断言了增量值与接续端子底座。每个基地码头自动地分配一个物理邻近性地址。一种输入输出模块控制读/写访问其登记空间由臂板信号机构，这种机构支持多螺纹处理。锁存模块而且假定了ungranted信号要求的自我排除状态在释放信号量其中信号量保留通信模块。
",NATIONAL INSTRUMENTS CORPORATION,多实时机级联技术,1.0,关键词匹配: 分布式,"实现, 包括, 处理, 控制, 系统, 计算, 配置","通信, 网络, 控制"
US05533037A,US08/248417,"等待时间检错电路包括两个级联的锁存器接收时钟信号从测量系统基于事件的发生并且相对地断言位给处理系统，用于清除第一锁存器电路用这样的方式处理系统确认检测该位是被断言。如果第二锁存器计时之前，该第一锁存器被清零，第二锁存器设置错误位的延迟指示错误条件。处理器系统监视错误位的等待时间，以确定是否发生了错误。
",NATIONAL INSTRUMENTS CORPORATION,多实时机级联技术,1.0,关键词匹配: 级联,"包括, 设置, 处理, 系统, 检测, 电路",检测
