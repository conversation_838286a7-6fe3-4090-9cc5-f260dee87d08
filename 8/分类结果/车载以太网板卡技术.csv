﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN120342922A,CN202510421875.7,"本申请公开了一种基于虚拟化以太网卡的模拟系统和设备，所述系统包括N个虚拟化以太网卡vEth和N个预设容器，N为正整数；所述vEth包括通信驱动程序层，所述通信驱动程序层用于在应用程序和底层硬件之间，通过原始套接字Raw Socket对以太网报文进行发送和接收；所述预设容器用于基于应用容器引擎Docker在容器中运行vEth，每个预设容器对应一个vEth，所述预设容器通过Docker的网桥实现与外界网络的隔离，多个预设容器之间通过Docker的网桥互连。在vEth Driver中，通过创建Raw Socket，实现了对以太网报文的直接发送和接收。
",H04L43/20,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: 以太网,"实现, 包括, 设备, 系统, 模拟","通信, 网络"
CN120301727A,CN202510334812.8,"本申请公开了一种控制器唤醒方法和控制器，包括：接收唤醒报文；对唤醒报文的报文类型进行检测，得到报文类型检测结果；在报文类型检测结果指示报文类型为管理报文的情况下，将唤醒报文的报文数据发送至网络管理模块，以使网络管理模块对报文数据的内容格式进行检测，得到返回值，返回值用于指示唤醒报文是否包括有效PN请求；响应于控制器管理模块发送的报文检测请求，向控制器管理模块发送返回值，以使控制器管理模块根据返回值控制控制器是否唤醒。基于此，实现了通过检测唤醒报文中是否包括有效PN请求来控制控制器是否需要被唤醒，提高了对控制器唤醒的适用性，也无需通过CAN收发器了来实现PN请求对控制器的唤醒，降低项目实施成本。
",H04L12/40,经纬恒润(天津)研究开发有限公司,车载以太网板卡技术,3.0,关键词匹配: CAN; IPC分类号匹配: H04L12/40,"实现, 包括, 方法, 控制, 检测","检测, 网络, 控制"
CN120223648A,CN202510238792.4,"本发明公开一种CAN报文合并均匀性优化方法及系统，涉及汽车电子技术领域，包括：将报文信息保存至第一数组和第二数组；计算分配给第一数组和第二数组中报文的邮箱数；依次将当前数组中的各个报文ID和掩码相与，根据相与结果将当前数组拆分为两组；将当前数组对应报文的邮箱数减1；判断当前数组对应报文的邮箱数是否为0，若不为0，则将掩码值右移一位，作为新的掩码；依次将操作数组中的各个报文ID和新的掩码相与，根据相与结果将操作数组拆分为两组，并将当前数组对应报文的邮箱数减1。本发明每次合并时优先处理包含元素多的组，从而避免报文ID越小，合并越稀疏，报文ID越大，合并越集中的问题，提高报文分组的均匀性。
",H04L47/41,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"包括, 方法, 处理, 系统, 计算",汽车
CN120223637A,CN202510364752.4,"本发明公开一种CAN通道多报文错峰发送自动分配方法，涉及汽车电子技术领域，包括搭建上位机；获取周期时间类型的集合和各个类型对应的任务数量的集合；计算周期时间类型的最小公倍数和任务总数；定义变量并根据变量计算各时刻发送的报文任务情况；设置目标模型；利用经典编码个体和贪婪编码个体，生成种群并计算适应度值；更新种群；计算当前种群中各个个体的适应度值；选取当前种群中适应度值最优的个体，作为被选个体；将被选个体的发送方案作为CAN通道的多报文错峰发送方案。本发明基于DBC文件，自动分配报文的发送时刻，实现报文数据传输的平均分配，降低系统在数据传输过程中的整体负载率和峰值负载率，提升系统性能，提高开发者的工作效率。
",H04L47/125,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"实现, 包括, 模型, 方法, 设置, 生成, 系统, 计算","汽车, 数据传输"
CN120032443A,CN202510180547.2,"本发明涉及汽车电子车身域控制器技术领域，公开了一种门锁状态反馈方法及系统，门锁状态反馈方法包括：针对不同类型的硬线信号，设计不同的帧格式；设计与硬线信号对应的CAN信号；利用硬线信号和CAN信号反馈门锁状态。通过融合当前汽车控制器门锁状态反馈的硬线信号和CAN报文信号，优化硬线信号的帧格式，使得硬线信号具备CAN报文信号的多样性的优点，同时通过硬线信号和CAN报文信号的冗余设计，提高了门锁状态反馈的可靠性。
",G07C9/00,天津经纬恒润科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"系统, 方法, 包括, 控制","汽车, 控制"
CN120017439A,CN202510174265.1,"本公开涉及一种CAN总线的分并网装置及方法，分并网装置包括：总线、两条总线段、n个终端电阻连接模块及m个分并网开关。n个终端电阻连接模块并联连接于总线；分并网开关设置于相邻终端电阻连接模块之间；m个分并网开关均用于将总线与两条总线段之间切换；终端电阻连接模块用于根据分网指令，控制位于总线段两端的终端电阻连接模块将终端电阻接入总线段，以及用于根据并网指令，控制位于总线两端之间的终端电阻连接模块将终端电阻与总线断连。本公开通过对总线上各个终端电阻连接模块的位置进行检测，使得仅有总线或总线段两端的终端电阻自动接入，在节省人力的同时，还避免了手动操作失误的问题，进一步保证了对总线通信质量。
",H04L12/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,3.0,关键词匹配: CAN; IPC分类号匹配: H04L12/40,"包括, 装置, 方法, 设置, 控制, 检测","检测, 通信, 控制"
CN119996058A,CN202510322968.4,"本申请公开了一种车载以太网规则的压缩方法及装置，所述方法包括：获取目标网络层的待添加访问规则；查询预先构建的协议表中是否存在待添加访问规则；若预先构建的协议表中不存在待添加访问规则，则将待添加访问规则进行存储，并根据预先配置的目标网络层对应的访问规则，从待添加访问规则中选取出与目标网络层的匹配项相匹配的多个访问条目；将多个访问条目下发至硬件芯片中。从而通过预先配置车载以太网络中每个数据层的访问规则对应的匹配项，然后再根据配置好每个数据层的匹配项下发访问规则中的访问条目，进而可以有效地压缩和下发访问条目，同时还提高了硬件的利用率。
",H04L9/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载以太网, 以太网","方法, 包括, 装置, 配置",网络
CN119892872A,CN202510018526.0,"本申请提供了一种确定胎压数据接收率的方法及装置，方法包括获取胎压数据源文件、各车轮的身份标识和分类标识；根据各车轮的身份标识和分类标识，从胎压数据源文件中提取出各车轮的报文信息；根据每个车轮的报文信息，确定各接收通道对应的接收时间列表；根据每个接收通道对应的接收时间列表和时间间隔，确定各接收通道的胎压数据接收率。这样，可以基于获取到的胎压数据源文件、各车轮的身份标识和分类标识，自动筛选数据，得到各车轮、各接收通道对应的接收时间列表，从而可以自动计算得到各车轮、各接收通道对应的胎压数据接收率，改善了人工计算导致的耗时长、易出错的情况，从而提高了胎压数据接收率确定的高效性和准确性。
",H04L67/12,经纬恒润(天津)研究开发有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L67/12,"提供, 包括, 装置, 方法, 计算",通用
CN119861638A,CN202510031306.1,"本发明提供一种基于SOA的应用层软件架构搭建方法及装置，建立底层协议栈与外部以太网的通信连接；建立底层协议栈与SOA服务处理模块中的软件组件的连接；在软件组件中创建第一可执行实体，在应用层逻辑实现模块中创建第二可执行实体；建立软件组件与应用层逻辑实现模块的通信连接；基于逻辑开发工具、第一可执行实体和第二可执行实体搭建功能逻辑。在本方案中，与现有技术相比增加了SOA服务处理模块实现了底层与应用层的分层，使整个架构更加清晰，出现问题时候，可以在每一层增加调试信息，即可定位出哪一部分出现问题，并且进行软件平台化复用时候，仅仅只需要修改SOA服务处理模块处的接收接口即可，可以做到模块化的软件复用。
",G05B19/042,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: 以太网,"提供, 平台, 实现, 装置, 方法, 处理, 工具",通信
CN119858514A,CN202510096558.2,"本发明提供一种整车参数处理方法及装置、存储介质及电子设备，包括：确定车辆的各个整车参数；对于每个整车参数，获取整车参数的采集数据，并基于整车参数的采集数据，对整车参数进行标定；将存在自适应学习标记的整车参数确定为目标参数；对于每个目标参数，基于目标参数的自适应学习逻辑进行匹配处理，并在得到表征车辆的参数存在异常的匹配结果时，进行参数异常管控处理。对存在自适应学习标记的整车参数进行匹配处理后，在得到表征车辆的参数存在异常的匹配结果时，进行参数异常管控处理，降低因参数异常带来的风险，降低驾驶员的风险。
",B60R16/023,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/023,"提供, 包括, 装置, 方法, 处理, 设备","车辆, 驾驶"
CN119858517A,CN202510136391.8,"本公开涉及一种汽车域控制器及车辆，汽车域控制器包括：第一开关模块、第二开关模块、第一电源轨及第二电源轨；第一电源轨与第一电源连接，第一电源轨还通过第一开关模块及第二开关模块与第二电源轨连接，第二电源轨还与第二电源连接；第一电源用于通过第一电源轨及第二电源轨，为第一负载、第二负载供电以及为第二电源充电；在第一电源轨，和/或，第二电源轨运行异常的情况下，第一开关模块及第二开关模块断开。本公开在第二电源轨运行异常而导致第二负载无法正常使用时，通过第一电源轨维持第一负载的供电。以及在第一电源轨运行异常而导致第一负载无法正常使用时，采用第二电源通过第二电源轨维持第二负载的供电，从而保证车辆的行驶能力。
",B60R16/03,经纬恒润(天津)研究开发有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/03,"包括, 控制","车辆, 汽车, 控制"
CN119854386A,CN202411933792.8,"本申请提供了一种报文传输方法、装置及相关设备，该方法应用于车辆的车载通信系统。具体地，如果需要将以太网报文格式的第一报文发往某个采用CAN协议的目标模块，那么首先可以获取该第一报文并对第一报文进行格式转换以得到CAN报文格式的第二报文。接着，可以对第一报文进行分析，先根据第一关联关系，从PDUR模块的多个出口通道中确定与目标模块对应的第一出口通道，并基于第二关联关系，从PDUR模块的多个入口通道中确定与第一出口通道对应的第一入口通道。因此，通过第一入口通道向PDUR模块发送第二报文，第二报文可以经过PDUR模块的转发到达第一报文的目标模块。如此，在尽可能少地对现有车载通信系统的进行调整的前提下，实现了报文格式的转换和转发。
",H04L69/16,北京经纬恒润科技股份有限公司,车载以太网板卡技术,3.0,"关键词匹配: 车载通信, 以太网, CAN","提供, 实现, 装置, 方法, 设备, 系统","车辆, 通信"
CN119299243A,CN202411407776.5,"本申请提供一种数据通信系统，包括第一开发板和第二开发板，第一开发板包括第一处理器和第一万兆PHY芯片，第一处理器与第一万兆PHY芯片通过USXGMII接口协议通信连接，第二开发板包括第二处理器和第二万兆PHY芯片，第二处理器与第二万兆PHY芯片通过USXGMII接口协议通信连接，第一开发板和/或第二开发板还包括存储芯片，与第一万兆PHY芯片和/或第二万兆PHY芯片连接，存储芯片用于存储第一万兆PHY芯片和/或第二万兆PHY芯片的固件；其中，第一万兆PHY芯片与第二万兆PHY芯片通过万兆以太网传输线连接。这样，实现两个开发板之间通过万兆以太网进行数据互传，传输速率快，从而提高了车载以太网的传输效率，扩大了车载以太网的应用场景。
",H04L12/02,经纬恒润(天津)研究开发有限公司,车载以太网板卡技术,4.0,"关键词匹配: 车载以太网, 以太网; IPC分类号匹配: H04L12/02","提供, 实现, 包括, 处理, 系统",通信
CN119276732A,CN202411677046.7,"本申请公开了一种多变体车型网络的通信设计方法及装置，所述方法包括：分别针对每一种车型类型，根据车型类型的每个ECU及其对应的ECU使用场景，建立各个ECU使用场景的ECU变体；基于每一个ECU变体，建立每一种车型类型对应的各个变体单元；基于每一种车型类型对应的各个变体单元，建立每一种车型类型对应的各个变体组合；在各个变体组合下，配置每一种车型类型的每一个ECU对应的有效发送报文和有效接收信号，得到每一种车型类型对应的每个目标变体组合，并将其存储至预先构建的冗余数据库中。从而通过在不同车型类型下根据使用场景的不同对每个ECU进行划分和统一管理，有效地减少了网络设计的出错率较高的问题。
",H04L41/14,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L41/14,"方法, 包括, 装置, 配置","通信, 网络"
CN118921333A,CN202410977976.8,"本申请公开了一种通讯方法、装置及设备，涉及汽车技术领域。通讯方法包括：接收源网段发送的源报文；在源网段为CANFD网段，目标网段为CAN网段，源报文包括第一CANFD报文的情况下，将第一CANFD报文拆分为M条CAN报文，其中，M为大于或等于2的正整数；将M条CAN报文发送给CAN网段。根据本申请公开的方案，能够避免总线通信异常。
",H04L47/43,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"方法, 包括, 装置, 设备","通信, 汽车"
CN118869620A,CN202411108276.1,"本申请提供了一种软硬件结合的车载网络流量的处理方法及装置，包括：获取目标流量数据；遍历预置于硬件控制模块的硬件访问控制规则确定目标流量数据的第一匹配结果；响应于第一匹配结果为匹配失败，丢弃目标流量数据，或，按照预设的调试规则上报目标流量数据；响应于所述第一匹配结果为上送软件处理，发送目标流量数据至软件控制模块，以使得软件控制模块基于软件访问控制规则确定目标流量数据的第二匹配结果，所述第二匹配结果用于确定所述目标流量数据的最终处理结果。本申请基于硬件对流量进行初筛，再由软件实现流量的最终处理，结合硬件和软件实现车载网络流量的处理，提高车载网络报文的处理性能，从而提高车载网络访问控制方案的体验。
",H04L47/32,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: 车载网络,"提供, 实现, 包括, 装置, 方法, 处理, 控制","网络, 控制"
CN118869383A,CN202410845876.X,"本申请公开了一种CAN自匹配装置、CAN光桥自匹配系统及方法。所述CAN自匹配装置包括：电阻检测模块，用于检测CAN总线的电阻值；阻抗匹配模块，与CAN总线并联，包括多组备选电阻；所述阻抗匹配模块，用于在所述CAN总线的电阻值与CAN总线上的预设阻值存在差异的情况下，控制所述CAN总线与所述多组备选电阻中的至少一组连接，以使所述CAN总线的电阻值等于所述预设阻值。根据本申请实施例，能够实现CAN总线阻抗自匹配，减少了CAN总线失配导致的电磁兼容问题，保证信号良好传输；并且，减少了由于阻抗失配，人力排查CAN总线阻抗问题的时间成本，提高了CAN总线阻抗匹配效率。
",H04L12/40,天津经纬恒润科技有限公司,车载以太网板卡技术,3.0,关键词匹配: CAN; IPC分类号匹配: H04L12/40,"实现, 包括, 装置, 方法, 控制, 系统, 检测","检测, 控制"
CN118764374A,CN202410788340.9,"本申请实施例提供了一种应用管理方法、装置、设备及计算机可读存储介质，该方法包括：通过主节点监控从节点在运行过程中是否出现异常，从节点包括第一从节点和第二从节点；在第一从节点出现异常时，通过主节点将第一从节点中的高优先级应用转移到第二从节点；通过主节点向第二从节点发送应用唤醒报文，令第二从节点运行高优先级应用。通过本申请实施例，高优先级应用可以在节点间自由调度，有效提高高优先级应用的持续运行能力。
",H04L41/0668,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L41/0668,"提供, 包括, 装置, 方法, 设备, 计算",通用
CN118694824A,CN202410725070.7,"本申请公开了一种处理请求的方法和装置。应用于OTA云端系统中，OTA云端系统至少包括通信模块、监控模块及请求处理模块，该方法包括：监控OTA云端系统的当前资源使用信息；根据OTA云端系统的资源使用信息与负载等级之间的关联关系，确定当前资源使用信息所对应的负载等级，得到当前负载等级；确定在当前负载等级下从多个请求队列中获取请求的请求数量，多个请求队列用于存储具有不同处理优先级的请求；从多个请求队列中获取对应请求数量的请求，并对获取到的请求进行处理，得到请求处理结果。本申请实施例所提供的方案降低了OTA云端系统的请求处理压力，提高了OTA云端系统的可靠度。
",H04L67/61,经纬恒润(天津)研究开发有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L67/61,"提供, 包括, 装置, 具有, 方法, 处理, 系统",通信
CN118670757A,CN202411174664.X,"本申请提供的一种整车自兼容风险检测方法、装置、设备及存储介质。在一种整车自兼容风险检测方法中，根据整车的零部件的工作原理对整车的零部件进行分类，得到整车的干扰源识别结果；根据所述干扰源识别结果以及所述整车的线束原理，确定整车中需要监测的控制器局域网CAN线束段；获取CAN线束段的CAN信号；根据CAN信号确定整车的自兼容风险检测结果。通过上述方法，可以将零部件的工作原理和整车的线束原理结合起来，快速确定出存在自兼容风险的CAN线束段，并通过观测CAN信号，可以简单直观的监测自兼容风险，快速准确地进行自兼容风险检测。
",G01M17/007,经纬恒润(天津)研究开发有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"提供, 装置, 方法, 设备, 控制, 检测","检测, 控制"
CN118651019A,CN202410841156.6,"本发明提供一种悬架控制方法及装置，接收车辆CAN信号和悬架上的传感器信号，确定车辆的极限行程控制阻尼；若车辆处于行驶状态，计算车辆的各个激励等级；根据各个激励等级和预设关系表计算加权阻尼；基于极限行程控制阻尼和加权阻尼得到第一仲裁输出阻尼；根据第一仲裁输出阻尼控制悬架；若车辆处于刹车状态，确定车辆的停车控制阻尼；基于极限行程控制阻尼和停车控制阻尼得到第二仲裁输出阻尼；根据第二仲裁输出阻尼控制悬架。车辆行驶工况下，结合相应的激励等级确定第一仲裁输出阻尼；车辆刹车工况下，结合极限行程控制阻尼和停车控制阻尼确定第二仲裁输出阻尼，提高了悬架控制的合理性和灵活性，提高了乘客的乘坐体验和车辆的安全性。
",B60G17/015,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"提供, 装置, 方法, 控制, 计算","车辆, 传感器, 控制"
CN118646637A,CN202411095872.0,"本申请公开了基于车载系统日志的故障诊断方法、装置、设备和介质，涉及通信技术领域。具体包括：基于目标故障和第一对应关系，确定与目标故障对应的多个根节点系统事件作为系统事件集合；基于系统事件集合和故障诊断图谱，确定系统事件集合中每个根节点系统事件对应的多条逻辑链路作为逻辑链路集合；其中，每条逻辑链路串联导致对应的根节点系统事件发生的有逻辑关系的子节点事件；分析逻辑链路集合中每条逻辑链路涉及的子节点事件对应的日志文本，确定导致目标故障发生的原因。本申请中的方法可以高效地确定目标故障发生的原因，进而提高目标故障的修复速度，提高客户满意度。
",H04L41/0631,江西经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L41/0631,"包括, 装置, 方法, 设备, 系统","通信, 诊断"
CN118632310A,CN202410781401.9,"本申请实施例公开了一种车载通信终端的网络切换方法、装置及存储介质。其中，该方法包括：采集5G T‑BOX的第一网络质量数据；利用第一网络质量数据确定第一网络质量指标；利用第一网络质量指标确定是否将第一SIM卡切换为第二SIM卡。可见，本申请实施例能实现动态的跨运营商的5G网络的切换,从而能有效地保障无人驾驶的安全。而且，利用RSRP、RSSI和SINR共同确定网络质量指标，如此综合考虑多个参数对网络质量的影响，能实现更准确的将信号差的5G网络切换为信号较好的5G网络，从而使得无人驾驶场景安全性和可靠性大幅提升。
",H04W36/00,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: 车载通信,"方法, 包括, 装置, 实现","驾驶, 通信, 网络"
CN118590439A,CN202410634836.0,"本申请实施例提供了一种CAN报文发送方法、装置及设备。涉及车辆技术领域，本申请先获取待分配的周期报文所包括的周期时长，确定安排的各周期时长的顺序，并按照所述顺序安排各周期时长的周期报文发送的时间点。在安排目标周期时长的周期报文时，若目标周期时长的周期报文的报文数大于同一时间点发送的最大报文数，则建立目标周期时长的目标周期任务，并在目标周期任务的周期内，通过建立偏移任务和建立基于偏移任务的报文偏移来增加时间点，使目标周期时长所包括的周期报文安排在基于目标周期任务所建立的时间点进行发送。如此能够，避免同一时间点发送的报文过多造成总线负载率过高而丢帧的问题，同时降低控制器的负载。
",H04L47/10,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"提供, 包括, 装置, 方法, 设备, 控制","车辆, 控制"
CN118590424A,CN202410807238.9,"本申请公开了一种通信协议一致性测试方法及装置。所述方法包括：根据测试需求用例生成第一报文；将第一报文发送至车辆控制器；提取预先设计的车载网络的数据分发服务DDS协议栈开发设计文件，确定第一测试信息；接收车辆控制器发送的第二报文，第二报文是车辆控制器根据第一报文生成的，第二报文包括第二测试信息；判断第一测试信息与第二测试信息是否一致，以判断车辆控制器是否满足DDS协议一致性。根据本申请实施例，能够实现DDS协议一致性测试，从而确保车辆中使用不同操作系统的各控制器之间基于DDS协议进行通信时的互操作性，使全车通信更加安全可靠，极大降低了安全隐患。
",H04L43/18,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: 车载网络,"实现, 包括, 测试, 装置, 方法, 生成, 控制, 系统","测试, 控制, 车辆, 通信, 网络"
CN118524011A,CN202410718404.8,"本发明提供了一种车载以太网数据处理方法、系统及电子控制单元ECU，车端ECU中的通讯缓冲模块在检测到与目标对象之间网络中断的情况下，通过进入等待状态以使日志收集模块在检测到通讯缓冲模块进入等待状态的情况下，停止向通讯缓冲模块发送日志数据。日志收集模块对于检测到的日志文件中的新增日志数据，在日志收集模块中第一缓冲区的容量已达到上限时，停止将新增日志数据存入第一缓冲区，能够实现在网络长时间中断情况下保证日志数据不被丢弃。直到通讯缓冲模块检测到与目标对象之间网络恢复正常时退出等待状态，实现日志收集模块与通讯缓冲模块以及通讯缓冲模块与目标对象之间日志数据的正常发送，保证目标对象接收到的日志数据是连续的。
",H04L41/069,北京经纬恒润科技股份有限公司,车载以太网板卡技术,4.0,"关键词匹配: 车载以太网, 以太网; IPC分类号匹配: H04L41/069","提供, 实现, 方法, 处理, 控制, 系统, 检测","检测, 网络, 控制"
CN118509097A,CN202410642768.2,"本申请公开了一种干扰测试设备的时间补偿方法及系统，所述方法包括：获取第一延迟时间以及第二延迟时间；对第一延时时间以及第二延时时间进行分解，得到第一延时时间对应的纳秒级延迟时间，以及第二延时时间对应的皮秒级延迟时间；通过可配置超前补偿与输出模块对纳秒级延迟时间进行超前补偿，得到纳秒级超前补偿时间；通过延迟补偿模块对皮秒级延迟时间进行滞后补偿，得到皮秒级延迟补偿时间，并计算纳秒级超前补偿时间以及皮秒级延迟补偿时间的和，得到皮秒级超前补偿时间；基于皮秒级超前补偿时间，在CAN总线上施加干扰信号。从而通过延迟补偿模块中多个时序控制器级联的方式，实现了皮秒级别的延时补偿时间。
",H04J3/06,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"实现, 包括, 测试, 方法, 设备, 控制, 系统, 计算, 配置","测试, 控制"
CN118487890A,CN202410702986.0,"本发明公开了CAN总线通信可靠性检测方法及装置，第一CAN总线发送第一CAN报文给第二CAN总线同时启动计时器，判断是否接收到第一发送成功标识；若否，判定为第一发送端故障；若是，获取第一计时时长，判断第一计时时长是否超过预设时长阈值；若超过，第二CAN总线仍未接收到第一CAN报文，判定为总线传输故障；若未超过，第二CAN总线已经接收到第一CAN报文，获取第一CAN报文的第一标识，对第一标识和第一CAN报文进行验证；若验证失败，判定为总线传输故障。上述过程，基于对第一发送成功标识，第一计时时长、第一标识和第一CAN报文的判断或分析，可以得到不同场景下具体的故障类型，提高了可靠性检测的精度。
",H04L12/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,3.0,关键词匹配: CAN; IPC分类号匹配: H04L12/40,"检测, 方法, 装置","检测, 通信, 验证"
CN118282792A,CN202410370156.2,"本申请提供了一种智能驾驶座舱的信息处理系统、方法和智能驾驶座舱，该系统包括：主组件和与所述主组件连接的至少两个从组件，任一从组件对应一功能设备；第一从组件解析第一功能设备传输的输入信息得到第一指令；所述主组件基于预设处理逻辑确定所述第一指令匹配的第二从组件，将所述第一指令发送给第二从组件；所述第二从组件响应所述第一指令控制第二功能设备从第一状态切换为第二状态。
",H04L12/28,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/28,"提供, 包括, 方法, 处理, 设备, 控制, 系统","驾驶, 控制"
CN118012006A,CN202410144188.0,"本申请公开了一种CAN总线数据采集功能测试方法、装置、设备及介质，涉及整车数据采集技术领域。CAN总线数据采集功能测试方法包括：获取多个控制器局域网CAN总线数据；根据多个CAN总线数据，生成总线镜像数据；通过目标测试脚本向车载网络服务发送总线镜像数据，测试CAN总线数据采集功能，其中，CAN总线数据采集功能包括以下所列项至少其中之一：车载网络服务接收和解析总线镜像数据的功能、CAN记录器接口的录制功能、目标应用程序的数据采集和上传功能。根据本申请实施例，能够提高测试CAN总线数据采集功能的效率。
",G05B23/02,经纬恒润(天津)研究开发有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载网络, CAN","包括, 测试, 装置, 方法, 生成, 设备, 控制","测试, 网络, 控制"
CN117978575A,CN202410057635.9,"本申请提供一种更新波特率的方法、装置和通信系统，方法包括，按第一波特率接收主节点发送的第一报文，第一波特率是从节点配置的第一波特率参数对应的波特率；根据第一报文检测主节点的波特率参数对应的第二波特率；获得第二波特率和第一波特率之间的第一波特率偏差；在第一波特率偏差大于偏差阈值时，根据第二波特率将第一波特率参数更新为第二波特率参数，第二波特率和第二波特率参数对应的第二波特率之间的第二波特率偏差小于或等于偏差阈值。
",H04L12/40,天津经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/40,"提供, 包括, 装置, 方法, 系统, 检测, 配置","检测, 通信"
CN117978708A,CN202410101798.2,"本申请公开了一种车辆遥控器测试系统和方法，包括：远程控制系统，和车辆遥控器无线连接；CAN收发器，CAN收发器和远程控制系统有线连接；终端，终端与CAN收发器无线连接；终端和车辆遥控器之间通过CAN收发器和远程控制系统传输用于测试车辆遥控器的测试信号对应的数据报文，以使终端和车辆遥控器中的数据报文接收方对测试信号进行响应，得到响应结果。基于此，通过利用TBOX和CAN收发器可以让车辆遥控器和终端之间建立通信连接，无需涉及Spy3等设备的使用，降低了车辆遥控器测试系统的构建成本，以及使用难度，使得车辆遥控器测试系统可以更广泛地应用，提高了车辆遥控器测试系统的适用性。
",H04L43/50,经纬恒润(天津)研究开发有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"包括, 测试, 方法, 设备, 控制, 系统","车辆, 通信, 测试, 控制"
CN117914694A,CN202410102665.7,"本申请提供了一种车载网络通信控制方法及系统，任一ECU获得自身的通信配置信息，通过与服务器进行心跳协商方式，确定该ECU和服务器都支持的候选通信模式以及来自服务器的通信信息后，将基于候选通信模式的优先级，建立该ECU与服务器之间的目标通信通道，利用对应的通信信息，通过该目标通信通道，实现该ECU与服务器之间的数据交互。可见，本申请实现了对不同ECU的差异化服务，通过丰富每一个ECU与服务器之间的通信方式，提高了数据传输可靠性和安全性。
",H04L41/0803,北京经纬恒润科技股份有限公司,车载以太网板卡技术,3.0,关键词匹配: 车载网络; IPC分类号匹配: H04L41/0803,"提供, 实现, 方法, 控制, 系统, 配置","通信, 数据传输, 网络, 控制"
CN117880083A,CN202410057613.2,"本申请提供了一种用于智能驾驶的传感器部署方案的确定方法和装置，该方法包括：获得车辆中部署传感器的部署参考信息和初始部署方案；以初始部署方案为候选部署方案，依据部署参考信息中车辆的车型参数、不同类型传感器的属性参数和候选部署方案，仿真出该车辆在至少一种虚拟交通场景中的环境感应结果；基于环境感应结果，确定车辆采用该候选部署方案的感应性能；确定车辆采用候选部署方案所产生的资源耗费；根据资源耗费以及感应性能对候选部署方案进行优化，将经过优化的候选部署方案确定为车辆的传感器部署方案。本申请能够更为合理地确定车辆中用于智能驾驶的传感器部署方案。
",H04L41/0803,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L41/0803,"提供, 包括, 装置, 仿真, 方法","车辆, 驾驶, 传感器"
CN117831150A,CN202311847489.1,"本申请公开了一种车载通信方法及系统、电子设备、存储介质，车载通信系统包括一个电池管理系统以及多组电池监控单元，电池管理系统通过一条CAN总线分别与各组电池监控单元连接。通过PWM线束将电池管理系统和各组电池监控单元进行环形连接。然后由电池管理系统向第一组电池监控单元发送指定频率和指定占空比的目标信号，并按照通过PWM线束形成的环形，依次由上一个电池监控单元向下一个电池监控单元发送相应的目标信号，直至最后一组电池监控单元向电池管理系统发送相应的目标信号。各个电池管理系统基于所接收到信号确定所属的位置并学习所属位置对应的通信ID，以在通过CAN总线与电池管理系统进行通信时，发送所学习到的通信ID。
",G07C5/00,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载通信, CAN","系统, 方法, 包括, 设备",通信
CN117792975A,CN202311797472.X,"本申请公开了一种虚拟环境测试车载网络通信的方法、系统及可读存储介质，基于车载网络通信测试的测试项目，利用至少一台服务器创建与测试项目所需的至少一个被测目标板对应的至少一个容器，基于测试项目对应的待测试车载网络类型，对至少一个容器进行虚拟网络配置，得到虚拟网络测试环境，待测试车载网络类型包括以太网和CAN总线中的至少一种，基于虚拟网络测试环境进行车载网络通信测试。根据本实施例，基于虚拟技术搭建虚拟网络测试环境，有效降低了对被测目标板的需求及集成开发的时间成本，无需人工接线，降低了人工成本。
",H04L43/50,北京经纬恒润科技股份有限公司,车载以太网板卡技术,3.0,"关键词匹配: 车载网络, 以太网, CAN","包括, 测试, 方法, 系统, 配置","通信, 测试, 网络"
CN117749556A,CN202311551309.5,"本申请公开了一种总线切换电路及装置。总线切换电路包括：控制模块，控制模块的通信端用于与测控模块连接，控制模块用于根据测控模块发送的通信数据生成控制信号；切换模块，切换模块的信号输入端与控制模块的输出端连接，切换模块的多个选通信号端分别与CAN总线以及多个测试对象连接，切换模块用于根据控制信号将多个测试对象中的至少一个与CAN总线连通，或者，将多个测试对象中的至少一对相互连通。根据本申请实施例，能够实现测试对象通过CAN总线与上位机进行通信或测试对象与其他的测试对象之间进行相互通信的切换控制。
",H04L12/40,天津经纬恒润科技有限公司,车载以太网板卡技术,3.0,关键词匹配: CAN; IPC分类号匹配: H04L12/40,"实现, 包括, 测试, 装置, 生成, 控制, 电路","通信, 测试, 控制"
CN117729548A,CN202311766977.X,"本发明提供了一种车载网络主机入侵检测方法及系统，通过将包括告警规则配置文件和eBPF运行配置文件的配置文件写入第一共享内存，并通过将eBPF程序加载到内核，从而在应用程序触发系统调用时，触发eBPF程序根据eBPF运行配置文件采集系统调用数据，并将系统调用数据写入第二共享内存，读取第二共享内存中的系统调用数据，对系统调用数据进行加工处理，根据告警规则配置文件即可确定加工处理后的系统调用数据是否存在告警事件，并在存在告警事件的情况下输出对应的告警信息。由于应用程序都需要触发系统调用，本发明不需要关心多样化的用户态应用程序，仅通过检测内核中系统调用数据即可进行主机入侵检测，降低了入侵检测系统的发开难度和工作量。
",H04W12/122,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: 车载网络,"提供, 包括, 方法, 处理, 系统, 检测, 配置","检测, 网络"
CN117579416A,CN202311548921.7,"本发明提供一种总线数据的处理方法及装置，在缓存层将总线数据赋值到RxService结构类型的第一静态变量，将第一静态变量中的总线数据拷贝到uint8*结构类型的指针变量，得到总线数据包副本和对应的指针并存储到单向队列，提取指针并将指针对应的总线数据包副本差额拷贝到应用层RxService结构类型的第二静态变量。在本方案中，在缓存层通过RxService结构类型缓存总线数据，以uint8*结构类型分配总线数据的大小，去冗余拷贝总线数据得到指针并入队，出队时基于差额拷贝以及C++强制转换的特性，将总线数据拷贝到应用层RxService结构类型，以解决总线数据在缓存层内存占用过大以及数据冗余的问题。
",H04L12/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/40,"提供, 方法, 处理, 装置",通用
CN117560244A,CN202311516517.1,"本申请公开了一种网络管理的实现方法、装置、设备、存储介质及架构。该方法包括：在车辆的通信网络中的同一子网中同时存在第一控制器和第二控制器的情况下，在第一控制器中设置第一定时器，在第二控制器中设置第二定时器，其中，第一控制器和第二控制器遵循不同的通信协议，第二控制器的通信依赖于令牌环机制；对具有对应关系的第一定时器和第二定时器，在第一定时器的计数值和第二定时器的计数值之间建立约束关系；调整第一控制器的网络管理报文的报文格式，以使网络管理报文不干扰令牌环机制。根据本申请实施例，能够实现不同标准的控制器在同一子网内协同工作。
",H04L12/417,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/417,"实现, 包括, 装置, 具有, 方法, 设置, 设备, 控制","车辆, 通信, 网络, 控制"
CN117478761A,CN202311412199.4,"本发明提供一种车载以太网通信验证方法及装置，基于地址解析协议，将片上系统SOC的IP地址作为目的IP地址生成请求报文；按照以太网帧结构，将MCU的MAC地址作为源MAC地址，将广播地址作为目的MAC地址，封装请求报文得到广播帧；将广播帧进行广播发送；接收SOC发送的响应报文；基于地址解析协议解析响应报文得到源IP地址；若源IP地址与目的IP地址一致，则确定MCU和SOC之间的以太网通信建立。在本方案中，增加车载MCU和SOC之间的以太网通信连接，基于地址解析协议，广播请求报文，接收SOC发送的响应报文，验证以太网通信是否正常，从而实现了提高车载MCU与SOC之间的数据传输速率的目的。
",H04L69/16,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载以太网, 以太网","提供, 实现, 装置, 方法, 生成, 系统","通信, 数据传输, 验证"
CN117439776A,CN202311317169.5,"本申请公开了一种车载以太网通信方法，包括：客户端向目标服务器发起第一连接请求，所述第一连接请求用于请求通过所述目标服务器的安全通信端口建立连接，所述安全通信端口为所述目标服务器用于进行车载以太网安全通信的端口；在接收到所述目标服务器发送的第一响应信息的情况下，按照预设协议与所述目标服务器进行车载以太网通信，所述预设协议为用于进行安全通信的协议。上述过程中，客户端通过目标服务器的安全通信端口与目标服务器建立连接并通信，且客户端与目标服务器之间基于安全通信协议进行通信，可以提高客户端与目标服务器之间车载以太网数据通信的信息安全性。
",H04L9/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载以太网, 以太网","方法, 包括",通信
CN117278421A,CN202311204914.5,"本申请公开了一种基于架构建模工具的通信模型生成方法、系统及电子设备，确定车载通信协议的客户端与服务端的通信时序；确定车载通信协议的选定元素与建模元素之间的映射关系；通过架构建模工具利用建模语言，基于通信时序及选定元素与建模元素之间的映射关系构建车载通信协议的客户端与服务端的模型模板；基于车载通信协议的需求服务信息对车载通信协议的客户端与服务端的模型模板进行替换，获得车载通信协议的客户端与服务端通信模型。
",H04L41/14,北京经纬恒润科技股份有限公司,车载以太网板卡技术,3.0,关键词匹配: 车载通信; IPC分类号匹配: H04L41/14,"模型, 方法, 生成, 设备, 系统, 工具",通信
CN117240587A,CN202311385319.6,"本申请提供了一种信息安全诊断方法和装置，应用于车载以太网入侵检测系统的云端系统，该方法包括：接收前端系统上传的信息安全诊断指令，所述前端系统是接收操作者输入操作的系统；解析所述信息安全诊断指令，至少得到执行处理器信息，所述执行处理器属于车端系统，所述车端系统是设置于车辆的系统；将所述信息安全诊断指令发送给车端系统的所述执行处理器，以使得所述执行处理器执行所述信息安全诊断指令，得到执行结果；接收所述执行处理器的执行结果，以基于所述执行结果进行安全分析。
",H04L9/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载以太网, 以太网","提供, 包括, 装置, 方法, 设置, 处理, 系统, 检测","车辆, 检测, 诊断"
CN117162943A,CN202311150435.X,"本申请公开了一种车身控制器及控制方法、装置，车身控制器包括微处理器、电源管理模块以及监测控制模块，所述电源管理模块与所述微处理器电连接，所述监测控制模块与所述微处理器电连接、所述电源管理模块电连接，其中，所述监测控制模块被设置为：基于电源管理模块的第一电平信号和所述监测控制模块的第二电平信号进行判断，控制所述车身控制器进入跛行模式，所述第一电平信号用于指示所述电源管理模块在无法与所述微处理器进行通讯时发出的电平信号，所述第二电平信号用于指示所述监测控制模块中与所述跛行模式相关联的电平信号。如此，能够避免车身控制器在接收到第一电平信号时无法区分跛行模式和其他模式，进而避免错误进入跛行模式。
",B60R16/023,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/023,"包括, 装置, 方法, 设置, 处理, 控制",控制
CN117130312A,CN202311216080.X,"本发明涉及汽车电子技术领域，公开了一种电子控制器单元的远程标定方法、装置及电子设备。其中，该方法包括：获取中间转换器的第一连接地址以及目标标定信息；解析目标标定信息，确定目标标定参数以及待标定主体的第二连接地址；基于第一连接地址构建云端服务器与中间转换器的通信连接，并将目标标定参数以及第二连接地址发送至中间转换器，中间转换器基于第二连接地址向待标定主体发送与目标标定参数对应的标定指令进行参数标定。通过实施本技术方案，采用以太网的形式实现了XCP的远程标定，使得参数标定更加便捷，无需人工现场标定，同时不再依靠CAN总线进行通信，无需设置较多的CAN总线，降低了整车重量以及标定成本，可扩展性大大提高。
",G05B19/042,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,"关键词匹配: 以太网, CAN","实现, 包括, 装置, 方法, 设置, 设备, 控制","通信, 汽车, 控制"
CN117087569A,CN202311159591.2,"本发明提供了一种应用于车辆防护的设备控制方法及系统，在车辆的目标周围区域内存在指定攻击行为时，即在有攻击者，如儿童，攻击车辆时，按照对应的优先级标识，依次执行报警交互策略中的子策略，控制目标设备执行目标交互操作，吸引攻击者的注意力，提示并哄劝攻击者不再攻击车辆，通过本发明能够在一定程度上避免攻击者划伤车辆，提高用户体验，且不需要额外配置如车辆覆盖物等部件，不提高成本，也避免了使用车辆覆盖物等部件的繁琐性。并且，本发明中的子策略与指定攻击行为对应的攻击者的特征信息相匹配，则使得报警交互策略与车辆攻击场景的匹配度更高，易被攻击者接受，更能有效的保护车辆。
",B60R16/023,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/023,"提供, 方法, 设备, 控制, 系统, 配置","车辆, 控制"
CN117082007A,CN202311061501.6,"本申请实施例提供了一种报文处理方法和装置，本申请实施例通过目标报文的五元组信息，确定目标报文的目标报文类型，获取目标报文中第一目标字段和第二目标字段，根据第一目标字段，利用预设规则，对目标报文进行合法性校验，在目标报文通过合法性校验的情况下，按照第二目标字段对应的多个匹配规则的优先级顺序，利用多个匹配规则，对第二目标字段进行匹配，得到第一匹配结果，并在与任一匹配规则匹配对应的传输操作为丢弃，则确定所述第一匹配结果对应的传输操作为丢弃，本申请实施例可以滤除被篡改的报文，或者由于网络调试错误产生的无用报文，或者当前ECU不需要的报文等非正常报文，提高车载以太网的通信安全性。
",H04L47/6275,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载以太网, 以太网","提供, 方法, 处理, 装置","通信, 网络"
CN117061364A,CN202311133218.X,"本发明涉及车辆通信技术领域，公开了车辆通信系统的部署方法、通信方法、车辆及存储介质，本发明的部署方法包括获取目标车辆中终端节点的数量以及终端节点的流量信息，流量信息包括流量种类及流量大小；基于流量种类确定流量的流量属性，流量属性包括持续流量以及非持续流量；基于流量属性以及流量大小，确定终端节点的实际流量大小，以得到目标车辆的实际需求流量；根据实际需求流量以及网络节点的带宽，确定网络节点的数量；基于网络节点的数量以及终端节点，确定车辆通信系统。结合流量属性确定终端节点的实际流量大小保证了所得到的实际流量大小的准确性，保证了所得到的网络节点的数量的准确性，能够得到可靠性较高的车辆通信系统。
",H04L41/14,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L41/14,"系统, 方法, 包括","车辆, 通信, 网络"
CN116846922A,CN202310628026.X,"本申请公开了一种信号打包方法，包括：获取报文以及当前信号，所述报文包括顺序分布的多个字节；若所述报文的剩余空位大于或者等于所述当前信号的信号长度，则对所述报文按照分布顺序进行搜索；若搜索到目标连续空位，则将所述当前信号放置于所述目标连续空位中，所述目标连续空位的长度大于或等于所述当前信号的信号长度；若未搜索到所述目标连续空位，则对所述报文中已放置的信号的位置进行调整，获得所述目标连续空位，并将所述当前信号放置于所述目标连续空位中。通过上述过程，可以将当前信号打包到报文中，在充分利用报文的容量的同时，使得报文中的信号不会出现重叠，整个过程无需人工参与，可提高信号打包的效率。
",H04L67/12,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L67/12,"方法, 包括",通用
CN116719261A,CN202310709851.2,"本申请公开了一种汽车中央网关控制器、控制方法及汽车电气架构，汽车中央网关控制器包括处理器和以太网通信模块，其中处理器的内核包括第一内核和第二内核，以太网通信模块包括第一以太网物理层收发器和以太网交换机，第一内核和第二内核中的任意一个内核与第一以太网物理层收发器连接，第一内核和第二内核均与以太网交换机连接。根据本申请实施例，在处理器中的一个内核发生故障时，仍可以通过另一个未故障的内核收发以太网交换机的以太网报文，使以太网交换机的所有以太网报文都不会丢失，从而有效提高汽车中央网关控制器的功能安全等级。
",G05B19/042,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: 以太网,"方法, 包括, 处理, 控制","通信, 汽车, 控制"
CN116708606A,CN202310595633.0,"本申请公开了一种电池管理系统BMS的通讯协议场景切换方法及装置，该方法应用于BMS，BMS与诊断上位机通讯连接，BMS内设置有应用于高性能新能源汽车场景的第一CAN通讯协议、应用于梯次场景的第二CAN通讯协议，BMS中还设置有用于指示动力电池的性能状态的变量，其包括：检测变量的变量值；在变量值为第一数值情况下，根据与诊断上位机的报文交互过程，确定是否通过BMS的应答访问机制；在确定通过BMS的应答访问机制的情况下，将BMS由第一CAN通讯协议切换至第二CAN通讯协议。本申请在不对动力电池进行拆解的前提下，实现了电池由高性能新能源汽车场景到梯次场景的应用，延长动力电池全生命使用周期，提高了电池的利用率。
",H04L69/18,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"实现, 包括, 装置, 方法, 设置, 系统, 检测","检测, 汽车, 诊断"
CN116582390A,CN202310723779.9,"本申请公开了一种LIN总线通信的测控装置。LIN总线通信的测控装置包括控制器控制电路和收发器控制电路；控制器控制电路和收发器控制电路电连接，收发器控制电路与LIN总线电连接；控制器控制电路用于生成控制信号，且接收收发器控制电路所监测的LIN总线的状态；收发器控制电路用于根据控制信号生成LIN总线传输的电平，并监测LIN总线的状态。根据本申请实施例，有利于改善LIN总线测试效率过低、自动化水平低、可靠性差的缺点。
",H04L12/40,天津经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/40,"包括, 测试, 装置, 生成, 控制, 电路","通信, 测试, 控制"
CN116533902A,CN202310574288.2,"本申请公开了一种低压能量管理方法和装置。其中，该方法包括：获取低压供电系统在目标工况下的剩余可用功率、第一动态调节次序和第二动态调节次序，在剩余可用功率小于第一预设阈值的情况下，根据第一动态调节次序依次对多个功能进行功率调节，直至剩余可用功率大于第二预设阈值或多个功能的功率均调节完毕，每个功能的功率调节次数均不多于一次，在多个功能的功率均调节完毕且剩余可用功率不大于第二预设阈值的情况下，根据第二动态调节次序依次对多个功能进行开关状态调节，直至剩余可用功率大于第二预设阈值或多个功能的开关状态均调节完毕。根据本申请实施例，能够满足用户的实际需求，提升用户体验感。
",B60R16/023,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/023,"系统, 方法, 包括, 装置",通用
CN116470980A,CN202310316059.0,"本发明提供了一种时间同步系统及方法，该系统包括：以太网通信链路中的主节点、桥节点和从节点，以及CAN链路中的CAN节点。主节点基于由GPS模组发送的脉冲信号和UTC时钟信息更新本地时钟，并将第一时钟同步信息发送给桥节点；桥节点利用第一时钟同步信息更新本地时钟，并将第二时钟同步信息发送给从节点；从节点利用第二时钟同步信息更新本地时钟，并将第三时钟同步信息发送给CAN节点；CAN节点基于第三时钟同步信息更新本地时钟。本方案使车载混合网络中各个节点都采用相同时钟源来进行时间同步，从而提高时间同步精度。
",H04J3/06,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,"关键词匹配: 以太网, CAN","系统, 提供, 方法, 包括","通信, 网络"
CN116405512A,CN202211724694.4,"本申请实施例提供一种车载远程信息处理器T‑BOX系统，该系统包括：应用处理器AP和三个调制解调器Modem，AP处理器和三个Modem上分别设置有PCIe接口，AP处理器与每个Modem通过PCIe接口连接。本申请实施例，提高了外部移动网络为整车信息娱乐域以及智能驾驶域提供的网络带宽。
",H04L67/12,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L67/12,"提供, 包括, 设置, 处理, 系统","驾驶, 网络"
CN116366463A,CN202211666186.5,"本申请公开了一种车载总线报文数据的仿真方法及装置，涉及车辆技术领域。其方法包括：在车辆异常的情况下，获取车辆运行中的日志文件信息；解析日志文件信息，得到M个报文数据及M个时间戳；向第二设备发送M个报文数据和M个时间信息；将M个报文数据和M个时间信息缓存至第三队列中；基于各报文数据对应的时间信息，获取到报文数据的时间差，时间差为报文数据的时间戳与上一帧报文数据的时间戳之差；按照各报文数据的时间差，将第三队列中的报文数据逐个发送至车载总线。根据本申请实施例，能够准确复现车辆出现异常问题的驾驶行为，从而提高车辆的测试效率。
",H04L41/14,北京经纬恒润科技股份有限公司,车载以太网板卡技术,3.0,关键词匹配: 车载总线; IPC分类号匹配: H04L41/14,"包括, 测试, 装置, 仿真, 方法, 设备","车辆, 驾驶, 测试"
CN116279204A,CN202211665269.2,"本申请实施例提供的一种车辆TBOX的控制方法及车辆控制系统，通过结合车辆的整车模式、授权状态、联网状态、缓存共享状态，确定当前车辆所处的状态。因此，当前车辆所处的状态可以表示当前车辆所处的生命周期、使用场景以及用户的需求。而且每两种车辆所处的状态之间可以根据整车模式、车辆授权状态、联网状态、缓存共享状态的变化进行跳转。因此，车辆所处状态可以根据车辆所处的生命周期、使用场景以及用户不同时期的需求的变化进行跳转。根据车辆所处状态与数据共享状态的映射关系，确定当前车辆所处的状态所映射的目标数据共享状态，此时，目标数据共享状态考虑到了车辆所处的生命周期、使用场景以及用户需求，因此，可以更好的适应现实需求。
",B60R16/023,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/023,"系统, 提供, 方法, 控制","车辆, 控制"
CN116279227A,CN202310183891.8,"本申请公开了一种车载域控制器供电电路及控制方法，涉及车辆技术领域。车载域控制器供电电路包括：第一电压比较器、第二电压比较器、开关控制子电路，第一电压比较器的输入端与第一电源连接，第一电压比较器的输出端与开关控制子电路连接；第二电压比较器的输入端与第二电源连接，第二电压比较器的输出端与开关控制子电路连接；开关控制子电路还与第一电源、第二电源和多个车载域控制器连接；第一电压比较器比较第一电源提供的第一电压与电压阈值的大小，第二电压比较器比较第二电源提供的第二电压与电压阈值的大小，以控制开关控制子电路中开关的通断对多个车载域控制器进行供电。能够降低电源管理系统和热管理复杂度，降低成本。
",B60R16/03,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/03,"提供, 包括, 方法, 控制, 系统, 电路","车辆, 控制"
CN116248428A,CN202211656895.5,"本申请实施例提供了一种仲裁场与数据场的定位方法及装置，该仲裁场与数据场的定位方法包括通过示波器采集总线CANFD被测设备发送的信号，信号包括标识符和仲裁场波特率，基于标识符确定信号中仲裁场数据帧的起始位，根据仲裁场波特率和预设数据位数计算延时时间，基于起始位和延时时间计算信号的目标位，目标位为信号中仲裁场数据帧与数据场数据帧的区分位。根据本申请实施例，在对起始位定位后基于进行延时时间延时，可以准确定定位并区分仲裁场与数据场，从而提高了CANFD总线测试时，对仲裁场和数据场的参数测量结果的准确性。
",H04L12/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,3.0,关键词匹配: CAN; IPC分类号匹配: H04L12/40,"提供, 包括, 测试, 装置, 方法, 设备, 计算",测试
CN116015806A,CN202211619849.8,"本申请公开了一种CAN总线异常数据的检测方法及装置，利用多个由N个ID对应的CAN总线数据组成的数据序列训练得到的单类支持向量机模型，在进行CAN总线异常数据的检测时，将待检测的N个ID对应的CAN总线数据组成待检测数据序列，将待检测数据序列输入训练好的单类支持向量机模型即可得到待检测数据序列的检测分数，通过将检测分数与预设的分数阈值进行比较，即可确定待检测N个ID对应的CAN总线数据中是否存在异常数据。根据本申请实施例，只需一个单类支持向量机模型即可实现对N个ID对应的CAN总线数据的综合异常检测，相比于现有的针对每个ID对应的CAN总线数据分别采用不同的模型进行检测，无需进行模型的切换，检测效率更高。
",H04L9/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"实现, 模型, 装置, 方法, 检测",检测
CN115987963A,CN202211472917.2,"本发明提供一种车辆数据上传方法、无线网联控制器及系统，车辆数据上传方法应用于车辆中的Tbox。Tbox中搭建NFS服务，并设置为Client模式，各ECU中搭建NFS服务且为Server模式，以向Tbox共享本地数据。Tbox接收数据平台发送的数据上传指令，解析得到数据特征，以及确定数据特征对应的目标ECU，挂载目标ECU，以获取数据特征对应的目标数据，并将目标数据上传到FTP服务器。即本发明中，Tbox挂载ECU，以从ECU中获取目标数据，并上传到FTP服务器，则在使用时只需保证车辆中的Tbox的网络安全即可，不再需要对每个ECU进行改造，进而减少开发难度和工作量，安全性更高。
",H04L67/06,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L67/06,"提供, 平台, 方法, 设置, 控制, 系统","车辆, 网络, 控制"
CN115476795A,CN202211190887.6,"本申请公开了一种电源管理方法、装置及计算机可读存储介质，涉及车辆技术领域。其方法包括：在电源管理子系统监听到输入设备节点的待处理输入事件的情况下，控制电源管理子系统处于目标电源状态；在与电源管理子系统中关联的至少一个事件处理模块中，查找与待处理输入事件匹配的目标事件处理模块；电源管理子系统接收目标事件处理模块的唤醒请求；响应于唤醒请求，在电源管理子系统中生成目标事件处理模块的唤醒锁并保存；控制电源管理子系统将电源状态信息传输至目标事件处理模块，以使目标事件处理模块根据目标电源状态进行相应处理。能够对车载终端的电源进行统一管理，减少对软件代码的修改，从而提高了车载嵌入式系统的可移植性。
",B60R16/03,经纬恒润(天津)研究开发有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/03,"包括, 装置, 方法, 生成, 处理, 设备, 控制, 系统, 计算","车辆, 控制"
CN115314522A,CN202210884694.4,"本申请公开了一种车载操作系统，该系统包括：驱动设备，用于基于通信协议与车载硬件设备通信，其中，通信协议包括至少一个服务级别、至少一个服务级别分别对应的功能协议和至少一个服务级别分别对应的实时性要求；每个服务具有至少一个服务级别，不同服务被设置为不同的优先级，存储器，用于存储至少一个服务级别、至少一个服务级别分别对应的功能协议和至少一个服务级别分别对应的实时性要求，监控器，用于监控车载操作系统的系统资源，在系统资源大于或等于预设阈值的情况下，触发流控算法，基于流控算法根据至少一个服务级别的配置资源，调整配置资源以及车载操作系统内的至少一个服务的服务级别。解决现有技术中系统卡顿、实时性低的问题。
",H04L67/12,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L67/12,"包括, 算法, 具有, 设置, 设备, 系统, 配置",通信
CN115243263A,CN202210867786.1,"本发明实施例提供了一种信息处理方法、系统、存储介质及电子设备。其中，该方法应用于电子控制单元，电子控制单元对车载网络入侵信息进行解析，获得车载网络入侵信息的信息类型；电子控制单元确定与信息类型对应的入侵事件相似度，基于入侵事件相似度确定信息过滤方式；电子控制单元基于信息过滤方式对同一信息类型的多个车载网络入侵信息进行过滤，将过滤后的车载网络入侵信息中的至少部分信息发送至云端。本发明通过对车载网络入侵信息进行过滤，可以减少网络入侵信息量，避免大量网络入侵信息导致网络瘫痪的问题。
",H04W12/088,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: 车载网络,"提供, 方法, 处理, 设备, 控制, 系统","网络, 控制"
CN115174554A,CN202210723806.8,"本申请公开一种控制器的日志上传方法及系统，应用于能访问公网的主控制器的方法包括：接收不能访问公网的从控制器通过车载局域网发送的日志上传请求；在日志等级表征待上传日志为正常日志的情况下，向服务器发送包括日志等级和日志标识的正常日志生成通知，在接收到服务器发送的第一上传命令后，从从控制器的日志存储路径下拷贝日志标识对应的待上传日志，将待上传日志上传至服务器；在日志等级表征待上传日志为异常日志的情况下，从从控制器的日志存储路径下拷贝日志标识对应的待上传日志，并将待上传日志上传至服务器。本申请不仅可以自动化回传控制器日志，还可以根据日志等级和上传需求进行上传，从而提高了负责人获取日志的效率。
",H04L67/06,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L67/06,"包括, 生成, 方法, 控制, 系统",控制
CN115102888A,CN202210686759.4,"本申请提供的车载以太网协议一致性测试方法，该方法应用于自动化测试系统中的电子设备，自动化测试系统中的转换设备上的传统以太网接口与电子设备连接、车载以太网接口与待测车载以太网控制器连接。响应于输入操作来搭建待测车载以太网控制器对应的测试序列；对于测试序列中的各测试用例，运行该测试用例并调用测试库文件中已封装的、与该测试用例相匹配的测试脚本，生成测试报文，并通过转换设备向待测车载以太网控制器发送测试报文；通过转换设备接收待测车载以太网控制器基于测试报文所返回的响应报文，处理所述响应报文得到该测试用例的测试结果。
",H04L43/18,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载以太网, 以太网","提供, 测试, 方法, 生成, 处理, 设备, 控制, 系统","测试, 控制"
CN115086056A,CN202210736401.8,"本发明提供一种车载以太网防火墙分类统计方法、装置和设备，方案首先配置防火墙规则，为每一条防火墙规则定义唯一的标签信息，并基于预先定义的预设规则统计信息，设定各个规则统计组基于所述标签信息的过滤条件，在获取到防火墙规则统计信息及其对应的标签信息后，通过各个规则统计组的过滤条件以及上述标签信息，来确定防火墙规则统计信息与所述规则统计组之间的对应关系，以使得规则统计组能够统计制定目标的防火墙规则统计信息，使得系统对于防火墙规则统计信息能够进行灵活管理，从而更能满足用户的不同需求，极大地方便了用户的统计信息收集。
",H04L9/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载以太网, 以太网","提供, 装置, 方法, 设备, 系统, 配置",通用
CN115001894A,CN202210574316.6,"本申请提供了车载总线信号的存取方法和装置，方法包括：将采集到的若干车载总线信号的数据点以二进制字节格式存储在对应的数据文件中，以得到数据文件集；在需要读取指定车载总线信号的数据点时，从数据文件集中确定指定车载总线信号对应的数据文件，作为目标数据文件；根据目标数据文件中的数据指针当前指向的数据点的位置和指定时间段，确定数据指针需要移动的字节个数；按照字节个数移动数据指针，判断移动后数据指针指向范围内的数据点是否为指定时间段内的数据点；若是，则对移动后数据指针指向范围内的所有数据点进行解析，得到指定时间段内的所有数据点的解析结果。本申请无需抛弃历史数据且存取速度快，能精确解析指定时间段内的数据点。
",H04L12/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,3.0,关键词匹配: 车载总线; IPC分类号匹配: H04L12/40,"提供, 方法, 包括, 装置",通用
CN115002034A,CN202210679822.1,"本公开提供的一种报文通信网络构建、报文传输方法及装置，通过确定需要在车载网络中传输的预设通信报文，并根据预设通信报文确定环形通信网络中的阻断链路。基于阻断链路，对车载网络中的交换机生成对应的报文传输配置信息，在报文传输配置信息中，对交换机中与阻断链路连接的端口设置禁止传输报文类型，构建出由各交换机串联的报文通信网络，使得交换机可以基于该报文通信网络，按照报文传输配置信息对预设通信报文进行传输，在不影响车载网络的链路冗余通信基础上，避免禁止传输报文类型的通信报文引起广播风暴，保障车载网络的正常运行。
",H04L47/10,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: 车载网络,"提供, 装置, 方法, 设置, 生成, 配置","通信, 网络"
CN114978656A,CN202210534839.8,"本公开提供一种车载以太网检测防御方法及装置，方法包括：在TBOX分别通过公网与远端中心建立通信连接，通过车载以太网与ECU建立通信连接之后，启动TCP端口侦听服务，接收ECU发送的第一注册请求，第一注册请求包括ECU标识、ECU申请加入的至少一个入侵检测防御子组的第一组名；在确定第一组名已创建且根据ECU标识确定ECU未注册第一组名对应的入侵检测防御子组的情况下，将第一组名对应的入侵检测防御文件同步给ECU，第一组名对应的入侵检测防御文件是TBOX从远端中心获取的。通过该方案，不仅可以减少远端中心需要维护的连接数、降低车辆被外界入侵的风险，还可以避免入侵检测防御文件传输的资源浪费，在远端中心上将属于同一辆车的多个ECU关联起来呈现整车效果。
",H04L9/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载以太网, 以太网","提供, 包括, 装置, 方法, 检测","车辆, 检测, 通信"
CN114938289A,CN202210426623.X,"本公开实施例提供的车载以太网的认证方法，在以太网认证系统启动后，服务器端根据配置信息为每个客户端建立一个定时器，并形成一个定时器链表。然后等待建立定时器的客户端的响应报文，在收到客户端的响应报文的情况下，删除客户端的定时器，在没有收到客户端的响应报文的情况下，则会一直等待。在客户端的定时器超时、仍没有收到客户端的响应报文，为客户端建立一个新的定时器，继续等待，持续循环，直到收到客户端的响应报文为止。该方法可以实现客户端的认证状态恢复。
",H04L9/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载以太网, 以太网","提供, 实现, 方法, 系统, 配置",通用
CN114844711A,CN202210535033.0,"本申请提供了一种车载以太网安全状态检测方法和装置，车载以太网的各个ECU上运行客户端，客户端的状态机在客户端启动后处于初始状态，客户端的状态机包括有限状态集Q、有限输入集合Σ、有限输出集合Λ、输出函数和转移函数，有限状态集Q表征ECU能够处于的状态集合，有限输入集合Σ表征接收到客户端交互的服务器端发送的报文事件和定时器超时触发的事件的集合，有限输出集合Λ表征客户端采取的输出动作集合，方法包括：根据有限状态集Q和有限输入集合Σ，结合转移函数确定状态机的输出状态；根据有限状态集Q和有限输入集合Σ，结合输出函数确定状态机的输出动作。本申请的整个状态跳转过程无需人为干预，确保整车所有ECU工作在可控可管的状态下。
",H04L9/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载以太网, 以太网","提供, 包括, 装置, 方法, 检测",检测
CN114826680A,CN202210322263.9,"本公开提供一种车载数据处理方法及装置，该方法应用于车载网络中任一防火墙，包括：在车载网络设备发送的待测报文中源MAC地址与预设安全规则中所有预设MAC地址均不相同的情况下，将所述待测报文中源IP地址与预设动态学习安全规则中预设IP地址进行匹配；在所述源IP地址与预设IP地址相同的情况下，将所述待测报文中源MAC地址作为预设MAC地址增加到所述预设安全规则中，并允许所述待测报文通过；在所述源IP地址与所有预设IP地址均不相同的情况下，将所述待测报文丢弃。该方法可以通过过滤IP地址的方式自动更新预设安全规则，而无需人工参与，从而不仅节省了人力，还可以提高预设安全规则的更新效率，进而提高车载网络设备的接入效率。
",H04L9/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: 车载网络,"提供, 包括, 装置, 方法, 处理, 设备",网络
CN114710356A,CN202210376775.3,"本发明公开一种车载防火墙的数据处理方法、装置及车载防火墙设备，方法包括：响应于车载应用层内应用数据包的传输，加载基于内核数据包过滤器机制实现的防火墙应用程序，防火墙应用程序包括针对不同应用服务设置的检测子程序链；当传输的应用数据包经过防火墙应用程序在数据路径中内核包过滤器流量控制子系统上挂载的触发点时，利用尾调机制调用防火墙应用程序中的检测子程序链，对应用数据包执行安全检测；基于防火墙应用程序对应的返回值，对应用数据包执行处理动作。通过上述方法可以实现车载防火墙的数据处理，解决了现有车载以太网协议实现可以将所有应用层数据报文都能送达应用层进行处理而导致车载网络存在安全隐患的问题。
",H04L9/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,3.0,"关键词匹配: 车载以太网, 车载网络, 以太网","实现, 包括, 装置, 方法, 设置, 处理, 设备, 控制, 系统, 检测","检测, 网络, 控制"
CN114701440A,CN202210373622.3,"本申请公开一种基于HUD的辅助驾驶系统，该系统包括域控制器、HUD、DMS摄像头、ADAS摄像头和车身内部传感器，HUD、DMS摄像头、ADAS摄像头和车身内部传感器分别与域控制器通过串行解串线路通信连接；DMS摄像头，用于采集驾驶员图像信息，并通过串行解串线路将驾驶员图像信息发送给域控制器；车身内部传感器，用于采集车身信号，并通过串行解串线路将车身信号发送给域控制器；ADAS摄像头，用于采集车辆前方视野图像信息，并通过串行解串线路将车辆前方视野图像信息发送给域控制器；域控制器，用于根据驾驶员图像信息、车身信号和车辆前方视野图像信息并通过串行解串线路控制HUD进行辅助驾驶。
",B60R16/023,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/023,"系统, 包括, 控制","驾驶, 控制, 车辆, 通信, 摄像, 传感器"
CN114666214A,CN202111574779.4,"本申请公开了一种车联网汽车的系统防火墙配置方法、装置和T‑BOX，该方法和装置具体为当T‑BOX首次启动时，基于预存的初始配置文件对系统防火墙进行配置；根据文件变更请求接收报文数据；基于报文数据对初始配置文件进行修改，得到当前配置文件；根据当前配置文件对系统防火墙进行重新配置。这样，当车辆所处地域发生变化后，通过对配置文件的修改即可实现对系统防火墙的重新配置，使得该系统防火墙适应当地域名配置的特点，保证了在车辆所处地域发生变化时T‑BOX能够继续正常联网，并且能够满足用户对于上网行为限制的要求。
",H04L41/0813,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L41/0813,"实现, 装置, 方法, 系统, 配置","车辆, 汽车"
CN114655142A,CN202210415631.4,"本发明公开了一种双MCU控制系统及汽车，包括：主MCU、从MCU、隔离芯片、隔离芯片控制电路、第一系统基础芯片、第一分压电阻、第二分压电阻和驱动芯片，第一系统基础芯片对隔离芯片的控制优先级高于从MCU对隔离芯片的控制优先级，当第一系统基础芯片监控到从MCU故障时，第一系统基础芯片通过隔离芯片控制电路可强制打开隔离芯片，使主MCU对驱动芯片的控制路径强制打开，实现主MCU对驱动芯片的控制。主MCU向驱动芯片输出的控制信号的优先级高于从MCU向驱动芯片输出的错误控制信号的优先级，因此本发明可避免从MCU在故障时输出的错误控制信号对主MCU输出的控制信号产生干扰，保证主MCU对驱动芯片的可靠控制。
",B60R16/023,天津经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/023,"实现, 包括, 控制, 系统, 电路","汽车, 控制"
CN114615061A,CN202210241428.X,"本申请公开一种以太网接入认证方法及装置，方法包括至少一个ECU、TBOX和车载交换机，每个ECU中部署认证客户端，TBOX部署有认证服务端，方法还包括：获取整车MAC地址信息表中存储的TBOX的MAC地址，整车MAC地址信息表包括TBOX的MAC地址、各个ECU的MAC地址；生成单播开始认证报文，单播开始认证报文的源地址为认证客户端所属ECU的MAC地址，目的地址为TBOX的MAC地址；向车载交换机发送单播开始认证报文，以使车载交换机根据TBOX的MAC地址将单播开始认证报文转发给认证服务端。该方案可以通过更改预设以太网协议(如IEEE 802.1X协议)，使得报文格式由组播改为单播，从而避免车载交换机丢弃报文，进而成功完成认证流程。
",H04L9/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: 以太网,"生成, 方法, 包括, 装置",通用
CN114338234A,CN202210188836.3,"本发明提供了一种处理报文的方法及装置，该方法包括：接收由第一ECU发送至目标端口的待处理报文；解析待处理报文的数据链路层数据和网络IP层数据，以得到至少一条待匹配字段；将待匹配字段与相应的过滤规则进行匹配，获取匹配结果；根据由各条待匹配字段与相应的过滤规则进行匹配所得到的匹配结果，确定允许待处理报文通过或者丢弃待处理报文。本方案中，在接收到待处理报文时，解析待处理报文的数据链路层数据和网络IP层数据，得到至少一条待匹配字段。利用过滤规则对待匹配字段进行匹配，并根据匹配结果确定允许待处理报文通过或者丢弃待处理报文，以实现对数据链路层数据的过滤，提高车载网络系统的安全性。
",H04L9/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: 车载网络,"提供, 实现, 包括, 装置, 方法, 处理, 系统",网络
CN114301792A,CN202111646091.2,"本发明提供一种交通流模拟方法及交通流传感器，应用于汽车技术领域，该方法在获取目标仿真车的行驶路线和车辆信息后，根据行驶路线和车辆位置，在动态地图中确定至少一条与目标仿真车的行驶过程相关的相关车道，获取动态地图中记录的各相关车道的车道信息，并根据车辆运行拓扑中记录的各相关车道的车辆队列以及目标仿真车的车辆位置，确定目标仿真车四周的相关仿真车，最后根据各相关车道的车道信息、各相关仿真车的车辆信息以及目标仿真车的车辆信息，确定目标仿真车的驾驶策略并控制目标仿真车行驶。本方法车辆集合中的各仿真车均可以使用动态地图以及车辆运行拓扑，能够降低交通流模拟过程对计算资源的消耗，进而提高智能驾驶测试效果。
",H04L41/12,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L41/12,"提供, 测试, 仿真, 方法, 控制, 计算, 模拟","驾驶, 测试, 汽车, 控制, 车辆, 传感器"
CN114282519A,CN202111514547.X,"本发明公开一种CAN数据的解析方法及装置，包括：获取根据CAN数据库文件生成的XML文件；将XML文件中CAN报文参数信息存储到报文数据结构，CAN信号参数信息存储到信号数据结构；从待解析的CAN报文中获取目标CAN通道标识和目标CAN报文标识；对CAN报文进行解析获得目标CAN报文基本信息，根据报文数据结构使用目标CAN报文基本信息更新由目标CAN通道标识和目标CAN报文标识的组合对应的初始化的CAN报文基本信息，即更新目标初始化的CAN报文基本信息；对CAN报文进行解析获得由目标CAN通道标识和目标CAN报文标识的组合对应的目标CAN信号标识下的目标CAN信号基本信息，根据信号数据结构使用目标CAN信号基本信息更新目标CAN信号标识对应的初始化的CAN信号基本信息。
",G06F40/205,经纬恒润(天津)研究开发有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"生成, 方法, 包括, 装置",通用
CN114248709A,CN202111499975.X,"本发明实施例公开一种燃油车低压电气系统能量管理方法及装置，该方法包括：获取燃油车的电源模式信息、蓄电池荷电状态SOC信息、车辆运动状态信息、供电端故障信息；根据电源模式信息、蓄电池荷电状态SOC信息、车辆运动状态信息、供电端故障信息、预设车辆低压能量管理策略，确定与当前车辆对应的低压能量控制策略，其中，预设车辆低压能量管理策略是对燃油车低压电气系统能量进行管理的策略，低压能量控制策略包括预警信息和限制负载功率信息中的至少一种。本公开中提出的燃油车低压能量管理方法考虑范围更加全面，能量管理策略更加精细化，能更好地保证燃油车辆正常使用。
",B60R16/023,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/023,"包括, 装置, 方法, 控制, 系统","车辆, 控制"
CN114257986A,CN202210105796.1,"本发明公开一种车辆CAN网络攻击的识别方法及装置，方法包括：响应于CAN网络中节点被唤醒，接收网络管理系统发送的网络管理报文，利用节点休眠前存储的计数值生成用于验证网络管理报文的安全认证信息，并将安全认证信息填充至网络管理报文的预留字节中；在CAN网络中节点进行通信的过程中传输填充有安全认证信息的网络管理报文，并利用安全认证信息对总线发送的网络管理报文进行多重合法性验证；在多重合法性验证均通过的情况下，将网络管理报文作为合法报文传输至网络管理系统。通过上述方法可以实现车辆CAN网络攻击的识别，解决了现有车辆CAN网络的通信方式无法准确识别车辆CAN网络攻击的问题。
",H04W4/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"实现, 包括, 装置, 方法, 生成, 系统","车辆, 通信, 网络, 验证"
CN114143746A,CN202111444898.8,"本发明提供一种目标网段休眠状态管理方法和相关设备，方案在获取到各个网段对应的网络管理报文后，对报文进行解析，基于报文中的各个网段的动态休眠指示位、目标网段休眠指示位和目标网段唤醒需求指示位的指示状态进行判断，基于判断结果控制部分网段满足条件的网段进入休眠状态，对于日渐复杂的车载网络而言，本发明可以灵活动态实现不同网段的休眠和唤醒，降低了系统损耗。
",H04W4/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,1.0,关键词匹配: 车载网络,"提供, 实现, 方法, 设备, 控制, 系统","网络, 控制"
CN114137937A,CN202111416284.9,"本发明公开了一种自动化诊断功能测试方法及系统，方法包括：上位机通过以太网协议端口输出诊断数据至以太网网关；以太网网关接收测试节点在接收到诊断内容后返回的诊断响应数据并传送给上位机；上位机通过CAN协议工具采集获取CAN总线上测试节点的实车数据；上位机比较分析诊断响应数据和所述实车数据，得到诊断功能的测试结果。上述方案的实现无需开发新型以太网诊断工具，降低开发成本同时可靠性得到保证；利用CANoe工具链实现自动化测试稳定简易，大大的提高了工作的效率和保证了测试的准确性。
",G05B23/02,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,"关键词匹配: 以太网, CAN","实现, 包括, 测试, 方法, 系统, 工具","测试, 诊断"
CN114120481A,CN202111402473.0,"本发明公开了一种CAN数据的采集方法、装置及系统，网关设备与车辆TBOX通过车载以太网连接，网关设备对连接的所有CAN通道进行数据采集，得到每个CAN通道内的CAN报文，按照预设的以太网帧报文格式，分别将每个CAN通道内CAN报文打包成以太网帧，并通过车载以太网发送至车载TBOX，以使车载TBOX将所有以太网帧上传给后台服务器。本发明通过将网关设备与车载TBOX之间车载以太网连接，保证了传输带宽，同时本发明车载TBOX将所有所述以太网帧上传给后台服务器，存储空间充足，可满足对所有CAN通道进行数据采集，实现全量CAN数据的采集和传输。
",G07C5/08,北京经纬恒润科技股份有限公司,车载以太网板卡技术,3.0,"关键词匹配: 车载以太网, 以太网, CAN","实现, 装置, 方法, 设备, 系统",车辆
CN114050947A,CN202111320440.1,"本发明公开了车载SPI总线的通信方法及装置，Master节点向Slave节点发送注册请求帧，以令Slave节点对注册请求帧进行响应，发送注册响应帧到Master节点；在接收到Slave节点发送的注册响应帧，且连接正常的情况下，将收到的应用层消息打包成数据帧并向Slave节点发送数据帧；数据帧传输完毕后向Slave节点发送解注册帧，以令Slave节点对解注册帧进行报文有效性检查，在有效性检查通过的情况下关闭SPI通信功能。该方法在通信协议中加入了对Master节点和Slave节点的连接管理，在连接正常的情况下，才可以发送数据，保证了Master节点和Slave节点在通信过程中的可靠性。
",H04L12/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/40,"方法, 装置",通信
CN113442860A,CN202110907775.7,"本发明公开了一种用于车载低压蓄电池的充电管理方法及系统，当需要对车辆电压进行变压控制时，首先，获取当前控制周期的车速规划信息，基于当前控制周期的车速规划信息计算当前控制周期的目标档位序列；然后，获取车辆参数信息，基于车辆参数信息及当前控制周期的目标档位序列计算当前控制周期的发动机转速预测序列；最终，得到当前控制周期的目标电压优化序列，将当前控制周期的目标电压优化序列中的目标优化电压作为当前控制周期的目标电压决策值。本发明充分利用了蓄电池具备一定深度可充放电能力的特点，能够结合交通及道路状况提前对蓄电池进行充放电管理，提高了能量使用效率，避免了蓄电池电量过放，延长了蓄电池使用寿命。
",B60R16/033,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/033,"系统, 方法, 计算, 控制","车辆, 控制"
CN113162796A,CN202110205635.5,"本发明提供了一种设备更新方法、装置及设备更新系统，其中，设备更新方法应用于网关，网关记录有需要进行更新的目标设备的信息，当接收到更新通知消息时，将其发送至多个网段中的空闲网段上的设备；当接收到空闲网段上的设备针对更新通知消息反馈的响应信息时，根据该响应信息和目标设备的信息，从具有未更新的目标设备的每个空闲网段上确定一未更新的目标设备，作为当前待更新设备；将当前待更新设备反馈的响应信息发送至控制设备；当接收到设备更新信息时，将其发送至当前待更新设备，以使当前待更新设备根据设备更新信息进行更新。本发明可对多个空闲网段上的设备进行并行更新，该更新方式耗费的时间较短，对于网络带宽的利用率较高。
",H04L12/24,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/24,"提供, 装置, 具有, 方法, 设备, 控制, 系统","网络, 控制"
CN112543129A,CN202011367405.0,"本发明实施例提供队列深度的确认方法、系统及报文模拟器，以对不同预设场景下网关控制器在目标网段的实际队列深度进行确认，报文模拟器与整车网络中的各个网段相连接。该方法包括：根据目标场景初始化整车网络报文传输场景；在测试阶段，报文模拟器根据目标场景模拟实体设备进行报文发送，统计测试过程中收发两端的报文数量，并在总线上获取目标网段的实际队列深度；网关控制器在测试阶段对接收到的报文进行路由，周期性统计目标网段的实际队列深度并发送至总线上；在测试阶段结束后，报文模拟器将统计得到的报文数量和实际队列深度的最大值输出至测试结果。
",H04L12/26,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/26,"提供, 包括, 测试, 方法, 设备, 控制, 系统, 模拟","测试, 网络, 控制"
CN112511396A,CN202011360265.4,"本发明公开了一种整车通信监控方法及装置，当网关在工作周期内检测到总线发送的总线报文时，网关会中止在工作周期内的执行任务，进入接收中断，并执行接收总线报文的操作，在当前接收的总线报文的报文ID，与需由网关将报文从该报文所在的网段转发至目标网段的待接收总线报文ID相同，且目标网段不包含诊断仪或TBox所在网段，则在接收到包含总线报文ID的映射请求的情况下，采用动态发送方式，将当前接收的总线报文映射到发送映射请求的诊断仪或TBox。本发明利用网关连接各个网段的拓扑特性，实现了对车内任意一条或一组报文的实时监控，通过将总线报文发送至诊断仪或是Tbox，实现基于总线报文对总线数据进行分析并查找故障原因。
",H04L12/40,北京经纬恒润科技股份有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/40,"检测, 方法, 装置, 实现","检测, 通信, 诊断"
CN111935027A,CN202010916219.1,"本发明提供了一种整车网络负载优化方法及系统，方法包括在整车的网络平均负载大于预设的网络平均负载阈值时，网关在每连续接收到处于稳定模式的同一报文N次后，向该报文对应的目标节点转发该报文一次；目标节点在标记报文的所处模式为稳定模式后，进行计时，并每当计时结果等于该处于稳定模式的报文的正常报文周期时，自行触发报文接收中断，并执行该处于稳定模式的报文指示的操作，以及将计时结果清零并重新开始计时。相对于传统网关每次都转发报文，本发明提供的方案，网关接收到处于稳定模式的同一报文多次仅转发一次，降低了网络负载；且目标节点还周期性触发接收中断，这样既降低了网络负载，也不会对整车性能有不好的影响。
",H04L12/801,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/801,"系统, 提供, 方法, 包括",网络
CN111726777A,CN202010618953.X,"本发明公开了一种数据传输方法及装置，通过在网关和T‑Box之间设置以太网总线来增大传输带宽，网关对获取的整车通信数据进行处理得到X个用户数据单元，按照预设打包规则，将X个用户数据单元存储至N块存储区域，保证整车通信数据的全部接收和存储；当达到数据发送时间，网关在检测到已存满的存储区域时，锁定已存满的存储区域，将该存储区域中的用户数据包添加UDP报头得到UDP数据包，并传输至T‑Box，在传输完成后解锁存储区域。本发明中整车通信数据的采集过程以及传输过程为异步进行，通过在网关设置存储区域实现对整车网络中全部数据的收集，通过用以太网总线代替CAN总线来增大传输带宽，从而可传输全部的整车通信数据。
",H04W4/40,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,"关键词匹配: 以太网, CAN","实现, 装置, 方法, 设置, 处理, 检测","网络, 检测, 数据传输, 通信"
CN111698175A,CN202010589887.8,"本发明公开了一种用于网关的报文收发方法及系统，方法包括：在网关的报文接收端设置由多个硬件邮箱构成的报文接收队列，在网关的报文发送端设置由多个硬件邮箱构成的报文发送队列，基于报文接收队列控制网关的报文接收端接收报文，基于报文发送队列控制网关的报文发送端发送报文。本发明通过在报文接收端设置由多个硬件邮箱构成的报文接收队列，在接收报文时能够有效降低接收报文的丢帧风险，并且，通过在网关的报文发送端设置由多个硬件邮箱构成的报文发送队列，在发送报文时能够有效降低发送报文的丢帧风险。
",H04L12/823,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/823,"包括, 方法, 设置, 控制, 系统",控制
CN111694766A,CN202010547933.8,"本申请提供了一种CAN数据采集方法及装置，该方案由车载设备采集车辆上所有CAN通道的CAN数据，并将CAN数据进行压缩后上传至云端服务器。车载设备不对CAN数据进行过滤、筛选、解析、处理等，云端服务器负责CAN数据的利用包括接收、解析、入库和利用。这样，云端服务器能够获得车辆的全量CAN数据，当面临需求变更时，能够快速从接收到的全量CAN数据中获得新的应用需求所需的CAN数据，即快速响应新的场景需求变化。此外，车载设备对CAN数据进行压缩后再上传，减少上传CAN数据所需的带宽和流量。
",G06F12/06,北京经纬恒润科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"提供, 包括, 装置, 方法, 处理, 设备",车辆
CN111641635A,CN202010467418.9,"本发明提供了一种无损传输CAN数据的方法及装置，该方案采集车辆的全量CAN数据，当业务需求发生变化时，只需从全量CAN数据选取对应的CAN数据即可，无需修改CAN数据采集程序，因此降低了车载设备的开发成本和开发难度，同时，提高程序的可靠性。而且，该方案在接收到CAN数据后根据精确绝对时间重建了CAN数据的时间戳，使得所有CAN数据具有基于同一精确计时标准的时间戳，因此，进一步能够准确重现整车状态。
",H04L29/06,北京经纬恒润科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"提供, 装置, 具有, 方法, 设备",车辆
CN111600765A,CN202010523929.8,"本发明实施例提供通信故障记录方法及网关控制器，基于预设的关键事件的报文处理逻辑：处理步骤、各处理步骤的操作次序，每一处理步骤所涉及的报文特征(报文标识和所在网段)。该方法包括：网关控制器判断关键事件的目标处理步骤是否执行成功；若目标处理步骤执行失败，确定关键事件产生通信故障，并记录本次通信故障对应的故障记录；若目标处理步骤执行成功，根据报文处理逻辑判断目标处理步骤是否为最后一步处理步骤；若是，将报文处理逻辑中的第一步处理步骤确定为目标处理步骤；若否，执行目标处理步骤的下一处理步骤，并将下一处理步骤确定为目标处理步骤，返回执行判断关键事件的目标处理步骤是否执行成功的步骤及后续步骤。
",H04L12/24,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/24,"提供, 包括, 方法, 处理, 控制","通信, 控制"
CN111555953A,CN202010472748.7,"本申请提供一种基于车载以太网的远程诊断方法、装置、系统、TSP服务器，该方法利用TSP服务器在待诊断车辆和远程诊断设备间搭建VPN网络，形成异地局域网，从而满足远程诊断场景下的标准DoIP协议流程的实施，能够利用标准DoIP协议流程降低远程诊断的开发难度和工作量。
",H04L12/46,北京经纬恒润科技有限公司,车载以太网板卡技术,4.0,"关键词匹配: 车载以太网, 以太网; IPC分类号匹配: H04L12/46","提供, 装置, 方法, 设备, 系统","车辆, 网络, 诊断"
CN111409572A,CN202010236984.9,"本发明提供一种车身闭合系统的控制方法及装置，应用于汽车技术领域，该方法在车身闭合系统执行关闭操作，且移动部件进入防夹区的情况下，计算多个连续的检测周期对应的防夹力，并确定驱动电机的转速变化趋势。在检测到驱动电机的实际转速大于参考转速后，如果驱动电机的转速呈单调上升趋势、且各检测周期的防夹力为负值，且各防夹力的绝对值均大于预设阈值，则可判定车辆正经过起伏路或波形路，则增大防夹判断阈值，并根据增大后的防夹判断阈值识别防夹事件。本发明提供的方法，可以实现对起伏路、波形路等坏路路面的准确识别，并在判定车辆经过起伏路或波形路时增大防夹判断阈值，避免错误的识别到防夹事件，降低误防夹事件的发生几率。
",B60R16/023,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/023,"提供, 实现, 装置, 方法, 控制, 系统, 检测, 计算","车辆, 检测, 汽车, 控制"
CN111404697A,CN202010243485.2,"本发明公开了一种车载以太网仿真板卡，包括：电源模块、PCIE扩展模块、以太网交换机、至少一个以太网控制器、至少两个车载以太网仿真监控通道模块、工业以太网扩展通道模块和FPGA，其中，每个车载以太网仿真监控通道模块用于与一个车载以太网设备连接。由于本发明设置了至少两个车载以太网仿真监控通道模块，而每个车载以太网仿真监控通道模块能够与一个车载以太网设备连接，因此，本发明可以实现对多个车载以太网设备的仿真，这样，当需要对多个车载以太网设备进行仿真时，通过设置多个车载以太网仿真监控通道模块，即可实现每次同时对多个车载以太网设备进行仿真，从而大大提高了仿真效率。
",H04L12/02,北京经纬恒润科技有限公司,车载以太网板卡技术,4.0,"关键词匹配: 车载以太网, 以太网; IPC分类号匹配: H04L12/02","实现, 包括, 仿真, 设置, 设备, 控制",控制
CN111404819A,CN202010237098.8,"本发明公开了一种基于路由功能的网络控制方法及系统，方法包括：在网关休眠条件下，其中一个网段收到需要路由的报文时，唤醒网关，基于路由表确定需要路由的报文对应的源网段和目标网段，唤醒需要路由的报文对应的源网段和目标网段；当其中一个网段作为源网段或目标网段被唤醒后，在唤醒状态维持时间内未再次收到报文或需要路由报文时，保持所述源网段或目标网段处于唤醒状态；再次收到报文或需要路由报文时，重新计时源网段或目标网段的唤醒状态维持时间；唤醒时间达到唤醒状态维持时间，且未再次收到报文或需要路由报文时，将源网段或目标网段由唤醒状态切换为休眠状态。本发明能够通过路由表简单有效的对网关和网段进行休眠唤醒控制。
",H04L12/725,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/725,"系统, 方法, 包括, 控制","网络, 控制"
CN111098808A,CN201911272976.3,"本发明公开了一种基于直流电机纹波控制车身闭合部件的方法及其系统，该方法包括：采集车身闭合部件的电机在运动过程中产生的电流信号；计算电机在运动过程中产生的电流纹波的个数及各电流纹波的周期；根据电流纹波的个数及各电流纹波的周期得到电机当前时刻的运行位置和运行速度；对电流信号进行滤波处理得到电机在当前时刻的电流直流量；根据运行速度和运行位置进行闭环调速控制；根据运行位置和电流直流量进行防夹检测控制。本发明技术方案仅通过采集电机实际运行中产生的电流信号进行分析计算实现闭环调速和防夹检测控制，与传统技术相比，减少了硬件投入，同时保证控制的可靠性，利于降低生产成本，适于推广。
",B60R16/02,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/02,"实现, 包括, 方法, 处理, 控制, 系统, 检测, 计算","检测, 控制"
CN110979210A,CN201911301384.X,"本发明提供了一种整车电源系统配置方法及装置，方法包括获取目标车辆中电子器件的已配置参数；根据目标车辆中电子器件的已配置参数，计算得到整车电源系统中至少一个电子器件的未配置参数的需求值，至少一个电子器件的未配置参数的需求值包括起动机的最小功率、蓄电池的最小容量、蓄电池的最大低温电阻值和发电机的输出电流平均值的最小值中的至少一个，进而选择符合要求的起动机、蓄电池和/或发电机。本发明通过正向配置，只需通过分析和台架测试就可以得到符合车辆行驶要求的整车电源系统，不需要通过其它车型的配置反推，优化了整车电源系统的配置和匹配程度，缩短了配置时间且降低了配置成本。
",B60R16/02,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/02,"提供, 包括, 测试, 装置, 方法, 系统, 计算, 配置","车辆, 测试"
CN110794745A,CN201911149212.5,"本发明实施例提供一种车门远程控制方法及系统，系统包括手机客户端、T‑BOX和车门控制器，手机客户端与T‑BOX通过WIFI、3G、4G或5G等网络通信，相比现有RKE频段网络稳定，信号可靠性高；且T‑BOX和车门控制器通过CAN总线通信，不受外界信号的干扰，提高了车门远程控制的可靠性。并且由于CAN总线通信在车辆内部，保证了车门远程控制的安全性。
",G05B19/042,天津经纬恒润科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"提供, 包括, 方法, 控制, 系统","车辆, 通信, 网络, 控制"
CN110723086A,CN201911001956.2,"本发明提供车辆启动控制系统及方法，以解决现有车辆启动控制方式存在的耗时长、对周围信号环境要求高的问题。上述启动控制系统包括：车内启动系统；车内启动系统包括第一微处理器和第一近场通信模块；第一近场通信模块至少用于：检测智能钥匙；在检测到智能钥匙时，读取智能钥匙的钥匙数据并传输至第一微处理器；第一微处理器至少用于：根据钥匙数据验证智能钥匙的合法性；若智能钥匙合法，在满足第一预设条件时，通过与车身控制系统通讯，令整车进入启动状态。由于近场通信技术是短距离高频无线通信技术，与蓝牙技术相比，耗时相对较短。并且，近场通信技术不需要借助基站或者卫星进行通信，对周围信号环境要求较低。
",B60R16/02,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/02,"提供, 包括, 方法, 处理, 控制, 系统, 检测","通信, 验证, 控制, 车辆, 检测"
CN110708190A,CN201910916765.2,"本发明提供一种SOME/IP通信系统的配置方法、装置及系统，其中方法包括：获取SOME/IP通信系统适用于当前应用场景的系统通信矩阵；转换系统通信矩阵中各个电子控制单元的服务信息，获得各个电子控制单元的配置文件；分别将各个电子控制单元的配置文件与适用于各个电子控制单元的核心文件集成编译，获得各个电子控制单元的可执行文件；加载各个电子控制单元的可执行文件至各个电子控制单元。本发明在集成编译时直接使用预先统一搭建SOME/IP通信协议栈，可以大大提高各个ECU可执行文件的搭建效率，并且基于系统通信矩阵可以转换获得各个ECU的配置文件，这又进一步提高搭建各个ECU可执行文件的搭建效率。
",H04L12/24,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/24,"提供, 包括, 装置, 方法, 控制, 系统, 配置","通信, 控制"
CN110661847A,CN201910801803.X,"本申请公开了一种车辆诊断方法以及装置，该方法包括：接收诊断请求报文，其中，诊断请求报文包括诊断请求报文的协议类型，诊断请求报文的协议类型包括CANFD和CAN；根据诊断请求报文的协议类型确定诊断请求报文的路由方式和节点的诊断响应报文的路由方式；以诊断请求报文的路由方式向节点发送与节点的协议类型一致的诊断请求报文；以诊断响应报文的路由方式返回与诊断请求报文的协议类型一致的诊断响应报文。通过本申请的上述技术方案，能够支持使用CANFD和CAN通信协议的诊断仪设备，提升系统的可扩展性和兼容性，提高CANFD节点的更新速度。
",H04L29/08,北京经纬恒润科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"包括, 装置, 方法, 设备, 系统","车辆, 通信, 诊断"
CN110460520A,CN201910789650.1,"本发明公开了一种数据报文传输方法及整车网络系统，该方法包括：网关向具有联网功能的网络节点发送网关状态报文，网关状态报文中至少包括标识网关已满足数据报文发送条件的状态标识；网关从网络节点接收节点初始状态报文，节点初始状态报文中至少包括标识网络节点在数据报文接收之前，已满足数据报文接收条件的状态标识；网关向网络节点发送数据报文。在发送数据报文之前，网关与网络节点进行了状态的交互，保证在网关满足数据报文发送条件下，以及网络节点满足数据报文接收条件下，网关才向网络节点发送数据报文，保证了网关和网络节点之间数据传输的可靠性。
",H04L12/66,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/66,"系统, 方法, 包括, 具有","数据传输, 网络"
CN110266574A,CN201910537532.1,"本发明提供一种冲突解决调度表的调整方法、相关设备及存储介质，该方法通过在对主调度表中的报文轮询过程中，若检测到报文冲突，则调用冲突解决调度表，并按照冲突解决调度表的轮询顺序，依次对冲突解决调度表中的报文进行轮询；针对冲突解决调度表中每一个被轮询的报文，若判断出被轮询的报文被更新，则调整被轮询的报文的变化次数；若完成对冲突解决调度表中处于末位的报文的轮询，则依据冲突解决调度表中的每一个报文的变化次数，按照更新规则更新冲突解决调度表的轮询顺序；调用主调度表，并对主调度表中在检测到报文冲突时的下一个报文进行轮询，实现动态更新冲突解决调度表的轮询顺序，提高用户体验。
",H04L12/403,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/403,"提供, 实现, 方法, 设备, 检测",检测
CN110177032A,CN201910609960.0,"本发明提供一种报文路由质量监测方法及网关控制器，以对报文路由质量进行监测。上述方法包括：网关控制器统计各路由关系的报文成功接收次数以及报文成功发送次数；在路由关系的报文成功接收次数大于报文成功发送次数时，网关控制器发送表征路由关系发生路由失败的警示报文；其中，警示报文的数据域包括：发生失败的路由关系的唯一标识，以及，路由失败次数；路由失败次数为发生失败的路由关系的报文成功接收次数与报文成功发送次数间的差值。可见，在本发明中，网关控制器会统计各路由关系的报文成功接收次数和成功发送次数，在报文成功接收次数大于报文成功发送次数时，发送警示报文，以此实现对报文路由质量的监测。
",H04L12/26,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/26,"提供, 实现, 包括, 方法, 控制",控制
CN110149348A,CN201910536910.4,"本发明提供了一种车载网络的防护方法及装置，该方法包括：获取车载网络中的总线上的报文的负载率；其中，报文的负载率为报文在车载网络中的总线上出现的频率；根据报文的负载率，判断车载网络中的总线通信是否异常；若判断出车载网络中的总线通信异常，则停止车载网络中的总线通信。从而实现对车联网与车载网络的信息安全进行防护。
",H04L29/06,北京经纬恒润科技有限公司,车载以太网板卡技术,1.0,关键词匹配: 车载网络,"提供, 实现, 包括, 装置, 方法","通信, 网络"
CN109981410A,CN201910236931.4,"本发明公开了一种车载智能终端的测试系统及方法，该系统包括整车网络架构、测试端网络架构和测试装置，整车网络架构包括：第一网关控制器、一个第一被测车载智能终端和N个控制器节点；测试端网络架构包括：第二网关控制器和M个第二被测车载智能终端，每个第二被测车载智能终端与第一被测车载智能终端的硬件及硬件中存储的应用程序均相同。本发明通过建立与整车车载智能终端网络环境相同的、多个平行的测试环境，实现同步测试多个功能平行的车载智能终端，从而能够更快的发现测试问题，达到提高开发和测试进度的效果。
",H04L12/26,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/26,"实现, 包括, 测试, 装置, 方法, 控制, 系统","测试, 网络, 控制"
CN109921970A,CN201910193252.3,"本发明提供了一种数据处理方法及系统，通过获取预设统计周期内的缓存数据中的CAN报文，并确定CAN报文包括的报文帧，确定在预设统计周期内报文帧中各个字段分别对应的变化次数，根据各个字段分别对应的变化次数由高至低对CAN报文的报文帧进行结构重组，将结构重组后的CAN报文的报文帧进行压缩并存储。通过上述方法，将各个字段分别对应的变化次数由高至低对CAN报文的报文帧进行结构重组，基于重组后的报文帧进行压缩处理，得到长度最短的压缩存储数据，从而提高压缩报文帧的压缩效率。
",H04L12/40,北京经纬恒润科技有限公司,车载以太网板卡技术,3.0,关键词匹配: CAN; IPC分类号匹配: H04L12/40,"提供, 包括, 方法, 处理, 系统",通用
CN109895714A,CN201910182343.7,"本发明提供了一种挡位值的确定方法及装置，该方法包括：获取目标车辆的车辆数据，根据车辆数据，确定目标车辆是否未处于怠速停车状态且未处于空挡行驶状态，若是，获取预设挡位值计算模型，基于预设挡位值计算模型以及车辆数据，确定目标车辆的挡位值。通过本发明提供的挡位值的确定方法及装置，可以对车辆数据进行数据处理进而确定出挡位值，不再需要额外安装挡位值传感器，进而也不会增加成本。
",B60R16/023,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/023,"提供, 包括, 模型, 装置, 方法, 处理, 计算","车辆, 传感器"
CN109808615A,CN201910221386.1,"本发明提供了一种自动驾驶方法及装置，获取前方路段的天气预测信息，判断前方路段的天气预测信息是否符合自动驾驶条件，若不符合，则规划符合所述自动驾驶条件的备选路线，基于所述备选路线，驱动所述车辆自动驾驶。通过本发明，可以依据天气预测信息进行自动驾驶中的路径规划，将天气预报服务应用到自动驾驶功能中。
",B60R16/023,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/023,"提供, 方法, 装置","车辆, 驾驶"
CN109768850A,CN201910179938.7,"本发明提供了一种车载通信方法和系统，该方法通过处于上电状态的车载智能终端接收服务器发送的指令报文；当进入双冗余通信模式，接收到指令报文的主车载智能终端和备车载智能终端进行指令报文同步；主车载智能终端将指令报文转换为CAN报文发送至自动驾驶控制单元ADU；当进入单通信模式，接收到指令报文的主车载智能终端或备车载智能终端将指令报文转换为CAN报文发送至自动驾驶控制单元ADU。通过上述公开的车载通信方法和系统，采用双冗余通信模式，当主车载智能终端和备车载智能终端中任一一个车载智能终端异常工作时，通过另一个车载智能终端与服务器进行通信连接，从而实现车载智能终端与服务器之间实时通信。
",H04L1/22,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载通信, CAN","提供, 实现, 方法, 控制, 系统","驾驶, 通信, 控制"
CN109412919A,CN201811275712.9,"本发明提供了一种通信控制方法、装置及存储介质，该方法包括：监听到通信请求对象的通信请求后，确定各通信请求对象的对象类型及通信属性，以得到对各通信请求对象的通信请求的处理方式，及所请求通信的是哪个通信通道，这样，在当前存在多个通信请求对应同一个通信通道时，能够按照预设的对象优先级权衡机制，对各通信通道的通信请求进行响应，满足实际工作需求，避免当前时间多个通信请求对象对同一通信通道具有通信需求时，对该通信通道的使用冲突。
",H04L12/40,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/40,"提供, 包括, 装置, 具有, 方法, 处理, 控制","通信, 控制"
CN109347590A,CN201811219832.7,"本发明实施例提供车载以太网中DoIP实体的同步方法和DoIP实体，以实现DoIP实体间的同步。上述车载以太网中的任一DoIP实体均安装在同一台车辆中。DoIP实体上电后自检是否是同步管理者，若否，执行静默策略；若是，周期性组播关系维护报文，以声明自身为同步管理者；并且，DoIP实体在作为非同步管理者且有同步需求时，向接收到的关系维护报文中所声明的同步管理者单播同步查询报文，并接收同步管理者返回的同步回复报文；其中，同步回复报文携带有第一确认码和同步信息，或者，携带有第二确认码；第一确认码用于表征同步成功，第二确认码用于表征同步失败，同步信息至少包括车载以太网所对应车辆的车辆识别号码。
",H04J3/06,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载以太网, 以太网","提供, 方法, 包括, 实现",车辆
CN108123838A,CN201711392709.0,"本发明公开了一种节点状态管理方法，包括：第一总线网段中的节点在满足本地休眠条件时进入准备睡眠状态；统计第一总线网段中的节点进入准备睡眠状态的时长；当第一总线网段中的节点接收到网络管理报文时，将第一总线网段中的节点进入准备睡眠状态的时长重置；当第一总线网段中的节点进入准备睡眠状态的时长达到第一时间阈值时，第一总线网段中的节点进入预睡眠状态；当第一总线网段中的节点进入预睡眠状态的时长达到第二时间阈值时，第一总线网段中的节点进入睡眠状态。基于本发明公开的方法，能够缩短整车休眠所需的时间，减小整车功耗，延长电池的有效供电时间。本发明还公开了节点状态管理装置。
",H04L12/24,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/24,"方法, 包括, 装置",网络
CN108011743A,CN201710630149.1,"本发明提供了一种故障注入的方法及装置，本发明中对第一报文中部分或全部报文进行故障注入，得到第二报文，根据对第一报文注入的故障和报文接收时间得到与第二报文中每个报文对应的报文禁发信号和报文触发信号，根据与第二报文中每个报文对应的报文禁发信号和报文触发信号，将第二报文中需要发送给第二控制器的报文在其对应的报文发送时间发送给第二控制器，进而能够去测试第二控制器接收到故障报文时的响应，解决了目前还没有一种针对J1939类型报文进行CAN总线故障注入的方法的问题。
",H04L12/24,北京经纬恒润科技有限公司,车载以太网板卡技术,3.0,关键词匹配: CAN; IPC分类号匹配: H04L12/24,"提供, 测试, 装置, 方法, 控制","测试, 控制"
CN107612774A,CN201710833750.0,"本发明提供了一种多通道同步睡眠方法及系统，包括：监测每条总线上所有控制器的状态；当每条总线上所有控制器的状态均满足第一睡眠条件时，生成每条总线的睡眠标志；判断本地网关是否满足第二睡眠条件；当本地网关满足第二睡眠条件时，网关向每条总线发送睡眠确认报文；控制每条总线上所有控制器以及本地网关同步进入睡眠状态。本发明提供的多通道同步睡眠方法及系统能够在不会对网关外部连接产生影响的条件下，使网关连接的各个总线能同步进入睡眠状态。
",H04L12/26,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/26,"提供, 包括, 方法, 生成, 控制, 系统",控制
CN107360262A,CN201710800632.X,"本发明提出一种软件更新方法及装置。该方法包括：分别将车载网络中归属于同一CAN总线的待更新电子控制单元，归集为一个待更新电子控制单元集合；分别对各个待更新电子控制单元集合进行独立的软件更新。通过本发明提供的软件更新方法及装置，对各条CAN总线上的电子控制单元的更新是相对独立地进行的，各条CAN总线上的电子控制单元的更新没有先后顺序限制，可以多条CAN总线同时进行，因此提高了软件更新效率。
",H04L29/08,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载网络, CAN","提供, 包括, 装置, 方法, 控制","网络, 控制"
CN106911546A,CN201710114773.6,"本发明实施例中公开了一种报文传输方法、装置、系统及诊断平台，通过从ECU中获取多条CAN报文；将所述多条CAN报文转化成至少一条通信协议报文；将所述至少一条通信协议报文传输至上位机，由于一条通信协议可以承载多条CAN报文中的内容，因此，转化成的通信协议报文的条数少于CAN报文的条数，诊断平台与上位机之间进行的是通信协议的交互，从而减少了诊断平台与上位机之间的交互次数，减少上位机的资源开销。
",H04L12/40,北京经纬恒润科技有限公司,车载以太网板卡技术,3.0,关键词匹配: CAN; IPC分类号匹配: H04L12/40,"系统, 方法, 装置, 平台","通信, 诊断"
CN106878128A,CN201710096252.2,"本发明公开了一种标识符接收滤波器的配置方法及装置，判断接收到的报文中标准帧和扩展帧的总数是否大于预设的接收滤波器的数量，若大于判断是否包含网络管理报文，若包含依据获得的网络管理报文的基地址和节点数量对网络管理接收滤波器中的接收寄存器和掩码寄存器进行配置；对报文中的非网络管理报文采用比例分组法，计算分配给非网络管理报文中的标准帧和扩展帧滤波器的数量，再采用位分组算法分别对非网络管理报文中的标准帧和扩展帧进行分组，为每组分配一个非网络管理接收滤波器，并对每组非网络管理接收滤波器的接收寄存器和掩码寄存器进行配置。这样，不仅可以将网络管理报文单独进行处理，还有效的避免了同一报文被多个滤波器接收的问题。
",H04L12/40,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/40,"装置, 算法, 方法, 处理, 计算, 配置",网络
CN106828359A,CN201710034560.2,"本发明公开了一种电压缓慢上升的检测方法及装置，通过将目标车辆的预设防夹区域依据预设的第一规则分成多段，在车身闭合系统运行的过程中获取预设防夹区域中当前段与上一段的实际电压变化量，依据实际电压变化量与预设的电压变化阈值的关系以及实际电压变化量是否大于零，计算当前电压变化总量；依据相邻段的实际电压变化量的绝对值与电压变化阈值中较小的值以及上一段的电压变化总量对当前段的电压变化总量进行计算；若当前段的电压变化总量大于预设的电压变化总量阈值，置电压漂移检测标志。因此，不仅可以有效的识别出电压缓慢上升的过程，而且不会将局部电压波动误检测为电压波动，进而避免了车身闭合系统在运行过程中产生误防夹的问题。
",B60R16/023,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/023,"装置, 方法, 系统, 检测, 计算","车辆, 检测"
CN106789672A,CN201710037773.0,"本发明提供了一种报文路由处理方法及装置，报文路由处理方法包括：接收通信驱动软件模块发送的报文接收指示以及接收报文信息；从独立内存块中读取路由配置数据列表，并在路由配置数据列表中查找是否存在接收报文信息对应的路由关系；若在路由配置数据列表中查找到接收报文信息对应的路由关系，从接收报文信息对应的路由关系中获取目标报文的信息；发送目标报文的信息至通信驱动软件模块；通信驱动软件模块根据目标报文的信息，发送目标报文至目标网络。本发明降低了软件风险控制难度，并且缩短了路由功能的开发周期。
",H04L12/751,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/751,"提供, 包括, 装置, 方法, 处理, 控制, 配置","通信, 网络, 控制"
CN106696867A,CN201710034548.1,"本发明公开了一种车身闭合系统的更新方法及系统，所述更新方法包括：在当前闭锁过程中，对于防夹区依次设置的多个分段，从第二个分段开始逐一对各个分段进行阻力特性采集值检测；根据对所有分段的阻力特性采集值检测结果，对标准阻力特性值进行更新。本发明技术方案能够通过自适应学习过程，不断对标准阻力特性值进行更新。当由于车身闭合系统机械结构的性能、电器设备的性能以及运行特性曲线的改变导致标准阻力特性值发生变化时，通过本发明技术方案能够及时更新标准阻力特性值，保证防夹判断的准确性，防止误夹问题。
",B60R16/023,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/023,"包括, 方法, 设置, 设备, 系统, 检测",检测
CN106534366A,CN201611177920.6,"本发明公开了一种驾驶行为分析方法，包括：从车辆的CAN总线采集CAN报文；对CAN报文进行解析，获得车辆的车辆运行信号；根据与车辆所属行业对应的驾驶行为分析模型对车辆运行信号进行分析，确定是否发生危险驾驶行为事件；若确定发生危险驾驶行为事件，则记录发生的危险驾驶行为事件及其发生时间；确定在预定时间段内发生的危险驾驶行为事件，根据在所述预定时间段内发生的危险驾驶行为事件、利用驾驶行为分析模型包含的评价算法生成驾驶行为评价结果，保存驾驶行为评价结果。基于本发明公开的驾驶行为分析方法，能够对驾驶员的驾驶行为进行准确、全面的分析。本发明还公开了相应的驾驶行为分析装置以及驾驶行为分析系统。
",H04L29/08,北京经纬恒润科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"包括, 模型, 装置, 算法, 方法, 生成, 系统","车辆, 驾驶"
CN106453016A,CN201610997758.6,"本发明提供了一种报文匹配关系的生成方法及装置，将报文的标识符分类，形成包含请求报文的标识符和与请求报文对应的响应报文的标识符的多个报文标识符组，并对每个所述报文标识符组添加连接组信息，得到报文信息表，可以实现通过连接组信息将每个报文标识符组中的请求报文的标识符和响应报文的标识符进行匹配，进而实现了请求报文和响应报文的匹配关系的建立。并且，由于在报文标识符组添加的连接组信息，并不受车型的影响，可以适应于各种车型，所以这种报文匹配关系的生成方法的可移植性高；另外，在报文标识符组添加的连接组信息的数量，可以依据待构建的报文标识符的数量进行调整，因此，这种报文匹配关系的生成方法具有较高的可扩展性。
",H04L12/40,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/40,"提供, 实现, 装置, 具有, 方法, 生成",通用
CN106302010A,CN201610657222.X,"本申请提供了一种CANopen网络通信仿真测试方法及相关设备，CANopen网络通信仿真测试方法包括：接收上位机发送的模型程序，模型程序为在Simulink模型框架中Simulink驱动模块与CANopen卡数据交互程序进行集成后，自动生成的可执行代码，Simulink驱动模块为所述上位机在所述Simulink模型框架中搭建出的当前CANopen网络通信仿真测试任务对应的模块；运行模型程序，以执行CANopen网络通信仿真设备中的CANopen卡在CAN总线上收发数据的过程。在本申请中，通过以上方式缩短了可执行代码生成的时间，从而缩短了CAN网络通信仿真设备的CANopen功能的实现周期。
",H04L12/26,北京经纬恒润科技有限公司,车载以太网板卡技术,3.0,关键词匹配: CAN; IPC分类号匹配: H04L12/26,"提供, 实现, 包括, 模型, 测试, 仿真, 方法, 生成, 设备","通信, 测试, 网络"
CN105681199A,CN201511018735.8,"本发明公开了一种车载总线中报文数据的处理方法及装置，应用于车载总线中的目标节点，所述方法包括：接收目标报文数据，所述目标报文数据至少具有：物理接收节点属性值及逻辑接收节点属性值；提取所述目标报文数据中的物理接收节点属性值及逻辑接收节点属性值；将所述逻辑接收节点属性值与所述物理接收节点属性值进行比对，得到比对结果；基于所述比对结果，对所述目标报文数据进行路由和/或解析处理。本发明中将代表路由信息的两个接收节点属性值设置在报文数据中，无需额外编辑和维护一张路由表，而且在解析等处理时，也不需要结合源数据库和路由表，节省系统开销，提高数据处理效率。
",H04L12/741,北京经纬恒润科技有限公司,车载以太网板卡技术,3.0,关键词匹配: 车载总线; IPC分类号匹配: H04L12/741,"包括, 装置, 具有, 方法, 设置, 处理, 系统",通用
CN105634902A,CN201511001237.2,"本申请公开了一种半实物仿真系统及其通信方法，半实物仿真系统包括PC机、第一实体物理层设备、第二实体物理层设备和半实物仿真接口，PC机内包含有虚拟网络仿真环境。本发明采用半实物仿真接口将第一实体物理层设备、第二实体物理层设备与虚拟网络仿真环境连接在一起，通过半实物仿真接口实现PC机发送的虚拟数据包与第一实体物理层设备和第二实体物理层设备发送的实体数据包之间的协议转换，从而实现了网络仿真系统与实体物理层设备之间的双向信息交互，并通过映射节点的双物理层模型实现终端节点的发送、接收和中继转发的自组网功能，使实体物理层设备能够作为终端节点参与到网络自组织过程，满足自组网的半实物仿真需求。
",H04L12/46,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/46,"实现, 包括, 模型, 仿真, 方法, 设备, 系统","通信, 网络"
CN104834286A,CN201510144548.8,"本申请公开了一种重编程方法、系统、重编程设备及电子控制单元，重编程设备与目标电子控制单元建立连接后，首先重编程设备对目标电子控制单元进行安全访问，在目标电子控制单元由当前的刷写功能状态转变为解锁状态后，重编程设备对目标电子控制单元中的应用程序进行擦除重编程，并在应用程序重编程完成后，利用由编程数据得到的校验和对目标电子控制单元下载数据的完整性和正确性进行校验，并在确定校验和通过后，告知相应的目标电子控制单元。重编程设备和目标电子控制单元信息交互过程采用的命令为CAN标定协议预先定义的命令，因此实现了在基于CAN标定协议的基础上对重编程过程进行完善，解决了重编程功能不完善、操作过程繁琐等问题。
",G05B19/418,北京经纬恒润科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"实现, 方法, 设备, 控制, 系统",控制
CN104660500A,CN201510109964.4,"本发明提供一种信号处理方法及装置，其中信号处理方法，包括：当所接收到的报文为信号路由的源报文，且源报文中源信号对应的目标信号是事件信号时，设置目标报文的发送请求；当目标报文为路由信号的目标报文，且已经接收到目标报文对应的源报文时，将源报文中源信号的值赋给目标报文中的目标信号；发送携带有目标信号的目标报文。这样在源报文和目标报文的发送频率不同的情况下，为目标信号的赋值操作仅在目标报文发送前执行一次。相对于现有技术中每接收到一个源报文进行一次赋值操作来说，降低中断执行时间，当总线负载比较高的情况下，可以降低路由报文的丢包率，降低CAN中断的负载、CPU峰值负载和平均负载，提高系统的实时性。
",H04L12/701,北京经纬恒润科技有限公司,车载以太网板卡技术,3.0,关键词匹配: CAN; IPC分类号匹配: H04L12/701,"提供, 包括, 装置, 方法, 设置, 处理, 系统",通用
CN104539494A,CN201510017196.X,"本发明公开了一种识别唤醒信号的方法，该方法包括：当局域互联网络LIN节点进入睡眠状态后，使能串行通信接口SCI，使得SCI接收中断信号，并使SCI将所接收的中断信号发送到接收寄存器，所述中断信号包括数据中断信号和错误中断信号；读取所述接收寄存器中的中断信号的数据；计算所述数据中的显性位的持续时间；当所述持续时间大于预定数值则所述中断信号为唤醒信号；利用上述方法识别唤醒信号。
",H04L12/28,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/28,"方法, 包括, 计算","通信, 网络"
CN104506384A,CN201410822478.2,"本发明公开了一种双余度仿真同步的方法及系统，通过主机判断主机仿真步长运行个数与从机仿真步长运行个数是否相等，如果相等，则更新主机仿真步长运行个数、主机最后一次同步时间和主机仿真质量信息，向从机发送生成的第一同步信号，并实时监控从机同步相关信息；如果不相等，则更新主机失步相关信息，主机和从机不同步时的主机仿真步长运行个数、主机最后一次同步时间和主机仿真质量信息，向从机发送生成的第二同步信号，并实时监控从机同步相关信息；从机依据第一同步信号或第二同步信号更新从机仿真步长运行个数、从机最后一次同步时间和从机仿真质量信息，实现了为分析同步失败原因提供有效信息，进而提高了解决同步失败问题的效率。
",H04L12/26,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/26,"提供, 实现, 仿真, 方法, 生成, 系统",通用
CN104022919A,CN201410289600.4,"本申请提供一种控制多总线接口数据激励的方法、装置及系统，通过触发IO接口板卡对接收到的总激励信息进行解析得到与该IO接口板卡对应的激励信息，并统一发送激励指令至各个IO接口板卡，以使得IO接口板卡响应激励指令，调用与本IO接口板卡对应的总线接口进程，完成对激励信息中激励报文的发送过程，本申请通过由各个IO接口板卡解析得到与其对应的激励信息，并统一发送激励指令至各个IO接口板卡，以使得各个IO接口板卡调用对应的总线接口进程来完成对激励信息中激励报文的发送过程，进而实现在对各个总线接口进行数据激励过程中，保证数据激励的时序和逻辑关系、并避免总线接口数据激励的偏差、以及各个总线接口的时延累计效应。
",H04L12/26,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/26,"提供, 实现, 装置, 方法, 控制, 系统",控制
CN103780508A,CN201410058072.1,"本发明公开了一种CAN总线报文的软件滤波方法及系统，根据报文缓存装置的编号确定ID信息子集，然后在确定的ID信息子集（即部分ID信息）中遍历比较，从而减少了对一个CAN总线报文进行筛选所需要的遍历比较的次数，而根据报文缓存装置的编号确定ID信息子集的过程只需要执行一条指令即可，其仍然小于所减少的遍历比较的次数所需要执行的指令条数，因此，本申请实施例提供的CAN总线报文的软件滤波方法，减少了对一个CAN总线报文进行筛选所需要的遍历比较的次数，从而提高了电子控制单元传递报文数据信息的效率。本发明还公开一种电子控制单元。
",H04L12/861,北京经纬恒润科技有限公司,车载以太网板卡技术,3.0,关键词匹配: CAN; IPC分类号匹配: H04L12/861,"提供, 装置, 方法, 控制, 系统",控制
CN103685084A,CN201310717485.1,"本发明公开了一种生成数据帧的方法及装置，根据待传输消息中所包含的N种待传输周期消息的周期确定待生成数据帧的帧周期，根据所述帧周期确定待生成数据帧的个数，并为各个待生成数据帧分配帧序号，依据所述帧序号及所述N种待传输周期消息的周期，确定各个数据帧与所述待传输周期消息的对应关系，在确定当前待传输消息后，如果所述当前待传输消息为周期消息，则按照所述对应关系，将所述待传输消息添加至对应的待生成数据帧中，如果所述待传输消息为非周期消息，则将所述非周期消息添加至上一周期消息所在数据帧中。基于上述方法及装置，能够保证消息在航空总线上准确传输。
",H04L12/953,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/953,"生成, 方法, 装置",通用
CN103616830A,CN201310633673.6,"本申请公开了一种汽车控制器刷写控制方法、装置及一种汽车总线系统，其在控制ECU刷写时，同时向CAN总线系统中的所有ECU发送预编程控制指令，根据ECU反馈的响应消息判定相应ECU已进入预编程阶段；当接收到所有ECU反馈的响应消息，再控制所有ECU同时进行刷写，待所有ECU均完成刷写后，控制所有ECU进行编程后处理；从而避免了正在进行刷写的ECU因另一ECU进入预编程阶段造成自身状态改变、甚至刷写失败的现象，实现了对所有ECU的同时刷写，对于整个CAN总线系统而言，大大缩减了应用程序刷写消耗时间，提高了车辆生成及测试效率。
",G05B19/04,北京经纬恒润科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"实现, 测试, 装置, 方法, 生成, 处理, 控制, 系统","车辆, 测试, 汽车, 控制"
CN103607327A,CN201310645896.4,"本申请提供了一种诊断方法及集成ECU，集成ECU中的接口模块存储有包括多个不同ECU各自诊断报文的ID的预设诊断报文的ID信息及每个诊断报文的ID与每个诊断报文的ID关联的接收句柄的预设接收句柄对应关系，路由模块存储有每个诊断报文的ID关联的接收句柄与每个接收句柄关联的诊断模块的预设诊断模块对应关系。通过接口模块依据预设接收句柄对应关系，确定待诊断报文的接收句柄；通过路由模块，依据预设诊断模块对应关系，确定待诊断数组对应的诊断模块，并将待诊断数组和待诊断数组的接收句柄路由至诊断模块；通过诊断模块，对待诊断数组中的诊断信息进行处理。因此，在集成ECU中，实现了准确的故障诊断。
",H04L12/26,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/26,"提供, 实现, 包括, 方法, 处理",诊断
CN103532724A,CN201310533742.6,"本发明公开了一种MOST网络接口电路及相应的MOST网络唤醒方法，其通过独立的LIN收发器接收一脉冲信号发送至外部主机控制器，外部主机控制器判断该脉冲信号是否为ECL唤醒信号，如果是则生成一使能信号，以使能第一电源转换模块为MOST控制器供电，从而实现了MOST网络的ECL唤醒功能；也即MOST网络从节点对应的外部主机控制器不再与MOST控制器同步断电，而是保持在常电模式，当其他网络需要传输数据时，亦可实现对该节点的唤醒。相对于现有技术，本发明不仅保证了在MOST网络休眠状态下，仍能通过本接口电路接收其他网络数据，还避免了电源检测及管理功能冗余的问题，降低了ECU的成本。
",H04L12/10,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/10,"实现, 生成, 方法, 控制, 检测, 电路","检测, 网络, 控制"
CN103457788A,CN201310109730.0,"本发明公开了一种仿真系统数据的监控方法、装置及系统，所述仿真系统数据监控方法将客户端建立的模型中的待监控信号线连接到浏览器接口模块上，并将包括浏览器接口模块的模型程序编译下载至服务端，在服务端模型实时仿真的过程中，待监控信号线上传输的待监控数据就会被发送至与其连接的浏览器接口，进而浏览器接口通过网络将待监控数据发送至客户端的浏览器，以使得用户能够方便的通过常用的浏览器实时监控待监控数据。该方法、装置及系统充分利用了主流网络技术，通过浏览器实现对仿真系统数据的监控，因此不需要下载安装专用软件，且不需要通过特定的应用程序编程接口进行二次开发，成本低且适用性强。
",H04L12/26,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/26,"实现, 包括, 模型, 装置, 仿真, 方法, 系统",网络
CN103312548A,CN201310251182.5,"本发明涉及网络管理领域，公开了一种基于OSEK标准的休眠控制方法及装置。ECU满足休眠条件时，产生符合网络管理层协议的休眠命令，以设置本地休眠标志，在网络内其他ECU节点都符合休眠条件时，产生等待休眠通知信息，以使应用层生成关闭通信指令；接收到关闭通信指令后，通信层中的模块关闭通信功能；在网络管理层中的各模块进入等待休眠状态的时间达到预设值，且未发生唤醒事件时，产生进入休眠通知信息；之后应用层中的模块先后产生CAN收发器休眠指令和CAN控制器休眠指令，使CAN收发器和CAN控制器先后进入休眠状态。本发明所公开的休眠控制方法，使网络管理按照严格的流程实现，增加了ECU的稳定性和可靠性。
",H04L12/24,北京经纬恒润科技有限公司,车载以太网板卡技术,3.0,关键词匹配: CAN; IPC分类号匹配: H04L12/24,"实现, 装置, 方法, 设置, 生成, 控制","通信, 网络, 控制"
CN103259704A,CN201310109746.1,"本发明提供了一种CAN总线数据收发设备，可以包括微控制器、第一串行控制开关、第二串行控制开关、高速CAN收发器、低速/容错CAN收发器和继电器，微控制器可以根据选择指令控制第一串行控制开关与高速CAN收发器、低速/容错CAN收发器的连接状态，可以控制第二串行控制开关与高速CAN收发器、低速/容错CAN收发器的连接状态，可以控制继电器常闭端和常开端的切换，其中，高速CAN收发器与常闭端连接，低速/容错CAN收发器与常开端连接。本发明通过微控制器对第一串行控制开关、第二串行控制开关和继电器的控制，实现了高速CAN收发器和低速/容错CAN收发器的自动切换。
",H04L12/40,北京经纬恒润科技有限公司,车载以太网板卡技术,3.0,关键词匹配: CAN; IPC分类号匹配: H04L12/40,"提供, 实现, 包括, 设备, 控制",控制
CN103200035A,CN201310109895.8,"本发明涉及网络管理领域，公开了一种基于OSEK标准的网络休眠方法、装置及ECU。在本发明所公开的网络休眠方法中，将各ECU分属为不同的局部域，在ECU产生的第一网络管理报文中，加载自身的标识、所属局部域的标识、状态的标识和后继ECU的标识，并根据所述第一网络管理报文进行判断，在同一局部域内所有的ECU均满足休眠条件时，产生休眠指令，以使该局部域内所有的ECU进入休眠状态。通过本发明所公开的方法，只要同一局部域内所有ECU均满足休眠条件，则进入休眠状态。而现有技术中必须网段内的所有ECU均满足休眠条件时，才能进入休眠状态，和现有技术相比，本发明所公开的方法有利于实现ECU的休眠，节省能耗。
",H04L12/24,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/24,"方法, 装置, 实现",网络
CN103200062A,CN201310109738.7,"本发明提供了一种CAN总线数据收发设备，可以包括：微控制器、低速/容错CAN收发器、高速CAN收发器、切换电路和CAN类型判断电路。当切换电路与外部CAN总线连接切断时，CAN类型判断电路根据CAN高线上的电压和CAN低线上的电压的差值，判断设备当前接入的外部CAN总线类型，并将判断结果发送给微控制器，微控制器向切换电路发送CAN收发器切换信号，控制切换电路与低速/容错CAN收发器、高速CAN收发器的连接状态；向切换电路发送CAN收发器接入信号，控制切换电路与外部CAN总线连接的建立，实现CAN收发器与外部CAN总线的通信。
",H04L12/40,北京经纬恒润科技有限公司,车载以太网板卡技术,3.0,关键词匹配: CAN; IPC分类号匹配: H04L12/40,"提供, 实现, 包括, 设备, 控制, 电路","通信, 控制"
CN103095566A,CN201310054051.8,"本申请公开了一种报文发送方法，报文中设置有至少一个触发信号，该方法包括：当检测存在对报文的写操作时，判定写操作位于的触发信号；获取触发信号中预先设置的信号属性；依据与信号属性相对应的预先设置的发送方式对报文进行发送，解决了现有技术中各个电子控制单元之间进行信息交互时，因为只能按照报文内的预先设置的发送周期发送报文，当车辆出现紧急情况时，该紧急情况所对应的关键报文不能被迅速的发送出去，进而影响关键报文的实时性的问题。
",H04L12/70,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/70,"包括, 方法, 设置, 控制, 检测","车辆, 检测, 控制"
CN102891808A,CN201210430050.4,"本发明公开了一种报文调度方法，包括：预设一个定时脉冲信号，以定时脉冲信号的输出频率进行计时，以定时脉冲信号作为计时的开始信号，且将定时脉冲信号的输出间隔作为时间片；设置各个报文数据的初始触发时间和初始触发周期的值并进行存储，且各个报文数据的初始触发时间和初始触发周期均为时间片的正整数倍；以定时脉冲信号作为检索控制的开始信号，在时间片范围内逐条对各个报文数据的触发时间进行检索，得到各个报文数据的触发时间；将检索到的当前报文数据的触发时间与当前的计时时间相比较；若当前检索到的报文数据的触发时间与当前的计时时间相等，则发送当前报文数据。与现有技术相比，本发明准确的实现了报文数据的定时触发。
",H04L12/861,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/861,"实现, 包括, 方法, 设置, 控制",控制
CN102833156A,CN201210279636.5,"本申请公开了一种网络数据的处理方法及装置，应用于具有发送终端和接收终端的数据传输网络中，该方法包括：提取接收终端舍弃的网络数据中的标识信息；统计舍弃的网络数据的总数量；将标识信息和总数量发回给所述发送终端。该装置包括发送终端和接收终端，且在接收终端中设置有：提取单元、计数单元和反馈单元。本申请提供的方法及装置能够使接收终端在数据冲突时将提取的被舍弃的数据的标识信息和统计的被舍弃的数据的总数量反馈给发送终端做进一步处理，可以避免维护和研发人员因不清楚哪些数据在接收终端被丢弃而出现误操作。
",H04L12/56,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/56,"提供, 包括, 装置, 具有, 方法, 设置, 处理","数据传输, 网络"
CN102801633A,CN201210309481.5,"本申请公开了一种接收滤波器的配置方法及装置，所述方法能够根据需要接收的ID的个数和当前可用接收滤波器的个数关系来确定配置接收滤波器的方法，在需要接收的ID个数大于当前可用接收滤波器的个数时，将需要接收的ID分成若干个组合，根据各个组合中的元素配置接受滤波器的掩码寄存器值和接收寄存器值；在需要接收的ID个数小于或等于当前可用接收滤波器的个数时，为每一个需要接收的ID单独配置一个接收滤波器来接收所述ID。通过本发明实施例公开的接收滤波器的配置方法及装置，可以自动高效的配置接收滤波器，且保证了配置结果的准确率。
",H04L12/56,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/56,"方法, 装置, 配置",通用
CN102769546A,CN201210272302.5,"本发明涉及计算机自动控制系统技术领域，公开了一种报文分段保存的方法及系统，该方法包括：接收总线上传递的报文，并将所述报文存储在内存中；判断所述内存中的报文是否满足分段记录条件，所述分段记录条件为所述内存中的报文达到预设的数量或内存消耗量达到预设的数值；当不满足所述分段记录条件时，继续接收总线上传递的报文，并将所述报文存储在内存中；当满足所述分段记录条件时，记录所述报文的分段信息，预先生成用于保存所述报文的分段记录文件，并将内存中的所述报文保存到所述分段记录文件中。本发明保证了报文存储功能占用系统内存低、记录过程短和用户可以自由选择文件保存路径，使软件性能和用户体验得到很大提升。
",H04L12/24,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/24,"包括, 方法, 生成, 控制, 系统, 计算",控制
CN102700484A,CN201210204611.9,"本发明公开了一种汽车电子控制单元中的供电单元及汽车电子控制单元，该供电单元包括：依次链接的降压模块和可控的带保护的电压跟随模块。降压模块对接收到的第一电压依据预设降压条件进行处理，将获得的第二电压输出给可控的带保护的电压跟随模块，由其对第二电压进行滤波处理，并跟随汽车电子控制单元中的AD参考源的参考电压对第二电压进行稳压处理，获得传感器电压向汽车传感器供电。本发明通过采用降压模块与可控的带保护的电压跟随模块的双重降压，利用前者抗高压，后者防过流，使整个汽车电子控制系统具备耐高压、防过流的优点，实现了向汽车传感器供电稳定的目的，进而实现在不同系统中的通用，以及避免汽车电子控制单元误判的目的。
",B60R16/02,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/02,"实现, 包括, 处理, 控制, 系统","汽车, 传感器, 控制"
CN102594589A,CN201210022569.9,"本发明公开了一种以太网故障注入方法、装置及系统，该方法包括：建立待注入报文的报文配置信息，检测是否接收到第一终端发送的待注入报文，如果是，则解析所述待注入报文，获得所述待注入报文的唯一标识；根据所述唯一标识查找与所述待注入报文相应的报文配置信息；判断所述待注入报文是否需要进行故障注入，如果是，则将所述故障信息替换所述待注入报文中位于故障信息注入位置的信息，写入故障信息后的待注入报文为注入报文，并将所述注入报文发送至第二终端，如果否，则将所述待注入报文发送至第二终端。采用本发明实施例公开的方法、装置及系统可以在不对所述第二终端造成任何损害的情况下，对所述第二终端实现故障注入。
",H04L12/24,北京经纬恒润科技有限公司,车载以太网板卡技术,3.0,关键词匹配: 以太网; IPC分类号匹配: H04L12/24,"实现, 包括, 装置, 方法, 系统, 检测, 配置",检测
CN102594643A,CN201210064138.9,"本发明公开了一种控制器局域网总线通讯控制方法、装置及系统，上述方法包括：在通讯模式下检测总线的通讯状态，并在检测到通讯异常时，向CPU发送错误报告；接收所述CPU发送的，与所述错误报告对应的第一切换指示信息后，切换工作模式至监听模式；在所述监听模式下检测所述通讯异常是否消除，并在所述通讯异常消除时，向所述CPU发送错误消除报告；接收所述CPU发送的，与所述错误消除报告对应的第二切换指示信息后，切换工作模式至通讯模式。避免了在通讯模式下检测到通讯异常时，不停地向控制器局域网总线上传输大量错误帧，这些大量的错误帧占用总线带宽，造成网络资源浪费的问题。
",H04L12/40,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/40,"包括, 装置, 方法, 控制, 系统, 检测","检测, 网络, 控制"
CN102152766A,CN201110079040.6,"本发明公开了一种汽车电子门窗控制器及其控制方法、汽车电子门窗系统。其中，该汽车电子门窗控制器及其控制方法包括：汽车电子门窗控制器在正常运行状态下对点火开关及负载运行状态进行查询；汽车电子门窗控制器在点火开关处于关闭状态且负载不工作时，开始计时；以及汽车电子门窗控制器在计时的时间超过预设时间时，进入休眠状态，其中，在休眠状态下，汽车电子门窗控制器以低功耗模式运行。通过本发明，能够降低汽车电子门窗系统的功耗。
",B60R16/023,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/023,"系统, 方法, 包括, 控制","汽车, 控制"
CN101834751A,CN201010130514.0,"本发明公开了一种航空全双工交换以太网监测处理系统，包括：AFDX交换机和终端，还包括监控设备，该监控设备通过监控链路连接于AFDX交换机和终端之间的数据链路节点，获取并处理AFDX网络传输的数据链路信息。通过本发明，能够保证AFDX网络数据传输完整的同时节省交换机的存储空间，并保证交换机的稳定性。
",H04L12/26,北京经纬恒润科技有限公司,车载以太网板卡技术,3.0,关键词匹配: 以太网; IPC分类号匹配: H04L12/26,"系统, 包括, 处理, 设备","数据传输, 网络"
CN101800703A,CN201010124846.8,"本发明公开了一种AFDX交换机的流量控制方法及装置。其中，该方法包括：交换机监控入口处的数据链路信息，获取数据链路信息的到达时间；交换机根据数据链路信息的到达时间，判断数据链路信息是否有效；交换机发送有效的数据链路信息。通过本发明，能够提高AFDX网络交换机的性能和容量。
",H04L12/56,北京经纬恒润科技有限公司,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/56,"方法, 包括, 装置, 控制","网络, 控制"
