# dSPACE HIL相关技术专利详细分析报告

## 概述

本报告对dSPACE公司的1914项专利进行了技术分类分析，特别关注HIL（硬件在环）相关技术的专利布局情况。

## HIL相关技术专利总览

HIL相关技术专利总数: 829 条 (43.3%)

## HIL硬件在环仿真技术

**专利数量**: 54 条

**技术描述**: HIL硬件在环仿真相关技术，包括实时仿真、硬件模拟器等

**主要申请人**:
- 北京经纬恒润科技有限公司: 26 条
- 北京经纬恒润科技股份有限公司: 21 条
- 经纬恒润(天津)研究开发有限公司: 7 条

**重点专利**:

### CN119024721A
- **申请号**: CN202411129716.1
- **IPC分类号**: G05B17/02
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 硬件在环, 模拟器; IPC分类号匹配: G05B17/02
- **技术手段**: 提供, 实现, 测试, 装置, 仿真, 方法, 处理, 控制, 系统, 模拟
- **应用场景**: 测试, 汽车, 控制
- **技术摘要**: 本发明提供一种汽车网联模块的硬件在环仿真方法、装置及系统，启动仿真场景软件并解析仿真静态数据，初始化总线机柜、GNSS模拟器和V2X信号仿真模块，以使GNSS模拟器、V2X信号仿真模块和被测控制器进行时间同步；当完成时间同步时获取仿真动态数据。从处理后的仿真静态数据和仿真动态数据中获取所需目标仿真数据信息并发送至总线机柜、GNSS模拟器和V2X信号仿真模块以搭建仿真环境；通过仿真环境进行被测控制器...

### CN104460349A
- **申请号**: CN201410705507.7
- **IPC分类号**: G05B17/02
- **申请人**: 北京经纬恒润科技有限公司
- **分类理由**: 关键词匹配: HIL, 实时仿真; IPC分类号匹配: G05B17/02
- **技术手段**: 提供, 包括, 测试, 装置, 仿真, 方法, 控制, 系统, 计算, 模拟
- **应用场景**: 测试, 汽车, 传感器, 控制
- **技术摘要**: 本申请提供了一种实车在回路仿真测试方法、实时仿真机及系统，包括：实时仿真机仿真模拟交通环境；接收汽车定位装置获取的实车的第一运动状态信息；依据第一运动状态信息、模拟交通环境，计算出控制器传感器信号；发送控制器传感器信号至实车内的被测控制器；获取被测控制器针对控制器传感器信号输出的控制信号、实车控制器响应控制信号后输出的信号，接收实车的第二运动状态信息和交通环境等信息，作为被测控制器的性能测试的分析...

### CN111123740A
- **申请号**: CN201911319738.3
- **IPC分类号**: G05B17/02
- **申请人**: 北京经纬恒润科技有限公司
- **分类理由**: 关键词匹配: 硬件在环; IPC分类号匹配: G05B17/02
- **技术手段**: 模拟, 包括, 测试, 装置, 仿真, 方法, 配置
- **应用场景**: 车辆, 测试
- **技术摘要**: 本发明公开了一种硬件在环测试方法以及装置，该方法包括：接收车辆位置信号；根据预设的车辆行驶前瞻距离和车辆位置信号确定测试所需的虚拟地图信号；将测试所需的虚拟地图信号和车辆仿真信号发送至下位机，以使下位机对待测件进行测试；其中，上位机中运行的功能模块包括地图信号模拟模块，所地图信号模拟模块为预先配置；测试所需的虚拟地图信号记录于地图信号模拟模块中。通过本发明的技术方案，至少能够不依赖于地图盒子，不需...

### CN104615008A
- **申请号**: CN201410708701.0
- **IPC分类号**: G05B17/02
- **申请人**: 北京经纬恒润科技有限公司
- **分类理由**: 关键词匹配: HIL; IPC分类号匹配: G05B17/02
- **技术手段**: 提供, 包括, 测试, 仿真, 方法, 处理, 设备, 控制, 系统, 计算
- **应用场景**: 测试, 控制
- **技术摘要**: 本发明提供一种自动紧急刹车AEB系统的测试结果分析方法和系统，方法应用于AEB系统中，AEB系统包括电子控制单元ECU，还包括硬件回路HIL台架，HIL台架与ECU连接，用于对ECU进行仿真测试，方法包括：获取HIL台架中保存的，对ECU仿真测试完成后得到的仿真数据；对仿真数据进行预处理，获得有用数据；依据欧盟新车认证程序/中心Euro NCAP的评分规则和获得的有用数据，计算AEB系统的得分。因...

### CN104238372A
- **申请号**: CN201410460674.X
- **IPC分类号**: G05B17/02
- **申请人**: 北京经纬恒润科技有限公司
- **分类理由**: 关键词匹配: 实时仿真; IPC分类号匹配: G05B17/02
- **技术手段**: 实现, 包括, 模型, 装置, 仿真, 方法, 设置, 计算
- **应用场景**: 通用
- **技术摘要**: 本发明公开了一种多个模型并行仿真的调度方法和装置。所述方法包括：设置所述多个模型中每个模型的调度周期；计算所述多个模型中所有模型的调度周期的最大公约数和最小公倍数；计算所述最小公倍数对所述最大公约数的商，作为总帧数；所述多个模型中的每个模型的调度周期分别对所述最大公约数做商，得到每个模型的第一参数；从第一帧到最后一帧，分别用当前帧的帧序列号分别对每个模型的第一参数进行取余计算，如果结果为0，则将对...

---

## 模拟数字转换板卡技术

**专利数量**: 65 条

**技术描述**: 模拟数字转换相关技术，包括ADC、DAC等转换器技术

**主要申请人**:
- 北京经纬恒润科技有限公司: 28 条
- 北京经纬恒润科技股份有限公司: 23 条
- 经纬恒润(天津)研究开发有限公司: 12 条
- 天津经纬恒润科技有限公司: 2 条

**重点专利**:

### CN103326702A
- **申请号**: CN201310215688.0
- **IPC分类号**: H03K17/90
- **申请人**: 北京经纬恒润科技有限公司
- **分类理由**: 关键词匹配: 采样, 信号转换, 数字信号; IPC分类号匹配: H03K17/90
- **技术手段**: 提供, 实现, 处理, 控制, 检测, 电路
- **应用场景**: 检测, 控制
- **技术摘要**: 本发明公开的霍尔开关电路，通过振荡器为控制电路提供采样时钟信号，由控制电路控制输出模块输出迟滞控制信号至偏置电路，再由偏置电路输出迟滞电流信号及供电电压信号至霍尔盘电路；通过霍尔盘电路感应磁场强度，并进行初步失调消除；再通过迟滞比较器接收所述霍尔盘电路输出的霍尔电压并进行放大，由控制电路控制迟滞比较器进行极性检测及失调消除；最后通过输出模块将所述迟滞比较器的输出信号转换为数字信号进行输出。本发明公...

### CN117176165A
- **申请号**: CN202311135063.3
- **IPC分类号**: H03M1/12
- **申请人**: 经纬恒润(天津)研究开发有限公司
- **分类理由**: 关键词匹配: ADC, 采样; IPC分类号匹配: H03M1/12
- **技术手段**: 包括, 装置, 方法, 设置, 处理
- **应用场景**: 通用
- **技术摘要**: 本申请公开了一种信号处理方法及装置，其中方法包括：通过ADC进行多路信号同步采集，获得多路输入信号；对所述多路输入信号进行采样频率调节，获得对齐信号；对所述对齐信号进行累加平均运算，获得初步降噪信号；根据预设的小波基函数对所述初步降噪信号进行小波分解，获得去噪信号。通过上述步骤，在初步降噪时，可以设置较少的累加平均次数，来缩短处理周期，后续再通过小波分解降噪，来提升去噪信号的信噪比。也就是说，通过...

### CN103235567A
- **申请号**: CN201310109500.4
- **IPC分类号**: G05B19/418
- **申请人**: 北京经纬恒润科技有限公司
- **分类理由**: 关键词匹配: 数模转换, 转换器, 数字信号
- **技术手段**: 系统, 提供, 方法, 装置
- **应用场景**: 通用
- **技术摘要**: 本发明提供了一种标定数据的传输方法、装置及系统，本发明通过对标定数据进行编码得到BT656格式的数字信号，并将BT656格式的数字信号通过数模转换器转换为CVBS信号，再通过视频信号线传输所述CVBS信号。本发明通过将标定数据在视频信号线传输，而不是在CAN总线传输，由实验可知，本发明提供的视频信号线传输标定数据的速率约为20.736Mbps，而现有技术中通过CAN总线传输标定数据的速率最大为1M...

### CN102611372A
- **申请号**: CN201210062105.0
- **IPC分类号**: H02P8/12
- **申请人**: 北京经纬恒润科技有限公司
- **分类理由**: 关键词匹配: 模数转换, 采样, 数字信号
- **技术手段**: 提供, 包括, 方法, 处理, 控制, 系统
- **应用场景**: 控制
- **技术摘要**: 本发明提供了一种步进电机驱动器反馈电流获取方法及系统，所述方法包括：获取采样电阻两端的采样电压；对所述采样电压进行偏置放大处理，获得与所述采样电压相对应的偏置放大电压，并对所述偏置放大电压进行模数转换，将所述偏置放大电压转换为数字信号电压；依据所述采样电压、所述数字信号电压以及所述步进电机驱动器运行过程中的驱动参数获取反馈电流。本发明提供的一种步进电机驱动器反馈电流获取方法及系统，将驱动器自身特性...

### CN102436840A
- **申请号**: CN201110409931.3
- **IPC分类号**: G11C5/00
- **申请人**: 北京经纬恒润科技有限公司
- **分类理由**: 关键词匹配: 转换器, 模拟信号, 数字信号
- **技术手段**: 提供, 实现, 具有, 处理, 模拟
- **应用场景**: 数据传输
- **技术摘要**: 本发明提供的数字射频存储板的模拟/数字转换器模块用于高速采集外部模拟信号，将其转换为数字信号并传输到第一FPGA模块，第一FPGA模块用于将数字信号进行一级可编程处理后传输给第二FPGA模块，第二FPGA模块用于将数据进行二级可编程处理，最后将数据传输给数字/模拟转换器模块，数字/模拟转换器模块用于将数字信号高速转换为模拟信号并且输出。其中，第一SRAM模块与所述第一FPGA模块连接，用于缓存第一...

---

## 故障注入板卡技术

**专利数量**: 140 条

**技术描述**: 故障注入相关技术，用于测试系统的故障处理能力

**主要申请人**:
- 北京经纬恒润科技有限公司: 57 条
- 北京经纬恒润科技股份有限公司: 55 条
- 经纬恒润(天津)研究开发有限公司: 22 条
- 天津经纬恒润科技有限公司: 6 条

**重点专利**:

### CN106772013A
- **申请号**: CN201611249252.3
- **IPC分类号**: G01R31/327
- **申请人**: 北京经纬恒润科技有限公司
- **分类理由**: 关键词匹配: 故障检测, 故障诊断; IPC分类号匹配: G01R31/327
- **技术手段**: 包括, 方法, 处理, 控制, 系统, 检测, 电路, 配置
- **应用场景**: 检测, 汽车, 诊断, 控制
- **技术摘要**: 本发明公开一种继电器故障检测方法及系统，其应用于继电器故障检测电路，该继电器故障检测电路包括继电器、控制器和天窗电机，该方法包括：检测继电器A路和B路的控制信号和反馈信号；根据控制信号和反馈信号确定继电器的故障类型；根据继电器的故障类型进行继电器的故障重检处理。本发明可以通过检测继电器A路和B路的控制信号和反馈信号来检测天窗控制电机的继电器是否发生故障，并进行继电器故障处理，能够做到无论继电器发生...

### CN120446718A
- **申请号**: CN202510577674.6
- **IPC分类号**: G01R31/28
- **申请人**: 经纬恒润(天津)研究开发有限公司
- **分类理由**: 关键词匹配: 故障注入; IPC分类号匹配: G01R31/28
- **技术手段**: 实现, 测试, 方法, 设备, 系统
- **应用场景**: 测试
- **技术摘要**: 本申请公开了一种板卡测试系统及板卡测试方法，涉及电力电子技术领域。板卡测试系统中，上位机用于基于HIL设备信号列表文档，向待测信号板卡发送对应的目标激励信号；基于待测信号板卡的当前测试通道信息，向故障注入板卡发送通道选通指令；基于目标激励信号对应的目标待测信号类型，向信号测试模块发送测试切换指令；待测信号板卡用于响应于目标激励信号输出对应的目标响应信号；故障注入板卡用于响应于通道选通指令导通目标通...

### CN120028677A
- **申请号**: CN202510245200.1
- **IPC分类号**: G01R31/28
- **申请人**: 经纬恒润(天津)研究开发有限公司
- **分类理由**: 关键词匹配: 故障检测; IPC分类号匹配: G01R31/28
- **技术手段**: 包括, 装置, 方法, 设备, 系统, 检测, 电路
- **应用场景**: 检测
- **技术摘要**: 本申请公开了一种故障检测方法、装置及设备。该方法包括：在抬头显示系统投射出的图像异常的情况下，读取所述抬头显示系统对应的软件；在所述软件的读取结果指示所述抬头显示系统中的FPC链路存在异常的情况下，确定所述FPC链路中的至少一个组件，所述至少一个组件包括柔性电路板、印刷电路板、所述柔性电路板和所述印刷电路板的连接插座；在所述至少一个组件中确定存在故障的故障组件，以及所述故障组件中的故障点。根据本申...

### CN120009774A
- **申请号**: CN202510214464.0
- **IPC分类号**: G01R31/52
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 故障检测; IPC分类号匹配: G01R31/52
- **技术手段**: 系统, 提供, 检测, 电路
- **应用场景**: 驾驶, 检测
- **技术摘要**: 本申请提供一种短路检测电路和驾驶员监测系统，涉及电路故障检测技术领域。由于检测模块均在与自身一一对应的负载的分压小于设定电压时输出第一信号，而负载分压小于设定电压可以表明负载发生短路故障，所以如果接收到相应检测模块输出的第一信号，则能够检测出相应的负载发生短路故障，从而如果接收到部分检测模块输出的第一信号，则检测出部分检测模块所对应的负载发生短路故障，如果接收到全部检测模块输出的第一信号，则检测出...

### CN119881664A
- **申请号**: CN202510329565.2
- **IPC分类号**: G01R31/367
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 故障诊断; IPC分类号匹配: G01R31/367
- **技术手段**: 提供, 平台, 模型, 装置, 算法, 方法, 生成, 处理
- **应用场景**: 网络, 诊断
- **技术摘要**: 本发明提供一种电池模型参数的辨识方法及装置，从云平台数据库读取特定时间段内的电池数据，根据每条电池数据提取多个充放电片段数据，按时间戳降序排列对每个充放电片段数据进行离散傅里叶变换生成频谱图。根据频谱图确定每个充放电片段数据所属的频率区间，进行滤波处理得到目标充放电片段数据。根据目标充放电片段数据和预设频率区间估计充电与放电参数。本方案不依赖大规模的训练集和长时间训练，无需神经网络算法即可高精度获...

---

## 频率可调脉冲输出板卡技术

**专利数量**: 6 条

**技术描述**: 频率可调的脉冲输出技术，用于信号生成和控制

**主要申请人**:
- 北京经纬恒润科技有限公司: 3 条
- 北京经纬恒润科技股份有限公司: 1 条
- 经纬恒润(天津)研究开发有限公司: 1 条
- 天津经纬恒润科技有限公司: 1 条

**重点专利**:

### CN116566361A
- **申请号**: CN202310551666.5
- **IPC分类号**: H03K3/02
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 信号发生; IPC分类号匹配: H03K3/02
- **技术手段**: 包括, 设备, 电路, 控制
- **应用场景**: 控制
- **技术摘要**: 本申请公开了一种脉冲电路模组及电子设备，涉及电路技术领域。脉冲电路模组包括：电阻‑电容充电电路的输入端接入输入信号，输入信号为变频信号；分压电路的输入端与电阻‑电容充电电路的输入端并联连接，分压电路的电路参数可调；比较电路的第一输入端与电阻‑电容充电电路的输出端连接，比较电路的第二输入端与分压电路的输出端连接，比较电路用于比较电阻‑电容充电电路输出的第一电压和分压电路输出的第二电压，并根据比较结果...

### CN116131813A
- **申请号**: CN202211656544.4
- **IPC分类号**: H03K3/017
- **申请人**: 经纬恒润(天津)研究开发有限公司
- **分类理由**: 关键词匹配: 波形生成; IPC分类号匹配: H03K3/017
- **技术手段**: 包括, 装置, 生成, 方法, 处理, 设备, 计算
- **应用场景**: 通用
- **技术摘要**: 本申请公开了一种波形生成方法、装置、设备及介质。波形生成方法包括：采用数字信号处理器DSP对驱动目标设备的电参数进行采样，其中，电参数包括电压或电流；根据电参数，计算电参数的占空比，得到用于驱动目标设备的驱动波形的图形样式；根据图形样式中的电平变化，确定目标设备的开关管的动作时刻；采用现场可编程逻辑门阵列FPGA根据动作时刻，生成驱动波形。根据本申请实施例，能够生成复杂的驱动波形，并且成本较低。


### CN103873051A
- **申请号**: CN201410112585.6
- **IPC分类号**: H03L7/08
- **申请人**: 北京经纬恒润科技有限公司
- **分类理由**: 关键词匹配: 频率控制; IPC分类号匹配: H03L7/08
- **技术手段**: 检测, 实现, 电路, 控制
- **应用场景**: 检测, 控制
- **技术摘要**: 本发明公开的一种锁相环锁定指示电路，通过模数转换器接收电压控制信号，并对所述电压控制信号进行模数转换后输出；由控制器输出频率控制信号；再通过现场可编程逻辑阵列接收所述模数转换后的电压控制信号及所述频率控制信号，当所述模数转换后的电压控制信号及所述频率控制信号之间的差值小于第一预设值时，输出锁定指示信号，实现对所述锁相环的锁定指示功能；由于所述锁相环锁定指示电路通过检测所述电压控制信号及频率控制信号...

### CN109088621A
- **申请号**: CN201810842700.3
- **IPC分类号**: H03K5/1252
- **申请人**: 天津经纬恒润科技有限公司
- **分类理由**: IPC分类号匹配: H03K5/1252
- **技术手段**: 提供, 装置, 方法, 处理, 控制
- **应用场景**: 通信, 汽车, 控制
- **技术摘要**: 本发明提供一种信号滤波方法及装置，应用于汽车通信技术领域，该方法获取脉冲宽度满足预设偏差要求的第一高电平脉冲以及在第一高电平脉冲后，下一个出现的脉冲宽度满足预设偏差要求的第二高电平脉冲，然后判断第一高电平脉冲和第二高电平脉冲之间是否存在时间间隔，若存在时间间隔，确定时间间隔内获取的电平为低电平，得到滤波结果。通过本发明提供的方法及装置，将第一高电平脉冲和第二高电平脉冲之间的时间间隔内获取的电平确定...

### CN102857200A
- **申请号**: CN201210345465.1
- **IPC分类号**: H03K5/22
- **申请人**: 北京经纬恒润科技有限公司
- **分类理由**: IPC分类号匹配: H03K5/22
- **技术手段**: 提供, 包括, 电路
- **应用场景**: 通用
- **技术摘要**: 本发明提供了一种信号波形变换电路，包括电子开关单元与差动输出单元；其中，所述电子开关单元将输入信号转化为两个同相的输出信号，再由所述差动输出单元输出两个同相的输出信号的差值，转化输入信号为直流信号，不需要采用将输入信号转化为“馒头波”，再转化为直流信号，可以解决在将输入信号转化为“馒头波”中引入二极管造成的精度下降的问题，以及将“馒头波”转化为直波信号中引入的电容造成的延时较长的问题。


---

## 车载以太网板卡技术

**专利数量**: 159 条

**技术描述**: 车载以太网通信技术，包括车载网络协议和通信接口

**主要申请人**:
- 北京经纬恒润科技股份有限公司: 75 条
- 北京经纬恒润科技有限公司: 66 条
- 经纬恒润(天津)研究开发有限公司: 10 条
- 天津经纬恒润科技有限公司: 7 条
- 江西经纬恒润科技有限公司: 1 条

**重点专利**:

### CN119299243A
- **申请号**: CN202411407776.5
- **IPC分类号**: H04L12/02
- **申请人**: 经纬恒润(天津)研究开发有限公司
- **分类理由**: 关键词匹配: 车载以太网, 以太网; IPC分类号匹配: H04L12/02
- **技术手段**: 提供, 实现, 包括, 处理, 系统
- **应用场景**: 通信
- **技术摘要**: 本申请提供一种数据通信系统，包括第一开发板和第二开发板，第一开发板包括第一处理器和第一万兆PHY芯片，第一处理器与第一万兆PHY芯片通过USXGMII接口协议通信连接，第二开发板包括第二处理器和第二万兆PHY芯片，第二处理器与第二万兆PHY芯片通过USXGMII接口协议通信连接，第一开发板和/或第二开发板还包括存储芯片，与第一万兆PHY芯片和/或第二万兆PHY芯片连接，存储芯片用于存储第一万兆PH...

### CN118524011A
- **申请号**: CN202410718404.8
- **IPC分类号**: H04L41/069
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 车载以太网, 以太网; IPC分类号匹配: H04L41/069
- **技术手段**: 提供, 实现, 方法, 处理, 控制, 系统, 检测
- **应用场景**: 检测, 网络, 控制
- **技术摘要**: 本发明提供了一种车载以太网数据处理方法、系统及电子控制单元ECU，车端ECU中的通讯缓冲模块在检测到与目标对象之间网络中断的情况下，通过进入等待状态以使日志收集模块在检测到通讯缓冲模块进入等待状态的情况下，停止向通讯缓冲模块发送日志数据。日志收集模块对于检测到的日志文件中的新增日志数据，在日志收集模块中第一缓冲区的容量已达到上限时，停止将新增日志数据存入第一缓冲区，能够实现在网络长时间中断情况下保...

### CN111555953A
- **申请号**: CN202010472748.7
- **IPC分类号**: H04L12/46
- **申请人**: 北京经纬恒润科技有限公司
- **分类理由**: 关键词匹配: 车载以太网, 以太网; IPC分类号匹配: H04L12/46
- **技术手段**: 提供, 装置, 方法, 设备, 系统
- **应用场景**: 车辆, 网络, 诊断
- **技术摘要**: 本申请提供一种基于车载以太网的远程诊断方法、装置、系统、TSP服务器，该方法利用TSP服务器在待诊断车辆和远程诊断设备间搭建VPN网络，形成异地局域网，从而满足远程诊断场景下的标准DoIP协议流程的实施，能够利用标准DoIP协议流程降低远程诊断的开发难度和工作量。


### CN111404697A
- **申请号**: CN202010243485.2
- **IPC分类号**: H04L12/02
- **申请人**: 北京经纬恒润科技有限公司
- **分类理由**: 关键词匹配: 车载以太网, 以太网; IPC分类号匹配: H04L12/02
- **技术手段**: 实现, 包括, 仿真, 设置, 设备, 控制
- **应用场景**: 控制
- **技术摘要**: 本发明公开了一种车载以太网仿真板卡，包括：电源模块、PCIE扩展模块、以太网交换机、至少一个以太网控制器、至少两个车载以太网仿真监控通道模块、工业以太网扩展通道模块和FPGA，其中，每个车载以太网仿真监控通道模块用于与一个车载以太网设备连接。由于本发明设置了至少两个车载以太网仿真监控通道模块，而每个车载以太网仿真监控通道模块能够与一个车载以太网设备连接，因此，本发明可以实现对多个车载以太网设备的仿...

### CN120301727A
- **申请号**: CN202510334812.8
- **IPC分类号**: H04L12/40
- **申请人**: 经纬恒润(天津)研究开发有限公司
- **分类理由**: 关键词匹配: CAN; IPC分类号匹配: H04L12/40
- **技术手段**: 实现, 包括, 方法, 控制, 检测
- **应用场景**: 检测, 网络, 控制
- **技术摘要**: 本申请公开了一种控制器唤醒方法和控制器，包括：接收唤醒报文；对唤醒报文的报文类型进行检测，得到报文类型检测结果；在报文类型检测结果指示报文类型为管理报文的情况下，将唤醒报文的报文数据发送至网络管理模块，以使网络管理模块对报文数据的内容格式进行检测，得到返回值，返回值用于指示唤醒报文是否包括有效PN请求；响应于控制器管理模块发送的报文检测请求，向控制器管理模块发送返回值，以使控制器管理模块根据返回值...

---

## 多通道视频注入同步技术

**专利数量**: 14 条

**技术描述**: 多通道视频注入和同步技术，用于视频信号的处理和同步

**主要申请人**:
- 北京经纬恒润科技股份有限公司: 8 条
- 北京经纬恒润科技有限公司: 5 条
- 唐山港口实业集团有限公司；北京经纬恒润科技股份有限公司: 1 条

**重点专利**:

### CN119011784A
- **申请号**: CN202411296065.5
- **IPC分类号**: H04N7/18
- **申请人**: 唐山港口实业集团有限公司；北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 视频流; IPC分类号匹配: H04N7/18
- **技术手段**: 实现, 包括, 方法, 处理, 控制, 系统
- **应用场景**: 驾驶, 控制, 车辆, 摄像, 网络
- **技术摘要**: 本申请公开了一种视频传输控制方法及远程驾驶系统，涉及远程操控技术领域，包括：车辆利用预先确定的编码码率值对车载摄像头采集的车辆周围画面进行编码，得到编码后的视频流数据；按照流媒体传输协议将编码后的视频流数据打包，得到视频数据包，并向远程控制端传输视频数据包，以供远程控制端处理，并在接收到远程控制端依据视频数据包确定的网络性能参数的参数值后，重新确定所要利用的编码码率值。车辆能够利用与实时的网络条件...

### CN118803201A
- **申请号**: CN202410781050.1
- **IPC分类号**: H04N7/18
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 视频注入; IPC分类号匹配: H04N7/18
- **技术手段**: 包括, 仿真, 方法, 处理, 控制, 系统
- **应用场景**: 摄像, 数据传输, 控制
- **技术摘要**: 本申请实施例公开了一种视频注入方法、系统及仿真板卡。其中，该方法包括：仿真板卡接收摄像头发送的第一视频数据；仿真板卡对第一视频数据进行处理，得到视频数据传输协议的参数以及嵌入式数据；仿真板卡接收仿真场景数据；仿真板卡对视频数据传输协议的参数、嵌入式数据和仿真场景数据进行处理，得到第二视频数据；仿真板卡将第二视频数据发送给控制器。可见，本申请实施例中仿真板卡是从摄像头发送的视频数据中获取对应的视频数...

### CN118368405A
- **申请号**: CN202410404666.7
- **IPC分类号**: H04N17/00
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 视频注入; IPC分类号匹配: H04N17/00
- **技术手段**: 平台, 包括, 测试, 仿真, 方法, 控制, 系统
- **应用场景**: 测试, 摄像, 控制
- **技术摘要**: 本申请实施例公开了一种视频注入测试系统及方法。其中，该系统包括：仿真硬件平台、上位软件平台和下位软件平台；仿真硬件平台包括：SCP和视频注入板卡；上位软件平台包括自动测试软件；下位软件平台包括接口控制软件。自动测试软件用于向接口控制软件发送对应的测试视频；接口控制软件用于控制视频注入板卡将测试视频注入SCP；自动测试软件还用于获取SCP的工作状态信息，根据工作状态信息确定SCP功能测试是否通过。可...

### CN117082212A
- **申请号**: CN202311159716.1
- **IPC分类号**: H04N7/18
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 视频处理; IPC分类号匹配: H04N7/18
- **技术手段**: 提供, 包括, 装置, 方法, 处理, 设备, 控制, 计算, 配置
- **应用场景**: 车辆, 驾驶, 摄像, 控制
- **技术摘要**: 本发明涉及自动驾驶技术领域，公开了远程驾驶的视频处理方法、装置、计算机设备及存储介质，该方法包括获取目标驾驶区域的三维场景动画，三维场景动画是基于目标驾驶区域的静态场景以及动态物体的实时信息得到的；基于目标车辆的车载摄像头的参数，对三维场景动画进行视角参数配置，得到与车载摄像头对应的三维视角动画；获取车载摄像头采集的目标视频，并确定目标视频的视频传输质量；若视频传输质量低于预设需求，基于车载摄像头...

### CN120301998A
- **申请号**: CN202510421386.1
- **IPC分类号**: H04N5/76
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 视频处理, 视频流
- **技术手段**: 提供, 包括, 装置, 算法, 处理, 设备, 系统
- **应用场景**: 通信
- **技术摘要**: 本申请提供一种车载视觉感知系统及电子设备，包括：数据采集模块、算法集成模块和通信组件，还包括数据预览模块、视频处理模块、视频推流模块中至少一项；数据采集模块用于采集图像数据，将图像数据和采集完成时间发布；算法集成模块用于利用至少一种图像感知算法对图像数据进行感知，获得图像数据的感知结果和感知完成时间并发布；数据预览模块用于根据采集完成时间和感知完成时间，对图像数据和感知结果同步，转发给待预览装置进...

---

## 数据注入类型支持技术

**专利数量**: 180 条

**技术描述**: 不同数据注入类型的支持技术，包括数据格式转换和处理

**主要申请人**:
- 北京经纬恒润科技股份有限公司: 92 条
- 北京经纬恒润科技有限公司: 54 条
- 经纬恒润(天津)研究开发有限公司: 28 条
- 天津经纬恒润科技有限公司: 6 条

**重点专利**:

### CN105653490A
- **申请号**: CN201511001271.X
- **IPC分类号**: G06F13/42
- **申请人**: 北京经纬恒润科技有限公司
- **分类理由**: 关键词匹配: 数据处理, 数据传输; IPC分类号匹配: G06F13/42
- **技术手段**: 提供, 装置, 方法, 处理, 设备, 控制
- **应用场景**: 数据传输, 控制
- **技术摘要**: 本发明提供一种基于地址控制的数据处理方法及装置，在获取到存储装置的地址空间中存储的各个地址之后，可以将所获取的各个地址存储在智能芯片的存储装置中；当确定与数据对应的当前操作后，可以基于各个地址在数据空间中连续执行当前操作，这样智能芯片和电子设备在交互数据时，智能芯片则无需等待电子设备反馈的响应信息，就可以连续的与电子设备进行数据交互，提高数据传输效率。而数据传输效率的提高使得一定时间内传输的数据量...

### CN119861945A
- **申请号**: CN202411931498.3
- **IPC分类号**: G06F8/65
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 数据传输; IPC分类号匹配: G06F8/65
- **技术手段**: 提供, 方法, 装置
- **应用场景**: 通信, 数据传输
- **技术摘要**: 本申请提供了一种波特率自适应程序刷写方法及装置，涉及数据传输技术领域。在执行所述方法时，先获取波特率切换列表，后根据波特率切换列表中的波特率，向下位机发送波特率切换命令，直至收到下位机发送的回复报文，然后，将回复报文对应的波特率确定为目标波特率，再根据目标波特率与下位机建立通信连接，最后，基于与下位机的通信连接进行程序刷写。这样，将上位机和下位机统一成目标波特率，使得上位机和下位机可以建立通信连接...

### CN119520609A
- **申请号**: CN202411676926.2
- **IPC分类号**: H04L67/566
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 数据传输; IPC分类号匹配: H04L67/566
- **技术手段**: 提供, 实现, 包括, 装置, 具有, 方法, 设备, 控制
- **应用场景**: 通信, 数据传输, 控制
- **技术摘要**: 本申请实施例提供了一种数据传输方法、装置、设备及介质，涉及嵌入式软件技术领域。该方法包括：通过数据分发服务DDS，将服务端和客户端进行连接；在使用数据写入器将服务数据写入DDS时，利用服务接口中定义的事件、方法和字段，将服务数据映射到相应的DDS的主题上，以使数据读取器通过读取DDS的主题，对服务数据进行读取。由此，服务数据可以通过DDS协议进行传输，提供了QoS控制的数据共享，通过发布和订阅机制...

### CN118803084A
- **申请号**: CN202410843815.X
- **IPC分类号**: H04L69/08
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 数据类型, 数据传输, 数据转换
- **技术手段**: 提供, 实现, 包括, 装置, 方法
- **应用场景**: 通信, 数据传输
- **技术摘要**: 本申请公开了一种数据转换方法和装置。本申请实施例的数据转换方法和装置，该方法包括：在接收到第一报文的情况下，从所述第一报文中筛选出满足第一预设标识和第一预设长度的第二报文；按照第二预设长度对所述第二报文进行拆分，得到至少一个数据类型的第一信号片段；按照第三预设长度对所述至少一个数据类型的第一信号片段进行封装，得到第三报文；将预设报文头添加到所述第三报文的头部，得到第四报文。如此，通过对报文的拆分和...

### CN117851298A
- **申请号**: CN202410031395.5
- **IPC分类号**: G06F13/10
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 数据传输; IPC分类号匹配: G06F13/10
- **技术手段**: 实现, 装置, 方法, 设备, 控制, 配置
- **应用场景**: 车辆, 数据传输, 控制
- **技术摘要**: 本申请公开了一种基于PCIe的车辆资源共享方法、装置、设备、介质及车辆，车辆中的第一控制器中创建初始PCIe设备，第一控制器通过NTB获取第二控制器中第一PCIe设备的配置信息，第一PCIe设备为第一控制器和第二控制器需要共享使用的PCIe设备，第一控制器基于配置信息对初始PCIe设备进行配置，得到目标PCIe设备，第一控制器通过目标PCIe设备访问第一PCIe设备。根据本实施例，可以实现第一控制...

---

## 多实时机级联技术

**专利数量**: 86 条

**技术描述**: 多实时机级联技术，用于分布式实时计算和处理

**主要申请人**:
- 北京经纬恒润科技有限公司: 47 条
- 北京经纬恒润科技股份有限公司: 26 条
- 经纬恒润(天津)研究开发有限公司: 11 条
- 天津经纬恒润科技有限公司: 2 条

**重点专利**:

### CN117785387A
- **申请号**: CN202410033079.1
- **IPC分类号**: G06F9/455
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 分布式, 集群; IPC分类号匹配: G06F9/455
- **技术手段**: 实现, 方法, 设置, 处理, 控制, 系统
- **应用场景**: 驾驶, 控制
- **技术摘要**: 本申请公开了一种融合架构的构建方法、融合处理方法及系统，在虚拟化框架的底层中设置虚拟机监视器和硬件，虚拟机监视器用于将各个HPC集群组织成资源池来实现硬件资源的整合，硬件用于通过预设连接方式将各个HPC集群进行连接以建立分布式共享内存，在虚拟化框架的中间层中将各个虚拟机镜像进行横向融合，得到融合后的虚拟机，在虚拟化框架中通过上层应用实现整车控制功能、智能驾驶功能和智能座舱功能，通过处理后的虚拟化框...

### CN111400069A
- **申请号**: CN202010208245.9
- **IPC分类号**: G06F9/54
- **申请人**: 北京经纬恒润科技有限公司
- **分类理由**: 关键词匹配: 并行处理; IPC分类号匹配: G06F9/54
- **技术手段**: 实现, 包括, 装置, 算法, 方法, 处理, 系统, 计算
- **应用场景**: 通用
- **技术摘要**: 本发明公开了一种KCF跟踪算法的实现方法、装置及系统，包括：接收上位机发送的第一待处理数据和第一触发信号；依据第一触发信号，基于待处理数据处理得到尺度计算所需参数；依据预设的尺度数量进行尺度计算的任务分配；在接收到上位机发送的第二待处理数据和第二触发信号后，主核和至少两个从核进行尺度计算；汇总自身和至少两个从核的尺度计算结果，并确定出最优尺度；基于最优尺度更新目标参数和尺度计算所需参数。上述方案将...

### CN104199699A
- **申请号**: CN201410437192.2
- **IPC分类号**: G06F9/445
- **申请人**: 北京经纬恒润科技有限公司
- **分类理由**: 关键词匹配: 多处理器; IPC分类号匹配: G06F9/445
- **技术手段**: 实现, 装置, 方法, 处理, 设备
- **应用场景**: 通用
- **技术摘要**: 本发明实施例公开了一种程序加载方法、芯片启动方法、装置及主控设备，主控设备和多处理器核心芯片预先进行交互确定主控设备内存空间与多处理器核心芯片的内存空间和寄存器空间的映射关系，从而当确定与每一个目标处理器核心相对应的待加载程序后，主控设备可以将与第i个目标处理器核心相对应的待加载程序写入多处理器核心芯片中与第i个目标处理器核心对应的内存中；并在确定与第i个目标处理器核心相对应的待加载程序在多处理器...

### CN120447969A
- **申请号**: CN202510488223.5
- **IPC分类号**: G06F9/34
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: IPC分类号匹配: G06F9/34
- **技术手段**: 包括, 装置, 方法, 生成, 处理, 配置
- **应用场景**: 通用
- **技术摘要**: 本申请公开了一种应用变量数据标定方法及相关装置，涉及数据处理领域，包括：预先将各个目标应用的变量描述信息统一写入变量信息描述文件，当需要对目标应用进行标定时，基于所述变量信息描述文件中存储的目标应用的各个变量的描述信息生成标定指令，当应用本方法的网关在获取到该标定指令时，对所述标定指令进行解析，基于解析结果确定各个变量在共享内存中的目标位置，然后再基于解析结果对所述目标位置进行标定测量，由此，只需...

### CN120407173A
- **申请号**: CN202510488068.7
- **IPC分类号**: G06F9/50
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: IPC分类号匹配: G06F9/50
- **技术手段**: 系统, 方法, 装置, 实现
- **应用场景**: 通用
- **技术摘要**: 本申请公开了一种自定义显卡分配的方法、装置及系统。响应渲染进程启动命令，获取目标机器及其中的目标显卡，并获取目标显卡的显卡适配器接口。将与渲染进程启动命令对应的渲染进程中所包含的显卡适配器接口重定向为目标显卡的显卡适配器接口，再执行渲染进程启动命令，就能通过目标显卡完成渲染进程。从而实现脱离默认的显卡分配策略，自定义通过目标显卡完成渲染进程。本申请所述方法采用接口的重定向，操作简单、无需更改渲染进...

---

## 集群化测试技术

**专利数量**: 102 条

**技术描述**: 集群化测试技术，用于大规模并行测试和验证

**主要申请人**:
- 北京经纬恒润科技股份有限公司: 42 条
- 北京经纬恒润科技有限公司: 32 条
- 经纬恒润(天津)研究开发有限公司: 22 条
- 天津经纬恒润科技有限公司: 4 条
- 江西经纬恒润科技有限公司: 1 条

**重点专利**:

### CN120386725A
- **申请号**: CN202510341561.6
- **IPC分类号**: G06F11/3668
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 测试系统; IPC分类号匹配: G06F11/3668
- **技术手段**: 提供, 测试, 包括, 控制, 系统, 配置
- **应用场景**: 测试, 控制
- **技术摘要**: 本申请提供一种车载软件的自动化测试系统，系统包括：测试用例管理单元，用于确定待执行的目标测试用例，将目标测试用例发送给编译单元，目标测试用例包括目标测试代码文件和目标配置文件；编译单元，用于根据目标配置文件，对目标测试代码文件和目标测试代码中涉及的源代码文件进行编译，获得目标测试用例对应的可执行文件；执行单元，用于在与安装车载软件的车载控制器建立连接后，执行可执行文件，从车载控制器预先建立的测试数...

### CN119861247A
- **申请号**: CN202510096084.1
- **IPC分类号**: G01R31/00
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 测试系统; IPC分类号匹配: G01R31/00
- **技术手段**: 提供, 实现, 包括, 测试, 方法, 设备, 控制, 系统, 配置
- **应用场景**: 测试, 汽车, 控制
- **技术摘要**: 本申请提供了一种汽车配电保护测试系统、方法、电子设备及存储介质，该方法可应用于上位机包括：确定待测配电保护通道控制器的配电通道测试配置表，配电通道测试配置表包括多个配电通道测试配置数组，每一个配电通道测试配置数组包括测试配电通道序号、测试电流值以及测试期望关断时间；确定配电通道测试配置表中每一个配电通道测试配置数组对应的测试实际关断时间；分别将每一个配电通道测试配置数组中的测试期望关断时间与对应的...

### CN119472582A
- **申请号**: CN202411466319.3
- **IPC分类号**: G05B23/02
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 测试系统; IPC分类号匹配: G05B23/02
- **技术手段**: 包括, 测试, 方法, 控制, 系统, 模拟
- **应用场景**: 测试, 汽车, 控制
- **技术摘要**: 本申请涉及一种智能配电控制器电性能测试系统及方法，应用于汽车电子技术领域，系统包括：智能配电控制器、供电子系统、自动化控制子系统、数据采集子系统、信号模拟子系统、回路切换子系统和负载模拟子系统。智能配电控制器分别与供电子系统、信号模拟子系统和回路切换子系统连接；负载模拟子系统与回路切换子系统连接；自动化控制子系统分别和回路切换子系统、信号模拟子系统、数据采集子系统和负载模拟子系统连接；自动化控制子...

### CN118466463A
- **申请号**: CN202410725901.0
- **IPC分类号**: G05B23/02
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 测试系统; IPC分类号匹配: G05B23/02
- **技术手段**: 提供, 实现, 包括, 测试, 设备, 系统
- **应用场景**: 测试
- **技术摘要**: 本申请公开了一种故障注入测试系统。系统包括：背板模块，包括交换芯片、多个第一连接器以及多个第二连接器，多个第二连接器与故障轨连接，交换芯片用于与上位机连接，交换芯片分别与多个第一连接器连接；执行板模块，分别与对应的第一连接器和两个第二连接器连接，用于响应上位机指令通过其中一个第二连接器为故障轨提供对应的故障信号；至少一个故障注入模块，分别与对应的第一连接器和第二连接器连接，还用于与外部ECU连接，...

### CN118150997A
- **申请号**: CN202410282101.6
- **IPC分类号**: G01R31/327
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 测试系统; IPC分类号匹配: G01R31/327
- **技术手段**: 提供, 实现, 测试, 方法, 控制, 系统
- **应用场景**: 测试, 摄像, 控制
- **技术摘要**: 本申请提供一种电容式触控开关的测试系统及测试方法，通过电容式触控开关的测试系统集成了对控制板卡以及测试台架中工业摄像头的控制功能，从而实现了一套可自动执行测试并判断测试结果的测试方法。有效减少人力成本和测试台架搭建使用成本，并提高测试效率和准确度。


---

## 虚拟仿真软件技术

**专利数量**: 23 条

**技术描述**: 虚拟仿真软件相关技术，包括仿真建模、虚拟环境等

**主要申请人**:
- 北京经纬恒润科技股份有限公司: 11 条
- 北京经纬恒润科技有限公司: 9 条
- 经纬恒润(天津)研究开发有限公司: 3 条

**重点专利**:

### CN118260993A
- **申请号**: CN202410346575.2
- **IPC分类号**: G06F30/23
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 仿真模型, 数字孪生; IPC分类号匹配: G06F30/23
- **技术手段**: 实现, 模型, 仿真, 方法, 生成, 设备, 系统, 计算, 模拟
- **应用场景**: 通用
- **技术摘要**: 本发明涉及电机技术领域，公开了一种温度场监测方法、系统、计算机设备及存储介质，本发明通过获取永磁同步电机的运行状态参数；基于运行状态参数，确定永磁同步电机的热源；采用数字孪生方式，根据热源对永磁同步电机的温度场进行在线模拟监测，生成温度场监测数据。本发明解决了现有永磁同步电机温度场仿真过程复杂，效率低，且仿真结果与真实环境不贴合的问题；实现了在电机运行过程中可以根据参数变化实时调整仿真模型精度的效...

### CN111797475A
- **申请号**: CN202010620848.X
- **IPC分类号**: G06F30/15
- **申请人**: 北京经纬恒润科技有限公司
- **分类理由**: 关键词匹配: 虚拟仿真, 虚拟测试; IPC分类号匹配: G06F30/15
- **技术手段**: 提供, 包括, 模型, 测试, 仿真, 方法, 生成, 设备, 控制, 系统
- **应用场景**: 车辆, 测试, 控制
- **技术摘要**: 本发明提供V2X测试方法及系统。上述V2X测试系统包括仿真系统、车辆动力学模型系统和被测设备。方法包括：使用仿真系统搭建虚拟测试场景；虚拟测试场景中包含车道、被测设备对应的主车虚拟仿真车辆模型和基础设施设备模型；在测试阶段，仿真系统向被测设备输出主车信息和周边设备信息；主车信息和周边设备信息用于被测设备生成控制命令；仿真系统接收车辆动力学模型系统返回的车辆控制命令；车辆控制命令是车辆动力学模型系统...

### CN119578359A
- **申请号**: CN202411629322.2
- **IPC分类号**: G06F30/398
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 仿真软件; IPC分类号匹配: G06F30/398
- **技术手段**: 提供, 测试, 装置, 仿真, 方法, 设备, 计算
- **应用场景**: 测试
- **技术摘要**: 本发明提供一种PCB走线添加电容的方法、装置、电子设备和存储介质，计算基础电容值对应的PCB走线面积；将仿真PCB板导入仿真软件以添加PCB测试走线，根据PCB测试走线的测试电容不断调整PCB测试走线，直至最终PCB走线的测试电容等于基础电容值，若接收到包含待添加电容值的添加指令，计算待添加电容值对应的最终PCB走线的个数；在目标PCB板中添加对应个数的最终PCB走线，得到最终PCB板。通过预设基...

### CN118690587A
- **申请号**: CN202411173553.7
- **IPC分类号**: G06F30/20
- **申请人**: 经纬恒润(天津)研究开发有限公司
- **分类理由**: 关键词匹配: 仿真模型; IPC分类号匹配: G06F30/20
- **技术手段**: 实现, 包括, 模型, 装置, 仿真, 方法, 控制, 计算
- **应用场景**: 通信, 控制
- **技术摘要**: 本申请公开了一种重载线控底盘的能源消耗仿真方法及装置，所述方法包括：获取待仿真重载线控底盘的仿真工况以及实际数据；将仿真工况输入预先构建好的仿真能耗模型中，得到待仿真重载线控底盘对应的仿真能耗；其中，仿真能耗模型由预先构建的底盘模型与整车控制策略模型之间的联合仿真通信构成；底盘模型预先通过样本重载线控底盘的各个关键部位对应的元件进行组合；基于实际数据，计算重载线控底盘的实际能耗；基于仿真能耗以及实...

### CN117421922A
- **申请号**: CN202311475109.6
- **IPC分类号**: G06F30/20
- **申请人**: 北京经纬恒润科技股份有限公司
- **分类理由**: 关键词匹配: 仿真模型; IPC分类号匹配: G06F30/20
- **技术手段**: 包括, 模型, 装置, 仿真, 方法, 生成
- **应用场景**: 通用
- **技术摘要**: 本申请公开了一种仿真模型生成方法及装置，其中方法包括：获取初始文件，初始文件包括用于进行仿真的多组仿真信息，每组仿真信息包括多个仿真子信息；对初始文件进行解析，得到中间文件，中间文件包括多组中间信息，每组中间信息包括一组仿真信息中的仿真子信息；从多组中间信息中选择目标中间信息；根据目标中间信息中的仿真子信息，构建仿真模型。上述中，对初始文件进行解析，以对初始文件记录的多个仿真子信息进行梳理，将属于...

---

## 技术发展趋势分析

### 核心技术布局

1. **HIL硬件在环仿真技术** (62条, 14.4%): dSPACE在HIL技术方面拥有最多专利，体现了其在硬件在环仿真领域的技术领先地位。

2. **故障注入板卡技术** (50条, 11.6%): 故障注入技术是HIL测试的重要组成部分，用于验证系统的故障处理能力。

3. **数据注入类型支持技术** (54条, 12.5%): 支持多种数据类型的注入，提高了测试系统的灵活性和适用性。

### 技术特点

- **实时性**: 大量专利涉及实时仿真和实时数据处理
- **模块化**: 采用模块化设计，支持不同类型的板卡和接口
- **标准化**: 支持多种工业标准和通信协议
- **智能化**: 集成机器学习和人工智能技术

### 应用领域

- **汽车电子**: 车载网络、自动驾驶、电动汽车控制系统测试
- **航空航天**: 飞行控制系统、导航系统测试
- **工业自动化**: 工业控制系统、机器人控制测试
- **新能源**: 电池管理系统、电力电子设备测试

