﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN120301998A,CN202510421386.1,"本申请提供一种车载视觉感知系统及电子设备，包括：数据采集模块、算法集成模块和通信组件，还包括数据预览模块、视频处理模块、视频推流模块中至少一项；数据采集模块用于采集图像数据，将图像数据和采集完成时间发布；算法集成模块用于利用至少一种图像感知算法对图像数据进行感知，获得图像数据的感知结果和感知完成时间并发布；数据预览模块用于根据采集完成时间和感知完成时间，对图像数据和感知结果同步，转发给待预览装置进行显示；视频处理模块用于根据采集完成时间，将图像数据转换为预设编码格式的视频流发布；视频推流模块用于将视频流打包后推流。
",H04N5/76,北京经纬恒润科技股份有限公司,多通道视频注入同步技术,2.0,"关键词匹配: 视频处理, 视频流","提供, 包括, 装置, 算法, 处理, 设备, 系统",通信
CN120096454A,CN202510237808.X,"本申请公开了一种电子后视镜系统的控制方法、装置及电子后视镜系统，方法包括：CMS控制器获得第一摄像头采集的用户视频流；对用户视频流进行分析处理，确定其中是否存在包含手势图像；若存在，触发对用户视频流中的手势图像进行手势分类，得到识别手势；基于预设的识别手势与操作指令的映射关系，确定识别手势对应的操作指令；执行操作指令，以实现对CMS显示内容的控制，所述显示内容基于第二摄像头采集的视场图像获得，第二摄像头设置在舱外的后视镜中。上述方案通过DMS监测驾驶员的手势动作，实时传送手势视频流给CMS监视器，实现CMS监视器显示与驾驶员智能交互，通过手势进行监视器有及视场显示调节，可快速便捷的进行监视器调整。
",B60R1/22,北京经纬恒润科技股份有限公司,多通道视频注入同步技术,1.0,关键词匹配: 视频流,"实现, 包括, 装置, 方法, 设置, 处理, 控制, 系统","驾驶, 摄像, 控制"
CN119011784A,CN202411296065.5,"本申请公开了一种视频传输控制方法及远程驾驶系统，涉及远程操控技术领域，包括：车辆利用预先确定的编码码率值对车载摄像头采集的车辆周围画面进行编码，得到编码后的视频流数据；按照流媒体传输协议将编码后的视频流数据打包，得到视频数据包，并向远程控制端传输视频数据包，以供远程控制端处理，并在接收到远程控制端依据视频数据包确定的网络性能参数的参数值后，重新确定所要利用的编码码率值。车辆能够利用与实时的网络条件及车辆速度相适应的编码码率进行编码，实现了远程视频质量和视频传输质量的平衡，在一定程度上提高了弱网环境下的视频传输质量。
",H04N7/18,唐山港口实业集团有限公司；北京经纬恒润科技股份有限公司,多通道视频注入同步技术,3.0,关键词匹配: 视频流; IPC分类号匹配: H04N7/18,"实现, 包括, 方法, 处理, 控制, 系统","驾驶, 控制, 车辆, 摄像, 网络"
CN118803201A,CN202410781050.1,"本申请实施例公开了一种视频注入方法、系统及仿真板卡。其中，该方法包括：仿真板卡接收摄像头发送的第一视频数据；仿真板卡对第一视频数据进行处理，得到视频数据传输协议的参数以及嵌入式数据；仿真板卡接收仿真场景数据；仿真板卡对视频数据传输协议的参数、嵌入式数据和仿真场景数据进行处理，得到第二视频数据；仿真板卡将第二视频数据发送给控制器。可见，本申请实施例中仿真板卡是从摄像头发送的视频数据中获取对应的视频数据传输协议的参数以及嵌入式数据，如此能自适应调整视频数据传输协议的参数以及嵌入式数据，避免控制器版本发生升级时，视频数据传输协议校验失败和嵌入式数据校验失败的问题，避免视频数据无法注入控制器的情况。
",H04N7/18,北京经纬恒润科技股份有限公司,多通道视频注入同步技术,3.0,关键词匹配: 视频注入; IPC分类号匹配: H04N7/18,"包括, 仿真, 方法, 处理, 控制, 系统","摄像, 数据传输, 控制"
CN118368405A,CN202410404666.7,"本申请实施例公开了一种视频注入测试系统及方法。其中，该系统包括：仿真硬件平台、上位软件平台和下位软件平台；仿真硬件平台包括：SCP和视频注入板卡；上位软件平台包括自动测试软件；下位软件平台包括接口控制软件。自动测试软件用于向接口控制软件发送对应的测试视频；接口控制软件用于控制视频注入板卡将测试视频注入SCP；自动测试软件还用于获取SCP的工作状态信息，根据工作状态信息确定SCP功能测试是否通过。可见，本申请实施例中采用视频注入的方式代替真实的DMS摄像头和OMS摄像头，自动完成SCP相关测试，如此减少了SCP的测试周期，降低了SCP的测试成本，提高了SCP测试结果的准确性。
",H04N17/00,北京经纬恒润科技股份有限公司,多通道视频注入同步技术,3.0,关键词匹配: 视频注入; IPC分类号匹配: H04N17/00,"平台, 包括, 测试, 仿真, 方法, 控制, 系统","测试, 摄像, 控制"
CN117135309A,CN202310960774.8,"本申请公开了一种座舱监控系统，包括：至少两个图像采集装置，用于采集座舱内的第一视频数据，监控控制器，通过第一总线分别与图像采集装置连接，用于通过第一总线获取第一视频数据，并根据车机控制器发送的第一控制信息对第一视频数据进行处理，得到第二视频数据，车机控制器，通过第二总线与监控控制器连接，用于向监控控制器发送第一控制信息，以及接收监控控制器通过第二总线发送的第二视频数据。根据本申请实施例，能够降低整车成本，减少车机控制器的负荷。
",H04N7/18,北京经纬恒润科技股份有限公司,多通道视频注入同步技术,2.0,IPC分类号匹配: H04N7/18,"包括, 装置, 处理, 控制, 系统",控制
CN117082212A,CN202311159716.1,"本发明涉及自动驾驶技术领域，公开了远程驾驶的视频处理方法、装置、计算机设备及存储介质，该方法包括获取目标驾驶区域的三维场景动画，三维场景动画是基于目标驾驶区域的静态场景以及动态物体的实时信息得到的；基于目标车辆的车载摄像头的参数，对三维场景动画进行视角参数配置，得到与车载摄像头对应的三维视角动画；获取车载摄像头采集的目标视频，并确定目标视频的视频传输质量；若视频传输质量低于预设需求，基于车载摄像头的三维视角动画，对目标车辆进行远程驾驶的控制。若车载摄像头采集的目标视频的视频传输质量低于预设需求，利用三维视角动画提供清晰准确的三维场景画面，辅助驾驶员远程驾驶目标车辆，提高了远程驾驶的安全性。
",H04N7/18,北京经纬恒润科技股份有限公司,多通道视频注入同步技术,3.0,关键词匹配: 视频处理; IPC分类号匹配: H04N7/18,"提供, 包括, 装置, 方法, 处理, 设备, 控制, 计算, 配置","车辆, 驾驶, 摄像, 控制"
CN114511831A,CN202210040086.5,"本发明实施例提供了一种基于视觉可行驶区域的检测方法及系统，方法包括：获取车辆摄像头视频流信号中的实时鱼眼图像，根据第一预设映射关系确定与实时鱼眼图像对应的第一经纬展开图像，第一预设映射关系为从鱼眼图像到经纬展开图像的映射关系；将第一经纬展开图像作为深度网络模型的输入，得到第一经纬展开图像的像素级分类结果；通过边界搜索算法对像素级分类结果进行遍历，得到可行驶区域的多个边界点；根据第二预设映射关系确定车辆与每个边界点所对应真实位置之间的真实距离，第二预设映射关系包括经纬展开图像中每个像素点与车辆与该像素点所对应真实位置之间真实距离的映射关系。本发明可有效预警车辆横纵向的潜在危险，实现全方位预警。
",G06V20/56,北京经纬恒润科技股份有限公司,多通道视频注入同步技术,1.0,关键词匹配: 视频流,"提供, 实现, 包括, 模型, 算法, 方法, 系统, 检测","车辆, 检测, 摄像, 网络"
CN112565224A,CN202011355802.6,"本发明公开了一种视频处理方法及装置，能够在编码器接收到第一视频帧图像时记录第一时间；在输出编码后的第一视频帧图像时记录第二时间；将第一时间和第二时间嵌入编码后的第一视频帧图像，然后对其进行RTP分包，在首个RTP包头嵌入对应当前系统时间的第三时间并发往视频接收侧，以使视频接收侧能够根据接收编码后的第一视频帧图像的RTP分包的情况，结合前述嵌入第一视频帧图像的时间信息确定第一视频帧图像的链路传输时间。该视频处理方法及装置能够在视频数据传输过程中记录并传送各个处理节点的时戳信息，实现对视频传输各个环节延时的测试，能够在视频数据发生阻塞时方便的定位链路问题，便于后续控制决策的确定。满足实际应用需要。
",H04L29/06,北京经纬恒润科技股份有限公司,多通道视频注入同步技术,1.0,关键词匹配: 视频处理,"实现, 测试, 装置, 方法, 处理, 控制, 系统","测试, 数据传输, 控制"
CN111476848A,CN202010243473.X,"本发明提供一种视频流仿真方法及装置，应用于仿真技术领域，该方法在获取视频仿真软件输出的理想视频流后，依据目标摄像头的伽马校正公式对理想视频流进行逆向伽马校正，得到与目标摄像头对应的、未进行伽马校正的校正前像素，进一步计算各所得校正前像素经相应的滤镜过滤后的光强值，得到以过滤后的光强值表示的原始像素，最终根据原始像素生成原始视频流。本方法通过逆向伽马校正，还原理想视频流中伽马校正对各像素的改变，通过计算各像素经过滤镜过滤后的光强值，仿真物理摄像头中滤镜对光线的改变，得到原始像素，最终得到原始视频流，从而实现对未设置ISP芯片的摄像头所输出的原始视屏流的仿真，为控制器测试提供有效保障。
",G06T7/90,北京经纬恒润科技有限公司,多通道视频注入同步技术,1.0,关键词匹配: 视频流,"提供, 实现, 测试, 装置, 仿真, 方法, 设置, 生成, 控制, 计算","测试, 摄像, 控制"
CN109874007A,CN201910131153.2,"本发明实施例公开了一种摄像头故障注入方法及装置，该方法包括：首先获取摄像头视频信号，然后根据接收到的故障注入指令，对摄像头视频信号进行故障仿真处理，得到故障仿真信号，将故障仿真信号输出至待测控制器。本发明实施例公开的一种摄像头故障注入方法及装置，能够基于正常的摄像头视频信号，依据预先确定的信号故障类型对摄像头视频信号进行故障仿真处理，然后将经过故障仿真处理的视频信号输入待测控制器，从而实现了摄像头信号故障的处理及注入，便于对待测控制器进行更加全面的检测，保证待测控制器各项功能的稳定性与可靠性。
",H04N17/00,北京经纬恒润科技有限公司,多通道视频注入同步技术,2.0,IPC分类号匹配: H04N17/00,"实现, 包括, 装置, 仿真, 方法, 处理, 控制, 检测","检测, 摄像, 控制"
CN108627813A,CN201810916493.1,"本发明公开了一种激光雷达，该激光雷达采用位置固定的多通道激光发射器和多通道光电探测器，采用扫描模块旋转的方式同时对所述激光脉冲和所述激光脉冲回波信号进行水平方向的扫描，接收激光脉冲回波信号的瞬时视场可以做的很小，背景光噪声较低，易实现较大探测距离，且不需要滑环，不用进行无线供电和无线通信等功能模块，不用对激光发射器和光电探测器进行多次装调，具有成本低、装调方便、结构紧凑和易于量产等优点。
",G01S7/481,北京经纬恒润科技有限公司,多通道视频注入同步技术,1.0,关键词匹配: 多通道,"具有, 实现","雷达, 通信, 激光"
CN108549480A,CN201810265674.2,"本发明提供了一种基于多通道数据的触发判断方法及装置，确定目标触发条件对应的多个传感器电极；当每个传感器电极都满足相应的触发条件时，分别判断每个传感器电极在相应的触发持续时间内的变化趋势是否在相应的预设范围内；若是，计算多个传感器电极中最晚的开始触发时刻与最早的开始触发时刻的开始时刻差值，并计算多个传感器电极中最晚的结束触发时刻与最早的结束触发时刻的结束时刻差值；判断所述开始时刻差值是否小于第一预设值且所述结束时刻差值是否小于第二预设值；若是，判定多个传感器电极满足所述目标触发条件。提高了多通道数据的触发判断的准确性。
",G06F3/01,北京经纬恒润科技有限公司,多通道视频注入同步技术,1.0,关键词匹配: 多通道,"提供, 方法, 计算, 装置",传感器
CN108184109A,CN201711375073.9,"本发明公开了一种车载信息娱乐系统的测试平台及测试方法，测试平台包括：机柜和暗室；机柜包括：工控机和信号调理板卡；暗室包括：图像采集模块、声音采集模块和机械手，被测车机放置在暗室内。当对车载信息娱乐系统进行测试时，本发明使用机械手代替人手动操作，使用机器听视觉代替人的观察与判断能力，配合自动化测试程序，实现了对车载信息娱乐系统的自动测试，不仅在一定程度上排除了人主观因素对测试造成的影响，使得测试更容易，测试结果更可靠，而且还大大节约了测试时间和人力成本，满足了车载信息娱乐系统测试对平台化、系统化和自动化的需求，提高了测试的可行性和安全性。
",H04N17/00,北京经纬恒润科技有限公司,多通道视频注入同步技术,2.0,IPC分类号匹配: H04N17/00,"平台, 实现, 包括, 测试, 方法, 系统",测试
