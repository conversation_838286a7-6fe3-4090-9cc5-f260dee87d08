﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN120490849A,CN202510685630.5,"本发明提供电池SOC校正方法、装置、存储介质及电子设备，当电池的SOC变化量大于预设变化量阈值时，根据电池当前的温度、SOH和SOC，选取对应的电池模型、模型参数和电池SOC‑OCV曲线并基于此确定预设时长内的OCV值和曲线斜率；根据OCV值和曲线斜率，计算得到校正SOC值，并将初始时刻的SOC值校正为校正SOC值，将电池的SOC变化量置零；从初始时刻校正后的SOC值开始，进行安时积分计算，得到SOC变化量，直到SOC变化量大于所述预设变化量阈值时，进入下一个校正周期。本发明在电池的SOC变化量大于预设变化量阈值时，对SOC值进行校正，并重新监控SOC变化量，以此提高对SOC值监控的准确性。
",G01R31/382,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/382,"提供, 模型, 装置, 方法, 设备, 计算",通用
CN120469928A,CN202510646025.7,"本申请公开了一种港口数字孪生系统的场景编排方法及相关装置，涉及港口数字孪生领域，包括：接收港口数字孪生系统中前端页面针对港口设置各虚拟港口要素的各要素编排信息；利用虚幻引擎依据各要素编排信息，在数字孪生系统的港口作业场景中分别生成相应的虚拟港口要素，分别校验各虚拟港口要素；利用虚幻引擎依据通过校验的各虚拟港口要素生成目标港口作业场景；利用虚幻引擎提取该目标港口作业场景中各虚拟港口要素的目标编排信息，得到港口数字孪生系统的场景文件，目标编排信息是通过验证的要素编排信息。本申请基于利用虚幻引擎，实现在前端页面直接对于港口作业场景进行要素编排校验，无需开发人员在编辑器进行编排打包测试，提高编排效率。
",G06F11/3668,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/3668,"实现, 包括, 测试, 装置, 方法, 设置, 生成, 系统","测试, 验证"
CN120446718A,CN202510577674.6,"本申请公开了一种板卡测试系统及板卡测试方法，涉及电力电子技术领域。板卡测试系统中，上位机用于基于HIL设备信号列表文档，向待测信号板卡发送对应的目标激励信号；基于待测信号板卡的当前测试通道信息，向故障注入板卡发送通道选通指令；基于目标激励信号对应的目标待测信号类型，向信号测试模块发送测试切换指令；待测信号板卡用于响应于目标激励信号输出对应的目标响应信号；故障注入板卡用于响应于通道选通指令导通目标通道；信号测试模块用于响应于测试切换指令接入与目标待测信号类型对应的目标测试仪器；以及，将目标响应信号的测试结果传输至上位机。根据本申请实施例，能够更为高效、准确地实现HIL板卡自动化激励测试。
",G01R31/28,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,3.0,关键词匹配: 故障注入; IPC分类号匹配: G01R31/28,"实现, 测试, 方法, 设备, 系统",测试
CN120428206A,CN202510578655.5,"本申请提供了一种激光雷达的故障诊断方法、装置、电子设备及存储介质，该方法应用于车载终端包括：对各个接收到的雷达数据包进行校验，并将校验通过的雷达数据包放入数据缓冲区，雷达数据包包括激光雷达的点云数据；根据数据缓冲区内的雷达数据包，判断激光雷达的点云数据帧是否存在丢包；若是，则生成诊断信息，并将诊断信息发送至指定设备；若否，则将激光雷达的点云数据和预先设定的点云数据进行对比得到比对相似度，比对相似度用于评估激光雷达的点云数据与预先设定的点云数据之间的匹配程度，本申请通过软件层面的判断逻辑，不仅能够分析出激光雷达所处的网络环境状态，还能进一步判断激光雷达是否存在硬件设备故障。
",G01S7/497,北京经纬恒润科技股份有限公司,故障注入板卡技术,1.0,关键词匹配: 故障诊断,"提供, 包括, 装置, 方法, 生成, 设备","雷达, 网络, 诊断, 激光"
CN120315994A,CN202510232808.0,"本发明公开一种背靠背测试方法及系统，涉及自动化测试技术领域，包括：编写matlab脚本，生成测试面板；选择Simulink模型并生成代码；将生成的代码封装成SIL模块；连接Simulink模型和SIL模块中对应的输入端口和输出端口，形成待测模型；选择测试用例；将所选择的测试用例转换为待测模型的测试框架；将测试框架生成测试文件；将待测模型的实际输出结果与测试用例的期望结果进行对比验证，判断测试文件中的测试框架是否通过测试。本发明通过使用Matlab脚本自动化生成待测模型，不仅能够减少人为因素导致的错误，从而提高测试的可靠性和准确性，还显著的减少了手动测试所需要的时间和人力成本，提高了测试效率。
",G06F11/3668,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/3668,"包括, 模型, 测试, 方法, 生成, 系统","测试, 验证"
CN120216327A,CN202510336402.7,"本申请一种代码仓库验证方法及系统、电子设备、存储介质，所述方法包括：在当前代码仓库发生预先配置的任意一种触发事件时，接收代码仓库发送的当前触发事件对应的仓库变更请求以及当前触发事件的原始数据；响应当前触发事件的仓库变更请求，基于当前触发事件的原始数据以及预先配置的任务流关系信息，生成当前触发事件对应的触发任务信息；根据当前触发事件对应的触发任务信息，从各个验证任务流中检索出当前触发事件对应的各个当前任务流；执行各个当前任务流，并对各个当前任务流的执行状态进行监听；当各个当前任务流执行完成后，根据各个当前任务流程的执行结果，生成当前仓库触发结果，并将当前仓库触发结果反馈给当前代码仓库。
",G06F11/3604,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/3604,"包括, 方法, 生成, 设备, 系统, 配置",验证
CN120142967A,CN202510539307.7,"本申请公开了一种锂电池荷电状态的校正方法，首先通过基于初始荷电状态SOC与电流信息估算当前时刻的第一SOC，若锂电池处于去极化状态时，获取锂电池自开始去极化到当前时刻的变化电压，并预测经过预设时长但仍处于去极化状态时锂电池的开路电压OCV，然后根据该OCV以及预先构建的SOC‑OCV数据表确定第二SOC，由于根据越趋近于去极化完成时的OCV得到的SOC更接近于该阶段的真实SOC，可知第二SOC相比于根据当前OCV得到的SOC具有更高的精度，最后将第一SOC和第二SOC中更接近真实值的结果作为当前时刻的SOC，可以对去极化过程中的SOC进行校正，从而有效减小了可能由于初始SOC以及电流传感器采样误差导致的较大估计误差，提高了锂电池处于去极化阶段时SOC的计算准确性。
",G01R31/3842,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/3842,"具有, 方法, 计算",传感器
CN120104391A,CN202510174461.9,"本申请公开了一种记录异常数据的分析方法及装置。获取程序发生trap异常时调用的函数的指令地址，基于CSA链表的首节点获取第一函数，(程序在发生trap异常前调用的最后一个函数)的跳转地址。根据首节点的链接字查找第二函数(程序调用第一函数前调用的最后一个函数)对应的节点，基于第二函数对应的节点获取第二函数的跳转地址。重复通过节点的链接字获取跳转地址的步骤，直至链接字为0，并将这些跳转地址记录在列表中，从而实现程序发生trap异常后，及时记录函数的跳转逻辑的功能，能够防止异常数据的丢失，便于复现异常。
",G06F11/07,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/07,"方法, 装置, 实现",通用
CN120028677A,CN202510245200.1,"本申请公开了一种故障检测方法、装置及设备。该方法包括：在抬头显示系统投射出的图像异常的情况下，读取所述抬头显示系统对应的软件；在所述软件的读取结果指示所述抬头显示系统中的FPC链路存在异常的情况下，确定所述FPC链路中的至少一个组件，所述至少一个组件包括柔性电路板、印刷电路板、所述柔性电路板和所述印刷电路板的连接插座；在所述至少一个组件中确定存在故障的故障组件，以及所述故障组件中的故障点。根据本申请实施例，可以提高故障检测的准确性。
",G01R31/28,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,3.0,关键词匹配: 故障检测; IPC分类号匹配: G01R31/28,"包括, 装置, 方法, 设备, 系统, 检测, 电路",检测
CN120009774A,CN202510214464.0,"本申请提供一种短路检测电路和驾驶员监测系统，涉及电路故障检测技术领域。由于检测模块均在与自身一一对应的负载的分压小于设定电压时输出第一信号，而负载分压小于设定电压可以表明负载发生短路故障，所以如果接收到相应检测模块输出的第一信号，则能够检测出相应的负载发生短路故障，从而如果接收到部分检测模块输出的第一信号，则检测出部分检测模块所对应的负载发生短路故障，如果接收到全部检测模块输出的第一信号，则检测出全部检测模块所对应的负载均发生短路故障，又由于检测对象可以为补光灯电路，即负载可以为LED，所以该短路检测电路既能检测出补光灯电路整体发生短路故障又能检测出补光灯电路中的部分LED发生短路故障。
",G01R31/52,北京经纬恒润科技股份有限公司,故障注入板卡技术,3.0,关键词匹配: 故障检测; IPC分类号匹配: G01R31/52,"系统, 提供, 检测, 电路","驾驶, 检测"
CN120009738A,CN202510329144.X,"本申请公开了一种电池荷电状态校正方法及装置，当接收到充电请求时，将电池设置为充电状态，并获取初始荷电状态值及其对应的荷电状态‑开路电压曲线斜率区间。若斜率区间的最大斜率不大于阈值，则根据安时积分公式计算第一充电荷电状态值，并重新检查斜率区间。如果斜率大于阈值，则计算第二充电荷电状态值，并通过扩展卡尔曼滤波进行校正，得到最终荷电状态值。若电池已满充，则进行满充校正，并将电池状态设置为充电结束。通过结合扩展卡尔曼滤波算法和安时积分计算公式进行荷电状态值校正，可以有效地抑制安时积分累积误差，减轻电池静置时间不足对荷电状态上电校正的影响，从而提高充电过程中荷电状态校正的精度。
",G01R31/367,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/367,"装置, 算法, 方法, 设置, 计算",通用
CN120011242A,CN202510337222.0,"本申请实施例提供了一种整段测试序列的程序化执行方法及相关设备，其中，实例化模块能够根据测试序列中各个执行节点的节点属性信息以及预设全局实例类，来构建各执行节点对应的节点实例对象。其中，由于节点实例对象为预设抽象节点基类的派生节点，因而能够通过执行节点的节点实施函数以及抽象节点基类中所描述的通用特性，来构建节点实例对象。同时，由于在预设全局实例类中存储了各个节点属性信息对应的节点实施函数，使得OTX测试序列在进行程序化执行时，仅需从预设全局实例类中调用执行节点对应的实施函数即可，无需关注在节点底层中的通用特性，降低了OTX测试序列的程序化执行难度，提高了OTX测试序列程序化执行的通用性。
",G06F11/3668,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/3668,"提供, 方法, 测试, 设备",测试
CN119986193A,CN202510059005.X,"本申请实施例提供了一种电子电气架构的评估方法、装置、设备及介质，涉及汽车电子技术领域。该方法包括：获取评估模型，评估模型包括N个层级，N为正整数；构建评估模型的N‑1个层级分别对应的判断矩阵，判断矩阵的阶数等于对应的层级的模型元素的数量，判断矩阵中的项为判断矩阵的行模型元素和列模型元素相比的重要性标度值；根据N‑1个层级分别对应的判断矩阵的特征向量，得到评估结果。由此，利用评估模型中的层级对应的判断矩阵，将定性的评估标准转化为定量的数值。通过计算模型元素之间的比值，判断矩阵能够反映不同元素之间的相对重要性和优先级，使得评估更加客观和准确，体现了评估结果的可靠性。
",G01R31/00,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/00,"提供, 包括, 模型, 装置, 方法, 设备, 计算",汽车
CN119988116A,CN202510103754.8,"本公开涉及一种总线短路故障的检测电路及方法，检测电路包括：第一信号线、第二信号线、供电模块、n个外部检测模块及n个故障响应模块；总线的各支路通过第一信号线以及第二信号线并联，外部检测模块与第一信号线以及第二信号线电连接，故障响应模块与第一信号线以及第二信号线串联，外部检测模块与故障响应模块电连接，外部检测模块及故障响应模块与供电模块电连接。本公开通过外部检测模块根据发生短路故障，控制各个故障响应模块切断总线各支路的供电，在切断供电后，没有发生短路故障的总线支路的供电被恢复，而存在短路故障的总线支路对应的外部检测模块发出故障提醒，由此工作人员可以准确且快速的确定发生短路故障的总线支路的位置。
",G06F11/22,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/22,"包括, 方法, 控制, 检测, 电路","检测, 控制"
CN119902987A,CN202510059762.7,"本申请公开了一种针对驱动代码的自动化测试方法、装置、电子设备及介质，该方法包括：获取待测微控制器抽象层驱动代码、目标全局变量和目标读取函数；执行测试用例，以通过待测微控制器抽象层驱动代码中的接口函数，执行用于实现至少一种目标功能的操作，操作包含将用于实现至少一种目标功能的目标参数值写入目标寄存器；基于目标读取函数，读取目标寄存器中的目标参数值，并将目标参数值存储到目标全局变量；对目标寄存器对应的预期值和目标全局变量的值进行对比，对待测微控制器抽象层驱动代码进行验证。
",G06F11/3668,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/3668,"实现, 包括, 测试, 装置, 方法, 设备, 控制","测试, 验证, 控制"
CN119902920A,CN202510056592.7,"本发明提供一种诊断测试序列的执行方法及装置，调用外部API接口加载出诊断测试序列引用的ODX文件；调用外部API接口从ODX文件中，获取诊断测试序列节点标识的通信通道数据，基于通信通道数据创建诊断设备与待诊断ECU之间的通信连接；调用外部API接口从ODX文件中，获取诊断测试序列节点标识的诊断服务数据，基于诊断服务数据生成并发送诊断服务请求，接收并解析诊断服务响应得到响应参数值。在本方案中，应用了一套外部API接口，解决了诊断测试序列节点执行问题，使得开发人员专注于诊断逻辑的实现，无需编写过多的代码实现ODX文件解析以及与ECU之间的通信，以实现减小开发人员的工作量，提高开发效率的目的。
",G06F11/07,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/07,"提供, 实现, 测试, 装置, 方法, 生成, 设备","通信, 测试, 诊断"
CN119881664A,CN202510329565.2,"本发明提供一种电池模型参数的辨识方法及装置，从云平台数据库读取特定时间段内的电池数据，根据每条电池数据提取多个充放电片段数据，按时间戳降序排列对每个充放电片段数据进行离散傅里叶变换生成频谱图。根据频谱图确定每个充放电片段数据所属的频率区间，进行滤波处理得到目标充放电片段数据。根据目标充放电片段数据和预设频率区间估计充电与放电参数。本方案不依赖大规模的训练集和长时间训练，无需神经网络算法即可高精度获取电池模型参数，保证云端BMS向车端BMS实时更新参数。频率分析能够揭示电池在全生命周期内的参数变化，满足长时间尺度下的故障诊断和安全预警需求，丰富了云端BMS的电池参数辨识方法。
",G01R31/367,北京经纬恒润科技股份有限公司,故障注入板卡技术,3.0,关键词匹配: 故障诊断; IPC分类号匹配: G01R31/367,"提供, 平台, 模型, 装置, 算法, 方法, 生成, 处理","网络, 诊断"
CN119881536A,CN202510115021.6,"本申请提供一种电源网络的故障定位方法和电源网络的故障定位装置。在该故障定位方法中，由于电激励的频率小于每个电容器的谐振频率，所以在施加电激励时电流几乎只流过短路电容器，从而在短路电容器之前的不同检测电容器的两端电压会有明显变化，在短路电容器之后的不同检测电容器的两端电压几乎没有变化，因此如果某个范围符合上述情况，则可以确定出短路电容器在此范围内；又由于此后会对此范围内的每个电容器进行短路故障检测，所以可以实现对电容器的故障定位。由于本申请只对确定出的短路电容器所在的范围内的每个电容器进行短路故障检测，所以减小了进行短路故障检测的电容器的个数，从而降低了电源网络上的电容器进行故障定位的成本。
",G01R31/08,北京经纬恒润科技股份有限公司,故障注入板卡技术,3.0,关键词匹配: 故障检测; IPC分类号匹配: G01R31/08,"提供, 实现, 装置, 方法, 检测","检测, 网络"
CN119597659A,CN202411677320.0,"本申请实施例公开了一种OTX变量系统的创建、运行方法，首先构建开放式测试序列交换格式OTX变量系统的各个数据库数值表，并根据各个数据库数值表构建各个数据库数值表集合。接着根据各个数据库数值表集合构建数据库数值表管理器，得到OTX变量系统。本申请通过构建各个数据库数值表、数据库数值表集合和数据库数值表管理器，可以实现对OTX的变量系统的结构化管理。同时，能够使OTX的变量系统更加有序和清晰，便于维护和扩展。并且数据库数值表集合可以包含不同类型的键值对，如全局变量函数键值对、主函数键值对或非主函数键值对等。这种灵活性可以适应不同类型的变量需求，并支持在不同上下文中使用变量。
",G06F11/3668,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/3668,"系统, 方法, 测试, 实现",测试
CN118885332A,CN202411018049.X,"本申请提供了一种基于诊断服务报文的软件程序回滚方法和装置，获取目标下载请求，基于该下载请求将第一目标软件下载至软件运行区域；获取第一诊断服务报文，该报文中包括备份操作请求；基于该操作请求执行备份操作，备份操作用于将第一目标软件从软件运行区域拷贝至软件备份存储区域进行存储，得到第二目标软件；获取第二诊断服务报文，第二诊断服务报文中包括回滚操作请求；基于该操作请求执行回滚操作，回滚操作用于将软件运行区域中的第一目标软件擦除，并将第二目标软件从软件备份存储区域拷贝至软件运行区域，得到第三目标软件。如此，在下载软件后对该软件进行备份，然后再基于该备份进行回滚操作，避免软件程序难以进行回滚的情况的出现。
",G06F11/14,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/14,"提供, 方法, 包括, 装置",诊断
CN118796679A,CN202410756392.8,"本发明提供一种模型测试方法及装置，当接收到待测试模型的名称和输出变量个数时，基于名称和输出变量个数运行测试脚本，其中，测试脚本预先基于仿真函数进行编写得到，待测试模型预先利用仿真工具对原始模型的属性进行修改得到；通过测试脚本加载依据需求文档预先编制的测试用例，生成仿真点用例；基于仿真点用例测试待测试模型，得到仿真值；基于仿真值生成仿真测试记录，并将仿真测试记录转换为原始用例对应的测试记录；通过交互界面展示仿真测试记录和原始用例对应的测试记录。本发明基于测试脚本自动有序地读取用例，对模型进行快速仿真验证，简化了模型的调试和测试。自动生成测试记录，提高了工作效率又减少了误差率。
",G06F11/36,天津经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"提供, 测试, 模型, 装置, 仿真, 方法, 生成, 工具","测试, 验证"
CN118777823A,CN202410827718.1,"本申请公开了一种功率器件的结温估算方法及装置。功率器件至少包括待估算的第一功率芯片以及与第一功率芯片相邻的测温元件，该方法包括：从第一功率芯片产生热损耗的损耗功率中提取直流分量和交流分量；通过热阻‑热容网络模型对交流分量进行离散时域处理，得到第一功率芯片的结温波动量；基于测温元件所检测到的温度值、直流分量与平均结温之间的关联关系，计算第一功率芯片的平均结温；基于结温波动量、平均结温与结温估算值之间的关联关系，对第一功率芯片的结温进行估算，得到第一功率芯片对应的第一结温估算值。本申请所提供的方案提升了结温估算的准确性以及过温保护的准确性，避免了过温保护的失效。
",G01R31/26,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/26,"提供, 包括, 模型, 装置, 方法, 处理, 检测, 计算","检测, 网络"
CN118777917A,CN202410968984.6,"本申请公开一种电池包内部绝缘故障点位检测方法、装置及电子设备，方法包括：当电池管理系统上电初始化时，获取电池包内部各个电池单体的荷电状态值，并写入荷电状态数组；每隔预设时间间隔对所述荷电状态数组中各个电池单体的荷电状态值进行更新，得到更新后的荷电状态数组；根据所述更新后的荷电状态数组，确定所述电池包内部任意相邻的两个电池单体之间荷电状态值的相对变化；基于所述任意相邻的两个电池单体之间荷电状态值的相对变化，确定所述电池包内部的绝缘故障点位。本申请可以定位电池包内部的绝缘故障点位。
",G01R31/392,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/392,"包括, 装置, 方法, 设备, 系统, 检测",检测
CN118747131A,CN202410822221.0,"本申请公开了一种看门狗喂狗状态监测方法和装置，该方法应用于处理器，该方法包括：获取系统板在程序代码运行过程中发送的目标信号，其中，所述系统板上设置有看门狗模块；在确定所述目标信号为喂狗信号的情况下，获取发送所述喂狗信号的第一时间戳信息；判断所述第一时间戳信息是否在所述看门狗模块预先配置的预设喂狗时段内；在确定所述第一时间戳信息在所述预设喂狗时段内的情况下，确定所述看门狗模块的喂狗功能检测通过。以实现在已发布测试版本的软件上自行制造看门狗错误，对看门狗喂狗状态进行监测，减少了人工成本，提升了看门狗喂狗状态监测的便利性。
",G06F11/07,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/07,"实现, 包括, 测试, 装置, 方法, 设置, 处理, 系统, 检测, 配置","检测, 测试"
CN118708471A,CN202410692409.8,"本申请提供了一种测试序列的生成方法和装置，首先，获取待配置数据，根据所述待配置数据生成待匹配测试工具集合；然后基于所述待匹配测试工具集合中的目标测试工具和所述目标测试工具的数据库描述文件生成测试设备库；再确定所述目标测试工具的路径信息，根据所述目标测试工具的路径信息和所述测试设备库生成目标数据字典；最后，获取目标测试用例，基于所述目标数据字典和所述目标测试用例生成测试序列。如此生成的测试序列可以应用于不同的系统，当系统变更时仅需要对于测试设备库进行处理即使得测试用例应用于变更后的系统，不需要修改测试序列，提高测试序列的复用性。
",G06F11/36,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"提供, 测试, 装置, 方法, 生成, 处理, 设备, 系统, 工具, 配置",测试
CN118672873A,CN202410679008.9,"本申请实施例提供了一种自动检测任务最差执行时间的方法、装置及介质，用于提高在存在多个待检测任务时，最差执行时间的检测效率，该方法包括：获取待检测任务集；在待检测任务集中的每个待检测任务的起始检测点和结束检测点分别插入桩函数，得到每个待检测任务的起始桩函数和结束桩函数；根据起始桩函数获取每次执行待检测任务的起始执行时间数据；根据结束桩函数获取每次执行待检测任务的结束执行时间数据；根据每次执行待检测任务的起始执行时间数据，以及每次执行待检测任务的结束执行时间数据，确定每个待检测任务的最大执行时间数据；对每个待检测任务的最大执行时间数据进行数据分析，得到每个待检测任务的最差执行时间数据。
",G06F11/34,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/34,"提供, 包括, 装置, 方法, 检测",检测
CN118642966A,CN202410833624.5,"本申请提供一种冒烟用例生成方法及其装置，方法包括：获取待测试功能的逻辑控制需求文档，将逻辑控制需求文档转化为预设格式的数字化信息，预设格式基于预先构建的用例生成脚本中的配置参数确定，根据数字化信息，填入配置参数的参数值，根据配置参数的参数值，生成冒烟用例。这样，可以提高冒烟用例的编制速率，确保研发人员和测试人员各自工作的无缝衔接，从而保障项目进度，同时降低了因手动编制冒烟用例导致易出现遗漏的风险，从而保障了冒烟测试结果的精度。
",G06F11/36,天津经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"提供, 包括, 测试, 装置, 方法, 生成, 控制, 配置","测试, 控制"
CN118467359A,CN202410594668.7,"本申请公开了一种车载应用层软件测试方法、装置、设备、介质及产品，涉及车辆技术领域。车载应用层软件测试方法包括：加载待测试车载应用层软件对应的仿真模型；获取用于对待测试车载应用层软件进行测试的测试用例；利用测试用例对仿真模型进行测试，得到待测试车载应用层软件对应的测试结果。根据本申请公开的方案，能够提高车载应用层软件的测试效率。
",G06F11/36,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"包括, 模型, 装置, 测试, 仿真, 方法, 设备","车辆, 测试"
CN118444191A,CN202410520582.X,"本申请公开了一种电池异常检测方法及装置，其中方法包括：对电池进行数据采集，得到第一时间段对应的待测数据，第一时间段包括多个第一时间点；将待测数据输入至预获取的电池异常检测模型，得到每个第一时间点对应的第一异常得分，电池异常检测模型通过训练集中的第一样本数据训练得到，第一样本数据通过对电池进行数据采集得到；根据每个第一时间点对应的第一异常得分与预设阈值之间的大小，确定待测数据为正常数据或者异常数据。上述中，通过电池异常检测模型得到每个第一时间点对应的第一异常得分，并根据每个第一时间点对应的第一异常得分与预设阈值之间的大小，从而确定待测数据为正常数据或者异常数据，可以提高对待测数据判定的准确性。
",G01R31/392,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/392,"包括, 模型, 装置, 方法, 检测",检测
CN118445118A,CN202410513275.9,"本申请提供了一种车载终端的重启控制方法及相关设备，该方法包括：基于车载终端所属车辆的历史使用行为数据和预设的自适应计算模型，确定车载终端的预重启时段；在预重启时段中确定车载终端的目标重启时间点；在当前时间与目标重启时间点一致时，判断车载终端是否满足预设重启条件，当判断出车载终端满足预设重启条件，则对车载终端进行重启，本申请能够根据车载终端所属车辆的历史使用行为数据和预设的自适应计算模型，选择一个合适的重启时间点对车载终端进行重启，既不影响用户使用，又能够对软件状态进行一个复位，使软件保持一个健康良好的运行环境。
",G06F11/14,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/14,"提供, 包括, 模型, 方法, 设备, 控制, 计算","车辆, 控制"
CN118393395A,CN202410458786.5,"本申请提供了一种检测方法、装置及设备，通过控制单元，每隔预设周期，获取驱动单元的输出端口输出的脉冲宽度调制PWM信号的电平状态，PWM信号用于控制负载的运行；在电平状态指示PWM信号为电平上升沿状态的情况下，通过控制单元，基于预设通信接口从驱动单元中获取负载的运行电流；基于运行电流的数值大小，确定负载的运行状态，运行状态包括开路故障状态、短路故障状态以及正常状态。本申请实施例能够提高PWM检测的准确性。
",G01R31/50,天津经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/50,"提供, 包括, 装置, 方法, 设备, 控制, 检测","检测, 通信, 控制"
CN118331773A,CN202410424228.7,"本申请提供的一种日志系统的安全管理方法、装置、设备及存储介质。在一种日志系统的安全管理方法中，获取目标日志系统的日志信息，所述目标日志系统为多个不同类型的日志系统中的至少一个；基于日志信息以及预先设置的多个不同类型的日志系统的安全管理配置信息，确定目标日志系统是否出现使用异常；若确定目标日志系统出现使用异常，执行安全管理操作，以实现对目标日志系统的安全管理。通过上述方法，可以通过计算机程序自动监控不同类型的日志系统是否出现使用异常，在出现使用异常时自动执行安全管理操作，实现不同类型的日志系统的统一安全管理，避免因为日志系统的使用不当而丢失日志文件。
",G06F11/07,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/07,"提供, 实现, 装置, 方法, 设置, 设备, 系统, 计算, 配置",通用
CN118260193A,CN202410345951.6,"本申请提供了一种智能驾驶功能的用例的生成方法，获取目标智能驾驶功能；从目标子功能数据库中，提取目标智能驾驶功能的子功能；获取目标驾驶功能对应的设计运行条件；基于功能和设计运行条件，确定目标智能驾驶功能的子功能的应用场景；根据子功能的应用场景和预设行驶数据库，确定目标智能驾驶功能的主体；根据目标智能驾驶功能，确定目标智能驾驶功能的状态机；获取目标智能驾驶功能的主体对应的功能事件；利用状态机和功能事件生成目标功能事件图谱；基于预设的用例模板和目标功能事件图谱生成目标用例。如此从状态机、主体及事件三个维度进行结合并生成用例，使得生成的用例可以符合车辆真实驾驶场景所需要具备的真实性、覆盖性及高效性。
",G06F11/36,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"生成, 提供, 方法","车辆, 驾驶"
CN118245302A,CN202410404486.9,"本申请公开了一种HUD设备链路数据测试方法、装置、设备及介质，在能够拍摄到HUD设备投影出的开机测试图像的情况下，控制HUD设备对目标测试项目对应的目标图像进行投影，控制相机对HUD设备对应的投影区域进行拍摄，得到目标图像对应的投影图像，基于目标图像和投影图像之间的差异，确定目标测试项目对应的测试结果。根据本申请实施例，通过自动控制HUD设备进行图像投影和控制相机对投影图像拍摄，可以实现对HUD设备链路数据的自动测试，无需人眼观察，测试效率高，测试结果准确性高。此外，在检测到开机测试图像中的编码标记时再进行测试，可以避免由于HUD设备未开启导致的测试结果不准确的问题。
",G06F11/22,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/22,"实现, 测试, 装置, 方法, 设备, 控制, 检测","检测, 测试, 控制"
CN118193337A,CN202410346960.7,"本申请提供了一种数据转存方法及装置，通过在系统触发重启的情况下，获取重启的重启种类；在重启种类为预设种类的情况下，获取存储设备的目标存储地址，存储设备为非易失性存储设备；从目标存储地址中获取存储设备中存储的运行日志数据，运行日志数据为系统在触发重启前预设时间段内产生的所有运行日志，在系统触发重启前，系统产生的运行日志存储在系统的内存中；将运行日志数据从存储设备中转存至系统的内存中。本申请实施例能够获取系统在重启前的运行日志数据，提高数据的可维护性。
",G06F11/34,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/34,"提供, 装置, 方法, 设备, 系统",通用
CN118093361A,CN202311674947.6,"本发明提供一种软件项目测试方法及装置，包括：为测试工具配置软件包仓库地址；获取软件项目的项目配置文件，基于项目配置文件更新软件项目在测试时应用的用例包，将用例包安装在与软件包仓库地址对应的软件包仓库；对项目配置文件进行处理，获取合并配置信息；从合并配置信息中获取各个用例的依赖关系信息；对合并配置信息中存在变更需求的配置字段的内容进行更新，得到处理配置信息；基于软件包仓库中的用例包、处理配置信息和依赖关系信息，执行各个用例，对软件项目进行测试。对项目配置文件解析即可得到用例之间的依赖关系，无需将用例之间的关系以代码的方式添加在用例中，缩短测试软件项目所需的时间，提高测试效率。
",G06F11/36,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"提供, 包括, 测试, 装置, 方法, 处理, 工具, 配置",测试
CN118068109A,CN202410111045.X,"本申请公开了一种车辆电子电气测试方法、装置、设备、存储介质及程序，通过总线监控软件获取总线采集设备发送的第一数据，第一数据包括从车辆总线中采集的第一报文和总线采集设备为第一报文添加的时间戳，通过总线监控软件录制包含第一数据的测试日志，在测试日志录制完成的情况下，通过测试执行软件对测试日志进行分析得到测试报告。根据本申请实施例，通过总线监控软件录取测试日志后，利用测试执行软件离线分析测试日志来获取数据和时间戳以生成测试报告，能够将最后的测试报告与测试日志之间的数据和时间进行完全同步，便于测试报告和测试日志的对应以及后续的问题排查。
",G01R31/00,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/00,"包括, 测试, 装置, 方法, 生成, 设备","车辆, 测试"
CN117851255A,CN202410033086.1,"本申请公开了一种自动化测试HUD的集成方法、系统及软件。上述方法应用于自动测试软件，由于将功能测试与光学测试进行集成，可以利用基本功能测试用例使HUD进行高度调节、亮度调节和倾斜度调节中的一项操作，调用HUD的投影图像之后，利用图像识别算法获得投影图像的属性结果；若属性结果与预期结果一致，则说明HUD的基本功能测试通过，并利用设备功能测试用例对HUD设备功能进行测试，若设备数据与预期数据一致，则说明HUD的设备功能测试通过。在此过程中，由于系统集成了各个控制器用于控制HUD，因此可以通过测试用例调用控制器，以使HUD自动化完成HUD功能测试，减少了测试人员的工作量，提高测试效率。
",G06F11/36,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"测试, 算法, 方法, 设备, 控制, 系统","测试, 控制"
CN117741439A,CN202311616260.7,"本申请公开了一种修正参数的确定方法、装置及设备。该方法包括：获取电池的模型参数，以及获取卡尔曼滤波算法的多个修正参数组；获取电池在第一时刻的第一实际充电状态；基于模型参数和多个修正参数组通过卡尔曼滤波算法预测电池在第一时刻的多个第一预测充电状态，其中，每个第一预测充电状态对应一个修正参数组；基于第二预测充电状态在多个修正参数组中确定卡尔曼滤波模型的第一修正参数组，其中，第二预测充电状态为和第一实际充电状态匹配的第一预测充电状态，卡尔曼滤波模型用于预测电池在动态过程中的充电状态。根据本申请实施例，能够提升模型中修正参数的准确性。
",G01R31/367,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/367,"包括, 模型, 装置, 算法, 方法, 设备",通用
CN117632614A,CN202311687766.7,"本申请公开了一种CPU核的检测方法及装置。该方法应用于CPU，CPU包括N个按照第一顺序排列的CPU核，N为大于1的整数，N个CPU核包括第一CPU核和第二CPU核，第一CPU核为N个CPU核中的第i个，第二CPU核为N个CPU核中的第i+1个，i＝1、2…N，第二CPU核被设置基于第一周期周期性运行中断，该方法包括：通过第一CPU核按照第二周期周期性获取第二CPU核运行中断的第一次数，第二周期不小于第一周期，比较第一次数和第二次数，得到比较结果，第二次数是第一CPU核前一次获取的第二CPU核运行中断的次数，根据比较结果确定第二CPU核是否正常。这样可以实现对部分CPU核的检测。
",G06F11/22,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/22,"实现, 包括, 装置, 方法, 设置, 检测",检测
CN117584684A,CN202311598943.4,"本申请公开了一种悬架控制处理方法及系统，获取车辆底盘控制器局域网总线信号和传感器信号，对车辆底盘控制器局域网总线信号和传感器信号进行故障检测，若车辆底盘控制器局域网总线信号和传感器信号存在故障，对存在故障的信号进行故障处理，根据故障处理后的信号进行状态估算，得到当前车辆运动状态，根据预设评定方式和当前车辆运动状态进行评定，得到各个阻尼系数，其中，预设评定方式为通过预设车辆评定参数进行评定得到对应的阻尼系数的评定方式，对各个阻尼系数进行控制输出，得到控制阻尼，并通过控制阻尼进行悬架控制。
",B60G17/0185,北京经纬恒润科技股份有限公司,故障注入板卡技术,1.0,关键词匹配: 故障检测,"方法, 处理, 控制, 系统, 检测","车辆, 检测, 传感器, 控制"
CN117493193A,CN202311489851.2,"本申请公开了一种代码的测试方法、装置、设备及计算机存储介质。该方法包括：在检测到目标代码仓库接收到代码文件的情况下，获取第一参数；在提交人账号和密码通过目标代码仓库的验证的情况下，控制测试服务器获取第一参数对应的目标代码仓库的第一代码；控制测试服务器按照第一预设测试规则测试第一代码，得到第一测试结果，并将第一代码编译成可执行的第二代码；控制测试服务器向目标汽车控制器发送第二代码；控制目标汽车控制器按照第二预设控制规则测试第二代码的功能，得到第二测试结果。将汽车软件研发、代码测试以及功能测试串联到一起，解决了各环节割裂的问题。且全程不需要再进行人工干预，提高了测试效率。
",G06F11/36,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"包括, 测试, 装置, 方法, 设备, 控制, 检测, 计算","测试, 汽车, 验证, 控制, 检测"
CN117310486A,CN202311214980.0,"本申请公开了一种电机的故障检测方法、装置，通过预设时间段内的多个时刻，及多个时刻各自对应的信号值，生成信号对应的信号波形图；确定信号波形图中多个波峰值和多个波谷值；确定目标波谷值与第一波峰值之间的第一幅值以及目标波谷值与第二波峰值之间的第二幅值，第一波峰值和第二波峰值分别是与目标波谷值对应时刻相邻时刻对应的波峰值，目标波谷值为多个波谷值中的任一波谷值；利用第一幅值和/或第二幅值与预设幅值进行比较，得到比较结果；在比较结果满足预设条件的情况下，将比较结果确定为目标比较结果；在目标比较结果的数量超过故障次数阈值的情况下，确定电机故障。本申请实施例实现了对电机的故障进行检测。
",G01R31/34,北京经纬恒润科技股份有限公司,故障注入板卡技术,3.0,关键词匹配: 故障检测; IPC分类号匹配: G01R31/34,"实现, 装置, 生成, 方法, 检测",检测
CN117289133A,CN202311108377.4,"本申请公开了一种电池荷电状态SOC估算方法及其装置、系统、介质，该方法包括：获取电池在第一时刻的第一电压值；在第一电压值处于电压平台区的情况下，基于安时积分法确定第一时刻的第一SOC值；在第一电压值处于电压非平台区的情况下，基于滞回模型确定第一时刻的第一SOC值。根据本申请实施例，能够提升电池SOC的估算准确度。
",G01R31/367,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/367,"平台, 包括, 模型, 装置, 方法, 系统",通用
CN117290232A,CN202311279023.6,"本发公开了一种OTA整车升级的自动化测试方法及装置，方法包括：基于测试任务更新配置文件，获得车型信息，所述车型信息为系统中原有信息或为通过调用生成方法创建得到的信息；基于所述车型信息、车辆信息和活动配置信息确定车辆和控制器的关联关系，所述关联关系包括绑定关系和非绑定关系；基于所述关联关系和所述软硬件配置信息中的控制器软件版本数据，按照配置的自定义升级数据进行升级测试。上述方案能够将OTA疲劳测试的一系列复杂过程自动化，实现在服务器端创建车辆、绑定控制器及对应版本、创建升级活动、执行测试等工作，从而降低测试过程中人力和时间投入成本。
",G06F11/36,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"实现, 包括, 测试, 装置, 方法, 生成, 控制, 系统, 配置","车辆, 测试, 控制"
CN117289149A,CN202311145884.5,"本申请公开了一种电池自加热测试电路及测试装置。电路包括：第一电源，第一变换模块，用于与待测电池连接，并根据第一驱动信号调整待测电池的电压极性，第二变换模块，与第一电源连接，用于根据第二驱动信号调整第一电源的电压极性，储能模块，连接于第一变换模块与第二变换模块之间，储能模块用于根据待测电池的电压极性以及第一电源的电压极性，产生交流电流，加热检测模块，用于在不同信号周期、不同内移相比下，分别检测待测电池在不同电流频率、不同电流有效值下的自加热效率。根据本申请实施例，能够得到使得待测电池自加热性能较好的电流有效值和电流频率参数，并根据该参数驱动待测电池实现自加热升温，提升待测电池的自加热性能。
",G01R31/385,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/385,"实现, 包括, 测试, 装置, 检测, 电路","检测, 测试"
CN117195112A,CN202311108122.8,"本申请公开了一种故障诊断模型的训练方法和故障诊断方法、装置。该方法包括：获取第一样本集和第二样本集，利用第一样本集对第一初始故障诊断模型进行训练，得到训练后的第一故障诊断模型的目标模型参数，将目标模型参数导入第二初始故障诊断模型，得到第三初始故障诊断模型，利用第二样本集对第三初始故障诊断模型进行训练，得到训练后的目标故障诊断模型。这样，可以节约大量时间和人力成本。
",G06F18/2431,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,1.0,关键词匹配: 故障诊断,"方法, 包括, 模型, 装置",诊断
CN117169720A,CN202311159805.6,"本申请公开了一种电池异常检测模型的训练方法、电池异常检测方法和装置。电池异常检测模型的训练方法包括：获取训练样本集；根据每个所述电池特征信息和采集时间，对所述电池异常检测模型进行训练，直到满足预设训练结束条件，得到目标电池异常检测模型；其中，预先构建的电池异常检测模型包括编码单元和解码单元，编码单元用于分析电池特征信息，确定电池在采集时间的电池特征信息对应的隐变量，解码单元用于分析隐变量，生成电池特征信息的重构特征，重构特征与电池特征信息之间的误差用于判断是否生成电池异常预警信息。根据本申请实施例，及时检测出电池存在异常，提供足够的安全冗余，提高电池的安全性和可靠性。
",G01R31/36,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/36,"提供, 包括, 模型, 装置, 方法, 生成, 检测",检测
CN117129891A,CN202311163882.9,"本申请实施例提供了一种电池的荷电状态估算方法、装置，方法包括：获取电池的各个电芯的电芯电压，分别判断最大电芯电压和最小电芯电压是否小于预设电压阈值，在目标电芯电压小于预设电压阈值的情况下，查找与最大电芯电压对应的第一参考荷电状态以及与最小电芯电压对应的第二参考荷电状态，分别判断第一参考荷电状态和第二参考荷电状态是否满足预设修正条件，在目标参考荷电状态满足预设修正条件的情况下，确定最大电芯电压对应的高压初始荷电状态以及最小电芯电压对应的低压初始荷电状态，通过安时积分算法分别对高压初始荷电状态和低压初始荷电状态进行计算，得到电池的荷电状态。根据本申请实施例，能够准确估算电动汽车电池的荷电状态。
",G01R31/387,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/387,"提供, 包括, 装置, 算法, 方法, 计算",汽车
CN117076181A,CN202311153148.4,"本申请公开了一种数据对象生命周期管理方法和装置。数据对象生命周期管理方法包括：在向请求方发送目标数据对象的访问地址的情况下，获取用于存储目标数据对象的第一数据存储表；获取第一访问记录表；获取第一数据存储表中数据标识的数量，得到第一数量；在第一数据存储表包括的数据标识的第一数量大于第一预设阈值的情况下，基于第一指针遍历第一访问记录表中每个数据标识，并获取每个数据标识对应的计数标识；依次清除第一数据存储表中计数标识为预设数值的数据对象，以及清除第一访问记录表中计数标识为预设数值的数据对象对应的数据标识。根据本申请实施例，使数据对象在合理的时机进行释放，有效避免数据冗余和浪费内存资源。
",G06F11/07,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/07,"方法, 包括, 装置",通用
CN116909818A,CN202310813389.0,"本申请公开了一种安卓调试桥设备使用情况确定方法和装置，该方法包括：接收客户端发送的使用安卓调试桥ADB设备的使用请求；基于所述使用请求，校验所述ADB设备的分组信息；在确定所述分组信息为非公共分组的情况下，对所述第一对象进行权限校验；在确定第一对象具有使用所述ADB设备的权限的情况下，获取所述ADB设备的使用状态标识；根据所述使用状态标识，确定所述ADB设备的目标使用情况。以实现精确确定ADB设备的目标使用情况的效果。
",G06F11/22,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/22,"实现, 包括, 装置, 具有, 方法, 设备",通用
CN116879743A,CN202310883699.X,"本申请公开了一种电机电流采集方法及装置。方法包括：周期性地采集所述电机的电流值，每个采集周期用于采集所述电机的一个电流值；在采集到n个电流值的情况下，对所述n个电流值进行均值计算，得到所述电机的电流均值；其中，所述采集周期的长度大于所述电机的驱动周期的长度，n为大于1的整数，且所述采集周期的长度与n的乘积小于或等于电流采集最大延迟时间。本申请可以提高CPU利用率。
",G01R31/34,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/34,"方法, 包括, 计算, 装置",通用
CN116879786A,CN202310880071.4,"本申请公开了一种电池故障诊断方法及装置。该方法包括：分别获取目标电池包括的多个电池单体在目标时段内的累积总均衡电量增量，以及目标电池单体在目标时刻和多个历史时刻分别对应的第一偏离程度系数，根据多个累积总均衡电量增量，确定目标电池单体对应的第二偏离程度系数，并根据目标电池单体在目标时刻和多个历史时刻分别对应的第一偏离程度系数，确定目标电池单体对应的第三偏离程度系数，根据第二偏离程度系数和第三偏离程度系数对目标电池单体进行电池内短路故障和容量衰减故障的诊断，得到诊断结果。这样，可以对目标电池单体是否发生电池内短路故障和容量衰减故障进行联合诊断。
",G01R31/392,北京经纬恒润科技股份有限公司,故障注入板卡技术,3.0,关键词匹配: 故障诊断; IPC分类号匹配: G01R31/392,"方法, 包括, 装置",诊断
CN116879777A,CN202310883449.6,"本申请公开了一种电池荷电状态矫正方法和装置，该方法包括获取当前时刻电池组中各单体电芯的第一电压信息、电池组的第一温度信息和电池组的第一电流信息，在确定第一电压信息处于第一状态，且第一电压信息在可矫正范围内的情况下，基于第一电压信息、第一温度信息和第一电流信息，在预先构建的对应关系表中进行查询，得到与第一电压信息、第一温度信息和第一电流信息对应的每个单体电芯的第一荷电状态信息，基于每个单体电芯的第一荷电状态信息对各单体电芯的当前荷电状态信息进行矫正，得到各单体电芯的目标荷电状态信息。以提升电池的荷电状态的矫正精度。
",G01R31/388,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/388,"方法, 包括, 装置",通用
CN116820893A,CN202310896573.6,"本申请公开了一种时间保护方法及装置。该方法包括：监测操作系统中目标任务或目标中断的目标时长，目标时长包括执行时长、锁定时长和间隔时长中的至少一项，锁定时长为锁定目标任务所占用的目标资源的时长或锁定目标中断的时长，在目标时长满足预设条件的情况下，调用目标任务或目标中断的目标时长对应的时间保护函数，执行时间保护函数中的预设返回值所指示的操作，以对操作系统进行时间保护。这样，可以有效地对操作系统进行时间保护。
",G06F11/30,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/30,"系统, 方法, 包括, 装置",通用
CN116820865A,CN202310822530.3,"本申请公开了一种板卡测试方法、装置、系统及设备，该方法包括：接收用户输入的板卡测试指令，板卡测试指令包括待测板卡的板卡标识；响应于板卡测试指令，基于板卡标识获取待测板卡的板卡信息，板卡信息包括板卡状态信息；在板卡状态信息指示空闲状态的情况下，与网关建立socket连接，其中，网关与待测板卡连接；向网关发送待测板卡的测试请求，以使网关基于测试请求执行测试脚本，对待测板卡进行板卡测试，得到测试结果。根据本申请实施例，能够降低板卡测试场景下的板卡损坏风险。
",G06F11/263,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/263,"包括, 测试, 装置, 方法, 设备, 系统",测试
CN116767246A,CN202310716023.1,"本申请公开了一种车辆运动状态观测故障检测方法、装置及电子设备，其中方法包括：获取车辆的观测量，所述观测量包括所述车辆的横向车速、纵向车速和横摆角速度；根据所述观测量，获取所述车辆的车轮滑动程度；根据所述车轮横向滑动程度与预设阈值的关系，输出目标信号，所述目标信号用于确定所述观测量是否存在故障。通过上述步骤，即可对纵向车速、横向车速、横摆角速度进行故障检测，提高观测量的可靠性。
",B60W50/02,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,1.0,关键词匹配: 故障检测,"包括, 装置, 方法, 设备, 检测","车辆, 检测"
CN116775485A,CN202310752706.2,"本申请公开了一种软件自动化测试方法、装置、设备、存储介质及程序产品。方法包括：接收客户端发送的对目标软件的测试执行请求，测试执行请求包括目标测试套件的特征信息、第一测试用例和第一测试参数；在第一测试用例和第一测试参数均校验通过的情况下，利用目标测试套件对应的目标基础镜像创建目标基础容器；在目标基础容器运行目标测试套件对应的目标执行器，由目标执行器基于第一测试参数执行第一测试用例，得到目标软件的与测试执行请求对应的自动化测试执行信息；向客户端发送测试执行响应，测试执行响应包括测试执行请求对应的测试执行标识，测试执行标识用于管理自动化测试执行信息。本申请可以提高自动化测试效率。
",G06F11/36,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"包括, 测试, 装置, 方法, 设备",测试
CN116643976A,CN202310544587.1,"本申请公开了一种高级驾驶辅助系统的校验方法、装置、设备、介质及产品。方法包括：获取所述高级驾驶辅助系统的功能需求，利用所述功能需求，确定所述高级驾驶辅助系统中待校验的至少一个执行器，以及所述至少一个执行器中各执行器的性能目标要求，利用各执行器的性能目标要求，确定各执行器的测试用例，其中，对于各执行器，所述执行器的测试用例用于采用斜坡阶跃方式和正弦曲线方式中的至少一项校验所述执行器的性能，将各执行器的测试用例转换为各执行器的自动化测试脚本，通过总线工具运行各执行器的自动化测试脚本，得到各执行器的性能校验结果。本申请可以提升ADAS的开发效率和性能。
",G06F11/36,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"包括, 测试, 装置, 方法, 设备, 系统, 工具","驾驶, 测试"
CN116627722A,CN202310691720.6,"本申请提供了一种多节点热备份方法及装置，以预设频率向多个计算节点发送不同第一数据包；接收第一计算节点发送的第二数据包，第二数据包包括第一计算节点当前接收到第一数据包的第一时间戳和发送第二数据包的第二时间戳；对第一时间戳和第二时间戳进行匹配计算，得到第一计算节点的多个处理时长；基于多个处理时长，生成第一计算节点对应的第一概率分布图；在接收到第一计算节点发送的第三数据包的情况下，根据第一概率分布图，确定第一计算节点是否存在异常；在第一计算节点存在异常的情况下，将第一计算节点的备份节点对第一计算节点进行替换。本申请实施例不需要占用过多的计算资源，能够提高资源利用率。
",G06F11/14,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/14,"提供, 包括, 装置, 方法, 生成, 处理, 计算",通用
CN116577545A,CN202310526219.4,"本申请公开了一种故障检测方法及装置。该方法包括：在待测负载处于工作状态的情况下，获取待测负载在目标时刻的电参数，目标时刻为目标时段的中间时刻，目标时段为驱动待测负载的PWM信号的高电平时段，在电参数超出预设范围的情况下，确定待测负载发生故障。这样，可以降低对负载的要求，还可以对负载进行实时的故障检测。
",G01R19/165,天津经纬恒润科技有限公司,故障注入板卡技术,1.0,关键词匹配: 故障检测,"检测, 方法, 包括, 装置",检测
CN116302933A,CN202211711403.8,"本申请公开了一种测试脚本生成方法、装置、电子设备和可读存储介质。测试脚本生成方法，包括：利用待测试的车辆功能信息和预设信号矩阵表，生成测试用例，其中，车辆功能信息包括至少一个车辆功能标识，测试用例包括每个车辆功能标识对应的输入信号和/或输出信号，利用待测试的车辆功能信息和预设仿真模型中的仿真信息，其中，车辆功能信息包括至少一个车辆功能标识，仿真信息包括至少一个仿真参数标识，建立仿真信息与预设信号矩阵表的目标映射关系信息。根据测试用例和目标映射关系信息，生成第一预设文本格式的第一目标测试脚本。根据本申请实施例，能够有效提高测试脚本的生成效率。
",G06F11/36,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"包括, 模型, 装置, 测试, 仿真, 方法, 生成, 设备","车辆, 测试"
CN116302941A,CN202211724195.5,"本申请公开了一种中间件的性能测试方法和装置。该方法包括：获取运行环境信息，根据运行环境信息生成用于运行测试脚本的运行环境，获取与目标自动驾驶场景对应的场景信息，根据场景信息生成测试脚本，测试脚本用于模拟目标自动驾驶场景，目标自动驾驶场景包括自动驾驶应用程序与中间件的交互，执行测试脚本，采集测试脚本执行过程中生成预设事件的时间戳，根据时间戳生成中间件的性能质量信息。根据本申请实施例，本申请能够将中间件的性能测试与自动驾驶应用程序的性能测试解耦，能够生成单独评估中间件性能的性能质量信息。
",G06F11/36,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"包括, 测试, 装置, 生成, 方法, 模拟","驾驶, 测试"
CN116185762A,CN202211718511.8,"本申请公开了一种总线报文显示方法、装置、设备、存储介质及程序产品。方法包括：获取第一目标总线报文，将第一目标总线报文与目标标识对应的目标同标识参考报文中各数据字节的数值分别进行对比，将第一目标总线报文的N‑Q个数据字节的淡化级别调整为最低淡化级别，根据第一目标总线报文的时间戳与目标标识对应的目标时间参考报文的时间戳的差值与预设时长进行对比的第二对比结果，以及第i‑1个目标总线报文中Q个数据字节的淡化级别，确定第一目标总线报文的Q个数据字节的淡化级别，按照第一目标总线报文中各数据字节的淡化级别，在报文监控视图上渲染显示第一目标总线报文。本申请可以提高总线报文的显示灵活性。
",G06F11/30,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/30,"方法, 包括, 装置, 设备",通用
CN115952057A,CN202211686312.3,"本发明实施例公开了一种故障检测方法、装置、设备及存储介质，在目标应用程序中设置若干检查点，由目标应用程序在运行过程中上报检查点，根据目标应用程序上报的各个检查点对应的上报关系和/或上报次数确定目标应用程序的状态，当目标应用程序的状态为目标状态时，确定目标应用程序故障，实现了对应用程序的执行过程是否出现错误的实时监测，保证能够即时发现故障。
",G06F11/30,北京经纬恒润科技股份有限公司,故障注入板卡技术,3.0,关键词匹配: 故障检测; IPC分类号匹配: G06F11/30,"实现, 装置, 方法, 设置, 设备, 检测",检测
CN115773365A,CN202211640950.1,"一种挡位故障解决方法及装置，涉及汽车自动驾驶技术领域，该方法包括：在车辆启动自动驾驶功能后，ECU获取车辆的当前挡位状态，当前挡位状态包括挡位正常状态和挡位故障状态；ECU判断当前挡位状态是否处于挡位故障状态；若否，则ECU将请求挡位值发送至TCU；若是，则ECU将N挡值发送至TCU后，再次执行ECU判断当前挡位状态是否处于挡位故障状态的步骤。在不改变原始TCU控制器换挡策略和无可避免存在机械操作失误概率的前提下，基于挡位故障诊断方法自动判断车辆是否处于挡位故障状态，并通过切换挡位的方式解决了因挡位故障造成车辆表现异常问题，从而降低了车辆退出自动驾驶和人工接管的频率，提升自动驾驶车辆行驶效率。
",F16H61/12,天津经纬恒润科技有限公司,故障注入板卡技术,1.0,关键词匹配: 故障诊断,"方法, 包括, 装置, 控制","驾驶, 汽车, 诊断, 控制, 车辆"
CN115757007A,CN202211190440.9,"本申请公开了一种输入设备事件管理方法及装置，涉及通信技术领域。其方法包括：通过输入设备事件管理子系统对车载终端中的输入设备节点进行监听；在输入设备事件管理子系统监听到待处理输入事件的情况下，获取待处理输入事件的待处理输入事件信息；在与输入设备事件管理子系统中关联的至少一个事件处理模块中，查找与待处理输入事件信息匹配的目标事件处理模块；控制输入设备事件管理子系统将待处理输入事件信息传输至目标事件处理模块，以使目标事件处理模块对待处理输入事件进行处理。根据本申请实施例，能够对输入设备事件进行了统一管理，降低了车载嵌入式系统对硬件平台的依赖性，从而提高了车载嵌入式系统的可移植性。
",G06F11/30,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/30,"平台, 包括, 装置, 方法, 处理, 设备, 控制, 系统","通信, 控制"
CN115470124A,CN202211069685.6,"本申请实施例公开了一种测试场景的检测方法、装置、电子设备及存储介质。应用于自动化测试领域。包括：根据测试需求获取测试场景对应的多媒体数据。多媒体数据为测试场景中获取到的图像数据。多媒体数据用于对测试场景进行仿真。播放多媒体数据，得到测试场景对应的测试仿真过程，并在测试仿真过程中实时获取待测试目标对应的多个测试数据。根据测试数据验收标准对全部测试数据进行分析评价，并根据分析评价结果确定测试场景的合格结果。本申请实施例可以实现对测试场景的自动化检测，高效、便捷的完成对测试数据的验收。
",G06F11/36,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"实现, 包括, 测试, 装置, 仿真, 方法, 设备, 检测","检测, 测试"
CN114721963A,CN202210439708.1,"本发明提供了一种生成交通场景的方法及装置，获取由路采车辆采集得到的指定时间段内的路采数据。对路采数据进行坐标系的变换，利用进行坐标系变换后的路采数据生成OpenSCENARIO场景的实体部分、初始化部分和故事板的故事部分，得到最终的OpenSCENARIO场景。最终的OpenSCENARIO场景中包括了路采车辆和指定交通目标等交通参与者，且最终的OpenSCENARIO场景还以故事板描述了各个交通参与者的动作，能够反映真实的交通情况，也不需要技术人员手动搭建交通场景，提高工作效率和提高所搭建的交通场景的真实性。
",G06F11/36,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"提供, 包括, 装置, 生成, 方法",车辆
CN114416547A,CN202210016728.8,"本发明公开一种基于测试用例的测试方法，方法包括：获取预先设置的与业务相关的测试基类；根据预设用例字段和测试基类中每个基类函数的函数信息生成每个基类函数对应的测试用例模板；获取基于测试用例模板编辑得到的用例字段值，生成包括用例字段和用例字段值的测试用例，其中，每个测试用例对应一个测试用例模板；在目标执行次数满足预设次数要求的情况下，按照测试用例所对应基类函数在测试基类中的顺序，将所有测试用例转换为可执行的测试用例代码；生成用于调用预设测试用例执行逻辑的调用代码，并将调用代码添加到测试用例代码中，获得目标测试用例代码，以基于目标测试用例代码进行用例测试。
",G06F11/36,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"包括, 测试, 生成, 方法, 设置",测试
CN114356634A,CN202111667502.6,"本申请公开一种日志处理方法及装置。将诊断仪的OBD接口与车辆的OBD接口连接，车辆中的TBOX接收诊断仪发送的日志导出消息，该日志导出消息至少包含起始序号和结束序号，TBOX获得目标日志，目标日志为至少由起始序号和结束序号所指示的日志，向诊断仪发送目标日志。基于本申请公开的技术方案，在无法联网或者串口及USB接口被禁用的情况下，能够获取到TBOX的日志。
",G06F11/07,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/07,"方法, 处理, 装置","车辆, 诊断"
CN114296044A,CN202111660359.8,"本申请提供了一种激光雷达故障诊断方法和装置，其中，方法包括：在待诊断激光雷达包含的各子系统电压处于设定电压阈值范围的情况下，根据预设通信协议、预设保护措施和数据完整性保护算法，确定待诊断激光雷达对应的通信通道是否故障；在通信通道未故障，且满足特征匹配触发条件的情况下，根据接收的目标物体的点云信息，进行功能性诊断，以确定待诊断激光雷达的故障状态。由此可见，本申请能够实现对待诊断激光雷达进行故障诊断，降低了人工检查的难度和工作量，提高了待诊断激光雷达的故障诊断效率。
",G01S7/40,北京经纬恒润科技股份有限公司,故障注入板卡技术,1.0,关键词匹配: 故障诊断,"提供, 实现, 包括, 装置, 算法, 方法, 系统","雷达, 通信, 诊断, 激光"
CN114252771A,CN202111514699.X,"本发明实施例提供了一种电池参数在线辨识方法及系统，方法包括：建立电池的等效电路模型，离线获取等效电路模型的相关参数；将需要在线辨识更新的模型参数分为第一参数组和第二参数组；在线判断当前工况满足第一参数组中参数辨识过程的计算更新条件，还是满足第二参数组中参数辨识过程的计算更新条件；若满足第一参数组中参数辨识过程的计算更新条件，则对第一参数组中参数进行在线参数辨识并更新；若满足第二参数组中参数辨识过程的计算更新条件，则对第二参数组中参数进行在线参数辨识；在线判断当前工况是否满足第二参数组中参数的更新条件，若满足，则更新第二参数组中参数。对待估参数进行解耦，分别辨识不同参数。
",G01R31/36,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/36,"提供, 包括, 模型, 方法, 系统, 计算, 电路",通用
CN114253779A,CN202111454384.0,"本发明公开一种CAN总线数据的异常检测方法、装置及设备，方法包括：获取待检测的N个CAN总线数据，其中，所述N个CAN总线数据中不同CAN总线数据的身份标识号ID不同；将所述N个CAN总线数据组合成一个待检测的CAN总线数据序列；将所述待检测的CAN总线数据序列输入局部离群因子模型中，获得所述待检测的CAN总线数据序列的局部异常因子，其中，所述局部离群因子模型是根据多个CAN总线数据序列训练得到的、用于计算局部异常因子的模型，所述多个CAN总线数据序列中每个CAN总线数据序列的CAN总线数据的数据个数和ID种类均为N；若所述局部异常因子大于预设异常数据检测阈值，则确定所述N个CAN总线数据中存在异常数据。
",G06F11/22,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/22,"包括, 模型, 装置, 方法, 设备, 检测, 计算",检测
CN114137347A,CN202111424489.1,"本发明提供一种散热系统故障诊断方法、校验方法及装置，应用于汽车技术领域，散热控制器在获取风扇的开启指令后，向各风扇中的至少一个输出PWM控制信号，并监测各风扇的工作电流以及各风扇的中断信号，如果各风扇的工作电流都小于预设电流阈值且各风扇的中断信号都异常，则判定散热系统存在风扇开路故障。本方法的故障诊断过程，是基于各风扇的工作电流和控制信号两方面因素实现的，与现有技术单纯依靠工作电流实现的诊断方法相比，参考因素更多，判定故障的条件更为严格，因此有助于提高诊断结果准确率，满足实际应用需求。
",G01R31/00,北京经纬恒润科技股份有限公司,故障注入板卡技术,3.0,关键词匹配: 故障诊断; IPC分类号匹配: G01R31/00,"提供, 实现, 装置, 方法, 控制, 系统","汽车, 诊断, 控制"
CN113848494A,CN202111100784.1,"本发明公开了一种汽车动力电池温度的在线监测方法及车载T‑BOX，将获取的当前时刻的每个动力电池单体温度和车况数据同时输入至动力电池温度预测模型得到当前时刻的下一时刻的电池温度预测值，该电池温度预测值实际为该动力电池单体在电池单体温度正常情况下的电池温度理论值，通过计算下一时刻的电池温度理论值与实际电池温度之间的温度偏离量，即可对动力电池单位温度的变化趋势进行预测，根据每个动力电池单体的温度偏移量与对应的故障权重得到故障量级，与预设报警等级阈值的大小关系对每个动力电池单体是否发生升温故障进行监测。本发明可在监测到动力电池单体刚开始发生升温故障时及时采取保护措施，有效避免动力电池进入热失控阶段。
",G01R31/392,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/392,"方法, 模型, 计算",汽车
CN113297011A,CN202110643780.1,"本发明公开了一种主从MCU升级失败的自恢复方法及系统，当主MCU确定升级结果记录文件中记录的各个被升级MCU中存在升级状态标志置位的目标MCU，且所有被升级MCU的升级结果标志存在未置位的时，主MCU对所有已执行升级操作的各个目标MCU执行自恢复操作，并针对自恢复操作成功的目标MCU的升级结果标志置位。本发明通过在被升级MCU执行升级操作时设置升级状态标志，以及在被升级MCU执行升级操作结束和/或被升级MCU升级失败后需要重启执行恢复机制时设置升级结果标志，使得在出现多MCU升级失败的情况时，对所有已执行升级操作MCU均执行自恢复操作，将已成功升级MCU和未成功升级MCU均恢复至升级前状态。
",G06F11/14,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/14,"系统, 方法, 设置",通用
CN113297085A,CN202110601895.4,"本发明公开了一种基于SOC平台的算法验证方法及装置，包括：将算法模型划分为ARM模型和FPGA模型，添加相对应的驱动模块，自动将添加了第一驱动模块的ARM模型和添加了第二驱动模块的FPGA模型生成相应类型的代码，得到适用于ARM芯片的第一代码和FPGA芯片的第二代码；通过操作系统的控制命令调取相应的编译工具分别对第一代码和第二代码进行编译，并生成ARM镜像文件和FPGA镜像文件；基于下载指令，将ARM镜像文件和FPGA镜像文件发送到SOC平台上。由此，实现了自动化的将算法模型编写成不同类型代码的目的，并且，还实现了自动将算法模型部署到SOC平台上的目的，从而使得算法可以在SOC平台上进行验证。
",G06F11/36,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"平台, 实现, 包括, 模型, 装置, 算法, 方法, 生成, 控制, 系统, 工具","验证, 控制"
CN113051112A,CN202110281275.7,"本发明公开了一种ECU故障信息的获取方法及系统，网关根据ECU上报的故障ECU信息确定对应的故障ECU，并向故障ECU发送故障通知报文，获取故障ECU发送的在故障期间缓存的ECU故障运行日志，并将ECU故障运行日志和自身在与故障期间相同时间段缓存的整车CAN报文数据上传至远程服务器。本发明中所有ECU实时缓存ECU运行日志，网关实时缓存整车CAN报文数据，因此，当某个ECU发生偶发故障时，可直接获取故障ECU在故障期间缓存的ECU故障运行日志以及网关在相同时间段缓存的整车CAN报文数据，准确定位故障ECU故障产生原因，且无需搭建CAN报文数据采集环境，从而解决了现有问题。
",G06F11/22,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/22,"系统, 方法",通用
CN113051167A,CN202110346109.0,"本发明实施例公开一种模型与模型代码等效性的测试方法及装置，该方法包括：电子设备根据被测模型及激励输入数据和数据收发结构，构建模型测试框架；获得模型测试工程；利用模型测试框架、目标嵌入式平台对应的数据收发模块及模型测试工程，确定代码测试框架及其代码测试工程；利用模型测试工程及模型测试框架，获取仿真结果数据；在目标嵌入式平台运行的情况下，利用代码测试工程及等效模型，将激励输入数据发送至目标嵌入式平台，目标嵌入式平台运行部署模型对应的程序代码，确定激励输入数据对应的代码结果数据，反馈至等效模型；电子设备利用仿真结果数据及程序代码的代码结果数据，验证模型与其代码之间的等效性，以实现提高测试效率。
",G06F11/36,北京经纬恒润科技股份有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"平台, 实现, 包括, 模型, 装置, 测试, 仿真, 方法, 设备","测试, 验证"
CN113009378A,CN202110251227.3,"本发明提供了一种电池微短路检测方法及装置，方法包括：分别测量待测电池和正常电池的开路电压；将待测电池和正常电池并联后，测量待测电池与正常电池之间的电流；根据待测电池的开路电压、正常电池的开路电压、测量的电流以及预先标定的待测电池和正常电池的状态参数与SOC的对应关系，计算得到N个判断参数，N≥2；在至少两个判断参数符合预设的条件时，确定待测电池发生微短路，否则，确定待测电池未发生微短路。本发明通过至少两个判断参数的综合分析，来判断待测电池是否发生了微短路，可以在电池单体之间未进行均衡的情况下，检测是否发生微短路。
",G01R31/52,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/52,"提供, 包括, 装置, 方法, 检测, 计算",检测
CN112578286A,CN202011323313.2,"本发明提供了一种电池SOC估计方法及装置，方法包括基于电化学电池的状态方程和输出方程，采用预设滤波算法分析得到电化学电池SOC的估计值。采用的电化学电池的状态方程和输出方程，作为电化学降阶模型且不存在偏微分方程，具有较高精度的同时还能够在工程上的电池管理系统中进行应用。
",G01R31/382,经纬恒润(天津)研究开发有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/382,"提供, 包括, 模型, 装置, 算法, 具有, 方法, 系统",通用
CN111929074A,CN202010838458.X,"本发明提供了一种车辆机械旋转部件故障诊断方法及装置，方法包括利用共振解调技术对车辆机械旋转部件的振动信号进行处理，得到共振解调信号；根据预设的干扰信号频率，对共振解调信号中的干扰信号进行滤除；根据滤除干扰信号的共振解调信号的峰值因子与预设的冲击性阈值的关系，确定车辆机械旋转部件是否存在振动冲击，存在振动冲击则对滤除干扰信号的共振解调信号进行时频转换，得到频率信号；最后根据预设的各个部件的故障特征频率，对频率信号进行分析，确定故障部件。通过滤除干扰信号，提高了故障诊断的准确性；根据共振解调信号的峰值因子与预设的冲击性阈值的关系，确定车辆机械旋转部件是否存在振动冲击，减少了大量不必要的检测。
",G01M17/007,北京经纬恒润科技有限公司,故障注入板卡技术,1.0,关键词匹配: 故障诊断,"提供, 包括, 装置, 方法, 处理, 检测","车辆, 检测, 诊断"
CN111796967A,CN202010622963.0,"本发明实施例提供数据读写控制方法和装置。在本发明实施例中，引入了校验信息，将包括数据信息和校验信息的数据包存储于m个存储地址中互相备份。在读取时，会对相互关联互为备份的m个数据包均进行校验操作和有效标志位设置操作。对于每一数据包，若校验操作通过，将其有效标志位设置为第一取值，否则，将其有效标志位设置为第二取值。若存在有效标志位为第一取值且数据信息一致的数据包，表明有可靠的数据包，可使用可靠的数据包中的数据对不可靠数据包(异常数据包)中的数据进行恢复。而若仅存在一个有效标志位为第一取值的数据包，则使用该数据包对其他相关联的数据包所对应的存储地址进行数据恢复。
",G06F11/10,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/10,"提供, 包括, 装置, 方法, 设置, 控制",控制
CN111766516A,CN202010673739.4,"本发明提供了一种直流电机参数标定方法及装置，方法包括在多种不同测试条件下，分别控制直流电机进行相应运行过程，在直流电机运行过程中，采集直流电机的电枢电流、电枢电压以及转子转速，计算得到多个转子温度对应的扭矩系数和电枢电阻，进而计算得到转子温度与扭矩系数和电枢电阻的关系公式。测试条件包括直流电机的转子温度、直流电机的工作电压和直流电机的负载，因此相同转子温度对应多种测试条件。根据多种测试条件计算得到的某个转子温度对应的扭矩系数和电枢电阻更加准确，进而使得热保护算法计算得到的直流电机温度更加准确，可以更好地保护直流电机不被烧毁。本发明中每种测试条件下直流电机的运行时间均较短，缩短了整个标定时间。
",G01R31/34,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/34,"提供, 包括, 测试, 装置, 算法, 方法, 控制, 计算","测试, 控制"
CN111679929A,CN202010493866.6,"本发明公开了一种应用于多核异构系统的控制方法，方法包括：微控制器或微处理器生成复位请求信息，并将复位请求信息发送至系统控制单元，其中，复位请求信息包括：看门狗被触发信息或心跳交互异常信息；系统控制单元将接收到的微控制器发送的复位请求信息发送至微处理器，或者系统控制单元将接收到的微处理器发送的复位请求信息发送至微控制器；微控制器或微处理器在接收到系统控制单元发送的复位请求信息后，基于复位请求信息进行复位控制。本发明通过采用灵活的控制机制，能够保证多核异构系统的有效性，进而为智能车的行车安全提供了有效保障。
",G06F11/07,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/07,"提供, 包括, 方法, 生成, 处理, 控制, 系统",控制
CN111522686A,CN202010232002.9,"本发明提供了一种非易失性数据的读写方法及装置，方法包括：数据安全管理模块在接收到非易失性数据读取请求的情况下，触发不可信分区中的非易失性数据管理模块将从存储设备中读取到的非易失性数据写入不可信分区的缓存中，通过对非易失性数据进行校验，并将校验通过的非易失性数据写入可信分区的缓存，保证可信分区中非易失性数据的正确性。由于在非易失性数据管理系统中将可信分区中的数据安全管理模块与不可信分区进行了隔离，在整个非易失性数据的读写过程中，不可信分区发生任何问题都只会影响不可信分区的数据，不会影响可信分区的数据，保证了非易失性数据读写的安全性。
",G06F11/10,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/10,"提供, 包括, 装置, 方法, 设备, 系统",通用
CN111475410A,CN202010246780.3,"本申请提供一种测试用例生成方法及装置，将测试文档拆分得到功能文本段，将功能文本段输入语义理解模型中，经过解析后输出测试条件和测试结果；分别确定每个测试条件对应的测试流程，基于测试流程之间的逻辑关系对测试流程进行归并，并结合对应的测试结果生成逻辑测试用例；确定逻辑测试用例包括的待赋值变量及其取值范围，基于待赋值变量的取值范围及与测试结果的对应关系，对待赋值变量进行赋值，生成具体测试用例。由于基于测试文档自动生成了具体测试用例，因此基于具体测试用例可以完成对车辆各项功能的测试，实现了自动测试的目的，提高了测试的自动化程度以及测试效率。
",G06F11/36,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"提供, 实现, 包括, 模型, 装置, 测试, 方法, 生成","车辆, 测试"
CN111475357A,CN202010244742.4,"本发明提供一种总线故障注入系统，应用于故障注入技术领域，该系统包括控制终端和故障注入设备，控制终端以通信的方式向故障注入设备发送故障注入指令，待测控制器通过故障注入设备与车载CAN网络相连，故障注入设备获取车载CAN网络传输的实车CAN报文以及故障注入指令，并根据所得故障注入指令对实车CAN报文进行故障注入，最终将所得故障CAN报文发送至待测控制器，以完成对待测控制器的安全功能测试。本系统中待测控制器经故障注入设备与车载CAN网络连接，所得故障CAN报文是故障注入设备基于与车辆实际行驶情况相对应的实车CAN报文经过故障注入后得到的，能够满足实际测试需求，同时提高功能安全验证结果的可信度。
",G06F11/22,北京经纬恒润科技有限公司,故障注入板卡技术,3.0,关键词匹配: 故障注入; IPC分类号匹配: G06F11/22,"提供, 包括, 测试, 设备, 控制, 系统","测试, 验证, 控制, 车辆, 通信, 网络"
CN111123110A,CN201911325645.1,"本发明公开了一种计算电池剩余放电能量的方法以及装置，该方法包括：根据电池当前SOC值和放电截止SOC值，将电池剩余放电过程分成n个放电段；n为大于1的整数；确定每个放电段所对应的等效电路模型参数和每个放电段内的电量变化量；等效电路模型参数包括电池内阻和开路电压；根据等效电路模型参数以及当前电池预测功率计算每个放电段的等效工作电压；根据每个放电段的等效工作电压和电量变化量确定电池剩余放电能量；其中，电池预测功率表征电池放电功率大小，依据电池历史功率确定。本发明的上述技术方案，至少能够精确估计电池在当前工况下的剩余放电能量。
",G01R31/367,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/367,"包括, 模型, 装置, 方法, 计算, 电路",通用
CN110658000A,CN201910977813.9,"本发明实施例公开了一种实车故障注入测试的恢复方法，获取车辆的若干姿态参数，根据该若干姿态参数评估车辆是否进入危险状态，当评估结果为车辆进入危险状态时，将故障注入设备与电子电气系统中的第一设备间的故障注入通路关闭，并将电子电气系统中的第二设备与第一设备间的正常信号注入通路连通，使车辆电子电气系统恢复正常状态，实现了实车故障注入测试过程中在车辆进入危险状态时自动恢复系统正常连接，防止因测试人员未及时恢复故障导致的车辆失控和人身伤害。
",G01M17/007,北京经纬恒润科技有限公司,故障注入板卡技术,1.0,关键词匹配: 故障注入,"实现, 测试, 方法, 设备, 系统","车辆, 测试"
CN110412378A,CN201910695889.2,"本发明公开了一种目标物体检测方法及装置，该方法及装置可应用于毫米波雷达模型，具体的，先确定仿真场景中包括的全部目标物体；再从中筛选出位于毫米波雷达模型的检测范围内的目标物体；期间还可获取毫米波雷达模型的设置的杂波模型产生的杂波；最后，从位于毫米波雷达模型的检测范围内的目标物体以及杂波中确定出毫米波雷达模型检测到的目标物体。上述方法及装置，通过在仿真场景的全部目标物体及杂波模型产生的杂波中确定出最终的目标物体，使得毫米波雷达模型可以全面检测仿真场景中的目标物体，并且可以实现虚警，从而使得毫米波雷达模型生成的传感器数据与真实的毫米波雷达生成的传感器数据更贴近，提升了毫米波雷达模型的输出保真度。
",G01R31/00,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/00,"实现, 包括, 模型, 装置, 仿真, 方法, 设置, 生成, 检测","雷达, 检测, 传感器"
CN109917297A,CN201910252995.3,"本发明提供一种电池检测方法、电路和装置，电路包括：互并联的多条电池支路；各个电池支路的第一端与第一节点电连接，各个电池支路的第二端与第二节点电连接，且每个电池支路中设置有一个待测电池，其中一个电池支路记为参考支路，所述参考支路中的待测电池的漏电流为已知量；电流采集器，所述电流采集器用于直接或间接检测各个电池支路中的电流，为对待测电池进行批量测量提供了电路基础。
",G01R31/385,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/385,"提供, 包括, 装置, 方法, 设置, 检测, 电路",检测
CN109709489A,CN201910167911.6,"本发明提供一种计算电池功率限值的方法及系统，该方法为：基于电池在当前时刻的荷电状态SOC和温度值，结合预先存储的峰值功率与SOC和温度值的对应关系，计算电池在当前时刻的峰值功率。计算电池在当前时刻的极化电压。基于当前时刻的峰值功率和极化电压，计算电池在当前时刻的功率限值。本发明提供的方案中，通过计算电池在当前时刻的极化电压，结合基于电池在当前时刻的SOC和温度值计算得到的峰值功率，实时计算电池在当前时刻的功率限值，能精确反映电池在动态工况下的实际功率输出能力。
",G01R31/367,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/367,"系统, 提供, 方法, 计算",通用
CN109581241A,CN201811496274.9,"本发明提供了一种电池特征模拟方法及装置，装置包括与试验电池并联的模拟电阻，测量模拟电阻电流的电流传感器、测量试验电池电压的电压传感器、为电池加热的加热器、测量试验电池温度的电池温度传感器、控制加热器功率的可控功率电源、控制模拟电阻和可控功率电源的可编程控制单元。采用上述装置，电池特征模拟方法通过可编程控制单元、模拟电阻、可控功率电源和控制逻辑的合理配置和控制，可以实现内短路电池热电特性的精确模拟，这种方式不需要对电池内部结构进行改造，可控性和可重复性好，可应用于内短路检测算法开发与验证、电池包结构安全与可靠性测试等工作场景。
",G01R31/392,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/392,"提供, 模拟, 实现, 包括, 测试, 装置, 算法, 方法, 控制, 检测, 配置","测试, 验证, 控制, 检测, 传感器"
CN109444747A,CN201811330555.7,"本发明提供了一种对象的信息检测方法及装置，采用基于变遗忘因子的递归最小二乘法对指定对象进行信息检测，并且，基于待检测量控制遗忘因子的变化，一方面，采用变遗忘因子，可以有效地检测出较小的待检测量或出现时间较短的待检测量，克服了现有技术中的递归最小二乘法由于遗忘因子固定而导致的漏检、错检等问题，另一方面，基于待检测量控制遗忘因子，使得本发明提供的方法及检测装置能够满足待检测量离散或有特定含义的检测情况的要求，检测精度较高，且不易受到干扰和噪声影响。
",G01R31/36,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/36,"提供, 装置, 方法, 控制, 检测","检测, 控制"
CN109116254A,CN201811002537.6,"本发明提供了一种动力电池功率状态估算功能测试方法和装置，该方法包括：获取电池管理系统依据半实物仿真平台输出的电池物理信息计算得到的电池包模型的SOP估算结果；依据下一时刻最大允许脉冲充放电电流或所述下一时刻最大允许持续充放电电流计算得到电池包模型的电池包总电压和单体电压，通过判断电池包总电压是否位于电池包电压报警范围内、单体电压是否位于单体电压报警范围内，判断SOP估算结果是否合理。通过本发明提供的方法和装置，提高了SOP估算结果可靠性测试的精准性。
",G01R31/36,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/36,"提供, 平台, 包括, 模型, 装置, 测试, 仿真, 方法, 系统, 计算",测试
CN109061364A,CN201810953884.0,"本发明公开了一种无应答状态检测方法及装置，该方法包括：实时检测CAN控制器是否处于正常发送报文状态；若否，确定CAN控制器的CAN总线通信异常；每发送失败一帧报文，CAN控制器的硬件错误计数器的数值按第一预设值递增；实时读取并判断硬件错误计数器的数值是否等于设定阈值；若是，确定CAN控制器的CAN总线处于无应答状态。本发明公开的方法及装置，通过以上方式可以实现对CAN总线的无应答状态的有效检测。
",G01R31/02,天津经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/02,"实现, 包括, 装置, 方法, 控制, 检测","检测, 通信, 控制"
CN109030994A,CN201810696914.4,"本发明公开了一种测试方法及系统，该系统包括主测试模块，通过第一总线接口与主测试模块通信的Tbox，通过REST接口与主测试模块通信的网关，主测试模块向Tbox发送总线数据，Tbox向网关发送与总线数据对应的无线数据，主测试模块从网关中获取无线数据，并将无线数据与向Tbox发送的总线数据进行对比，而是判断当前对Tbox的测试是否通过。可见，本实施例实现了对Tbox的自动测试。
",G01R31/00,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/00,"实现, 包括, 测试, 方法, 系统","通信, 测试"
CN108983034A,CN201811256264.8,"本发明提供了一种内短路检测方法及装置，方法包括：获取至少两个电流传感器测得的电流作为实测内短路电流，其中，每个电流传感器设置于对称环形电路中任意两节相邻电池之间的正极环路或负极环路上，任意两节相邻电池之间最多设置一个电流传感器；确定对称环形电路中每节电池对应的理论内短路电流，并确定实测内短路电流与每节电池对应的理论内短路电流的相关系数，以得到对称环形电路中每节电池对应的相关系数；通过对称环形电路中每节电池对应的相关系数，确定对称环形电路是否发生了内短路。本发明提供的内短路检测方法及装置检测精度较高，检测效果较好。
",G01R31/02,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/02,"提供, 包括, 装置, 方法, 设置, 检测, 电路","检测, 传感器"
CN108957192A,CN201810832357.4,"本发明公开了一种电磁干扰诊断系统及方法，该系统包括：电磁干扰诊断装置、预放大器、射频接收设备和上位机，电磁干扰诊断装置将输入的电磁干扰信号的差模干扰和共模干扰，分别转换成第一射频输出电压，以及第二射频输出电压，并经预放大器、射频接收设备处理后发送给上位机，上位机基于第一射频输出电压计算得到差模干扰强度，基于第二射频输出电压计算得到共模干扰强度。本发明公开的系统及方法通过将差模干扰和共模干扰分别转化为差异明显且与干扰强度成正比的射频电压，实现了对共模干扰和差模干扰强度确定的、量化的测量，以及对电磁干扰类型的诊断。
",G01R31/00,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/00,"实现, 包括, 装置, 方法, 处理, 设备, 系统, 计算",诊断
CN108845273A,CN201811001489.9,"本发明提供一种动力电池功率状态估算功能测试方法和装置，该方法包括：基于SOP估算结果和当前时刻电池包总电压，计算电池包模型下一时刻最大允许充放电电流；获取电机模型请求输出电流和充电机模型输出充电电流；基于电池包模型的工作状态由下一时刻最大允许充放电电流、电机模型请求输出电流和充电机模型输出充电电流中选择电池包模型输入，获取对加载电池包模型输入后的电池包模型的测试结果，判断测试结果与目标测试结果是否一致，依据判断结果判断SOP估算结果是否可靠，提高了SOP估算结果可靠性测试的精准性。
",G01R31/36,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/36,"提供, 包括, 模型, 装置, 测试, 方法, 计算",测试
CN108680813A,CN201810953885.5,"本发明公开了一种电磁干扰建模仿真方法及装置，该方法包括：建立直流有刷电机的低频阻抗模型；在低频阻抗模型的基础上，建立直流有刷电机换向的低频等效电路模型，并利用低频等效电路模型，获得干扰源的低频特性；利用干扰源的低频特性，计算干扰源的高频特性；建立直流有刷电机的简化结构的全波模型；将干扰源的高频特性集成到全波模型中，得到集成模型，并对集成模型进行数值仿真计算，得到直流有刷电机的电磁干扰。本发明的方法及装置实现了在设计阶段对直流有刷电机建模仿真分析；同时基于建模和电磁兼容原理，计算出数百MHz的高频率的干扰源特性，因此能覆盖到电磁兼容关注的较高频率范围，具有较好的工程实用价值。
",G01R31/00,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/00,"实现, 包括, 模型, 装置, 具有, 仿真, 方法, 计算, 电路",通用
CN108387802A,CN201810297374.2,"本发明公开了一种整车接地系统的电磁兼容评估方法及装置，该方法包括：识别整车中影响电磁兼容的部件；利用整车设计文件中的车身结构数模建立车身金属的模型；利用整车设计文件中的线束数模中与影响电磁兼容的部件相关的线束数模，建立接地网络线束的模型；以等效电路的方式，建立接地点和接地连接的模型；以等效电路的方式，建立影响电磁兼容的部件的地的等效电路模型；对整车接地系统电磁兼容分析的模型进行仿真，得到干扰源部件和敏感体部件之间通过地的耦合关系；对耦合关系进行分析，确定整车接地系统的电磁兼容性能。相对于现有技术，本发明实现了在整车电气设计阶段对整车接地系统的电磁兼容性能进行分析。
",G01R31/00,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/00,"实现, 包括, 模型, 装置, 仿真, 方法, 系统, 电路",网络
CN108132396A,CN201711306624.6,"本发明提供一种运动位置的确定方法及装置，本发明中每隔第一预设时间采集一次电机的电机电流，根据每次采集的电机电流，实时生成电流变化曲线，实时对所述电流变化曲线进行幅值滤波和脉宽滤波，得到滤波后的曲线，根据滑动参考线，将所述滤波后的曲线转换成方波曲线，根据所述方波曲线，确定闭合部件的运动位置。可以采用上述软件方法确定闭合部件的运动位置，进而不需要使用高通滤波器和比较器，降低硬件成本。
",G01R31/00,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/00,"生成, 提供, 方法, 装置",通用
CN108121326A,CN201711305919.1,"本发明公开了一种故障诊断方法及系统，该方法包括：发送故障诊断请求至被测电子控制单元；当接收到返回的与故障诊断请求对应的响应报文的首帧报文时，根据首帧标识判断响应报文是否为多帧报文，如果是，则创建流控制帧模型生成响应报文的流控制帧，并根据流控制帧接收电子控制单元发送的剩余响应报文；在接收响应报文的同时采集所述全部响应报文，并解析获得所述响应报文对应的故障代码；判断所述故障代码与故障注入列表中预设的故障代码和故障状态是否一致，如果是，则生成故障诊断结果。通过本发明实现了自动化故障诊断，提高了故障测试效率的目的。
",G05B23/02,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,"关键词匹配: 故障注入, 故障诊断","实现, 包括, 模型, 测试, 方法, 生成, 控制, 系统","测试, 诊断, 控制"
CN107783040A,CN201710994520.2,"本申请提供了一种直流有刷电机电磁干扰的建模方法、装置及系统，方法包括：获取与等效电路模型的模型参数对应的目标测量数据，等效电路模型为基于直流有刷电机产生的电磁干扰建立的模型，模型参数包括共模导纳参数、差模导纳参数以及电流源的电流参数；基于目标测量数据确定等效电路模型的模型参数，获得具有模型参数的等效电路模型作为直流有刷电机电磁干扰的模型。本申请提供的建模方法简单、通用性好、工程实用性强，有利于设计开发过程中预测、分析、改善电磁兼容性，提高研发效率，降低开发成本，并且，建立的电磁干扰的模型可作为一个组件在包含直流有刷电机的电子电气系统级电磁兼容分析中使用，有助于提高系统级电磁兼容设计的效率。
",G01R31/34,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/34,"提供, 包括, 模型, 装置, 具有, 方法, 系统, 电路",通用
CN106951336A,CN201710183776.5,"本发明提供了一种MCU的复位方法、装置及系统，通过在依据定时器计时所生成的第一计时时间值达到第一预设时间阈值时，对这段时间值内是否接收到主MCU发送的方波信号进行判断，能够使从MCU及时获知主MCU是否发生故障，之后在判断出第一计时时间值达到预设时间阈值时从MCU仍未接收到主MCU发送的方波信号，则及时发送第一复位信号到主MCU，使其进行复位操作，可以及时停止主MCU继续向从MCU发送故障数据，有效避免了从MCU利用故障数据进行相关操作的问题，进而提高了汽车系统运行的可靠性。
",G06F11/07,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/07,"提供, 装置, 方法, 生成, 系统",汽车
CN106897203A,CN201710196842.2,"本发明提供了一种CPU负载率计算方法及装置，该方法包括：根据嵌入式实时操作系统的系统时间实时更新预设标识的状态，并在嵌入式实时操作系统的空闲任务运行线程之前添加第一接口函数线程；开启第一接口函数线程，并根据预设标识的状态和嵌入式实时操作系统的第一当前系统时间确定空闲任务的启动时间；开启空闲任务运行线程，并根据预设标识的状态和嵌入式实时操作系统的第二当前系统时间确定空闲任务的结束时间；进而根据空闲任务的启动时间和结束时间计算CPU负载率。由于空闲任务在嵌入式实时操作系统中的优先级最低，因此，避免了统计测试时段内任务激活、结束、抢占的时间以及中断预处理、后处理的时长，从而提高了CPU负载率的计算准确度。
",G06F11/34,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/34,"提供, 包括, 测试, 装置, 方法, 处理, 系统, 计算",测试
CN106772013A,CN201611249252.3,"本发明公开一种继电器故障检测方法及系统，其应用于继电器故障检测电路，该继电器故障检测电路包括继电器、控制器和天窗电机，该方法包括：检测继电器A路和B路的控制信号和反馈信号；根据控制信号和反馈信号确定继电器的故障类型；根据继电器的故障类型进行继电器的故障重检处理。本发明可以通过检测继电器A路和B路的控制信号和反馈信号来检测天窗控制电机的继电器是否发生故障，并进行继电器故障处理，能够做到无论继电器发生什么故障，都能执行紧急停止，增强了电动天窗的可靠性，且故障检测逻辑比较简单，且只需要配置一个控制器和一个双路继电器，在综合考量后降低了成本，并适合汽车电控天窗的继电器电路设计及故障诊断。
",G01R31/327,北京经纬恒润科技有限公司,故障注入板卡技术,4.0,"关键词匹配: 故障检测, 故障诊断; IPC分类号匹配: G01R31/327","包括, 方法, 处理, 控制, 系统, 检测, 电路, 配置","检测, 汽车, 诊断, 控制"
CN106407066A,CN201610849814.1,"本发明公开的一种电子产品的下线测试方法及系统，基于统一诊断服务UDS的诊断服务，该方法包括：获取电子产品的端口外部输入的预期状态信息，预期状态信息为ON状态或OFF状态；根据UDS诊断服务指令查询端口的实际状态信息，实际状态信息可以为ON状态或OFF状态；通过判断预期状态信息和实际状态信息是否一致来确定端口电路是否正常。本发明基于UDS的诊断功能，且诊断功能均为标准配置要求，通过UDS诊断完成产品硬件电路的诊断减低了额外开发下线测试协议的工作量，且需要通过一定级别的安全访问才能进行检测控制权，在诊断掉线后，自动恢复到产品工正常作模式，因此避免了产品陷于EOL测试模式而无法正常工作的情况。
",G06F11/263,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/263,"包括, 测试, 方法, 控制, 系统, 检测, 电路, 配置","检测, 测试, 诊断, 控制"
CN106294155A,CN201610657046.X,"本申请提供了一种分布式I/O仿真控制测试方法、系统及仿真计算机，分布式I/O仿真控制测试方法包括：上位机生成模型程序代码并下载到仿真计算机，模型程序为在Simulink模型框架中Simulink驱动模块与数据交互程序进行集成后，自动生成的可执行代码，Simulink驱动模块为上位机在所述Simulink模型框架中搭建出的当前分布式I/O仿真控制测试任务对应的模块；运行模型程序，以执行分布式I/O仿真控制系统中EtherCAT主站和EtherCAT从站进行通信的过程。在本申请中，通过以上方式缩短了可执行代码生成的时间，从而缩短了EtherCAT主站功能的实现周期。
",G06F11/36,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"提供, 实现, 包括, 模型, 测试, 仿真, 方法, 生成, 控制, 系统, 计算","通信, 测试, 控制"
CN106130414A,CN201610499797.3,"本发明公开一种汽车天窗从电机霍尔信号故障检测方法、相关装置及系统，该方法包括：从电机的控制指令有效时，周期性检测从电机的第一路和第二路霍尔信号；若第一路霍尔信号无有效跳变沿，控制第一故障计数器增加第一预设数值，若第二路霍尔信号无有效跳变沿，控制第二故障计数器增加第二预设数值，若第一路和第二路霍尔信号均存在有效跳变沿且各时刻电平相同，控制第三故障计数器增加第三预设数值；若第一故障计数器达到第一预设阈值，确定第一路霍尔信号故障，若第二故障计数器达到第二预设阈值，确定第二路霍尔信号故障，若第三故障计数器达到第三预设阈值，确定第一路和第二路霍尔信号短接故障。本发明能够实现对从电机霍尔信号的故障检测。
",H02P6/16,北京经纬恒润科技有限公司,故障注入板卡技术,1.0,关键词匹配: 故障检测,"实现, 包括, 装置, 方法, 控制, 系统, 检测","检测, 汽车, 控制"
CN106124990A,CN201610506453.0,"本申请公开的一种堵转检测方法和装置，采用电流阈值的堵转检测方法对电机进行实时堵转检测；同时在实时电流大于电机正常运行时的电流阈值时，启动基于纹波的堵转检测方法进行实时堵转检测。即在电机的实时电流大于电机正常运行时的电流阈值时，同时采用电流阈值的堵转检测方法和基于纹波的堵转检测方法对电机进行实时堵转检测。而基于波纹的堵转检测方法对电机的堵转检测，检测过程依据预设时间内波纹信号中是否有新的波纹脉冲产生，不依据电流阈值。因此，当基于电流阈值的堵转检测方法的电流阈值设置不精确出现漏检的情况时，利用纹波信号的检测方法可以进行有效补充，实现了对电机的有效保护，提高了电机的保护方案的可靠性。
",G01R31/34,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/34,"实现, 装置, 方法, 设置, 检测",检测
CN105974252A,CN201610617554.5,"本发明公开了一种PWM输出故障诊断方法，包括：判断PWM输出占空比是否为100％，若是，则执行占空比为100％的诊断过程，依据反馈信号端状态输出诊断结果；若否，则：判断PWM输出占空比是否为0％，若是，则执行占空比为0％的诊断过程，依据反馈信号端状态输出诊断结果；若否，则：执行常规诊断过程，依据反馈信号端状态输出诊断结果。本发明通过读取在不同占空比下反馈信号端的状态，就能够实现故障诊断，提高了PWM输出故障诊断的实时性。本发明还公开了一种PWM输出故障诊断系统。
",G01R31/00,北京经纬恒润科技有限公司,故障注入板卡技术,3.0,关键词匹配: 故障诊断; IPC分类号匹配: G01R31/00,"系统, 方法, 包括, 实现",诊断
CN105677560A,CN201511032326.3,"本申请公开了一种测试方法，应用于MATLAB模型，所述MATLAB模型上预先编译有用于读写文件的第一函数，MATLAB模型运行于下位机预先分配的第一内存区域中，上位机下载MATLAB模型到下位机后，利用第一函数，将回放数据从下位机的硬盘加载到第一内存区域，然后根据测试周期，读取回放数据发送给被测目标系统进行测试。在上述过程中，MATLAB模型读取的是下位机内存区域的数据，无需再从上位机获取，保证了数据读取的实时性和准确性，能够满足对实时性要求较高的被测目标系统的测试需求。
",G06F11/36,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"系统, 方法, 测试, 模型",测试
CN105607011A,CN201610169510.0,"本申请提供了一种电池健康状态SOH的估算方法及装置，其中方法包括：从确定电池满足电量累计的起始条件时起，至确定电池满足电量累计的结束条件时为止，对每个采样间隔均执行以下步骤：获取对电池电流采样后得到的电池放电电流与电池充电电流，基于所述电池放电电流与所述电池充电电流计算所述采样间隔内的净放电量；计算所有采样间隔内电池的净放电量的累计值；将所述累计值确定为电池在所述起始条件与所述结束条件之间的当前电量值；基于所述当前电量值估算电池的健康状态SOH。本申请可以得到的电池准确的当前电量值，从而可以利用准确的当前电量值估算电池的健康状态SOH。
",G01R31/36,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/36,"提供, 包括, 装置, 方法, 计算",通用
CN105426312A,CN201511031282.2,"本申请公开了一种冒烟测试用例集的生成方法和装置。该方法获取被测对象的测试需求，并对测试需求进行约简处理，缩小了冒烟测试用例需要覆盖的测试需求的范围，从源头上保证了测试用例集的最小化。另外，本方法在对测试需求中高优先级的基本功能性需求进行测试时，选择与该基本功能性需求对应的测试方法，并按照选取的测试方法生成对应的测试用例集。与现有技术相比，本发明可结合被测对象的测试需求的特定选取相应的测试方法，进而生成与测试方法相应的测试用例集，避免了人为因素的干扰，同时在测试用例生成时，保证了测试用例集的最小化，因而提高了冒烟测试的效率。
",G06F11/36,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"测试, 装置, 生成, 方法, 处理",测试
CN104820193A,CN201510288949.0,"本发明提供一种电池电压检测系统、RKE测试设备及RKE产品。系统包括携带有电池的RKE产品，和RKE测试设备；其中，所述RKE产品具备自检测电池电压，生成电池电压信息，并将所述电池电压信息发出的功能；所述RKE测试设备用于依据接收到的用户测试指令，触发所述RKE产品进行自检测电池电压，生成并发出所述电池电压信息，所述RKE测试设备接收所述电池电压信息，并依据所述电池电压信息判断所述电池是否正常。本发明实现了对RKE产品中的电池在线带载检测，测量精度高，提高了对电池是否正常的测试结果的准确性。
",G01R31/36,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/36,"提供, 实现, 包括, 测试, 生成, 设备, 系统, 检测","检测, 测试"
CN104636215A,CN201510110127.3,"本申请提供一种硬件看门狗及其应用电路，通过通信接口电路实现控制芯片与监测芯片之间的报文发送和接收；通过监测芯片定时向控制芯片发送喂狗请求报文，要求控制芯片在预设起止时间内回复喂狗操作报文，否则输出复位信号；并在接收控制芯片发送的低功耗模式请求报文后，回复低功耗模式确认报文，并在第一规定时间段内暂停喂狗请求报文的发送；由复位电路接收复位信号，生成并输出复位控制信号至控制芯片，控制控制芯片复位。本申请提供的硬件看门狗，为独立于控制芯片的电路，较现有技术中的软件看门狗可靠性高；同时通过通信接口电路实现低功耗模式的请求，从而实现低功耗，比传统硬件看门狗为系统提高了节能性。
",G06F11/10,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/10,"提供, 实现, 生成, 控制, 系统, 电路","通信, 控制"
CN104461879A,CN201410713659.1,"本发明实施例公开了一种自动测试方法及装置，该方法包括：将原始数据按照预设协议进行打包形成打包数据；将形成的打包数据添加到指定队列中；接收用于自动测试的数据使用模块的数据请求，从所述指定队列中取出打包数据；对出队的打包数据按照所述预设协议进行解析；将解析结果发送给所述数据使用模块，以使所述数据使用模块使用所述解析结果执行相应操作，能提高自动测试的开发效率，有利于开发过程中的维护和优化，提高测试效率，降低测试成本。
",G06F11/36,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"方法, 包括, 装置, 测试",测试
CN104391791A,CN201410677475.4,"本发明实施例公开了一种嵌入式控制算法的测试方法及装置。该方法包括：创建系统测试工程，以加载嵌入式系统模型，并获取相应的至少一个测试用例、测试判定准则和需要保存的测试数据项；通过运行系统测试工程对嵌入式系统模型进行仿真测试；嵌入式系统模型为在MATLAB的Simulink中所创建的由用户自定义S-Function模块以及标准模块组成的嵌入式系统对应的模型；用户自定义S-Function模块包括通过MATLAB中的S-Function，将嵌入式控制算法封装得到的S-Function控制模块。本发明实施例可实现对嵌入式控制算法的自动化测试，提高测试效率，保证测试的准确性，降低问题修复成本。
",G06F11/36,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"实现, 包括, 模型, 装置, 算法, 测试, 仿真, 方法, 控制, 系统","测试, 控制"
CN104330688A,CN201410708177.7,"本申请公开了一种机载电缆的导通测试装置和方法，用于对总装完成的机载电缆进行导通测试，机载电缆的导通测试装置中的控制器在启动导通测试时，按照预设顺序控制多条测试电路中的继电器闭合或断开，使得同一时间只有一个继电器处于闭合状态，并检测处于闭合状态的继电器所在的测试电路中是否存在回路电流，如果存在，则判定待测机载电缆中与处于闭合状态的继电器所在的测试电路相连接的芯线导通，否则不导通。这样，可以由控制器控制与待测机载电缆中各芯线相连的多条测试电路轮流导通，来依次判断待测机载电缆中各芯线的导通情况，实现在机载电缆已经在飞机上安装部署完毕的限制条件下，对机载电缆进行快速简单、高可靠性的自动化导通测试。
",G01R31/02,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/02,"实现, 测试, 装置, 方法, 控制, 检测, 电路","检测, 测试, 控制"
CN104268027A,CN201410487757.8,"本发明公开了一种嵌入式实时操作系统的故障处理方法和装置。所述方法包括：启动嵌入式实时操作系统，进入第一应用模式；检测所述嵌入式实时操作系统在所述第一应用模式下出现的第一故障；在检测到所述第一故障后，如果需要重启所述嵌入式实时操作系统，则确定所述嵌入式实时操作系统重启后进入的第二应用模式，并执行操作系统重启；重新启动所述嵌入式实时操作系统后，进入所述第二应用模式。本发明通过重启操作系统动态切换应用模式，避免了系统故障的重复发生。
",G06F11/00,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/00,"包括, 装置, 方法, 处理, 系统, 检测",检测
CN103744772A,CN201410042181.4,"本申请提供一种检测任务运行性能的方法，应用于嵌入式实时操作系统中，操作系统中有至少一个任务，包括：在预设检测周期内，检测操作系统中的各个任务状态，当检测到操作系统中任一任务状态发生转换时，生成任务状态转换记录，其中，任务状态转换记录包括：任务的识别信息、转换类型以及转换时刻的计时值；统计记录得到统计结果；基于统计结果，分析检测周期内操作系统的任务运行性能。本方法中，通过对操作系统中所有任务的状态转换以及状态转换发生的时间点进行记录统计，根据该统计的结果可得到在本检测周期中操作系统的所有任务运行情况，依据该统计结果对检测周期中任务运行性能进行分析，能够实现对操作系统中任务运行性能进行全面检测。
",G06F11/30,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/30,"提供, 实现, 包括, 方法, 生成, 系统, 检测",检测
CN103678138A,CN201410003501.5,"本申请提供了一种生成状态转换测试用例的方法及装置，方法包括：生成包括系统存在的各个状态和状态转换信息的状态转换表，状态转换信息用于指示状态与状态之间是否能够进行转换；依据状态转换表生成N-Switch状态转换树，并依据N-Switch状态转换树生成有效的状态转换测试路径；依据状态转换表生成无效的状态转换测试路径；通过有效的状态转换测试路径和无效的状态转换测试路径生成状态转换测试用例集。本申请提供的方法及装置，能覆盖系统的所有状态以及状态与状态之间的转换关系，因此能够保证测试用例的充分性，并且，能够保证生成的状态转换测试用例集的确定性和唯一性，避免产生不同人设计出的测试用例不同的情况。
",G06F11/36,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"提供, 包括, 测试, 装置, 方法, 生成, 系统",测试
CN103631695A,CN201310706393.3,"本发明实施例提供了一种时间监测方法及装置，该方法包括：接收第一时间监测指示；根据预先设置的时间参数与时间监测指示的对应关系，获得所述第一时间监测指示对应的第一时间参数，所述第一时间参数可以为执行时间参数、内部到达时间参数、资源锁定时间参数和中断关闭时间参数中的一种或多种；根据所述第一时间参数与所述第一时间监测指示对应的实际运行时间，确定是否发出报错信号。采用本发明实施例提供的方法及装置可以实现监测时间的具体实现方法。
",G06F11/30,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/30,"提供, 实现, 包括, 装置, 方法, 设置",通用
CN103559112A,CN201310544465.9,"本申请提供了一种软件故障注入方法和系统，其中一种软件故障注入方法，包括：上位机生成故障信息并发送至实时仿真机中；实时仿真机依据故障信息，确定故障信号类型；实时仿真机将故障信号类型对应的故障数据添加至无故障测试信号中，生成故障测试信号，并发送故障测试信号，由待测系统对故障测试信号进行仿真测试。由于实时仿真机在生成故障测试信号的同时，上位机可以生成一故障信息，该故障信息不同于上位机之前所生成的故障信息，从而实现故障测试信号生成的同时，上位机在线修改实时仿真机下一次生成的故障测试信号所依据的故障信息，使得修改更加便捷。
",G06F11/26,北京经纬恒润科技有限公司,故障注入板卡技术,3.0,关键词匹配: 故障注入; IPC分类号匹配: G06F11/26,"提供, 实现, 包括, 测试, 仿真, 方法, 生成, 系统",测试
CN103530232A,CN201310507417.2,"本发明实施例提供了一种软件测试管理框架构建方法及装置，方法包括：创建包括测试服务模块、测试客户端模块、测试管理模块的测试管理框架；为测试服务模块、测试客户端模块、测试管理模块分别嵌入支持离线操作功能的配置管理工具；创建配置管理库；在测试管理框架处于离线状态时，测试客户端模块完成产品测试工作，当测试管理框架处于在线状态时利用配置管理工具将测试工作产品提交至配置管理库中。本发明实施例基于嵌入支持离线操作功能配置管理工具的测试管理框架，解决了现有基于C/S或B/S架构的测试管理平台无法开展实时离线测试活动的问题。
",G06F11/36,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"提供, 平台, 包括, 测试, 装置, 方法, 工具, 配置",测试
CN103440176A,CN201310419119.8,"本发明公开了一种实时操作系统中内存的保护方法及装置，所述实时操作系统中内存的保护方法在运行一个任务前，会首先确定操作系统下一个即将运行的任务的数据访问权限，并在运行这个任务前，根据确定的任务的数据访问权限来设置内存的保护权限，这样，在每执行一个任务前，都会根据该任务的一些属性来确定任务对内存的数据访问权限，并根据确定的结果动态的来设置内存的保护权限，从而能够准确的对实时操作系统中的内存进行保护，有效防止操作系统运行过程中对内存错误访问的情况发生。
",G06F11/00,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/00,"系统, 方法, 设置, 装置",通用
CN103309783A,CN201310284677.8,"本发明公开了一种基于总线通信的测试方法及装置，首先获取预设的第一配置文件，所述第一配置文件中包含配置属性信息；用户通过输入待测产品的协议参数，按照协议参数中各个参数的属性信息对第一配置文件重新配置，生成第二配置文件，最后根据第二配置文件就能完成对待测产品的测试。通过上述基于总线通信的测试方法，用户无需对编程语言熟练运用，只需通过简单的对协议参数的输入，即能生成不同的配置文件，实现对通信接口协议的配置，最终根据不同的配置文件，实现对不同的被测产品的测试。
",G06F11/22,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/22,"实现, 测试, 装置, 方法, 生成, 配置","通信, 测试"
CN103164306A,CN201310097591.4,"本发明公开了一种测试逻辑的生成方法，所述方法基于配置语言来设计解析和执行引擎，并分离逻辑控制与模块配置内容，测试软件中执行特定动作的模块和整个测试逻辑也采用上述配置语言来统一描述，从而用户可以通过应用程序提供的界面，根据测试软件中已被描述和分离的具有特定测试功能的模块来定义目标测试逻辑需完成的动作及动作顺序。该方法在测试不同功能的电子产品时，能够使用户方便的自由定义测试功能及测试顺序，不需要用户再专门编写特定功能的程序。同时本发明还公开了一种测试逻辑的生成装置，所述装置同样不需要用户在面对不同功能电子产品的测试问题时专门编写测试程序，对使用者的专业水平要求低，适用性强。
",G06F11/25,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/25,"提供, 测试, 装置, 具有, 方法, 生成, 控制, 配置","测试, 控制"
CN103150255A,CN201310109566.3,"本申请公开了一种脚本测试方法及装置，该方法通过接收设置有至少一条测试语句的待测试脚本，然后对待测试脚本中的测试语句进行编译，最后依据编译后测试语句中的目标对象类型标识、目标对象标识和测试目的标识对预先存储在数据库中的目标对象进行测试。本申请通过对待测试脚本中的各个测试语句进行独立执行，并且使得各个测试语句通过目标对象类型标识、目标对象标识和测试目的标识对各个测试语句中的变量进行区分，从而实现无论调整或者删除待测试脚本中任意一个测试语句，均不会对其他测试语句的变量产生影响，可以解决现有技术在进行脚本测试时，通常会因为脚本中定义变量较多、且脚本内容复杂，导致脚本测试准确性降低的问题。
",G06F11/36,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"实现, 测试, 装置, 方法, 设置",测试
CN102810851A,CN201210279701.4,"本发明提供了一种过流保护电路和数字输出电路，所述过流保护电路包括限流电路，计时电路，电压调节电路和比较器。当数字输出电路过流时，过流保护电路通过所述限流单元将电流限制在第二阈值内，以确定过流时的电流大小；并通过电压调节单元中比较器输出第一控制信号控制所述开关管截止迅速地关闭输出，保证过流时关断速度快，且不用更换保险丝；当所述开关管截止时间大于所述计时单元的第一计时时间时，所述电压调节单元输出第二控制信号控制所述开关管导通，且所述开关管导通时间为所述计时单元的第二计时时间，如果输出仍然过流，则再次关闭输出，本发明通过如此周期性的反复尝试，直到输出不再过流，使得输出恢复正常的时间得以时间确定。
",H02H9/02,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: H02H9/02,"提供, 包括, 电路, 控制",控制
CN102735987A,CN201210244727.5,"本发明提供了一种LED检测电路，包括与LED灯串联的第一电阻，以及与所述第一电阻并联的电压获取单元；当所述LED灯通路时，所述第一电阻两端的电压值为第一电压值；当所述LED灯断路时，所述第一电阻两端的电压值为第二电压值；所述电压获取单元对所述第一电阻两端的电压值进行检测，当检测到所述第一电阻两端的电压值由所述第一电压值变为所述第二电压值时，生成报警信号。本发明的检测电路与LED灯串联，用户可以自主选择灯具，本发明的检测电路不局限于灯具的类型，其可以检测任意一种灯具是否掉电，增大了灯具使用的灵活性。
",G01R31/02,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/02,"提供, 包括, 生成, 检测, 电路",检测
CN102622278A,CN201210058703.0,"本发明公开了一种看门狗监控电路，通过串行接口接收被监控设备发送的数据，来配置相关的配置寄存器，即配置监控时间、输出脉冲的有效时间、监控电路工作模式、计数器和监控电路的使能以及产生触发信号，来实现被监控设备根据自己需求通过串行接口配置监控电路的各种参数，使其能使用于更多类型的被监控设备；另外，由于在监控电路上设置了两个视窗监控结构：非触发窗口和触发窗口，对其进行了具体的功能作用的限制，从而实现了对连续触发信号的监控；最后，由于其触发信号是通过配置寄存器产生的，这样为看门狗监控电路减少了一个引脚。
",G06F11/00,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/00,"实现, 设置, 设备, 电路, 配置",通用
CN102608469A,CN201210088974.0,"本发明公开了一种测试装置，为各模块之间的连接均设置了开关，这样，用户就可以根据需要让相应的开关闭合来实现不同的线路连接方式，以完成各种测试。因此，本发明实施例可以使用户方便的实现测试线路的切换，省去了插拔大量连线的工作，一方面节省了工作量，另一方面测试线路切换的准确性也很高。
",G01R31/00,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/00,"实现, 设置, 装置, 测试",测试
CN102290806A,CN201110243553.6,"本发明涉及一种LDO输出过压保护电路，包括：脉冲产生电路和关断电路，该脉冲产生电路输入端与负载关断信号端口SLEEP连接，该关断电路用于在接收到脉冲产生电路产生的窄脉冲信号时将PMOS管P2关断。相应的，本发明还提供一种使用该保护电路的LDO，包括PMOS管P2，还包括脉冲产生电路和关断电路。当窄脉冲消失时，PMOS管P2的栅极电压会慢慢降低到LDO正常工作状态，从而有效避免了LDO的输出过压现象。本发明通过采用脉冲产生电路和关断电路的结构，电路结构简单，成本低廉，可以大大降低负载突变时引起的LDO输出过压问题。
",H02H9/04,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: H02H9/04,"提供, 包括, 电路",通用
CN102135922A,CN201110066991.X,"本发明公开了一种应用程序的刷新方法和系统。其中，该方法包括：电子控制单元获取来自下载工具的重编程指令；电子控制单元根据重编程指令进入重编程模式；将擦写驱动程序下载至电子控制单元的临时存储区域；通过擦写驱动程序来擦除旧的应用程序，并写入新的应用程序。通过本发明，能够在防止电子控制单元被误擦写的同时，节省了存储空间，提高了安全性。
",G06F11/00,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/00,"包括, 方法, 控制, 系统, 工具",控制
CN101788621A,CN201010130513.6,"本发明公开了一种用于汽车生产线的汽车电器系统的下线检测设备，包括电器检测模块(3)，其通过模拟整车汽车电器系统，检测汽车电器系统的被测部件的工作状态，电器检测模块(3)包括：主板(301)；功能板卡，每种功能板卡都通过总线与主板(301)连接，基于来自主板的控制命令而输入/输出一种信号，每种信号均以多路传输；通道板(311)，与每种功能板卡都连接，并经由插接件连接至被测部件，其具有多个通道，每个通道均与每种功能板卡的多路中的一路相对应；主板(301)基于不同被测部件，通过总线发送命令，控制通道板上的每个通道选择一种输入/输出信号，以适应于被测部件对各个通道上传输的信号种类的要求。
",G01R31/00,北京经纬恒润科技有限公司,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/00,"包括, 具有, 设备, 控制, 系统, 检测, 模拟","检测, 汽车, 控制"
