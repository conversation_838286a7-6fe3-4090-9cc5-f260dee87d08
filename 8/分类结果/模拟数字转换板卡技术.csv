﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN120455855A,CN202510798342.0,"本申请公开了一种白平衡调节方法及相关装置，涉及图像处理技术领域，包括：获取车辆四周的多颗摄像头采集的多个图像，采样每个图像对应的每个视野重叠区域内的多个采样点的三通道像素值，组成每个图像对应的一个三通道像素值集合，以得到多个图像各自对应的两个三通道像素值集合，根据多个图像各自对应的两个三通道像素值集合，确定多个图像各自的矫正系数，依据多个图像各自的矫正系数对多个图像分别进行白平衡调节，得到调节后的多个目标图像。本申请使用各视野重叠区域内的同一个物体在对应的两个图像中的成像对比，确定多个图像各自的矫正系数，进而进行白平衡调节，能够消除环境光带来的色偏和亮度变化影响，确保了图像亮度和颜色更一致。
",H04N23/88,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"方法, 包括, 处理, 装置","车辆, 摄像"
CN120403908A,CN202510592940.2,"本申请公开了一种光纤测温方法和光纤测温装置。该光纤测温方法包括：响应于测温指令，生成激光延迟信号，激光延迟信号包括多个延迟参数，多个延迟参数均为时间延迟参数或相位延迟参数；根据多个延迟参数对激光散射信号进行延迟采集，得到多个信号序列，每个信号序列包含在对应的延迟参数下所采集到的激光散射信号，激光散射信号为脉冲激光注入到测温光纤后，测温光纤所产生的散射光信号；按照多个信号序列中每个激光散射信号的采样时间点，对多个信号序列中的激光散射信号进行排序，得到目标信号序列；对目标信号序列进行分析，确定测温光纤的光纤温度。本申请实施例所提供的方案能够提升光纤测温系统的空间分辨率，降低光纤测温系统的成本。
",G01K11/32,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 包括, 装置, 方法, 生成, 系统",激光
CN120161317A,CN202510155732.6,"本申请公开了一种PCB覆铜通流能力检测方法及装置，方法包括：响应于用户触发的选取指令，确定待测PCB的点击位置以及点击位置对应的覆铜图形的参数信息；其中，参数信息包括覆铜图形的属性信息和构成覆铜图形的边缘线段的线段信息；基于点击位置，生成多条经过点击位置且倾斜角不同的预设直线；基于每条预设直线与覆铜图形的边缘线段的两个交点，确定两个交点的交点间距；从多个交点间距中确定最小交点间距，得到点击位置的覆铜最窄宽度；基于覆铜最窄宽度和覆铜图形的属性信息，确定点击位置的最大通流电流。本申请能够提升PCB覆铜位置通流能力的检测效率和检测精度。
",G01R31/28,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,2.0,IPC分类号匹配: G01R31/28,"包括, 装置, 生成, 方法, 检测",检测
CN120047916A,CN202311586969.7,"本发明涉及智能泊车技术领域，公开了车位检测模型的训练方法、车位检测方法及装置，训练方法包括：获取车位区域图像，车位区域图像为训练集中的图像；利用车位检测模型对车位区域图像进行特征提取，得到第一特征图，并利用图像检测辅助头从车位检测模型的第一下采样层输出的结果中获取第二特征图，并利用图像分割辅助头从车位检测模型的第二下采样层输出的结果中获取第三特征图；其中，图像检测辅助头和图像分割辅助头均为添加于车位检测模型上的特征检测头；根据第一特征图、第二特征图及第三特征图，确定目标损失函数的结果；基于目标损失函数的结果，对车位检测模型进行迭代训练。相比于相关技术，本发明有助于提高车位检测效果的准确性。
",G06V20/58,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"包括, 模型, 装置, 方法, 检测",检测
CN119975380A,CN202510329386.9,"本申请公开了一种预测车身角度的方法及相关装置，应用于车辆控制技术领域，该方法包括：获得车辆在最近多个采样时刻的多组行驶参数集和多组车身角度的角度值，每组行驶参数集包括至少一种车辆行驶参数的参数值；基于各组行驶参数集中各车辆行驶参数的参数值和多组车身角度的角度值，拟合出车身角度与至少一种车辆行驶参数之间的目标函数关系；基于目标函数关系以及最近一次采集得到的行驶参数集中各车辆行驶参数的参数值，确定车身角度的预测角度值。本申请的方案能够在较小计算量的前提下，预测出车辆的车身角度。
",B60W40/10,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"包括, 装置, 方法, 控制, 计算","车辆, 控制"
CN119974863A,CN202510329572.2,"本申请公开了一种基于路面识别的半主动悬架的控制方法及装置，包括：周期性获取传感器采集的当前加速度信号，并根据当前加速度信号，计算车辆的当前频段特征参数；根据当前频段特征参数，确定采样周期内所有高频信号的总和；判断所有高频信号的总和是否不小于高频阈值；若不小于高频阈值，则确定车辆的当前车身振动状态为高频振动状态，并记录高频振动状态的持续时间；实时检测记录高频振动状态的持续时间是否满足预设时间阈值；若满足预设时间阈值，则开启车辆的电流补偿，以控制车辆的半主动悬架。从而通过路面识别过程来辨识车辆的激励形式，进而确定电流补偿，以控制车辆的半主动悬架，进而不需要硬件的介入，有效地降低了硬件成本的问题。
",B60G17/015,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"包括, 装置, 方法, 控制, 检测, 计算","车辆, 检测, 传感器, 控制"
CN119959600A,CN202510121725.4,"本申请公开了一种电流检测方法以及电流检测系统。该方法包括：向第二开关器件发送双脉冲驱动信号，测量在第二开关器件处于开通暂态下，电流测试电路的待检测点的输出电压；在检测到输出电压大于预设电压值，且输出电压的变化值处于预设电压范围内时，获取电流采样时间对应的目标平台时间；根据与第一母线电压对应的目标关联关系，确定目标平台时间对应的输出电流，目标关联关系用于表征逆变器的半桥输出电流与待检测点对应的电压平台时间之间的关系；确定目标平台时间对应的输出电流为逆变器的半桥输出电流。采用本申请所提供的方案，可实现半桥输出电流的简易测量，避免传感器的额外引入所造成的占用系统空间，降低系统集成度的问题。
",G01R19/00,经纬恒润(天津)研究开发有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 平台, 实现, 包括, 测试, 方法, 系统, 检测, 电路","检测, 测试, 传感器"
CN119765186A,CN202411847889.7,"本申请公开了一种过流保护芯片的参数配置方法及装置，涉及电力电子技术领域。该方法包括：基于采样电阻的标定电阻值和电阻标定误差，计算目标电流下采样电阻两端的最大初始采样电压和最小初始采样电压；基于最大初始采样电压、最小初始采样电压、控制器的标定模数转换参数以及对应的模数转换参数标定误差，计算最大采样电流和最小采样电流；根据最大采样电流、最小采样电流以及目标电流，计算第一电流采样精度和第二电流采样精度；根据第一电流采样精度和第二电流采样精度中的至少一项，对过流保护芯片的预设过流保护曲线和目标线束的选型参数中的至少一项进行配置。根据本申请实施例，能够在充分保障过流保护的可靠性的同时尽可能降低线束成本。
",H02H3/00,经纬恒润(天津)研究开发有限公司,模拟数字转换板卡技术,2.0,"关键词匹配: 模数转换, 采样","包括, 装置, 方法, 控制, 计算, 配置",控制
CN119673211A,CN202411907673.5,"本发明公开了一种音频分类方法、装置、存储介质和电子设备，可以对目标音频进行解码分析，得到时域下的数字音频信号；基于预先训练的卷积神经网络对所述数字音频信号进行特征提取，得到相应的特征向量；对所述特征向量进行分类，得到所述特征向量分别属于各音频类别的置信度，其中，一个音频类别对应一个置信度；根据各所述置信度，确定所述目标音频的音频类别。由此可以看出，本发明可以直接对时域的数字音频信号进行分类，无需将时域的数字音频信号转换到频域，降低了整体耗时，提高响应速度。
",G10L25/51,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 信号转换,"方法, 装置, 设备",网络
CN119493985A,CN202311034224.X,"本申请提供了一种训练采样集获取方法及其装置，方法包括：获取初始样本集，初始样本集包括N个初始样本，且每个初始样本中包括至少一个对象，针对每个初始样本，将至少一个对象与目标检测对应的C个类别的目标对象进行匹配，确定初始样本对应的第一类别，及初始样本的类别元素个数，类别元素个数为第一类别中不同类别的数量，根据每个初始样本对应的第一类别，从初始样本集中，确定出与C个类别中各类别对应的样本的第一总数量，根据每个初始样本的类别元素个数和各类别对应的样本的第一总数量，从初始样本集中获取训练样本集，训练样本集用于训练目标检测模型。这样，提高了训练样本集的准确性和均衡性，从而提高后续模型训练的效果。
",G06F18/214,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 包括, 模型, 装置, 方法, 检测",检测
CN119471302A,CN202411505340.X,"本申请公开了一种多通道驱动芯片的诊断方法、装置和设备，包括：在确定多通道驱动芯片中存在需要开启的目标通道的情况下，接收控制器发送的用于诊断目标通道的第一诊断使能信号；检测目标通道在第一诊断使能信号的使能下的电性能参数，得到第一检测结果；在第一检测结果指示目标通道为正常状态的情况下，接收控制器发送的用于诊断目标通道的第二诊断使能信号；检测目标通道在第二诊断使能信号的使能下的电性能参数，得到第二检测结果。基于此，通过接收第一诊断使能信号及第二诊断使能信号，从而检测目标通道占空比在0％及100％时的电性能参数，由此确定第一检测结果和第二检测结果，实现了对目标通道的检测，提高了对多通道驱动芯片诊断的适用性。
",G01R31/28,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,2.0,IPC分类号匹配: G01R31/28,"实现, 包括, 装置, 方法, 设备, 控制, 检测","检测, 诊断, 控制"
CN119276397A,CN202411676609.0,"本发明提供的一种电磁干扰探测装置，通过使用定向天线模组中的子天线单元采集装置周围的电磁信号，射频合路器用于获取每个子天线单元的电磁信号，然后向频谱监测模块发送与每个子天线单元的电磁信号对应的射频模拟信号；频谱监测模块对接收到的每个射频模拟信号进行分析，得到每个子天线单元的频谱数据，然后将每个子天线单元的频谱数据向上位机反馈；上位机对各个频谱数据进行处理，输出电磁干扰探测结果。应用本发明提供的装置，可以直接采集电磁信号，无需工作人员搭建采集设备和监测设备，可以快速的检测是否发生电磁干扰，避免因搭建采集设备而错过电磁干扰的时机，提高检测结果的准确性，确保检测结果的时效性，并且减少工作人员的工作量。
",H04B17/345,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 模拟信号,"提供, 装置, 处理, 设备, 检测, 模拟",检测
CN119239622A,CN202411605371.2,"本发明提供一种停车阶段的加速度控制方法及装置、存储介质及电子设备，该方法包括：获取当前采样时间点对应的前置加速度、车辆速度和障碍距离；基于车辆速度和障碍距离，确定规划速度和规划距离；确定期望末端加速度；依据规划速度、规划距离、前置加速度和期望末端加速度，确定期望平均加速度；确定规划加加速度；依据期望平均加速度、前置加速度、规划加加速度和期望末端加速度，确定当前采样时间点的贝塞尔曲线数据，并基于贝塞尔曲线数据确定曲线规划加速度；对曲线规划加速度进行斜率限制处理，将处理结果作为当前的请求加速度。应用本发明的方法，基于贝塞尔曲线实现加速度规划，使加速度变化平滑，有利于改善用户体验。
",B60W40/107,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 实现, 包括, 装置, 方法, 处理, 设备, 控制","车辆, 控制"
CN119154735A,CN202411273122.8,"本申请实施例提供了一种抑制车辆抖动的系统及方法，用于减少主动阻尼控制所需采集的变量，降低抑制车辆抖动的复杂度，该系统包括：主动阻尼控制模块、电流查找表、电流控制器、逆变器和电机；主动阻尼控制模块，用于基于当前采样周期采集到的电机角速度和当前采样周期采集到的电机转矩，输出转矩补偿值；电流查找表，用于查找与修正转矩对应的指令电流值；修正转矩为转矩补偿值和当前时刻电机转矩指令的和；电流控制器，用于基于电流差值对下一采样周期的电流值进行补偿，输出针对逆变器的控制信号；电流差值为指令电流值与实际电流值的差值；逆变器用于基于控制信号，输出三相电；电机用于基于逆变器输出的三相电，输出修正后的角速度。
",H02P21/05,经纬恒润(天津)研究开发有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 包括, 方法, 控制, 系统","车辆, 控制"
CN119102443A,CN202411497578.2,"本发明公开了一种车窗防夹检测方法、装置、存储介质及汽车，在电机加载过程中，基于获取的当前电机电压确定基准电流阈值，基于获取的当前电机电流确定当前电机直流分量，计算截止到当前时刻的连续N个采样周期的当前电机直流分量均值，将当前电机直流分量均值与上一时刻电机直流分量均值求差得到直流分量均值差，基于直流分量均值差与不同级别直流分量均值变化量的大小关系对基准电流阈值进行补偿得到目标电流阈值，在当前电机直流分量不小于目标电流阈值时控制车窗下降。本发明通过在电机加载过程中根据实时采集的电机电压和电机电流动态更新电流阈值，提高了电机启动过程中防夹检测的准确性，有效避免了车窗误防夹及车窗夹紧力过大的情况。
",E05F15/42,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"装置, 方法, 控制, 检测, 计算","检测, 汽车, 控制"
CN118962221A,CN202411096786.1,"本发明公开一种冗余监测电路采样基准电压确定方法、装置、电路及车辆，该方法通过ADC模块获取VCC管脚的采样电压以及VOUT管脚的采样电压，对VCC管脚的采样电压和VOUT管脚的采样电压进行比较，当判断VOUT管脚的输出电压正常，确定VOUT管脚的输出电压为ADC模块的采样基准电压。由此，通过在电路中增加对DCDC芯片的VCC管脚进行电压采样，以及对VCC管脚的采样电压和VOUT管脚的采样电压进行比较来判断VOUT管脚的输出电压是否正常，并在VOUT管脚的输出电压正常时，确定VOUT管脚的输出电压为ADC模块的采样基准电压，从而得到更加准确的采样基准电压，进一步提高采样结果的准确性。
",G01R19/00,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,2.0,"关键词匹配: ADC, 采样","方法, 装置, 电路",车辆
CN118918018A,CN202410961644.0,"本发明提供一种红外图像和可见光图像的融合方法及装置，对红外图像和可见光图像进行高通滤波，得到红外高频图像和可见光高频图像，对红外高频图像进行非下采样剪切波变换得到红外高频方向子带图像，将红外高频方向子带图像与可见光高频图像进行堆叠得到目标图像，将目标图像输入到包含注意力机制模块的融合网络得到高频加权特征图，利用将高频加权特征图与可见光图像进行短接得到融合图像。在本方案中，高通滤波提高了融合网络的泛化性，注意力机制模块能够在融合过程中突出前景信息，非下采样剪切波变换具有多尺度和平移不变性的特点，可以提高不同尺寸物体的融合质量，最终实现提高融合图像质量的目的。
",G06T5/50,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 实现, 装置, 具有, 方法",网络
CN118840278A,CN202410831383.0,"本申请公开了一种图像的去噪方法及装置，所述方法包括：通过获取待去噪图像，将待去噪图像输入至预先训练好的去噪网络模型中，输出去噪图像。去噪网络模型预先利用多个通道的噪声图像对进行训练，采取降采样后的噪声图像对数据集对去噪网络模型进行去噪训练，从而有效地降低了模型的训练时间，然后将降噪后的样本图像和降采样后的噪声对数据集及其升采样后的噪声对数据集进行损失函数的计算，有效地减少降采样和升采样带来的精度损失，最后基于损失函数确定去噪网络模型训练成功，以使可以基于训练好的去噪网络模型对待去噪图像进行去噪，有效地保持图像的清晰度以及图像的纹理细节。
",G06T5/70,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"包括, 模型, 装置, 方法, 计算",网络
CN118466299A,CN202410617747.5,"本申请公开了一种采样芯片的控制方法、装置、设备、介质及产品。该方法包括：在每次发送第一信号之后，判断第一引脚是否为第一电平，第一信号为启动信号、采样信号或读取信号；在第一引脚为第一电平的情况下，向第一采样芯片发送第二信号；其中，在多个采样芯片中存在未被启动的采样芯片的情况下，第二信号为启动信号，第一采样芯片为任一未被启动的采样芯片；在多个采样芯片中不存在未被启动的采样芯片且存在未被触发采样的采样芯片的情况下，第二信号为采样信号，第一采样芯片为任一被启动且未被触发采样的采样芯片；在多个采样芯片均完成采样的情况下，第二信号为读取信号，第一采样芯片为任一未被读取的采样芯片。这样，可以提高采样效率。
",G05B19/042,天津经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"包括, 装置, 方法, 设备, 控制",控制
CN118317216A,CN202410375779.9,"本申请公开了一种过流保护系统及方法。该系统包括：摄像头驱动模块、模拟数字转换器ADC驱动模块和高端驱动芯片HSD，HSD分别与摄像头驱动模块、ADC驱动模块和目标摄像头电连接；摄像头驱动模块用于在HSD使能的情况下，通过HSD为目标摄像头供电；ADC驱动模块用于在HSD使能的情况下，通过HSD接收目标摄像头的电信号，基于电信号确定目标摄像头是否出现过流，并在确定目标摄像头出现过流的情况下，向摄像头驱动模块发送第一通知信息；摄像头驱动模块还用于基于第一通知信息，为目标摄像头断电。这样，无需对摄像头的硬件进行改动，便可以简单地实现对摄像头的过流保护。
",H04N25/70,经纬恒润(天津)研究开发有限公司,模拟数字转换板卡技术,2.0,"关键词匹配: ADC, 转换器","实现, 包括, 方法, 系统, 模拟",摄像
CN118259264A,CN202211699595.5,"本申请公开了一种激光雷达点云数据的处理方法及装置，包括利用下采样对从高线束激光雷达中获取的原始点云数据进行预处理，得到目标点云数据；根据目标点云数据以及多个点云特征训练第一学生模型，以得到目标学生模型，多个点云特征为采用老师模型从原始点云数据对原始点云数据进行三维特征提取、二维特征提取以及密集特征提取所获得的；通过目标学生模型对激光雷达中的点云数据进行识别，以得到点云数据识别结果，相较于现有技术，本申请通过预先训练完成的老师模型提取到的原始点云数据的多个点云特征以及目标点云数据训练得到目标学生模型，使得目标学生模型学习到老师模型提取到的点云特征，从而提升了识别低线束激光雷达的点云数据的精度。
",G01S7/486,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"包括, 模型, 装置, 方法, 处理","雷达, 激光"
CN117651222A,CN202311694471.2,"本申请公开了一种图像数据信号转换图像的方法、装置及设备，涉及图像处理领域，该方法包括：对获取到的图像数据信号进行亮度统计得到亮度数据；基于亮度数据识别图像数据信号的场景类型，场景类型包括：过曝场景或暗态场景；当图像信号的场景类型为过曝场景时，将亮度数据输入至过曝校正网络，并基于过曝校正网络输出的第一亮度映射曲线以及颜色映射矩阵进行图像数据信号的图像转换；当图像信号的场景类型为暗态场景时，将亮度数据输入至暗态调整网络，并基于暗态调整网络输出的第二亮度映射曲线以及图像增益值进行图像数据信号的图像转换。消除传统的ISP Pipeline在不同场景下的局限性，提高了生成图像的视觉效果。
",H04N23/76,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 信号转换,"包括, 装置, 方法, 生成, 处理, 设备",网络
CN117595613A,CN202311589268.9,"本申请公开了一种机电设备的配电系统及控制方法，解决了传统保险丝使用一次就必须更换的问题。该系统包括：控制单元、DC/DC转换器、自搭旁路、高边驱动器1～n和二极管1～n；电源端经DC/DC转换器接控制单元；控制单元在机电设备正常工作时开启高边驱动器1～n并关闭自搭旁路，在机电设备休眠时停止工作；电源端经高边驱动器i接负载i；i＝1、2、…、n；高边驱动器i将电源端与负载i连通，一旦检测到负载i过流则切断该连通；电源端经自搭旁路接二极管i的阳极，二极管i的阴极接负载i；自搭旁路在未接收到关闭信号时，将电源端与二极管1～n的阳极连通，一旦检测到有负载过流则切断该连通并开启高边驱动器1～n。
",H02M1/00,经纬恒润(天津)研究开发有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 转换器,"包括, 方法, 设备, 控制, 系统, 检测","检测, 控制"
CN117289280A,CN202311188258.4,"本申请公开了一种目标边界框的确定方法、装置、设备及存储介质，涉及车辆技术领域。应用于车辆，其方法包括：获取在第M帧下检测到的目标对象的点云信息和车辆的速度信息，速度信息包括车速值和横摆角速度值，点云信息包括N个采样点的点信息，点信息包括径向速度值，M为正整数，N为正整数；根据N个采样点的径向速度值、车速值和横摆角速度值，确定目标对象的航向角，航向角用于表示目标对象与车辆航向之间的夹角；根据目标对象的航向角，确定目标对象的边界框。根据本申请实施例，能够准确测量距离，从而准确确定目标对象的边界框。
",G01S13/931,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"包括, 装置, 方法, 设备, 检测","车辆, 检测"
CN117227477A,CN202311142180.2,"本申请公开了一种供电系统和供电控制方法，该系统包括：低压供电子系统，低压供电子系统中低压电池用于给N个第一用电设备供电，N个第一配电开关的输出端分别与N个第一用电设备连接；DCDC供电子系统，DCDC供电子系统中的DCDC转换器用于将动力电池的高压电信号转化为低压电信号，DCDC转换器的一端与动力电池连接，另一端分别与M个第二配电开关的输入端连接，M个第二配电开关的输出端分别与M个第二用电设备连接；隔离开关，隔离开关的一端连接低压供电子系统，另一端连接DCDC供电子系统，用于控制低压供电子系统和DCDC供电子系统之间的连接状态。以实现在任一供电子系统失效的情况下，车辆仍然可以正常行驶。
",B60L3/00,经纬恒润(天津)研究开发有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 转换器,"实现, 包括, 方法, 设备, 控制, 系统","车辆, 控制"
CN117176165A,CN202311135063.3,"本申请公开了一种信号处理方法及装置，其中方法包括：通过ADC进行多路信号同步采集，获得多路输入信号；对所述多路输入信号进行采样频率调节，获得对齐信号；对所述对齐信号进行累加平均运算，获得初步降噪信号；根据预设的小波基函数对所述初步降噪信号进行小波分解，获得去噪信号。通过上述步骤，在初步降噪时，可以设置较少的累加平均次数，来缩短处理周期，后续再通过小波分解降噪，来提升去噪信号的信噪比。也就是说，通过对对齐信号进行初步降噪和小波分解降噪两次降噪处理，可在提升去噪信号的信噪比的同时，降低信号的处理周期，保证信号处理的实时性。
",H03M1/12,经纬恒润(天津)研究开发有限公司,模拟数字转换板卡技术,4.0,"关键词匹配: ADC, 采样; IPC分类号匹配: H03M1/12","包括, 装置, 方法, 设置, 处理",通用
CN117095698A,CN202311067323.8,"本申请公开了一种报警音识别的方法、装置、电子设备及存储介质，应用于信号处理领域。在本申请中，首先对采样后的报警音频进行预处理得到第一音频，然后在所述第一音频的目标位置处进行剪切得到第二音频，所述目标位置为所述第一音频与标准音频符合相似度阈值的音频段位置。最后计算所述第二音频与所述标准音频的相关系数，基于所述相关系数进行报警音的识别，所述相关系数用于确定所述第二音频与所述标准音频之间的关联程度。本申请实现了提高报警音识别的效率及准确性，避免人力资源的浪费。
",G10L25/51,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"实现, 装置, 方法, 处理, 设备, 计算",通用
CN117068184A,CN202311216487.2,"本申请公开了一种车身侧偏角的确定方法、装置及设备。该方法包括：获取车辆在第一时刻的运动状态参数，其中，运动状态参数包括车辆的行驶速度、横向加速度、纵向加速度以及横摆角速度，根据车辆在第二时刻的侧偏角最终值和第一时刻的运动状态参数确定车辆在第一时刻的车轮力，其中，第二时刻为第一时刻一个采样步长之前的时刻，车轮力包括车辆的前轴横向力、后轴横向力、前轴纵向力和后轴纵向力，根据车辆在第一时刻的车轮力确定车辆在第一时刻的侧偏角最终值。根据本申请实施例，能够提升车身侧偏角测量的准确性。
",B60W40/10,经纬恒润(天津)研究开发有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"方法, 包括, 装置, 设备",车辆
CN116859212A,CN202310765367.1,"本申请提供了一种负载诊断电路及负载诊断方法，其中方法包括：获取所述负载的反馈电流流经第一阻值的电阻后的第一反馈值，以及所述负载的反馈电流流经第二阻值的电阻后的第二反馈值，所述第一阻值大于第一预设阈值，所述第二阻值小于第二预设阈值；基于所述第一反馈值、所述第二反馈值和预设的状态阈值，确定所述负载的多个单次工作状态，工作状态包括正常、开路、短路中的一种；基于多个所述单次工作状态的区别，确定所述负载的当前工作状态。通过上述方法，能够实现不同功率大小的负载的工作状态诊断，无需区分负载的功率大小，在进行负载诊断时，使用场景更为丰富。
",G01R31/28,天津经纬恒润科技有限公司,模拟数字转换板卡技术,2.0,IPC分类号匹配: G01R31/28,"提供, 实现, 包括, 方法, 电路",诊断
CN116767240A,CN202310712602.9,"本申请公开了一种车轮轮速修正方法、装置及电子设备，其中方法包括：在车辆正在进行转向的情况下，获取车辆在当前采样时刻测得的车轮轮速；利用车轮轮速，对第一左前轮轮速和第一右前轮轮速进行修正，获得第二左前轮轮速和第二右前轮轮速；根据第二左前轮轮速与上一采样时刻修正后的左前轮轮速之间的差值，确定当前采样时刻修正后的修正左前轮轮速；根据第二右前轮轮速与上一采样时刻修正后的右前轮轮速之间的差值，确定当前采样时刻修正后的修正右前轮轮速。通过上述步骤，在车辆正在进行转向的情况下对左前轮轮速和右前轮轮速进行修正，可以输出不含车轮转角角速度影响的车轮轮速，从而提高车轮轮速输出的准确性。
",B60W40/105,经纬恒润(天津)研究开发有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"方法, 包括, 装置, 设备",车辆
CN116614611A,CN202310542355.2,"本公开提供的一种图像高分辨率支持方法、装置和电子设备，可以获得正交像素图像；对正交像素图像进行电光转换处理，获得第一光空间信号；对第一光空间信号进行抗混叠低通滤波处理，获得第二光空间信号；对第二光空间信号进行光电转换处理，获得第一菱形像素图像；对第一菱形像素图像进行菱形降采样，获得第二菱形像素图像。本公开利用菱形像素图像的像素总量小于正交像素图像的特点，提供正交像素图像转换为菱形像素图像的技术过程，能够降低DMD芯片的尺寸对高分辨率图像的图像投影效果的影响，为高分辨率图像提供有效支持，从而提升图像投影后的视觉效果。
",H04N9/31,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 装置, 方法, 处理, 设备",通用
CN116176585A,CN202211718476.X,"本申请公开了一种车速控制方法及装置。该方法包括：在目标车辆的GPS信号和辅助修正信息异常的情况下，获取目标车辆的目标速度信息，根据目标速度信息和预设误差数据，确定目标车辆在当前采样周期内的目标位移误差，预设误差数据包括目标车辆的速度信息与位移误差之间的对应关系，根据目标位移误差和目标车辆的历史位移误差，确定累积误差，历史位移误差包括目标车辆在多个历史采样周期内的位移误差，在累积误差大于目标误差阈值的情况下，控制目标车辆的车速为目标预设车速。这样，可以提高目标车辆的安全性，降低发生危险的概率。
",B60W30/18,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"方法, 包括, 装置, 控制","车辆, 控制"
CN115833538A,CN202211699633.7,"本申请提供的一种单相逆变器的调制方法、装置、设备及存储介质。其中，在一种单向逆变器的调制方法中，根据单相逆变器的一个波形周期内数字信号的幅值与时间点的对应关系，生成波形数据表；根据波形数据表的第一存储顺序，将波形数据表中的数字信号转化为正弦波模拟信号。上述方法通过数字信号的幅值与时间点的对应关系可以直接将数字信号转化为正弦波模拟信号，无需通过复杂运算，降低单相逆变器生成调制波的性能需求，提高单相逆变器的调制效率。
",H02M1/08,经纬恒润(天津)研究开发有限公司,模拟数字转换板卡技术,2.0,"关键词匹配: 模拟信号, 数字信号","提供, 装置, 生成, 方法, 设备, 模拟",通用
CN115733904A,CN202211550432.0,"本申请公开了一种文件操作方法及装置，涉及通信技术领域。该文件操作方法应用于多核设备，该文件操作方法包括：接收第一文件操作请求，其中，所述第一文件操作请求由目标应用程序发起；调用与所述第一文件操作请求匹配的文件操作接口，经过基于锁与优先级的程序控制，将所述第一文件操作请求发往协议解析控制器，以使所述协议解析控制器将所述第一文件操作请求按照预设协议进行封装，生成第一报文，并将所述第一报文通过核间通信缓存发往服务器。根据本申请实施例，能够基于多核设备实现高实时性且轻量化的嵌入式核间远程文件操作，解决了部分CPU核心无法直接操作存储介质进行文件管理的问题。
",H04L69/22,经纬恒润(天津)研究开发有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 量化,"实现, 包括, 装置, 方法, 生成, 设备, 控制","通信, 控制"
CN114070149A,CN202111334204.5,"本公开提供的一种电机母线电流确定方法及装置，可以根据第一PWM开关控制周期的上一开关控制周期的电压矢量的作用时间确定的采样时刻触发采样事件，获得单电阻母线电流采样电路在第一PWM开关控制周期的采样电阻电压，对采样电阻电压进行偏置修正和逆派克变换，得到各采样电阻电压相应的电压矢量。基于空间矢量脉宽调制划分的扇区范围，计算各采样电阻电压相应的电压矢量所在的扇区对应的相电流。利用空间矢量脉宽调制，依据平均等效原理计算各采样电阻电压相应的电压矢量的作用时间。根据各采样电阻电压相应的电压矢量的作用时间和对应的相电流计算出第一PWM开关控制周期对应的第一电机母线电流值，提高计算出的电机母线电流的准确性。
",H02P21/14,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 装置, 方法, 控制, 计算, 电路",控制
CN114020071A,CN202111325931.5,"本发明提供一种恒温系统及恒温控制方法，该恒温系统包括：恒温装置、温度控制电路、温度检测电路和主控制器；恒温装置至少包括恒温腔体和TEC；恒温装置、温度控制电路、温度检测电路和主控制器之间对应连接；温度检测电路将测量到的温度值输入至主控制器；主控制器将温度值转换为PWM信号输入至温度控制电路；温度控制电路将PWM信号转换为电流信号，使TEC基于电流信号控制恒温腔体的温度处于目标温度值。在本方案中，将恒温腔体的内部温度和TEC的下表面温度作为控制目标，并对检测到的温度值进行处理，从而控制恒温腔体的内部温度和TEC的下表面温度处于目标温度值，实现高精度的恒温控制。
",G05D23/30,经纬恒润(天津)研究开发有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 信号转换,"提供, 实现, 包括, 装置, 方法, 处理, 控制, 系统, 检测, 电路","检测, 控制"
CN113485197A,CN202110829061.9,"本发明公开了一种车门窗电流标定方法、装置及系统，该方法主要包括：获取车门窗的负载电流；所述负载电流为车门窗电流采样电路中的采样电阻的电流；将所述车门窗的负载电流代入目标函数进行计算，获得所述车门窗的运行电流；所述目标函数为基于车门窗电流采样电路中负载电流和运行电流之间的映射关系得到的拟合一次函数，本发明能够解决现有技术检测车门窗运行电流精度较低的问题，通过提高门窗运行电流的采集精度，从而实现对门窗运行过程中的过流和堵转保护，降低了门窗控制器对门窗运行时的过流和堵转保护不及时对门窗电机的损害，延长了门窗电机的运行寿命。
",G05B19/042,北京经纬恒润科技股份有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"实现, 包括, 装置, 方法, 控制, 系统, 检测, 计算, 电路","检测, 控制"
CN111404534A,CN202010191455.1,"本发明的实施例提供了一种电容式触摸开关的触发判断方法及装置，方法包括将实际电容信号与参考电容信号的差值作为相对电容信号；判断相对电容信号是否满足触发条件，触发条件包括相对电容信号在一段时间内单调上升趋势，以及在这一段时间内的相对电容信号均大于预设的第一电容阈值且小于预设的第二电容阈值。相对于现有技术中在一段时间内相对电容信号均大于阈值就确定满足触发条件的方案，本发明还判断相对电容信号是否是单调上升趋势以及是否小于一个较大的阈值，在不增加硬件成本的前提下，识别到造成的电容信号变化趋势与正常触发时的电容信号变化趋势相似的一些干扰信号，提高了触发判断的准确度，进而降低了控制系统异常动作的发生概率。
",H03K17/96,北京经纬恒润科技有限公司,模拟数字转换板卡技术,2.0,IPC分类号匹配: H03K17/96,"提供, 包括, 装置, 方法, 控制, 系统",控制
CN111145355A,CN201911358271.3,"本发明提供道路网格数据生成方法及装置，以实现将OpenDRIVE地图文件中的内容转换成道路网格数据。上述方法包括：确定目标道路的参考线采样位置；沿每一参考线采样位置的垂直方向将目标道路切分为道路网格；提取各道路网格顶点的三维坐标信息；其中，目标道路的参考线为目标参考线；参考线采样位置包括第一类采样位置和第二类采样位置；第一类采样位置至少包括：目标参考线的起点和终点；第二类采样位置包括：位于相邻第一类采样位置之间的采样位置；其中，任一第二类采样位置与上一参考线采样位置之间的步长，由上一参考线采样位置的道路曲率估计值计算得到；上一参考线采样位置为第一类采样位置或第二类采样位置。
",G06T17/20,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 实现, 包括, 装置, 方法, 生成, 计算",通用
CN110962930A,CN201911315407.2,"本发明提供一种方向盘转角解析方法及装置，应用于汽车技术领域，该方法包括在获取得到基准角度之后，将基准角度对应的采样间隔作为起始采样间隔，并将基准角度作为起始采样间隔的方向盘转角，然后在起始采样间隔之后的每一个采样间隔，确定当前采样间隔对应的解析角度变化量，并将当前采样间隔对应的解析角度变化量与上一采样间隔的方向盘转角之和，作为当前采样间隔的方向盘转角，本方法以基准角度作为起始采样间隔的方向盘转角，在该基准角度的基础上，通过累加的方式计算得到每一个采样间隔所对应的方向盘转角，并非现有技术中的周期循环计数，不受角度传感器信号按照预设周期往复循环的影响，扩大了方向盘转角的解析范围。
",B62D15/02,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 包括, 装置, 方法, 计算","汽车, 传感器"
CN109649489A,CN201811572559.6,"本发明提供了一种车辆转向状态的识别方法、装置、电子设备及存储介质，用于在车辆行驶过程中识别车辆的转弯和/或掉头行为。该方法包括：获取历史航向角数据，历史航向角数据包括按预设的规律选取的多个采样位置对应的航向角，其中包括与车辆当前位置距离最近的采样位置对应的航向角，采样位置按满足预设规律的里程间隔选取；根据历史航向角数据，确定车辆是否发生行驶方向变更。通过上述方案，本发明实现了基于车辆航向角的对车辆转向状态的准确识别。
",B62D15/02,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 实现, 包括, 装置, 方法, 设备",车辆
CN109633509A,CN201811599515.2,"本发明提供的电机故障诊断系统、方法及可读存储介质，系统包括：控制器，以及依次串联的电源、电子开关、第一电阻、第二电阻、第三电阻；三电阻远离第二电阻的一端接地；第一电阻和第二电阻的连接节点，与霍尔传感器的供电电路的输出端连接；控制器控制电子开关的导通和断开，控制供电电路是否给霍尔传感器供电，持续获取所述第二电阻和所述第三电阻的连接节点的电压。本发明的技术方案，控制器控制霍尔传感器供电情况以及通过对采样电阻即第三电阻两端电压的采集，对霍尔传感器的供电电压进行分析，可以确定霍尔传感器的供电电路是否存在故障，避免了由于霍尔传感器的供电电路存在故障导致的堵转的误检，进而减少了对电机损伤。
",G01R35/00,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 包括, 方法, 控制, 系统, 电路","传感器, 诊断, 控制"
CN109618457A,CN201811622014.1,"本发明提供的灯具控制电路及其控制方法，设置与灯具并联的分压电阻，以及在驱动电路停止为灯具提供驱动电流时的电源，控制器通过判断分压电阻的电压是否大于开路诊断电压阈值，确定灯具是否处于开路。通过合理设置分压电阻之间的阻值关系，使得电压在灯具正常工作与开路时存在较大差异，控制器根据灯具正常工作时与开路时，采样电压的不同实现对LED灯具的开路诊断。
",H05B33/08,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 实现, 方法, 设置, 控制, 电路","诊断, 控制"
CN109506816A,CN201811431280.6,"本发明提供了一种转矩测量装置和测量方法，包括光源、光传输线路和光电位置传感器；光传输线路沿轴向固定在转轴上且随转轴的扭转而扭转；光源用于向光传输线路的输入端输入入射光线；光电位置传感器用于获取光传输线路的出射光线照射在光电位置传感器上的实际位置，以根据实际位置与基准位置之间的位移计算得到转轴的转矩；其中，基准位置为转轴未发生扭转形变时，光传输线路的出射光线照射在光电位置传感器上的位置。由于本发明所提供的转矩测量方法和测量装置，不需要在转轴上布置电子电路元件，也无需对转轴进行供电、采样或通信等，因此，不仅抗干扰能力强，而且结构简单，应用范围更宽广。
",G01L3/08,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 包括, 装置, 方法, 计算, 电路","通信, 传感器"
CN109031321A,CN201810673412.X,"本发明提供的超声波回波模拟方法、装置及系统，超声波回波模拟装置与停车距离控制PDC系统中的PDC控制装置之间，基于协议通信线进行直连通信；所述方法包括：基于所述协议通信线，检测所述PDC控制装置发出的超声波发射指令；当检测到所述超声波发射指令时，执行计时操作；当计时时长达到预设时长时，基于所述协议通信线，向所述PDC控制装置发出回波模拟信号。本发明能够有效解决采用超声波探头对贴方式进行通信时可靠性与稳定性较差的问题，并有效避免在超声波探头对贴方式下由于超声波信号传输易受干扰而导致测试结果出错的情况，从而保证了PDC系统功能测试结果的准确性。
",G01S15/93,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 模拟信号,"提供, 包括, 测试, 装置, 方法, 控制, 系统, 检测, 模拟","检测, 测试, 通信, 控制"
CN108032748A,CN201711235299.9,"本发明公开了一种充电终端与整车的通信交互方法及装置，该方法包括：当所述整车上的无线充电装置与所述充电终端的相对位置满足充电要求时，获取所述充电终端发送的认证密钥；判断所述认证密钥是否与第一预设密钥匹配；若是，通过所述无线充电装置产生的充电电磁场对所述充电终端进行无线充电，同时通过所述充电电磁场中调制的数字信号实现所述充电终端与整车的通信。可见，本实施例所提供的技术方案，不仅实现了无线充电装置对充电终端的充电功能，还实现了除充电之外的充电终端与整车的互动通信。
",B60L11/18,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 数字信号,"提供, 实现, 包括, 装置, 方法",通信
CN107994830A,CN201711456044.5,"本发明公开了一种抑制电机转矩纹波的方法及装置，通过以电机电角度作为自变量，在位置域构建增广系统，以及以电机电角速度作为中间参数构建有限元网格，对增广系统设计镇定器，将无限维矩阵不等式转换为有限维矩阵，然后根据周期性获取的电机位置采样信号得到第一目标控制参数矩阵和第二目标控制参数矩阵，进而根据更新后的控制参数矩阵和电机电流值计算输出量，按照输出量控制电机的运行，抑制电机的转矩纹波，即实现了将电机转矩纹波抑制问题与基于转角变化系统的跟踪控制问题相联系，以实现有效抑制电机转矩纹波，降低功率损耗、提升系统效率，从而改善驾乘舒适度。
",H02P21/18,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"实现, 装置, 方法, 控制, 系统, 计算",控制
CN107766653A,CN201710993546.5,"本申请提供了一种汽车电源线瞬态干扰源的仿真电路的构建方法及装置，方法包括：根据待仿真的瞬态干扰源的波形，确定电压源模型；基于波形的特征，确定电压源模型的参数，获得具有参数的电压源模型作为用于模拟波形的电压源模型；通过用于模拟波形的电压源模型，以及，用于模拟待仿真的瞬态干扰源的内阻的电阻，构建待仿真的瞬态干扰源的仿真电路。本申请提供的方法及装置可准确的模拟汽车电源线瞬态干扰，这使得在电路设计阶段对产品瞬态抗干扰的性能进行量化评估成为可能，从而避免把风险遗留到后期测试阶段，提高了研发效率，降低了设计风险，减少了后期测试阶段出现问题带来的时间和成本。
",G06F17/50,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 量化,"提供, 包括, 模型, 装置, 测试, 具有, 仿真, 方法, 电路, 模拟","测试, 汽车"
CN106899255A,CN201710203125.8,"本发明公开了一种闭合部件防夹检测方法及装置，所述方法包括：实时获取PWM信号的占空比和电机的转速；判断第一时刻的占空比是否大于或等于预设最大占空比；若是，则根据所述电机的转速变化，确定等效占空比；根据所述等效占空比和预先得到的无障碍占空比，获得所述闭合部件在所述第一时刻的防夹力；比较所述防夹力与预先得到的防夹力阈值；当所述防夹力大于所述防夹力阈值时，发送防夹信号，所述防夹信号用于控制所述电机停止或反转。本发明使用PWM占空比信号作为判断闭合部件阻力变化的依据，即使在占空比饱和时仍能够准确得出闭合部件的防夹力，无需增加额外的电流采样电路来判断闭合部件是否遇到障碍物的阻碍，降低了硬件成本。
",H02P29/00,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"包括, 装置, 方法, 控制, 检测, 电路","检测, 控制"
CN106899254A,CN201710203105.0,"本发明公开了一种闭合部件防夹检测方法，包括：获取PWM信号的当前占空比，所述PWM信号用于控制电机转动以带动闭合部件动作；根据所述当前占空比和预先得到的无障碍占空比，获得所述闭合部件的当前防夹力；比较所述当前防夹力与预先得到的防夹力阈值；当所述当前防夹力大于所述防夹力阈值时，发送防夹信号，所述防夹信号用于控制所述电机停止或反转。本发明使用PWM占空比信号作为判断闭合部件阻力变化的依据，无需增加额外的电流采样电路来判断闭合部件是否遇到障碍物的阻碍，降低了硬件成本。
",H02P29/00,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"包括, 方法, 控制, 检测, 电路","检测, 控制"
CN106596080A,CN201611154755.2,"本发明提供了一种前照灯的测试系统，该测试系统包括：控制开关模块，用于切换测试对象、切换测试模式及控制测试对象中步进电机的运行方向；控制开关模块包括：测试对象切换开关，用于切换测试对象至ALS系统或AFS系统；主控制模块，用于当测试对象切换至ALS系统时，发送步进电机驱动信号至ALS系统驱动模块；当测试对象切换至AFS系统时，发送步进电机驱动信号至AFS系统，驱动AFS系统中的直线步进电机和水平旋转步进电机运行；ALS系统驱动模块用于将接收到的步进电机驱动信号转换成脉冲电流，驱动ALS系统中的直线步进电机运行。通过本发明提供的一种前照灯的测试系统可以实现对ALS系统及AFS系统的兼容检测。
",G01M11/06,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 信号转换,"提供, 实现, 包括, 测试, 控制, 系统, 检测","检测, 测试, 控制"
CN105635012A,CN201510992503.6,"本发明公开了一种频移键控FSK波形文件的生成方法及装置，确定生成FSK波形所需的波形信息以及数字信号序列；根据所述波形信息以及所述数字信号序列生成二进制FSK波形；根据预设算法对所述二进制FSK波形进行运算，生成十六进制FSK波形；判断所述十六进制FSK波形的大小是否小于或等于预设文件大小，并得到第一判断结果；当所述第一判断结果表示所述十六进制FSK波形的大小小于或等于预设文件大小时，生成FSK波形文件。采用上述方法及装置生成的FSK波形文件能够适用于各种型号的波形信号发生器。
",H04L27/12,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 数字信号,"生成, 方法, 装置, 算法",通用
CN105606062A,CN201610140989.5,"本发明实施例提供一种路面粗糙度识别方法及装置，该方法包括：采样至少一个前轴轴高位移，一个采样时刻对应采样一个前轴轴高位移；确定各采样时刻对应的前轴轴高位移差；根据所述各采样时刻对应的前轴轴高位移差，确定前轴轴高曲线拐点数及前轴轴高曲线幅值总和；及根据各采样时刻对应的前轴轴高位移，确定前轴轴高曲线拟合斜率及前轴轴高曲线拟合方差；分别对所述前轴轴高曲线拐点数，所述前轴轴高曲线幅值总和，所述前轴轴高曲线拟合斜率，所述前轴轴高曲线拟合方差进行模糊化处理，得到模糊化处理后的粗糙度；从所述模糊化处理后的粗糙度中确定路面粗糙度。本发明实施例可提升路面粗糙度的识别准确度，并降低使用成本。
",G01B21/30,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 包括, 装置, 方法, 处理",通用
CN105429550A,CN201511032636.5,"本申请公开了一种电流采样偏差的修正方法及系统，将电动助力转向系统EPS控制器的电压分别加载在多个不同功率的电阻上，检测电压被分别加载在多个不同功率的电阻上时产生的多个不同的检测电流值，并获取EPS控制器采样的多个不同的采样电流值；利用多个不同的检测电流值和多个不同的采样电流值拟合得到EPS控制器的电流采样系数和电流采样偏置值；将电流采样系数和电流采样偏置值写入EPS控制器的微处理单元MCU中，以对MCU中预先存储的初始电流采样系数和初始电流采样偏置值进行修正。本申请方案可以提升不同的EPS控制器的成品的一致性，保证装车后各EPS控制器控制的助力手感的一致性。
",H02P23/14,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"方法, 处理, 控制, 系统, 检测","检测, 控制"
CN103929249A,CN201410153017.0,"本发明公开了一种远程虚拟直连数据传输方法及系统，通过源端的设备采集电气特性的待传输信号，可以完全保证信号的高保真性，且将所述电气特性的待传输信号转换为光特性的待传输信号，将所述光特性的待传输信号通过光纤以太网传输至目的端的设备，所述目的端的设备将所述光特性的待传输信号转换为电气特性的信号以输出至外部电子系统，通过光纤以太网传输信号，可以使得传输距离更远。
",H04B10/25,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 信号转换,"系统, 方法, 设备",数据传输
CN103825591A,CN201410091578.2,"本发明实施例公开了一种开关型霍尔芯片，包括用于生成具有正温度系数的激励源的激励源产生电路；比较器；与比较器的输出端相连的输出驱动电路；第一激励电流端接激励源产生电路、第二激励电流端接地、第一霍尔输出端接比较器的反相输入端、第二霍尔输出端接比较器的同相输入端的霍尔元件；用于抽取具有负温度系数的磁场迟滞的阈值电流的磁场阈值控制电路；连接磁场阈值控制电路和第二霍尔输出端的第二受控开关；连接磁场阈值控制电路和第一霍尔输出端的第一受控开关；以及分别与输出驱动电路、第一受控开关和第二受控开关相连的逻辑控制电路，以实现对所述开关型霍尔芯片的开启阈值和关闭阈值进行温度补偿。
",H03K17/14,北京经纬恒润科技有限公司,模拟数字转换板卡技术,2.0,IPC分类号匹配: H03K17/14,"实现, 包括, 具有, 生成, 控制, 电路",控制
CN103645658A,CN201310607831.0,"本发明实施例提供一种多片信号转换器的相位同步方法、装置及FPGA控制器，其中方法包括：可调延时单元接收多片信号转换器中的任一信号转换器的DCO信号；局部时钟缓冲器对可调延时单元接收的DCO信号进行分频，以通过分频后的信号驱动多个随路时钟接收模块接收各信号转换器的DCO信号；各随路时钟接收模块将所接收的DCO信号发送至相位检测逻辑单元；相位检测逻辑单元比对主片信号转换器与从片信号转换器的DCO信号的相位关系；信号转换器控制单元根据所述相位关系，将从片信号转换器的DCO信号的相位，调整为与主片信号转换器的DCO信号的相位相同步。本发明填补了现有信号转换器没有相位同步设计的缺陷，降低了电路调试难度。
",G05B19/042,北京经纬恒润科技有限公司,模拟数字转换板卡技术,2.0,"关键词匹配: 转换器, 信号转换","提供, 包括, 装置, 方法, 控制, 检测, 电路","检测, 控制"
CN103390877A,CN201310342136.6,"本申请提供了一种过流检测及保护电路，包括：驱动单元、采样单元、信号产生单元、关闭单元和保持单元，其中，驱动单元用于驱动负载电路；采样单元用于采集负载电路回路中的电流；信号产生单元用于当负载电路回路中的电流大于预设值时，产生过流信号；关闭单元用于在接收到过流信号时，关闭驱动单元；保持单元用于保持驱动单元的关闭状态，直至接收到MCU发送的开启驱动单元的控制信号。本申请提供的过流检测及保护电路，在检测到负载电路回路中的电流大于预设值，能快速关闭驱动单元并锁定关闭状态，从而实现对电路的保护。
",H02H3/08,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 实现, 包括, 控制, 检测, 电路","检测, 控制"
CN103326702A,CN201310215688.0,"本发明公开的霍尔开关电路，通过振荡器为控制电路提供采样时钟信号，由控制电路控制输出模块输出迟滞控制信号至偏置电路，再由偏置电路输出迟滞电流信号及供电电压信号至霍尔盘电路；通过霍尔盘电路感应磁场强度，并进行初步失调消除；再通过迟滞比较器接收所述霍尔盘电路输出的霍尔电压并进行放大，由控制电路控制迟滞比较器进行极性检测及失调消除；最后通过输出模块将所述迟滞比较器的输出信号转换为数字信号进行输出。本发明公开的霍尔开关电路，通过上述各个模块电路配合实现对霍尔开关的全极性检测及失调消除，而不需要单独的极性检测电路及失调处理电路，解决了现有技术中以大成本实现极性检测的问题。
",H03K17/90,北京经纬恒润科技有限公司,模拟数字转换板卡技术,5.0,"关键词匹配: 采样, 信号转换, 数字信号; IPC分类号匹配: H03K17/90","提供, 实现, 处理, 控制, 检测, 电路","检测, 控制"
CN103235567A,CN201310109500.4,"本发明提供了一种标定数据的传输方法、装置及系统，本发明通过对标定数据进行编码得到BT656格式的数字信号，并将BT656格式的数字信号通过数模转换器转换为CVBS信号，再通过视频信号线传输所述CVBS信号。本发明通过将标定数据在视频信号线传输，而不是在CAN总线传输，由实验可知，本发明提供的视频信号线传输标定数据的速率约为20.736Mbps，而现有技术中通过CAN总线传输标定数据的速率最大为1Mbps，因此通过本发明发送标定数据的速率是CAN总线的传输标定数据的速率的20多倍，大大的减小了标定数据的传输时间，进而使标定数据的传输时间满足现代化生产线的要求。
",G05B19/418,北京经纬恒润科技有限公司,模拟数字转换板卡技术,3.0,"关键词匹配: 数模转换, 转换器, 数字信号","系统, 提供, 方法, 装置",通用
CN102901865A,CN201210429996.9,"本发明公开了一种电机相电流检测电路，包括：开关模块，差分放大模块，模/数转换模块和控制模块，其中：所述开关模块中具有多个开关单元，多个所述开关单元并联，每个开关单元包括至少一个开关和至少一个电阻；所述差分放大模块，与所述开关模块相连，用于将所述开关模块两端的电压差进行差分放大，得到差分放大后的电压信号；所述模/数转换模块，与所述差分放大模块的输出端相连，用于将模拟形式的所述电压信号转换为数字形式；所述控制模块，与所述模/数转换模块的输出端相连，用于根据所述电压信号，控制所述开关模块中各个开关单元的状态，从而改变接入相线间电阻的大小，提高了测量精度。
",G01R19/25,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 信号转换,"包括, 具有, 控制, 检测, 电路, 模拟","检测, 控制"
CN102768299A,CN201210265593.5,"本发明公开了一种高端电流采样电路，该高端电流采样电路包括采样电阻R4和差分放大电路，其中：所述采样电阻R4，串联在被采样电路的高电压端；所述差分放大电路，连接在所述采样电阻R4的两端，且所述差分放大电路包括接地的接地端，用于对所述采样电阻R4两端的电压进行放大并输出，其中，放大后的电压以所述接地端为参考点。由于本发明的差分放大电路包括有接地的接地端，经所述差分放大电路放大后的电压即采样电压以所述接地端为参考点，从而避免了现有技术中的高端电流采样电路以电源作为参考进行采样的弊端，有效地转换了技术难点。
",G01R19/00,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"包括, 电路",通用
CN102611372A,CN201210062105.0,"本发明提供了一种步进电机驱动器反馈电流获取方法及系统，所述方法包括：获取采样电阻两端的采样电压；对所述采样电压进行偏置放大处理，获得与所述采样电压相对应的偏置放大电压，并对所述偏置放大电压进行模数转换，将所述偏置放大电压转换为数字信号电压；依据所述采样电压、所述数字信号电压以及所述步进电机驱动器运行过程中的驱动参数获取反馈电流。本发明提供的一种步进电机驱动器反馈电流获取方法及系统，将驱动器自身特性等因素对反馈电流传输的影响考虑到反馈电流的获取过程中，提高了反馈电流的精度，从而提高驱动器的控制精度，能够迅速改变驱动器所驱动的电机的最大输出力矩，对外提供负载服务。
",H02P8/12,北京经纬恒润科技有限公司,模拟数字转换板卡技术,3.0,"关键词匹配: 模数转换, 采样, 数字信号","提供, 包括, 方法, 处理, 控制, 系统",控制
CN102611077A,CN201210062086.1,"本发明公开一种车门窗电机堵转保护方法和系统，该方法包括：控制器接收用户输入的车门窗控制指令，确定该车门窗指令类型，并输出与车门窗指令类型相对应的控制信号，驱动电路依据控制信号驱动车门窗电机旋转，实现车门窗的上升或下降，采样调整电路采集车门窗电机的当前时刻电压值，确定该电压值为当前时刻反馈电压值并将当前时刻反馈电压值转换为当前时刻反馈电流值，并发送给控制器，如果当前时刻反馈电流值大于依据当前车门窗电机的工作状态和当前控制器的电压值设定的电机堵转阈值，且持续时间超出第一预设时间时，所述车门窗电机发生堵转，该方法应用于系统中，有效的保护了车门窗电机，提高了车门窗电机保护防范的可靠性。
",H02H7/085,北京经纬恒润科技有限公司,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"实现, 包括, 方法, 控制, 系统, 电路",控制
CN102436840A,CN201110409931.3,"本发明提供的数字射频存储板的模拟/数字转换器模块用于高速采集外部模拟信号，将其转换为数字信号并传输到第一FPGA模块，第一FPGA模块用于将数字信号进行一级可编程处理后传输给第二FPGA模块，第二FPGA模块用于将数据进行二级可编程处理，最后将数据传输给数字/模拟转换器模块，数字/模拟转换器模块用于将数字信号高速转换为模拟信号并且输出。其中，第一SRAM模块与所述第一FPGA模块连接，用于缓存第一FPGA模块的数据；第二SRAM模块与所述第二FPGA模块连接，用于缓存第二FPGA模块的数据。本发明的数字射频存储板具有高速数据采集和回放能力，能够灵活实现数据采集、存储、传输和回放功能。
",G11C5/00,北京经纬恒润科技有限公司,模拟数字转换板卡技术,3.0,"关键词匹配: 转换器, 模拟信号, 数字信号","提供, 实现, 具有, 处理, 模拟",数据传输
