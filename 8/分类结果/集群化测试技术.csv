﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN120449812A,CN202510409688.7,"本说明书公开一种电路原理图电容电气安全的自动检查方法及装置，方案可以包括：对EDIF200格式的目标电路原理图进行解析，根据解析结果中电容器件的属性参数生成电容参数矩阵Cap_data，所述电容参数矩阵Cap_data的列索引包括电容位号以及额定电压、电容引脚名称及电容引脚所连接的电气网络名称；确定所述电容参数矩阵Cap_data中各个电容的供电电压，将得到的各个电容的供电电压存储至电容供电电压矩阵Cap_power中；基于所述电容参数矩阵Cap_data以及所述电容供电电压矩阵Cap_power计算所述电容参数矩阵Cap_data中各个电容的电压降额因子，将计算所得的电压降额因子超过预设阈值的电容的电容位号进行标记。
",G06F30/398,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G06F30/398,"包括, 装置, 生成, 方法, 计算, 电路",网络
CN120449813A,CN202510409770.X,"本说明书公开一种电路原理图差分网络正确性的自动审查方法及装置，方案可以包括：输入EDIF200标准格式的电路原理图，解析该原理图文件，获取芯片管脚、阻容电感及相关电气网络数据，存储到芯片器件信息矩阵Part_U和非芯片器件信息矩阵Part_other。接着，依据设定的标准筛选差分管脚，存入矩阵Dif_pin。然后，拆分矩阵Dif_pin为矩阵Dif_pin1和矩阵Dif_pin2，遍历获取差分路径，存入矩阵DiffNet1和矩阵DiffNet2矩阵。之后，基于矩阵DiffNet1和矩阵DiffNet2，审查差分网络正确性和命名规范性，将不正确路径存入Diff_no_cor矩阵，命名不规范路径存入Diff_name_no_cor矩阵。最后，将两个矩阵中的路径转换为字符串并添加文字说明输出检查结果。
",G06F30/398,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G06F30/398,"方法, 包括, 装置, 电路",网络
CN120386725A,CN202510341561.6,"本申请提供一种车载软件的自动化测试系统，系统包括：测试用例管理单元，用于确定待执行的目标测试用例，将目标测试用例发送给编译单元，目标测试用例包括目标测试代码文件和目标配置文件；编译单元，用于根据目标配置文件，对目标测试代码文件和目标测试代码中涉及的源代码文件进行编译，获得目标测试用例对应的可执行文件；执行单元，用于在与安装车载软件的车载控制器建立连接后，执行可执行文件，从车载控制器预先建立的测试数组中读取测试变量的实际执行结果，将实际执行结果反馈给测试用例管理单元；测试用例管理单元，用于通过将测试变量的实际执行结果和期望执行结果进行比较，确定针对测试变量的测试结果。
",G06F11/3668,北京经纬恒润科技股份有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G06F11/3668,"提供, 测试, 包括, 控制, 系统, 配置","测试, 控制"
CN120257932A,CN202510279961.9,"本申请公开了一种目标交叉线的确定方法及装置，该方法包括：从电路原理图文件中获取多个连线信息，电路原理图文件的格式为电子设计交换格式，连线信息包括每个连线的位置信息和电气网络信息；在电路原理图中基于每个连线的位置信息，确定交叉线，其中，交叉线包括至少两个连线；检测交叉线中至少两个连线的电气网络信息的字符相似度，得到检测结果；在检测结果包括交叉线中至少两个连线的电气网络信息的字符相似度大于或等于阈值，且交叉线的交叉点不存在连接标识的情况下，确定交叉线为目标交叉线，上述确定目标交叉线的方式能够使确定的目标交叉的线的效率和准确率更高。
",G06F30/394,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G06F30/394,"包括, 装置, 方法, 检测, 电路","检测, 网络"
CN120255484A,CN202510472906.1,"本申请公开了一种数据的采集方法及系统，包括：从云平台获取所有的诊断模型文件；对每个诊断模型文件进行解析，得到每个诊断模型文件对应的诊断模型；分别针对每个诊断模型文件，周期性利用诊断模型文件对应的诊断模型对车辆进行故障检测；若未通过，则从预先构建的环形队列中获取当前时间点前后两个预设时间段内的所有CAN报文；将所有CAN报文进行压缩打包，得到CAN报文压缩包，并将CAN报文压缩包上传至云平台中进行存储。从而可以利用所有的诊断模型对车辆进行故障检测，有效规避了诊断内容更新导致的车端程序频繁修改问题，并且当检测出故障时，可获取当前时间点前后时间的所有数据，进而凭借所有数据有效地定位出异常发生的原因。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"平台, 包括, 模型, 方法, 系统, 检测","车辆, 检测, 诊断"
CN120166053A,CN202510428485.2,"本申请公开了一种车载时间敏感网络TSN测试系统及方法，该测试系统包括：测试处理设备以及与测试处理设备连接的测试设备，测试处理设备存储有TSN协议族中的至少一个子协议的协议配置信息，测试处理设备与被测试部件连接；测试处理设备基于测试需求信息，在协议配置信息中确定待测试的目标子协议，生成与目标子协议对应的测试指令；测试设备响应接收到测试指令，执行时钟同步配置，生成与测试指令对应的测试报文，将测试报文发送至被测试部件，接收被测试部件的反馈报文；测试处理设备生成与反馈报文相匹配的测试结果。本申请能够测试TSN的多种子协议，并且在测试执行前完成时钟同步，满足实际的测试需求，提升了测试精度和效果。
",H04L43/08,北京经纬恒润科技股份有限公司,集群化测试技术,1.0,关键词匹配: 测试系统,"包括, 测试, 方法, 生成, 处理, 设备, 系统, 配置","测试, 网络"
CN120124229A,CN202510158353.2,"本申请公开了一种电源树框图生成方法及装置，方法包括：基于多个电源的参数信息，确定电源等级以及电源序号；从外部电源开始，依次遍历外部电源的所有下级电源，并基于遍历到的每个电源的参数信息绘制对应的转换表示框和电源连接端点，直至遍历完所有下级电源；基于用电设备信息，依次绘制多个用电设备的用电表示框；用电表示框包括至少一个用电管脚；基于供电网络信息，确定每个电源连接端点分别对应的用电管脚以及各个用电管脚的上电顺序；在每个电源连接端点与对应的用电管脚之间绘制供电网络的连接线；连接线的弯折位置用于表示用电管脚的上电顺序；生成包含电源连接端点和用电表示框的电源树框图。本申请能够快速、准确地生成电源树框图。
",G06F30/18,经纬恒润(天津)研究开发有限公司,集群化测试技术,2.0,IPC分类号匹配: G06F30/18,"包括, 装置, 生成, 方法, 设备",网络
CN120068756A,CN202510099495.6,"本申请公开了一种测试点自动生成方法，该方法包括：获取网表文件，所述网表文件用于存储芯片测试数据；根据预设关键数据读取规则从所述网表文件中读取出多个芯片配置数据集合；利用预设数据处理规则对所述多个芯片配置数据集合进行数据处理，得到候选芯片测试数据集合，所述候选芯片测试数据包括候选芯片标识和对应的候选测试节点数据；确定待测试芯片标识；基于所述待测试芯片标识，从所述候选芯片测试数据集合中提取出与所述待测试芯片标识匹配的候选芯片测试数据作为目标芯片测试数据；根据所述目标芯片测试数据生成所述待测试芯片标识对应的待测试节点数据，能够自动生成准确的待测试节点。
",G06F30/333,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G06F30/333,"包括, 测试, 生成, 方法, 处理, 配置",测试
CN119937507A,CN202411958801.9,"本发明公开一种HIL测试中IO模型生成系统及方法，涉及HIL测试领域，包括：IO信号列表生成工具，用于生成IO信号列表；信号校验模块，用于在信号组中配置E2E校验信息；第一接口设置模块，用于设置故障注入接口；IO模型生成工具，用于生成IO模型；IO模型包括E2E校验模型，其中包括第一故障注入接口和第二故障注入接口；通过第一故障注入接口完成计数器信号处理模型的故障注入；通过第二故障注入接口完成校验计算模型的故障注入；通过回采计数器信号的当前值，分别计算下一帧报文中对应的计数器信号计算值和校验信号计算值，完成信号校验。本发明实现了不同系统下的IO模型自动生成，且实现了E2E校验及其故障注入。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"实现, 包括, 模型, 测试, 方法, 设置, 生成, 处理, 计算, 系统, 工具, 配置",测试
CN119861247A,CN202510096084.1,"本申请提供了一种汽车配电保护测试系统、方法、电子设备及存储介质，该方法可应用于上位机包括：确定待测配电保护通道控制器的配电通道测试配置表，配电通道测试配置表包括多个配电通道测试配置数组，每一个配电通道测试配置数组包括测试配电通道序号、测试电流值以及测试期望关断时间；确定配电通道测试配置表中每一个配电通道测试配置数组对应的测试实际关断时间；分别将每一个配电通道测试配置数组中的测试期望关断时间与对应的测试实际关断时间进行比较，得到待测配电保护通道控制器的配电通道测试结果，解决了现有通过测试人员手动实现测试，测试时间较长以及容易导致测试结果不满足预期效果，无法保证测试结果的准确性的问题。
",G01R31/00,北京经纬恒润科技股份有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G01R31/00,"提供, 实现, 包括, 测试, 方法, 设备, 控制, 系统, 配置","测试, 汽车, 控制"
CN119740536A,CN202411606869.0,"本发明公开一种PCB链路瞬时阻抗的改变方法及装置，该方法确定印刷电路板PCB板上的支路传输线的寄生电容的电容值，基于电容值确定支路传输线上配置的电感的电感值，基于电感值计算得到PCB板上的干路传输线的瞬时阻抗值，根据预设调整规则调高电感值，直至瞬时阻抗值的下降幅度在预设幅度范围内为止，得到调高后的电感值。由此，通过在支路传输线上增加电感，并通过调高所增加的电感的电感值直至瞬时阻抗值的下降幅度在预设幅度范围内为止时得到调高后的电感值，来实现控制支路传输线的电感值来减少支路传输线对干路传输线的瞬时阻抗的影响，使得干路传输线的瞬时阻抗稳定，从而减少反射，保证整体PCB链路的信号质量和信号完整性。
",G06F30/367,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G06F30/367,"实现, 装置, 方法, 控制, 计算, 电路, 配置",控制
CN119740543A,CN202411718979.6,"本发明公开一种确认PCB布线压降的方法及系统，涉及PCB设计领域，包括：设计计算模块；查找电源线包含的所有走线；获取每一条走线所在层的铜箔厚度；获取每一条走线的宽度和长度；获取电源线的电流；将电流、走线的宽度、长度及对应的铜箔厚度输入计算模块；根据电流、走线的宽度、长度及对应的铜箔厚度，计算得到每一条走线的电源压降；将所有走线的电源压降相加，得到电源线的电源压降；根据电路板的设计要求，确认电源线的电源压降是否满足要求。本发明通过设计一个计算模块，利用计算模块可以快速、准确地确认PCB布线的电源压降，帮助layout工程师在设计之初就保证电源压降在允许范围内，提前避免电源压降过大的风险。
",G06F30/394,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G06F30/394,"包括, 方法, 系统, 计算, 电路",通用
CN119556680A,CN202411804329.3,"本发明提供一种整车诊断方法及装置、存储介质及电子设备，应用于网关域控制器，对车辆的传输控制协议端口进行监听，在与传输控制协议端口对应的监听队列不为空的情况下，从监听队列中确定有效TCP连接；确定与有效TCP连接对应的连接信息；激活与连接信息对应的上位机的路由，以及激活车辆的车内以太网节点的路由；接收上位机发送的诊断请求，并基于诊断请求在各个车内以太网节点中确定目标节点；将诊断请求发送至目标节点，并向上位机反馈目标节点的诊断应答信息。网关域控制器实现了上位机与整车的节点之间的跨网段、跨通信协议间的通信转换，无需使用复杂的路由配置即可实现上位机与整车节点的通信转换，使得车辆的网络架构更加的简洁。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 实现, 装置, 方法, 设备, 控制, 配置","诊断, 控制, 车辆, 通信, 网络"
CN119514079A,CN202411521456.2,"本申请公开了一种气路模型的建立方法、装置、设备及存储介质。该方法包括：根据管路的属性建立管路模型，所述管路模型的输入参数为所述管路模型的管路进气温度、管路进气流量和管路排气流量，输出参数为所述管路模型的管路气体温度和管路气体压力；根据阀体的属性建立阀体模型，所述阀体模型的输入参数为所述阀体模型的阀体进气温度、阀体进气压力和阀体排气压力，所述阀体模型的输出参数为所述阀体模型的阀体排气温度和阀体排气流量；将至少一个所述管路模型和至少一个所述阀体模型进行交替连接，得到气路模型。根据本申请实施例，能够对气路系统进行快速建模，提升气路模型的准确性，开展功能测试等工作。
",G06F30/18,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G06F30/18,"包括, 模型, 装置, 测试, 方法, 设备, 系统",测试
CN119472582A,CN202411466319.3,"本申请涉及一种智能配电控制器电性能测试系统及方法，应用于汽车电子技术领域，系统包括：智能配电控制器、供电子系统、自动化控制子系统、数据采集子系统、信号模拟子系统、回路切换子系统和负载模拟子系统。智能配电控制器分别与供电子系统、信号模拟子系统和回路切换子系统连接；负载模拟子系统与回路切换子系统连接；自动化控制子系统分别和回路切换子系统、信号模拟子系统、数据采集子系统和负载模拟子系统连接；自动化控制子系统，用于在运行与智能配电控制器的配电回路类型对应的自动化测试序列的过程中，分别向信号模拟子系统、回路切换子系统、负载模拟子系统、数据采集子系统和供电子系统发送控制指令，输出测试结果。可提高测试效率。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G05B23/02,"包括, 测试, 方法, 控制, 系统, 模拟","测试, 汽车, 控制"
CN119126750A,CN202411230254.2,"本申请公开了一种硬件模块故障定位方法、装置、设备、介质及产品，涉及车辆技术领域。硬件模块故障定位方法包括：接收车载目标硬件模块上报的模块状态；根据预先设定的硬件模块与模块编码的对应关系以及模块状态和状态编码的对应关系，生成与车载目标硬件模块上报的模块状态对应的第一状态码；存储第一状态码，以存储车载目标硬件模块的模块状态；当车辆发生故障时，通过第一状态码，定位硬件模块故障。根据本申请公开的方案，能够提高车辆故障诊断效率。
",G05B23/02,经纬恒润(天津)研究开发有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"包括, 装置, 生成, 方法, 设备","车辆, 诊断"
CN119089729A,CN202411017725.1,"本申请提供了一种仿真建模方法及装置，通过对预获取的车辆衬套在多个方向下分别对应的刚度关系进行参数匹配，生成车辆衬套的等效模型，刚度关系用于表征车辆衬套的位移与力之间的对应关系；基于等效模型和预设有限元构建规则，构建车辆衬套的第一有限元模型，第一有限元模型为一维有限元模型；基于预获取的车辆推力杆的实体结构，构建车辆推力杆的第二有限元模型，第二有限元模型为三维有限元模型，车辆衬套设置于车辆推力杆上；对第一有限元模型和第二有限元模型进行静力学仿真分析，得到分析报告，分析报告包括车辆推力杆在不同工况下的仿真结果。本申请实施例能够提高对衬套仿真分析的准确性。
",G06F30/23,经纬恒润(天津)研究开发有限公司,集群化测试技术,2.0,IPC分类号匹配: G06F30/23,"提供, 包括, 模型, 装置, 仿真, 方法, 设置, 生成",车辆
CN119024806A,CN202410824190.2,"本申请公开了一种车辆检测方法及相关装置，应用于车辆网关，该方法包括：在目标车辆下电后，从CAN总线网络获取目标车辆的多个微控制器ECU各自对应的管理报文，然后基于各个管理报文确定指示对应的ECU为休眠状态或未休眠状态的状态报文，然后将多个状态报文发送至CAN总线网络，以实现远程信息处理控制单元T‑BOX通过CAN总线网络接收多个状态报文，并根据多个状态报文判断目标车辆是否休眠。其中，本申请实施例通过网络获取CAN网络中各个ECU的管理报文，并基于管理报文确定各个ECU的状态，实现对目标车辆的各个ECU的状态检测，提高检测效率。
",G05B23/02,江西经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"实现, 包括, 装置, 方法, 处理, 控制, 检测","车辆, 检测, 网络, 控制"
CN118915697A,CN202411045286.5,"本申请提供了一种虚拟仿真测试的方法、装置、电子设备及存储介质。其中，在虚拟仿真测试的方法中，若接收到测试开始指令，则启动主工作站的场景仿真程序、传感器以及从工作站的传感器。然后向主工作站的场景仿真程序、传感器以及从工作站的传感器发送单步运行指令。再获取场景仿真程序输出的场景仿真信息，并将场景仿真信息发送到各个传感器，并接收各个传感器的输出结果。最后若接收到测试结束指令，则关闭主工作站的场景仿真程序、传感器以及从工作站的传感器。本申请利用一个主工作站和N个从工作站进行关联工作，主工作站和N个从工作站保持通讯连接，能够共享数据并实现了主工作站和从工作站之间的同步运行，保证了测试效果。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 实现, 测试, 装置, 仿真, 方法, 设备","测试, 传感器"
CN118819105A,CN202410781094.4,"本发明提供一种整车热管理系统的MIL测试方法及装置，基于热仿真软件和预设工况试验参数对热仿真模型进行测试，当热仿真模型符合预设要求时，根据MIL测试需求创建目标热仿真模型，导出接口仿真模型；根据预设检测方法对接口仿真模型进行检测，当接口仿真模型无误时根据MIL测试需求和目标热仿真模型创建新的目标热仿真模型；将新的目标热仿真模型作为目标热仿真模型，并返回执行基于目标热仿真模型导出接口仿真模型这一步骤，直至MIL测试需求中所有接口均测试完毕。利用热仿真模型作为虚拟的热管理系统的台架，根据MIL测试需求对热仿真模型进行测试，提高了测试结果的可靠性，确保整车热管理系统在实际使用中的稳定性和安全性。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 测试, 模型, 装置, 仿真, 方法, 系统, 检测","检测, 测试"
CN118821609A,CN202410954772.2,"本申请提供一种代客泊车仿真定位方法、装置、设备和计算机可读存储介质，方法包括：获取真实地图，提取真实地图的真实语义图层数据和真实特征图层数据。将真实地图转换为仿真地图，利用仿真场景采集装置采集仿真环境的场景数据，提取特征数据得到仿真特征图层数据，将真实特征图层数据替换为仿真特征图层数据，结合仿真特征图层数据以及真实语义图层数据构建得到自动泊车地图，这样自动泊车地图是利用仿真环境中的特征信息以及真实地图中的位置矢量信息构建得到，能够实现仿真车辆利用该自动泊车地图在仿真环境中定位成功，进而实现代客泊车仿真测试。
",G06F30/27,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G06F30/27,"提供, 实现, 包括, 测试, 装置, 仿真, 方法, 设备, 计算","车辆, 测试"
CN118821593A,CN202410824597.5,"本申请公开了一种车辆严重度的评估方法、装置、电子设备及存储介质。该方法包括：获取车辆的目标行驶信息；从与目标行驶信息匹配的风险场景的场景信息中，提取目标碰撞工况；确定与目标碰撞工况的碰撞波形参数对应的目标碰撞评价响应，碰撞评价响应用于表征车辆的车体结构的安全性；确定与目标行驶信息对应的目标约束参数；确定与目标约束参数所对应的目标约束评价响应，约束评价响应用于表征车辆的约束系统的安全性；基于碰撞评价响应、约束评价响应与功能安全严重度之间的关联关系，确定车辆的目标功能安全严重度。本申请所提供的方案降低了主观经验对车辆的功能安全严重度评估的影响，提高了车辆安全评估的准确度。
",G06F30/27,经纬恒润(天津)研究开发有限公司,集群化测试技术,2.0,IPC分类号匹配: G06F30/27,"提供, 包括, 装置, 方法, 设备, 系统",车辆
CN118689202A,CN202410793823.8,"本发明提供的一种基于TBox的智能监控方法、系统及电子设备，该方法应用于车辆端，车辆端搭载有TBox，该方法包括：获得与TBox关联的监控数据，其中，监控数据包括目标状态数据和故障数据，目标状态数据包括TBox的工作状态数据和TBox采集到车辆端的车辆运行数据，故障数据为TBox利用目标状态数据生成的故障信息；利用云平台对监控数据进行数据分析处理，获得分析结果；利用分析结果对TBox进行远程控制。本发明结合TBox对车辆运行数据的收集能力和故障初步分析能力，使用云边协同技术对监控数据进行数据分析处理，可以有效监控包括TBox在内的车辆零部件，提升车辆的智能化水平，保障车辆智能控制的安全性。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 平台, 包括, 方法, 生成, 处理, 设备, 控制, 系统","车辆, 控制"
CN118625784A,CN202410644178.3,"本申请公开了一种车辆故障诊断方法及装置。在执行该方法时，先获取目标车辆的运动工况信息和行驶状态信息，然后根据目标车辆的运动工况信息，计算出目标车辆的实际能耗值；并将目标车辆的行驶状态信息输入至整车能耗预测模型，通过整车能耗预测模型计算出目标车辆的预测能耗值；若目标车辆的实际能耗值与目标车辆的预测能耗值之间的第一偏离度大于故障阈值时，确定目标车辆发生故障。通过上述方式，通过将目标车辆的实际能耗值与目标车辆的预测能耗值进行对比，确定出目标车辆是否发生故障，能够提高车辆故障诊断的准确性。而且通过对目标车辆能耗的检测能够完成车辆故障前的预警功能。
",G05B23/02,经纬恒润(天津)研究开发有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"模型, 装置, 方法, 检测, 计算","车辆, 检测, 诊断"
CN118605477A,CN202410881369.1,"本申请公开了一种诊断响应仿真方法、系统、设备、介质及产品，涉及车辆仿真技术领域。应用于诊断响应仿真系统的诊断响应仿真方法包括：接收诊断仪发送的针对目标电子控制单元的目标服务的诊断请求报文；确定与诊断请求报文对应的诊断仿真响应，其中，诊断仿真响应为预先创建的与诊断工具诊断目标服务相对应的诊断请求对应的诊断响应；向诊断仪发送与诊断仿真响应对应的响应报文。根据本申请公开的方案，能够提高诊断工具的开发效率。
",G05B23/02,经纬恒润(天津)研究开发有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"包括, 仿真, 方法, 设备, 控制, 系统, 工具","车辆, 诊断, 控制"
CN118466463A,CN202410725901.0,"本申请公开了一种故障注入测试系统。系统包括：背板模块，包括交换芯片、多个第一连接器以及多个第二连接器，多个第二连接器与故障轨连接，交换芯片用于与上位机连接，交换芯片分别与多个第一连接器连接；执行板模块，分别与对应的第一连接器和两个第二连接器连接，用于响应上位机指令通过其中一个第二连接器为故障轨提供对应的故障信号；至少一个故障注入模块，分别与对应的第一连接器和第二连接器连接，还用于与外部ECU连接，并用于响应上位机指令从故障轨选择相应的故障信号注入。本实施例能够实现对ECU所需IO信号多功能的故障注入，解决了设备一体化程度低的问题，实现了多功能故障注入测试设备高度集成效果。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G05B23/02,"提供, 实现, 包括, 测试, 设备, 系统",测试
CN118331217A,CN202410263702.2,"本申请公开了一种域控制器系统资源访问方法、系统、设备及存储介质，域控制器系统包括实时系统和运算系统，在执行该方法时，实时系统首先创建系统资源管理器，当实时系统向系统资源管理器发送第一资源访问请求时，资源管理器根据第一访问资源地址和实时系统对应的访问权限进行处理，将第一访问结果返回给实时系统；当运算系统向系统资源管理器发送第二资源访问请求时，资源管理器根据第二访问资源地址和运算系统对应的访问权限进行处理，将第二访问结果返回给运算系统。可见，本申请利用系统资源管理器实现实时系统资源和运算系统资源隔离，可以实现不同的功能安全等级、实时性和信息安全等级，提升了域控器系统使用的灵活性。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"实现, 包括, 方法, 处理, 设备, 控制, 系统",控制
CN118295372A,CN202410404672.2,"本申请实施例提供了一种功能测试方法及装置，该方法包括：向驾驶员监测系统DMS系统发送正常人脸注册模拟视频；向系统控制处理器SCP系统发送注册功能触发报文，令SCP系统从DMS系统获取正常人脸注册模拟视频，并基于正常人脸注册模拟视频进行人脸注册；监控SCP系统发送的注册报文；基于SCP系统发送的注册报文和预设的人脸注册正常条件，生成功能测试报告。本申请实施例能够以自动化方式完成SCP系统的HIL测试，并用视频注入的方式代替真实的DMS摄像头，减少了测试人员重复性高又耗时的工作量，能够高效、低成本地完成对SCP系统的功能测试。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 包括, 测试, 装置, 方法, 生成, 处理, 控制, 系统, 模拟","驾驶, 测试, 摄像, 控制"
CN118194799A,CN202410381361.9,"本申请实施例公开了一种金属氧化物半导体场效应晶体管的建模方法及装置。在该方法中，根据预设的第一拟合公式提取漏极电流值；根据预设的第二拟合公式提取非线性结电容值；根据预设的第三公式提取体二极管参数；提取杂散参数；根据漏极电流值、非线性结电容值、二极管参数和杂散参数构建金属氧化物半导体场效应晶体管模型。由此可见，利用本申请实施例提供的方案，能够快速地进行金属氧化物半导体场效应晶体管的搭建，从而快速准确地得到各种工作条件下的金属氧化物半导体场效应晶体管的参数信息。
",G06F30/367,经纬恒润(天津)研究开发有限公司,集群化测试技术,2.0,IPC分类号匹配: G06F30/367,"提供, 方法, 模型, 装置",通用
CN118150997A,CN202410282101.6,"本申请提供一种电容式触控开关的测试系统及测试方法，通过电容式触控开关的测试系统集成了对控制板卡以及测试台架中工业摄像头的控制功能，从而实现了一套可自动执行测试并判断测试结果的测试方法。有效减少人力成本和测试台架搭建使用成本，并提高测试效率和准确度。
",G01R31/327,北京经纬恒润科技股份有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G01R31/327,"提供, 实现, 测试, 方法, 控制, 系统","测试, 摄像, 控制"
CN117873025A,CN202311763189.5,"本申请提供了一种在环测试方法及装置，通过场景仿真平台构建仿真场景，仿真场景包括模拟车辆；通过待测智驾控制器基于仿真场景，生成车辆动力学模型的目标控制策略；通过整车控制器，按照目标控制策略控制车辆动力学模型进行运动，得到车辆动力学模型运动后的目标坐标，目标坐标用于指示车辆动力学模型在仿真场景中的位置；通过场景仿真平台，基于目标坐标调整模拟车辆在仿真场景中的位置；通过智驾云平台对待测智驾控制器生成控制策略的过程进行监测，得到监测结果。本申请实施例能够提高测试的效率。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 平台, 包括, 模型, 装置, 测试, 仿真, 方法, 生成, 控制, 模拟","车辆, 测试, 控制"
CN117850399A,CN202410033312.6,"本申请实施例公开了一种控制器功能检测方法、装置及系统，方法包括：向控制器的闪存区域注入具有第一引导加载程序的下线检测软件和客户方引导加载程序；在运行下线检测软件检测控制器的控制功能后，基于上位机的第一指令将第一引导加载程序从控制器的闪存区域复制到随机存储区域并运行；在第一引导加载程序的运行过程中，基于上位机发送的第二指令将所述控制器的闪存区域中的下线检测软件更新为第一目标软件；基于上位机发送的第三指令清除随机存储区域的第一引导加载程序。上述方案中下线检测软件独立存在且自身具有程序升级功能，因此不仅与客户的目标软件解耦，且客户软件的刷写基于下线检测软件的第一程序引导程序就能够实现。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"实现, 包括, 装置, 具有, 方法, 控制, 系统, 检测","检测, 控制"
CN117786819A,CN202410143366.8,"本发明公开了一种路面等级识别方法及装置，应用于车身系统和悬架系统，基于车身加速度传感器和悬架动行程传感器确定车身垂向加速度、悬架动行程和悬架运动速度，将车身垂向加速度、悬架动行程和悬架运动速度传递给系统辨识模型得到车身系统的簧上质量估计值和阻尼系数估计值；将簧上质量估计值和阻尼系数估计值传递给预设的经验公式进行计算，基于得到的路面状态参数确定当前路面的路面等级。上述过程，直接计算得到路面状态参数，基于路面状态参数直接确定路面状态等级，避免了先计算高程，再基于傅里叶变换、激励频率能量分段及小波变换分析等方法确定路面状态参数需要消耗大量计算资源，不但对对处理器的要求较高，而且计算周期较长的问题。
",G06F30/13,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G06F30/13,"模型, 装置, 方法, 处理, 系统, 计算",传感器
CN117741521A,CN202311499070.1,"本申请公开了一种保险丝盒测试系统及测试方法。该系统包括测试管理模块和电子负载，测试管理模块与电子负载通信连接；测试管理模块，用于向电子负载发送第一控制信号，以控制电子负载在第一时段内产生第一电流，在第二时段内产生第二电流；获取电子负载在第一时段内的第一电压和在第二时段内的第二电压；根据第一电压是否满足第一预设条件确定第一测试结果，根据第二电压是否满足第二预设条件确定第二测试结果；在第一测试结果和第二测试结果均表征通过测试的情况下，确定保险丝盒通过样件测试；在第一测试结果和第二测试结果中任一项表征未通过测试的情况下，确定保险丝盒未通过样件测试。这样便实现了对保险丝盒性能的准确测试。
",G01R31/74,北京经纬恒润科技股份有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G01R31/74,"实现, 包括, 测试, 方法, 控制, 系统","通信, 测试, 控制"
CN117687383A,CN202311704886.3,"本发明提供一种仿真测试方法、模拟ECU服务端及诊断仪，模拟ECU服务端在确定基于预设IP和端口号建立通信配置成功时，实时监听是否接收到输入输出I/O事件；若监听到I/O事件为连接建立请求，基于连接建立请求中诊断仪ID建立诊断仪和模拟ECU服务端的连接；接收连接的诊断仪发送的仿真测试请求，基于仿真测试请求中的模拟仿真业务类型获取对应的配置脚本；接收所述诊断仪基于所述仿真测试请求发送的报文，基于所述配置脚本对报文进行处理，确定返回值，并将返回值返回给诊断仪，以便诊断仪基于所述返回值确定仿真测试请求是否成功。本发明实施例中通过模拟ECU，以实现ECU与诊断仪之间的测试。
",G05B23/02,经纬恒润(天津)研究开发有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 模拟, 实现, 测试, 仿真, 方法, 处理, 配置","通信, 测试, 诊断"
CN117516878A,CN202311427838.4,"本申请提供了一种测试系统、方法、装置及设备，涉及光学测试领域，所述测试系统包括聚光透镜、光纤和光谱仪，所述光纤的第一端与所述光谱仪连接，所述聚光透镜位于待测平视显示器HUD与所述光纤的第二端之间，其中，所述聚光透镜用于将所述待测HUD输出的投影光线进行汇聚，并将得到的汇聚光线通过所述光纤的第二端传入至所述光纤中，所述光纤将所述汇聚光线通过第一端传输至所述光谱仪。通过使用上述系统，可以利用光谱仪对HUD有无电磁影响下光线的变化进行检测，提高对HUD性能测试的准确性。
",G01M11/02,天津经纬恒润科技有限公司,集群化测试技术,1.0,关键词匹配: 测试系统,"提供, 包括, 测试, 装置, 方法, 设备, 系统, 检测","检测, 测试"
CN117494656A,CN202410001196.X,"本发明提供了一种芯片结温热阻模型建模方法及装置，所述芯片固定于电路板，所述方法包括以下步骤：分别获取在恒定功率不同环境温度下的芯片结点温度和电路板温度；分别基于不同环境温度下的芯片结点温度和电路板温度，计算得到对应的芯片结点到电路板间、电路板到空气间的热阻；基于不同环境温度下的热阻，计算得到环境温度与热阻之间的对应关系；基于所述环境温度与热阻之间的对应关系，建立所述芯片结温的等效热阻模型。本发明的芯片结温热阻模型建模方法，可以体现温度对热阻的影响，提高了模型的精确度；此外，本发明实施例还提出一种使用压控电阻对热阻的温度特性进行表征的等效电路模型，该模型可以用于对器件的热设计的仿真分析。
",G06F30/398,北京经纬恒润科技股份有限公司；中国第一汽车股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G06F30/398,"提供, 包括, 模型, 装置, 仿真, 方法, 计算, 电路",通用
CN117311329A,CN202311548786.6,"本发明提供了一种车辆诊断方法及系统，接收远程诊断云端下发的诊断任务，并下载所述诊断任务对应的诊断脚本，在确定出满足所述诊断任务对应的任务执行触发条件的情况下，执行策略检查操作，在策略检测操作通过后，解析所述诊断脚本，得到待诊断部件，并向所述待诊断部件发送诊断报文，以使所述待诊断部件执行所述诊断报文对应的诊断操作，接收所述待诊断部件发送的诊断结果，并将诊断结果发送至远程诊断云端。通过本发明，实现了针对智能化车辆的车辆诊断。并且，本发明中，通过车辆软件实现车辆诊断，不再需要诊断仪等硬件设备，降低成本。且通过车辆能够随时进行车辆诊断，不需要前往指定诊断地点进行车辆诊断，提高车辆诊断效率。
",G05B23/02,经纬恒润(天津)研究开发有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 实现, 方法, 设备, 系统, 检测","车辆, 检测, 诊断"
CN117252144A,CN202310813163.0,"本申请提供了一种印制线路板设计方法及其装置，方法包括：获取印制线路板PCB的设计文本数据，设计文本数据包括PCB的网络名称信息和芯片类型信息，将网络名称信息和芯片类型信息进行分组存储，得到多个接口类型，且每个接口类型对应多个拓扑类型，响应于用户的第一输入，确定第一接口类型以及第一接口类型对应的第一拓扑类型，根据第一接口类型以及第一接口类型对应的第一拓扑类型，生成约束规则文件，约束规则文件用于指导设计PCB。这样，在设计PCB过程中，用户可以直接参考该约束规则文件进行针对性设计，无需翻阅信息量巨大的数据手册，节省设计时间，减少遗漏或出错的风险，从而提高了PCB设计的效率和精度。
",G06F30/392,经纬恒润(天津)研究开发有限公司,集群化测试技术,2.0,IPC分类号匹配: G06F30/392,"提供, 包括, 装置, 生成, 方法",网络
CN116859881A,CN202310752318.4,"本申请提供了一种测试方法及装置，基于车辆的车辆信息，搭建仿真场景，车辆信息包括车辆的行驶参数以及车辆上安装的传感器参数；在车辆运行在仿真场景的情况下，控制车辆下载软件包；模拟异常条件，使得待测对象在异常条件下根据软件包执行升级任务，待测对象包括OTA节点；对待测对象执行升级任务的过程进行监测，获得监测结果。本申请实施例能够为测试OTA节点提供了高效的测试分析手段，并且可以控制待测对象自动执行升级任务，提高了测试效率。
",G05B23/02,经纬恒润(天津)研究开发有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 包括, 测试, 装置, 仿真, 方法, 控制, 模拟","车辆, 测试, 传感器, 控制"
CN116859878A,CN202310542402.3,"本申请实施例提供一种测试系统，该测试系统包括：视频注入系统，用于获取目标注入视频数据以及摄像头参数信息，并向被测控制器发送所述目标注入视频数据和所述摄像头参数信息；所述被测控制器，所述被测控制器与所述视频注入系统通信连接，用于向所述视频注入系统发送调参指令；所述视频注入系统，用于根据所述视频调节参数，对所述目标注入视频数据进行闭环调节，得到调节后的目标注入视频数据，并向所述被测控制器发送调节后的目标注入视频数据，以用于所述被测控制器基于所述调节后的目标注入视频数据进行仿真测试。本申请实施例，提高了智能驾驶控制器的测试准确度。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G05B23/02,"提供, 测试, 包括, 仿真, 控制, 系统","驾驶, 测试, 控制, 通信, 摄像"
CN116822434A,CN202310889349.4,"本申请公开了一种功率开关模块电磁干扰仿真方法、装置、设备及介质，涉及电子器件仿真技术领域。功率开关模块电磁干扰仿真方法包括：构建待设计开发的功率开关模块的三维实体结构模型；根据三维实体结构模型，生成功率开关模块对应的三维等效电路模型；根据三维等效电路模型，生成用于对功率开关模块的电磁干扰进行仿真的仿真电路；向仿真电路中功率开关模块的下半桥开关栅极提供信号激励；通过电磁场探针获取电磁场探针所在位置处的电磁场以及功率开关模块的电磁场分布；对电磁场求解麦克斯韦方程组，得到功率开关模块的电磁干扰仿真结果。根据本申请实施例，能够缩短功率开关模块的开发周期，降低研发成本。
",G06F30/367,经纬恒润(天津)研究开发有限公司,集群化测试技术,2.0,IPC分类号匹配: G06F30/367,"提供, 包括, 模型, 装置, 仿真, 方法, 生成, 设备, 电路",通用
CN116719301A,CN202310740841.5,"本申请公开了一种车身控制器自动测试系统串口通信方法、装置及设备，上位机基于预设的数据帧格式对命令类型为数字量设置、数字量读取、模拟量设置、模拟量读取、脉冲宽度调制信号读取或电阻设置的目标命令进行封装获得第一数据帧并向下位机发送，下位机基于第一数据帧中的校验和对第一数据帧进行校验，响应于校验通过基于第一数据帧中的命令内容进行处理，获得符合数据帧格式的第二数据帧，向上位机发送第二数据帧，上位机基于第二数据帧中的校验和对第二数据帧进行校验，并响应于第二数据帧通过校验确定完成基于目标命令的测试。根据本申请实施例，上位机和下位机根据预设的数据帧格式生成硬线信息相应的数据帧，通过数据帧实现硬线信息的传输。
",G05B23/02,经纬恒润(天津)研究开发有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G05B23/02,"实现, 测试, 装置, 方法, 设置, 处理, 设备, 生成, 控制, 系统, 模拟","通信, 测试, 控制"
CN116668334A,CN202310560919.5,"本申请实施例提供一种测试系统，该测试系统包括：测试系统与被测控制器连接，被测控制器配置有数据安全保护机制，测试系统包括：报文构建模块，用于构建第一报文，第一报文包括正常报文或异常报文，并向测试模块发送第一报文；报文配置模块，用于配置第一报文的报文信息，并向测试模块发送第一报文的报文信息；测试模块，用于向被测控制器发送第一报文以及第一报文的报文信息；报告生成模块，用于接收被测控制器发送的报文反馈信息，并基于报文反馈信息生成第一测试报告。本申请实施例，提高了控制器的测试效率的同时，提高了控制器的测试准确度。
",H04L43/0817,北京经纬恒润科技股份有限公司,集群化测试技术,1.0,关键词匹配: 测试系统,"提供, 测试, 包括, 生成, 控制, 系统, 配置","测试, 控制"
CN116643552A,CN202310524772.4,"本申请公开了一种无人驾驶设备控制器的测试系统和方法，该系统包括图像采集设备用于采集第一图像数据，将第一图像数据发送至视频注入核心板中，第一图像数据至少包括第一图像属性以及第一图像内容，图像存储设备用于向视频注入核心板发送第二图像数据，第二图像数据包括第二图像内容，视频注入核心板用于对第一图像属性进行解析，得到解析后的第一图像属性，将第一图像内容替换为第二图像内容，得到目标图像数据，将目标图像数据发送至无人驾驶设备控制器，目标图像数据至少包括解析后的第一图像属性以及第二图像内容，无人驾驶设备控制器用于基于接收的目标图像数据输出控制指令，提高测试准确率。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G05B23/02,"包括, 测试, 方法, 设备, 控制, 系统","驾驶, 测试, 控制"
CN116626429A,CN202310657899.3,"本申请公开了一种电磁兼容抗扰度测试方法和系统，实现了电子产品电磁兼容抗扰度测试的高效率和高准确性。该方法包括：控制测试系统向被测系统施加满足测试要求的干扰信号；所述满足测试要求包括：达到规定的频点和强度；监测被测系统中的被测对象在当前干扰信号下是否出现功能状态异常，若否，记录测试结果，若是，调节当前干扰信号的强度，再次测试，直至查找到令被测对象出现功能状态异常的干扰信号临界强度，并记录测试结果；切换测试要求，重复上述测试，直至完成所有的测试要求下的测试；其中，不同测试要求中规定的频点不同。
",G01R31/00,天津经纬恒润科技有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G01R31/00,"实现, 包括, 测试, 方法, 控制, 系统","测试, 控制"
CN116627115A,CN202310738807.4,"本申请公开了一种车身控制器自动测试方法、装置、设备及计算机存储介质，上位机通过解析用于对被测车身控制器进行测试的测试用例，获得测试步骤和测试用例中的变量与下位机中资源接口的对应关系，也即资源对应关系，然后针对结果检测类或操作执行类的测试步骤，根据测试步骤和资源对应关系控制下位机对被测车身控制器进行测试，并根据测试步骤的执行情况生成被测车身控制器的测试报告。根据本申请实施例，通过上位机执行车身控制器自动测试方法，即可控制下位机自动对被测车身控制器进行测试，并生成相应的测试报告，整个测试过程几乎无需人工参与，相比于传统的人工测试方式，测试效率更高、测试质量更高、人力成本更低。
",G05B23/02,经纬恒润(天津)研究开发有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"测试, 装置, 方法, 生成, 设备, 控制, 检测, 计算","检测, 测试, 控制"
CN116614428A,CN202310752654.9,"本申请公开了一种自动化车辆通信测试方法、系统和电子设备。自动化车辆通信测试方法应用于自动化车辆通信测试系统，自动化车辆通信测试系统包括相互通信连接的测试单元、车辆和测试环境模拟单元；方法包括：测试单元获取测试环境模拟信息，控制测试环境模拟单元模拟测试环境；控制车辆在测试环境中运行；向车辆中的目标通信设备发送测试数据，生成第一测试结果，以及，生成第二测试结果；在第一测试结果和第二测试结果均为通过测试的情况下，返回执行未执行测试的环境测试参数，直到每个环境测试参数测试完成。根据本申请实施例，能够有效提高测试结果的可靠性。
",H04L43/50,经纬恒润(天津)研究开发有限公司,集群化测试技术,1.0,关键词匹配: 测试系统,"包括, 测试, 方法, 生成, 设备, 控制, 系统, 模拟","车辆, 通信, 测试, 控制"
CN116609602A,CN202310595592.5,"本申请实施例提供一种车载终端的测试方法、装置和系统，方法应用于车载终端的测试系统，测试系统与车载终端通信连接，方法包括：获取对车载终端的测试信息，其中，测试信息包括测试环境信息和测试数据的参考数据范围，测试环境信息包括：电压波动测试信息、定位环境测试信息和网络连接环境测试信息，测试环境信息与参考数据范围一一对应；在根据测试环境信息模拟目标测试环境的情况下，控制车载终端执行目标功能，获取车载终端运行目标功能所产生的测试数据；根据测试数据和测试环境信息对应的参考数据范围，生成目标功能的测试结果。本申请可以对车载终端进行测试，从而提高测试结果的准确性。
",G01R31/00,北京经纬恒润科技股份有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G01R31/00,"提供, 包括, 测试, 装置, 方法, 生成, 控制, 系统, 模拟","通信, 测试, 网络, 控制"
CN116594375A,CN202310626620.5,"本发明公开了一种车载故障注入装置、系统及方法，车载故障注入装置设置在第一设备和第二设备之间的线路上，包括：用户操作模块、故障配置模块和故障执行模块，用户操作模块获取用户选择的故障注入信息，故障配置模块基于故障注入信息配置出对应的故障，故障执行模块将故障输出至第二设备，完成故障注入。本发明公开的车载故障注入装置集成了各个故障注入类型，相对于现有车辆故障注入方案而言，可实现不同类型故障注入。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"实现, 包括, 装置, 方法, 设置, 设备, 系统, 配置",车辆
CN116594372A,CN202310511419.2,"本申请公开了一种仿真测试装置及其控制方法。仿真测试装置包括换热机构和检测机构，换热机构包括换热管路和换热器，换热器能够加热或冷却换热管路，换热管路包括相连通的出液管路和进液管路，出液管路和进液管路分别用于连通待测试设备的冷却机构；检测机构包括第一温度检测器和第二温度检测器，第一温度检测器设置于出液管路，第二温度检测器设置于出液管路。本申请提供的仿真测试装置可调节待检测设备的工作温度，减小或避免仿真测试过程中待检测设备过载烧坏。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 包括, 测试, 装置, 仿真, 方法, 设置, 设备, 控制, 检测","检测, 测试, 控制"
CN116578071A,CN202310745277.6,"本申请公开了一种车身控制器自动测试装置及系统，车身控制器自动测试装置包括电源控制子系统、第一单片机最小系统、数字量子系统和总线监控设备，第一单片机最小系统基于上位机发送的测试命令控制数字量子系统对车身控制器自动测试装置外接的被测车身控制器进行数字量输出、数字量输入和/或PWM读取，总线监控设备基于上位机的测试命令对被测车身控制器进行总线信号设置和/或总线信号读取，其中，总线信号包括CAN信号和/或LIN信号，如此，实现对被测车身控制器的自动测试。根据本申请实施例，车身控制器自动测试装置根据上位机发送的测试命令即可完成对车身控制器的自动测试，因此相比于人工测试，提高了测试执行效率。
",G05B23/02,经纬恒润(天津)研究开发有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"实现, 包括, 测试, 装置, 设置, 设备, 控制, 系统","测试, 控制"
CN116149293A,CN202211102074.7,"本发明实施例提供了一种车辆远程诊断方法、系统及计算机设备，方法包括：创建诊断任务；判断待诊断车辆是否支持MQTT协议；当待诊断车辆支持MQTT协议时，判断待诊断车辆是否在线；若待诊断车辆在线，通过MQTT协议发送诊断指令至所有任务关联的待诊断车辆，待诊断车辆通过HTTPS协议获取诊断任务并下载诊断包；当待诊断车辆不支持MQTT协议或待诊断车辆不在线时，待诊断车辆通过HTTPS协议周期性拉取诊断任务并下载诊断包；待诊断车辆执行诊断任务，得到诊断结果；待诊断车辆将诊断结果上传至远程诊断平台。采用MQTT协议与HTTPS协议相结合的通信协议方式，有效提高了复杂网络环境下远程诊断的执行率和成功率。
",G05B23/02,经纬恒润(天津)研究开发有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 平台, 包括, 方法, 设备, 系统, 计算","车辆, 通信, 网络, 诊断"
CN116068989A,CN202211607726.2,"本申请实施例提供了一种车辆嵌入式系统的状态监管方法、装置，该方法包括接收车辆嵌入式系统的第一外设模块发送的状态信息，状态信息包括第一外设模块的标识，将状态信息存储至数据库的历史状态记录表中，向云服务器发送历史状态记录表，以使云服务器基于历史状态记录表中的状态信息，通过预设专家系统推理机模型判断车辆嵌入式系统的目标状态，并在目标状态为故障前驱状态或故障状态的情况下，向状态监管系统发送状态监管信息，状态监管信息包括目标状态和第一外设模块的标识，接收云服务器发送的状态监管信息。根据本申请实施例，系统可以通过目标状态及时发现并处理故障，提高了故障检测效率。
",G05B23/02,经纬恒润(天津)研究开发有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 包括, 模型, 装置, 方法, 处理, 系统, 检测","车辆, 检测"
CN115981267A,CN202211491224.8,"本申请公开一种测试系统，包括上位机和控制型控制器；上位机用于：生成CAN总线指令，其中，若测试项需要配置车身域控制器的硬线管脚的状态，那么CAN总线指令具有第一标识；控制型控制器用于：获得具有第一标识的CAN总线指令，将具有第一标识的CAN总线指令转换为硬线信号，向车身域控制器传输该硬线信号；将从车身域控制器获得的硬线信号转换为具有第二标识的CAN总线信号，向上位机传输该具有第二标识的CAN总线信号；上位机还用于：比较接收到的CAN总线信号和与测试项对应的标准CAN总线信号，以确定车身域控制器是否通过该测试项。本申请公开的测试系统，能够降低测试成本、提高测试效率、提高测试准确度、减少人力消耗。
",G05B23/02,经纬恒润(天津)研究开发有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G05B23/02,"包括, 测试, 具有, 生成, 控制, 系统, 配置","测试, 控制"
CN115964771A,CN202211502392.2,"本申请公开了一种视线追踪系统的测试方法及装置。该方法包括：获取并显示与目标环境仿真数据对应的目标显示界面，获取人眼的视线发出位置和目标显示界面中目标物体的位置，根据视线发出位置和目标显示界面中目标物体的位置，确定目标物体对应的标定视线数据，在人眼看向目标物体的情况下，获取视线追踪系统检测到的人眼的测试视线数据，根据测试视线数据和标定视线数据，确定视线追踪系统的测试结果。采用本申请提供的视线追踪系统的测试方法及装置，可以节省人力物力，提高呈现多个不同场景的效率，而且测试场景的复现度也比较高，还可以避免人工测量带来的较大误差，提高测试的准确性。
",G06F30/12,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G06F30/12,"提供, 包括, 测试, 装置, 仿真, 方法, 系统, 检测","检测, 测试"
CN115857466A,CN202211483099.6,"本申请公开了一种控制器测试系统。该控制器测试系统包括：上位机，与板卡连接，用于向板卡发送测试信号，板卡，与信号控制电路连接，用于根据测试信号生成N路控制信号，并将N路控制信号发送至信号控制电路，信号控制电路，与通讯网络切换电路连接，用于根据N路控制信号生成M路控制信号，并发送至通讯网络切换电路，通讯网络切换电路，与上位机和控制器连接，用于基于M路控制信号控制M个通道中的任意通道切换为导通或关断状态，并接收控制器生成的反馈信号，发送至上位机，上位机还用于根据反馈信号和测试信号确定控制器是否合格，且M>N。这样，可以实现对控制器的自动化测试，提高了测试效率，节约了人力成本，还节省了控制点。
",G05B23/02,天津经纬恒润科技有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G05B23/02,"实现, 包括, 测试, 生成, 控制, 系统, 电路","测试, 网络, 控制"
CN115436797A,CN202211202266.5,"本申请实施例提供一种电容开关测试系统及方法，该系统包括：电容开关、上位机、板卡和继电器，板卡与上位机连接，板卡与继电器连接，板卡与电容开关连接，上位机，用于向板卡发送第一控制信号和第二控制信号，板卡，用于基于第一控制信号和第二控制信号，向继电器发送第三控制信号，继电器，用于基于第三控制信号，执行与第一控制信号对应的第一目标动作，并执行与第二控制信号对应的第二目标动作，板卡，还用于采集电容开关的开锁数据、对电容开关进行供电的供电数据，以及电容开关的闭锁数据，并基于开锁数据、供电数据、闭锁数据，以及预设开闭锁条件，确定电容开关的测试结果。本申请实施例，提高了电容开关的测试效率。
",G01R31/327,天津经纬恒润科技有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G01R31/327,"提供, 包括, 测试, 方法, 控制, 系统","测试, 控制"
CN115390546A,CN202210983254.4,"本申请公开了一种车辆诊断通信方法、装置、设备及介质。在车辆的域控制器中的控制单元一侧，该方法包括：接收诊断发起装置发送的诊断请求；响应于诊断请求，基于诊断请求中的请求内容确定诊断对象，其中，诊断对象包括诊断请求所要请求诊断或访问的目标元器件；根据诊断对象的诊断情况，向诊断发起装置返回诊断请求的响应消息。根据本申请实施例，控制单元作为诊断通信的控制端，在诊断通信中承担路由功能，减少了消息转发次数，提高了车辆诊断通信的效率。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"包括, 装置, 方法, 设备, 控制","车辆, 通信, 诊断, 控制"
CN115390545A,CN202210982180.2,"本申请公开了一种车辆诊断方法、装置、设备、可读存储介质及程序产品。方法包括：接收诊断仪发送的诊断请求报文，诊断请求报文为CANFD诊断请求报文或CAN诊断请求报文，对诊断请求报文执行数据链路层的路由，将诊断请求报文转发至目标节点，目标节点为CAN节点或CANFD节点，对目标节点发送的诊断响应报文执行数据链路层的路由，将诊断响应报文以控制器局域网CAN诊断响应报文发送至所述诊断仪，其中，在所述目标节点为CAN节点的情况下，所述诊断请求报文的数据长度代码DLC为8，在所述目标节点为可变速率控制器局域网CANFD节点的情况下，所述诊断响应报文的DLC为8。本申请拓展性好，且可以降低网关的软件复杂度，进而降低网关的研发成本。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"包括, 装置, 方法, 设备, 控制","车辆, 诊断, 控制"
CN115166391A,CN202210724640.1,"本发明公开一种整车收音机天线自兼容测试系统、方法和装置，系统包括音频源、信号发生器以及发射天线；音频源连接至信号发生器，信号发生器连接至发射天线，发射天线设置在距离被测车辆的车载收音机天线的预设距离处，并使发射天线与车载收音机天线基于同一参考面的分布参数相同，且发射天线和车载收音机天线任意部位之间的距离均不小于预设距离；音频源产生调制信号；信号发生器将调制信号作为输入并输出目标频率的无线电信号并通过发射天线向外发射，由车载收音机天线接收，被测车辆的车载收音机解调后进行播放，根据播放情况实现对车载收音机天线的整车电磁自兼容测试，该系统无需部署复杂的测试系统、成本低、周期短且能减少无效测试。
",G01R31/00,北京经纬恒润科技股份有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G01R31/00,"实现, 包括, 测试, 装置, 方法, 设置, 系统","车辆, 测试"
CN114995347A,CN202210679865.X,"本发明公开了一种故障注入方法、装置及设备，故障注入设备包括源和依次连接的控制器、机械继电器和固态继电器，机械继电器的每一个输出端口对应连接有一个所述固态继电器，其中：控制器用于向机械继电器发送端口选择控制信号和I/O控制信号；机械继电器用于基于端口选择控制信号实现I/O控制信号的通路选择；固态继电器用于基于I/O控制信号实现故障注入的控制；电源用于为控制器、机械继电器和固态继电器供电。上述方案中，控制器可以直接通过控制信号实现对机械继电器的控制以及对固态继电器的控制，从而可以通过电气元件电路自动化实现硬线故障注入的精准化控制，包括故障注入时间点和故障持续时间长度。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"实现, 包括, 装置, 方法, 设备, 控制, 电路",控制
CN114625108A,CN202210277550.2,"本发明提供一种仿真测试方法及装置，设置仿真步长对应的中断数和通过时钟板卡驱动设置时钟板卡的中断间隔。运行仿真程序中的调度进程并通过时钟板卡驱动启动时钟板卡，利用时钟板卡产生中断来实现仿真步长，避免仿真步长不规律的抖动，提高仿真测试的实时性和准确性。统计时钟板卡产生中断的次数以得到中断统计数，当中断统计数达到中断数时将中断统计数清零。在调度进程和/或任意其它进程未处于休眠状态的情况下，确定在仿真步长中发生超时，在每次检测到时钟板卡产生中断时判断仿真程序的所有进程是否均处于休眠状态。若所有进程均处于休眠状态，统计仿真程序在仿真步长中的运行信息，通过运行信息可分析超时原因，以满足仿真测试的功能要求。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 实现, 测试, 装置, 仿真, 方法, 设置, 检测","检测, 测试"
CN114415631A,CN202111672234.7,"本发明公开一种基于诊断工具的ECU诊断方法及诊断工具，包括：请求报文生成模块通过交互模块向用户设备提目标诊断请求列表，通过交互模块接收指定诊断请求标识，从诊断管理模块获取目标请求报文参数和目标请求报文结构，通过交互模块向用户设备提供目标请求报文参数，通过交互模块接收用户设备反馈的请求报文参数值，根据目标请求报文结构和请求报文参数值生成诊断请求报文，将诊断请求报文发送给待诊断ECU；响应报文解析模块接收待诊断ECU发送的对应于诊断请求报文的诊断响应报文，将诊断响应报文发送给诊断管理模块，接收诊断管理模块发送的目标响应报文结构，根据目标响应报文结构对诊断响应报文进行解析，通过交互模块向用户设备提供的解析结果。
",G05B23/02,经纬恒润(天津)研究开发有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 包括, 方法, 生成, 设备, 工具",诊断
CN114325409A,CN202111648379.3,"本发明实施例提供了一种电池热失控模拟方法、装置及电池包热失控蔓延测试系统。其中，方法包括：确定电池包中一个电池单体的热特性曲线；将电池单体替换为发热装置；发热装置的换热面积与电池单体的换热面积的差值在第一预设差值范围内，发热装置的热容量与电池单体的热容量的差值在第二预设差值范围内；在电池热失控模拟时，控制发热装置的温度依据电池单体的热特性曲线进行变化，以使发热装置的热特性曲线和电池单体的热特性曲线的相似度在预设相似度范围内。本发明能够提高电池包热失控蔓延测试的可信度。
",G01R31/378,经纬恒润(天津)研究开发有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G01R31/378,"提供, 包括, 测试, 装置, 方法, 控制, 系统, 模拟","测试, 控制"
CN114117857A,CN202111416281.5,"本发明提供了一种热泵系统中电池传热装置的仿真方法及装置，在对电池传热装置进行初始几何建模后，根据几何模型构建电池传热装置的蒸发/冷凝换热模型，通过将导热微分方程与蒸发/冷凝换热模型进行耦合模拟采用制冷剂蒸发为电池降温与采用制冷剂冷凝为电池加热的过程，实现对电池传热装置的仿真，在电池降温过程中的参考出口过热度和电池组最大温差以及电池加热过程中的参考出口过冷度和电池组最大温差均满足电池传热设计需求的情况下，输出该几何模型，反之则重新进行电池传热装置的几何建模及仿真。本发明实现对电池传热装置的仿真优化设计，降低了优化设计成本，缩短了优化设计周期，提高了优化设计效率。
",G06F30/23,经纬恒润(天津)研究开发有限公司,集群化测试技术,2.0,IPC分类号匹配: G06F30/23,"提供, 实现, 模型, 装置, 仿真, 方法, 系统, 模拟",通用
CN113985852A,CN202111274851.1,"本申请提出了一种车载终端日志处理方法、装置及系统，在目标车载终端与后台服务器之间的通信异常的情况下，目标车载终端能够获得据此生成的紧急模式触发指令，通过响应该紧急模式触发指令，重启激活目标车载终端预先配置的紧急系统运行，使得目标车载终端能够将获得的日志数据上报至紧急服务器进行存储，这样，日志分析设备就能够从该紧急服务器下载所需的日志数据，保证后台能够及时获得目标车载终端的日志数据，分析其异常原因，且相对于拆卸目标车载终端给售后工程师，通过复杂方式导出日志数据的处理方法，简化了车载终端日志数据获取步骤，解决了远距离邮寄目标车载终端带来的安全隐患，人力和时间的消耗，提高了异常分析效率。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"装置, 方法, 生成, 处理, 设备, 系统, 配置",通信
CN113050072A,CN202110245363.1,"本发明提供了一种激光雷达测试系统及方法，系统包括测试平台、电控直线导轨、电控旋转支架、电控靶标架、靶标、电控喷雾器、电控光源和中央控制器。测试平台包括电控升降平台、电控倾斜台和电控旋转平台。中央控制器控制各个电控设备的工作状态，实现了激光雷达性能测试的自动化；且可实现对视场角、分辨率、测量距离、测距准确度、测距精度、测距灵敏度、测距一致性、反射率准确度、反射率精度、反射率灵敏度、反射率一致性等性能参数的测试，以及还可测试距离、目标反射率、目标尺寸、激光入射角、环境光强、天气等条件对各个性能参数的影响。实现了对激光雷达性能的全面、自动的测试，提高了激光雷达测试的效率和测试数据的可靠性。
",G01S7/497,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,"关键词匹配: 测试平台, 测试系统","提供, 平台, 实现, 包括, 测试, 方法, 设备, 控制, 系统","雷达, 测试, 激光, 控制"
CN112947371A,CN202110160505.4,"本申请公开一种测试报文的仿真测试方法、装置及仿真测试设备。该方法包括：获取与测试场景对应的车辆数据；将车辆数据赋值给第一报文的数据位；确定第一报文的报文计数值；将报文计数值赋值给第一报文的报文计数位；调用预先构建的动态链接库，获得动态链接库对第一报文的数据位和报文计数位进行校验得到的第一校验码；将第一校验码赋值给第一报文的校验位，将第一报文作为测试报文。本申请公开的技术方案，在仿真测试报文的过程中，不依赖OEM提供的DBC数据库，且具有较高的通用性。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 包括, 测试, 装置, 具有, 仿真, 方法, 设备","车辆, 测试"
CN112486142A,CN202011355790.7,"本发明公开了一种将虚拟化IO架构和汽车应用集成在ECU的方法及系统，ECU包括：硬件架构和软件架构，硬件架构包括：至少一个芯片系统，至少一个芯片系统中的每个芯片系统包括：多处理器核以及用于与传感器或执行器进行接口的IO设备，软件架构包括：在至少一个具有多处理器核的芯片系统上运行的多核操作系统。本发明由虚拟化IO驱动软件和虚拟机监控器实现将虚拟化IO架构和汽车应用集成在ECU上，运行环境由ECU中的硬件架构和实时操作系统提供，并可被对称地部署在硬件架构的各个具有多处理器核的芯片系统上，使得安装在电子控制单元上的各个汽车应用能够共享电子控制单元上的传感器、执行器和其它IO设备。
",G05B23/02,北京经纬恒润科技股份有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 实现, 包括, 具有, 方法, 处理, 设备, 控制, 系统","汽车, 传感器, 控制"
CN112130545A,CN202011039470.0,"本发明公开了一种远程驾驶的接管系统及接管方法，接管系统包括：车载控制模块、后台服务器和远程驾驶端，车载控制模块采集车辆相关信息并经后台服务器将车辆相关信息转发至远程驾驶端，远程驾驶端根据预设接管决策确定车辆相关信息对应的接管方式，并向车载控制模块发送与接管方式对应的接管车辆指令，实现对车辆的远程驾驶接管。本发明对车辆的远程驾驶接管不再简单的依赖于自动驾驶的故障诊断功能及远程驾驶接管请求，且接管方式可以为强请求接管、弱请求接管和零请求接管中的一种，从而满足了远程驾驶的传统接管工况，扩展了远程驾驶的使用范围。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"实现, 包括, 方法, 控制, 系统","车辆, 驾驶, 诊断, 控制"
CN111694347A,CN202010598436.0,"本发明公开了一种基于总线开发环境的功能测试方法及装置，应用于总线盒子，该总线盒子连接在被测控制器和总线之间，具有模拟网关功能且设置有测试配置界面，方法包括：接收用户通过测试配置界面输入的测试配置参数，并依据测试配置参数进行相应的测试配置；接收总线数据，并基于测试配置对总线数据进行处理；若存在处理后的总线数据，将处理后的总线数据发送至被测控制器，以实现被测控制器相关功能的测试。所述方法及装置实现中不需要搭建复杂的测试框架并配备昂贵的设备，在实车测试过程中，对于难以制造的工况可通过上述方法或装置直接改变接收到的数据信号，使其满足前述工况条件，然后再输出给被测控制器，对其进行功能测试。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"模拟, 实现, 包括, 测试, 装置, 具有, 方法, 设置, 处理, 设备, 控制, 配置","测试, 控制"
CN111475358A,CN202010244773.X,"本申请公开一种控制器接口的自动化测试方法及装置。该方法包括：获取第一工作表，该第一工作表存储有控制器的多个待测接口的接口参数；硬件在环测试平台建立与标定工具之间的通信，启动数据处理软件，由数据处理软件读取第一工作表中多个待测接口的接口参数，基于读取到的接口参数生成变量文件，硬件在环测试平台从变量文件中依次读取每个待测接口的接口参数，在每次读取到待测接口的接口参数后，通过标定工具获取该待测接口的信号实际值，比对该待测接口的总线信号发送值和信号实际值，生成该待测接口的测试结果，输出测试结果。基于本申请公开的技术方案，能够自动化完成对控制器中大量接口的测试，以降低测试人员的工作量。
",G06F11/22,北京经纬恒润科技有限公司,集群化测试技术,3.0,关键词匹配: 测试平台; IPC分类号匹配: G06F11/22,"平台, 包括, 测试, 装置, 方法, 生成, 处理, 控制, 工具","通信, 测试, 控制"
CN111381583A,CN202010208234.0,"本发明提供的控制系统故障监测方法及装置，应用于汽车技术领域，该方法调用与目标控制系统，以及与目标控制系统相关的控制系统相对应的控制系统故障监测程序，以获取与目标控制系统相关的各目标故障监测项在当前监测周期内的状态信息，并针对每一目标故障监测项，对目标故障监测项的状态信息是否满足预设有效性判定条件进行判断，将状态信息满足相应预设有效性判定条件的目标故障监测项的状态刷新为相应状态信息对应的状态，本方法调用的控制系统故障监测程序，独立于目标控制系统，以及与目标控制系统相关的控制系统，因此，如果目标控制系统或相关控制系统发生更新，只需更新相应的目标故障监测项即可，程序更新过程简化，缩短更新周期。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 装置, 方法, 控制, 系统","汽车, 控制"
CN111258294A,CN202010013702.9,"本发明公开了一种故障容错时间测试系统及方法，测试系统包括：中央控制模块、无线通信模块、数据记录模块、IMU和故障注入模块，将故障注入模块设置在被测无人驾驶汽车的扭矩传感器和EPS控制器之间，通过响应中央控制模块发送的故障注入命令，在故障持续时间内，向扭矩传感器输出的扭矩信号注入与故障类型对应的故障信号，中央控制模块分析数据记录模块记录的车辆侧向加速度以及车辆侧向加速度的采集时刻，确定被测无人驾驶汽车的故障容错时间。本发明基于量化指标计算故障容错时间，提高了故障容错时间的计算精度。并且，通过上位机和无线通信模块远程控制被测无人驾驶汽车的行驶和故障注入，减少了车辆失控可能对实验人员造成的伤害。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G05B23/02,"包括, 测试, 方法, 设置, 控制, 系统, 计算","驾驶, 测试, 汽车, 控制, 车辆, 通信, 传感器"
CN111007834A,CN201911282763.9,"本发明公开了一种ADAS路径规划功能实验室测试系统及方法，该测试系统包括：HIL系统和导航信号模拟器，HIL系统根据智能车辆的行驶路径信息，得到智能车辆对应的仿真车辆的道路环境以及行驶路径，并仿真得到仿真车辆的实时经纬度坐标，通过导航信号模拟器实现对仿真车辆的导航信号的模拟，进而对实现被测ADAS控制器的定位模拟。本发明可以提供精确、可信且可重复的信号和干扰效应，无需人员驾驶车辆，因此，路径规划功能测试的风险低，成本低，测试环境具备重复性，可以作为ADAS控制器开发验证阶段可靠且统一的测试手段，且不会对被测ADAS控制器的工作性能造成影响，并提高了测试效率。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G05B23/02,"提供, 实现, 包括, 测试, 仿真, 方法, 控制, 系统, 模拟","驾驶, 测试, 导航, 验证, 控制, 车辆"
CN110703022A,CN201910983873.1,"本发明提供了一种复杂电磁环境构建系统、车辆电磁抗干扰测试系统及方法，其中，复杂电磁环境构建系统通过射频源单元、功放单元、功率合并单元、天线单元以及控制设备能够构建出车辆所处的复杂电磁环境；基于复杂电磁环境构建系统可构建车辆电磁抗干扰测试系统，基于复杂电磁环境构建系统构建的车辆电磁抗干扰测试系统能够对车辆抗电磁干扰的能力进行准确测试，且该系统轻便、具有通用性，有利于行业内的工程实际应用。
",G01R31/00,北京经纬恒润科技有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G01R31/00,"提供, 测试, 具有, 方法, 设备, 控制, 系统","车辆, 测试, 控制"
CN110514931A,CN201910806662.0,"本发明提供了一种具有车载以太网功能的设备的电磁兼容测试系统及方法，系统包括：负载板、被测设备、电磁兼容测试设备和监控设备；在电磁抗干扰测试环境中，负载板产生基准数据，将基准数据发往被测设备，被测设备接收数据，并将接收的数据回传至负载板，负载板接收回传数据，根据基准数据和回传数据确定车载以太网的数据传输状态，将数据传输状态发送至监控设备；被测设备获取自身的通信状态，通过自身的通信状态确定车载以太网的通信性能，将通信性能发送至监控设备；在电磁骚扰测试环境中，电磁兼容测试设备测量被测设备与负载板进行数据传输时的电磁骚扰强度。本发明能够全面、准确地确定被测设备中车载以太网模块的电磁兼容性能。
",G01R31/00,北京经纬恒润科技有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G01R31/00,"提供, 包括, 测试, 具有, 方法, 设备, 系统","通信, 测试, 数据传输"
CN110456766A,CN201910695805.5,"本申请提出一种生成分析用例的方法及装置，该方法包括：根据待分析内容，确定从分析对象的整体工作过程中筛选出与所述待分析内容对应的目标工作场景的筛选规则；还可以根据该分析内容，确定从所述目标工作场景中划分出与该待分析内容对应的场景切片的划分规则；以及根据所述待分析内容，确定评价准则；最后，综合所述筛选规则，和/或所述划分规则，和/或所述评价准则，生成分析用例。该分析用例可以作为数据分析软件的参考，执行对分析对象的分析，从而可以满足高效的数据后处理分析对分析用例的需求。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"包括, 装置, 生成, 方法, 处理",通用
CN110244696A,CN201910550580.4,"本发明公开了一种车身横向控制方法及ECU，ECU包括前向毫米波雷达、道路边界识别模块以及车身横向控制模块，道路边界识别模块从前向毫米波雷达获取至少一个目标物的参数，根据至少一个目标物的参数从至少一个目标物中确定出边界目标物，根据边界目标物确定当前时刻对应的边界曲线参数及边界曲线质量，将当前时刻对应的边界曲线参数及边界曲线质量输入车身横向控制模块以使车身横向控制模块根据当前时刻对应的边界曲线参数及边界曲线质量对车身进行横向控制。本发明的ECU进行车身横向控制时，是基于前向毫米波雷达获取的至少一个目标物的参数对车身进行横向控制的，相对于基于视觉传感器，受外界环境因素影响较小，保证ECU对车身的横向控制更准确。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"方法, 包括, 控制","雷达, 传感器, 控制"
CN109901559A,CN201910252929.6,"本发明提供了一种T‑BOX测试系统及方法，信道模拟器仿真T‑BOX进行数据传输时的信号传输环境，信令测试仪仿真T‑BOX进行数据传输时所使用的通信链路，导航信号仿真系统仿真导航装置输出的导航信号，实时系统与T‑BOX进行硬线信号和/或总线信号的信息交互，以上各部分与T‑BOX的服务后台可以组成一个闭环的仿真测试环境来对T‑BOX进行测试。通过本发明提供的T‑BOX测试系统，信道模拟器和信令测试仪为T‑BOX提供接近真实的信号传输过程，导航信号仿真系统为T‑BOX提供接近真实的导航信息，实时系统为T‑BOX提供接近实车的交互环境，通过本发明仿真实车测试环境，对T‑BOX的功能和性能进行测试。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G05B23/02,"提供, 测试, 装置, 仿真, 方法, 系统, 模拟","通信, 测试, 数据传输, 导航"
CN109883448A,CN201910243344.8,"本发明提供地图盒子测试方法及系统，以降低测试成本、提高工作效率。所述测试系统至少包括：仿真数据生成单元和测试单元；所述方法包括：所述仿真数据生成单元向地图盒子实时输出虚拟测试场景中车辆的运行仿真数据；所述仿真数据生成单元向所述地图盒子实时输出图像仿真数据；所述地图盒子使用接收的运行仿真数据和图像仿真数据计算出位置信息；所述测试单元将所述位置信息与标准位置信息进行对比，得到测试报告。在本发明实施例中，采用虚拟测试场景取代实际道路，对地图盒子计算所需的运行数据和图像数据进行了仿真模拟，在此过程中并不需要实车参与，也不需要大量的场地测试，可在降低测试成本的同时，提高工作效率。
",G01C25/00,北京经纬恒润科技有限公司,集群化测试技术,1.0,关键词匹配: 测试系统,"提供, 包括, 测试, 仿真, 方法, 生成, 系统, 计算, 模拟","车辆, 测试"
CN109709937A,CN201811628288.1,"本发明提供了电子控制单元功能配置方法、装置及电子控制单元，ECU配置有功能区分标识线，并且配置有非易失存储器，该非易失存储器用来存储该ECU对应的配置功能信息。在为ECU配置相应的功能时，先判断非易失存储器内的存储信息是否有对应的配置功能；若有则依据该配置功能配置ECU；若没有则读取功能区分标识线的标识线状态，并依据该标识线状态对应的配置功能配置ECU。可见，该方法通过功能区分标识线的标识线状态，以及在非易失存储器内存储配置功能的相关信息来区分功能相似的ECU的功能，避免了功能类似的ECU混装引起相应功能失效或错误。而且，不需要为功能相似的ECU分别设计不同结构的硬件进行区分，因此降低了管理成本和产品开发成本。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 装置, 方法, 控制, 配置",控制
CN109164426A,CN201811010440.X,"本发明提供一种雷达探测范围的测试系统及方法，该测试系统的移动平台接收到上位机发送的移动控制指令时，控制移动平台移动，并分别接收移动平台上第一传感器和第二传感器对雷达布上网格线的检测结果，以按照所述雷达布上网格线的方向控制所述测试杆移动，并将所处雷达布的行列位置信息发送至所述上位机；上位机获取雷达系统对测试杆的响应结果，并向移动平台发送移动控制指令。由于基于移动平台完成测试杆的移动后基于上位机可以获取到雷达系统对测试杆的响应结果，因此避免了人为移动测试杆且人为记录雷达系统对测试杆的响应结果，进而提高了测量效率，且避免了人为因素对测量结果的影响进而提高了测量的准确性。
",G01S7/40,北京经纬恒润科技有限公司,集群化测试技术,1.0,关键词匹配: 测试系统,"提供, 平台, 测试, 方法, 控制, 系统, 检测","测试, 雷达, 控制, 检测, 传感器"
CN108762234A,CN201810595082.7,"本发明提供一种车身控制器及其控制方法，属于车辆控制器领域，车身控制器包括MCU、SPI转CAN模块以及两个CAN收发器；第一CAN收发器连接MCU的CAN接口，SPI转CAN模块分别连接第二CAN收发器、MCU的SPI接口；第一CAN收发器用于连接第一CAN网段；第二CAN收发器用于连接第二CAN网段；MCU通过两个CAN收发器进行CAN报文的接收和发送，实现了两个CAN网段的数据交互。本发明提供的车身控制器在实现常规控制器功能的基础上，还实现了CAN网关功能，扩展了车身控制器的应用范围，并且，可以避免车辆搭载单独的CAN网关控制器，进而降低了整车成本、节省了车辆空间以及减小了功耗。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 实现, 包括, 方法, 控制","车辆, 控制"
CN108710692A,CN201810495019.6,"本发明提供了一种汽车零部件生产线测试系统及方法，该系统由测试配置模块和测试执行模块构成，测试配置模块包括测试配置管理子模块；测试配置管理子模块预先在数据库中创建工单和测试序列；测试执行模块则将工单中汽车零部件的产品信息和被测零部件的产品信息进行匹配，确定被测零部件的产品信息所在的目标工单；根据目标工单中所记录的序列信息，在测试序列中定位用于测试被测零部件的目标测试序列，并下载；基于目标工单中所记录的设备信息，确定用于测试被测零部件的目标测试设备；控制目标测试设备运行所下载的目标测试序列。基于该系统，无需多部门协同即可完成自动测试，可以满足不同被测零部件的测试需求，提高资源利用率。
",G06F17/30,北京经纬恒润科技有限公司,集群化测试技术,1.0,关键词匹配: 测试系统,"提供, 包括, 测试, 方法, 设备, 控制, 系统, 配置","测试, 汽车, 控制"
CN108037753A,CN201711347622.1,"本发明提供一种数据检测方法和装置。其中，方法包括：读取存储区中目标地址对应的数据，并采用预设的校验方法，得到所述数据的校验值；依据所述存储区中所有数据的校验值，以及预先存储在所述存储区中预设地址的标准校验值，判断所述存储区存储的数据是否发生故障。本发明提供的一种数据检测方法和装置，实现了对汽车电子控制单元中存储器中的数据进行安全检测，能够及时发现存储器中的数据发生故障。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 实现, 包括, 装置, 方法, 控制, 检测","检测, 汽车, 控制"
CN107544473A,CN201710951594.8,"本发明实施例公开了一种重编程方法及装置，该方法包括：判断是否接收到上电信号或复位信号；若接收到上电信号或复位信号，在运行ECU中的应用程序前，根据诊断仪发送的重编程请求报文，判断是否进入重编程模式；若进入重编程模式，运行默认流程重编程或更新流程重编程对应用程序进行重编程。可见，在应用程序运行前，增加了进入重编程模式的判断，并以进入重编程模式判断步骤取代了现有技术中以第一预设值和第二预设值的方式进行重编程的判断，由于进入重编程模式的步骤在应用程序运行之前进行，所以，本发明实施例提供的重编程方法及装置不受应用程序功能正常与否的影响，即使应用程序存在功能缺陷，对应用程序的重编程也能正常执行。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 方法, 包括, 装置",诊断
CN105652859A,CN201610201728.X,"本发明公开了一种控制器产品的早期生产遏制系统，包括上位机和快速原型设备；上位机用于确定所需测试的控制器产品的产品信息和针对控制器产品的测试项目信息，并在对控制器产品进行测试时向快速原型设备发送产品信息和测试项目信息；快速原型设备用于从基于模型开发的测试程序库中下载对应的测试程序，并在根据测试程序、产品信息和测试项目信息对控制器产品进行测试时，向控制器产品输出模拟信号。本申请采用的快速成型设备的通用性较高，可以用于测试不同的汽车控制器，而且快速成型设备可以从基于模型开发的测试程序库中下载对应的测试程序，基于模型开发测试程序的方法比较简单，可以降低系统成本并简化系统实现。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"实现, 包括, 模型, 测试, 方法, 设备, 控制, 系统, 模拟","测试, 汽车, 控制"
CN105530654A,CN201510984654.7,"本申请公开了一种自组网通信设备测试方法及系统，测试系统包括：基准自组网通信设备、无线通信环境模拟设备、PC机和测试接口设备，PC机内包含有自组网模拟测试环境。本发明采用软硬件协同测试思想，通过测试接口设备将支持硬件重编程的基准自组网通信设备和支持协议重组的自组网模拟测试环境连接在一起，构建了一种可灵活重构的自组网通信设备的测试系统。该测试系统能够实现链路测试、组网测试和端到端测试等多种测试场景，对自组网通信设备中进行无线通信的硬件设备和自组网协议栈软件进行可伸缩性测试，相比现有技术而言，大大减少了节点部署和系统管理的难度，从而降低了对自组网通信设备的测试难度。
",H04W24/00,北京经纬恒润科技有限公司,集群化测试技术,1.0,关键词匹配: 测试系统,"实现, 包括, 测试, 方法, 设备, 系统, 模拟","通信, 测试"
CN105223443A,CN201510677408.7,"本发明公开了一种硬件自动化测试方法，包括：生成控制待测试硬件所需加载的负载的第一控制指令；发送第一控制指令至所述负载，并依据第一控制指令启动负载；生成待测试硬件的命令序列；发送命令序列至待测试硬件；接收待测试硬件在加载负载的情况下，执行命令序列反馈的执行结果。本发明能够提高测试效率，且能够较为全面的对硬件的功能和性能进行测试。本发明还公开了一种硬件自动化测试系统及设备。
",G01R31/00,北京经纬恒润科技有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G01R31/00,"包括, 测试, 方法, 生成, 设备, 控制, 系统","测试, 控制"
CN104898637A,CN201510142225.5,"本申请公开了一种汽车控制器的自刷新方法及系统，应用程序在接收上位机发送的刷新指令之前，通过比较当前应用程序校验和与正确校验和来判断当前是否发生损坏；当引导程序对应用程序刷新完成后，引导程序通过比较刷新后的应用程序校验和与正确校验和来判断应用程序在刷新过程中是否出现损坏。可以看出，本发明采用校验和比较的方式实现对应用程序在刷新前和刷新后的诊断，从而可以及时发现应用程序出现损害的情况，避免应用程序执行未知操作，进而保证了产品的安全性。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"系统, 方法, 实现, 控制","汽车, 诊断, 控制"
CN104699083A,CN201510150429.3,"本申请公开了一种电子控制单元的测量方法及系统，应用于通过控制器局域网总线连接的ECU中的任意一个ECU中，该ECU与上位机连接，该ECU能够遵循XCP协议，并完成协议转换，当它在确定与待测ECU建立会话连接时，会根据配置信息按照XCP协议与待测ECU进行信息交互，从而实现对待测ECU的测量，即实现对车辆总线上其它ECU的测量，这样在对整车进行测量时，就不需要逐个使待测ECU与车辆总线分离，从而简化了整个测量过程，提高了测量效率。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"实现, 方法, 控制, 系统, 配置","车辆, 控制"
CN104049631A,CN201410321873.2,"本发明实施例所述的一种信号输出方法及装置，向待测的PEPS控制器的有效引脚输出变频和/或变幅信号，向其它引脚输出定频或定幅信号，其中，有效引脚为智能钥匙与车身实际构成预定位置关系时，待测的PEPS控制器中能够接收到变频和/或变幅信号的引脚，变频和/或变幅信号由智能钥匙发出的射频信号变换得到，可见，本发明实施例所述的方法及装置，可以模拟实际使用中，智能钥匙与车身实际构成预定位置关系时，PEPS控制器的各个引脚接收信号的情况，而无需通过在实车或仿实车的环境下，由人工携带智能钥匙，通过人工移动的方式模拟智能钥匙与车身的不同的相对位置的方式进行测试，所以，与现有的测试方法相比，能够提高测试效率。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"测试, 装置, 方法, 控制, 模拟","测试, 控制"
CN103926918A,CN201410174898.4,"本申请提供了一种硬件在回路设备的自检方法，包括：上位机发送被检测通道对应的通道检测命令至硬件在回路HIL设备；以及，接收所述HIL设备返回的通道检测结果，所述通道检测结果为所述HIL设备针对所述通道检测命令，控制所述被检测通道进行相应操作后得到的结果；以及，判断所述通道检测结果是否在所述被检测通道对应的预设标准阈值范围内；若是，确定所述被检测通道符合使用标准；若否，确定所述被检测通道不符合使用标准。因此，本申请提高了检测效率，提高了检测结果的可靠性。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 包括, 方法, 设备, 控制, 检测","检测, 控制"
CN103529822A,CN201310482297.5,"本申请公开了一种汽车故障检测方法和装置，其通过采集汽车的故障监控参数，判断所述故障监控参数是否满足预设故障判断条件，当所述故障监控参数满足预设故障判断条件时，确定故障类型，获取、输出并保存预设时段的运行状态信息；其中，所述故障监控参数包括发动机转速、汽车水温、行车速度和机油压力中的一种；应用上述技术方案，可在故障发生时，即故障监控参数满足预设故障条件时，确定相应的故障类型，并将相关时段的运行状态信息输出并保存，使得相关设备或技术人员可通过分析这些运行状态信息来对故障进行准确定位、进而可快速采取相应的措施排除故障，解决了现有技术的问题。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"包括, 装置, 方法, 设备, 检测","检测, 汽车"
CN103389730A,CN201310342075.3,"本发明提供了一种电动助力转向控制参数的配置方法及装置，所述方法及装置应用于电动助力转向系统中，该方法在系统进入参数配置模式后，接收上位机发送的配置信息，并根据所述配置信息调整相应的控制参数。由于该电动助力转向控制参数的配置方法及装置应用的电动助力系统中设置有参数配置模式，因此，当系统进入参数配置模式后，就可以接收上位机发送的配置信息，并在线调整控制参数。通过该方法及装置，电动助力转向控制参数的调整效率大大提高；而且，由于不需要再离线调整控制参数，因此能够满足现代研发工作实时监控电动助力转向系统的要求。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 装置, 方法, 设置, 控制, 系统, 配置",控制
CN103257648A,CN201310125302.7,"本发明公开了一种汽车标定系统，应用于上位机，包括：第一标定信息处理模块、第一封装模块和第一驱动模块；其中，第一标定信息处理模块与所述第一封装模块相互独立，所述第一封装模块中，包括至少一个封装单元，每一个封装单元对应一种总线类型，可以应用于至少一个总线类型的总线上，且每一个封装单元也对应一种总线类型，因此，各个封装单元也相互独立，可以应用到不同类型的总线上，而当需要移植到新的总线类型上，而封装模块中没有与该新的总线类型相对应的封装单元时，本领域技术人员只需要开发相应的封装单元即可，不需要对整个标定系统进行开发，因此，节省了开发成本，提高了汽车标定系统的通用性和可移植性。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"系统, 包括, 处理",汽车
CN103176904A,CN201310109775.8,"本申请提供了一种基于仿真测试平台软件架构的测试方法，仿真测试平台软件架构包括：模型变量描述文件、转发单元和多个监控单元，所述转发单元注册与当前测试任务相对应的模型变量描述文件和与当前测试任务相对应的监控单元组；所述转发单元接收所述监控单元组中待测试监控单元发送的修改变量请求，并将所述修改变量请求发送至所述监控单元组内除所述待测试监控单元之外的其他监控单元；接收到所述修改变量请求的监控单元对所述修改变量请求进行处理。因此，本申请可以在一种仿真测试中动态配置仿真测试平台软件架构中的模块，且在将仿真测试平台软件架构应用到不同的仿真测试中时，只需将不同测试任务对应的监控单元组注册到转发单元即可实现。
",G06F11/36,北京经纬恒润科技有限公司,集群化测试技术,3.0,关键词匹配: 测试平台; IPC分类号匹配: G06F11/36,"提供, 平台, 实现, 包括, 模型, 测试, 仿真, 方法, 处理, 配置",测试
CN102879684A,CN201210372341.2,"本申请实施例公开了一种电器部件的测试系统及方法，其中系统包括：至少两个用于安装电器部件的工装；为测试系统供电的电源系统；选定待测电器部件，产生对应测试指令并输出，及接收与所述测试指令对应的反馈信息，依据所述反馈信息确定待测电器部件的测试结果的工控机；分别与所述工装和所述工控机相连，接收所述工控机输出的测试指令，依据所述测试指令，通过与安装待测电器部件的工装相绑定的测试通道，向待测电器部件输出相应的激励信号，及通过与安装待测电器部件的工装相绑定的测试通道，采集所述激励信号下安装待测电器部件所反馈的信息，将反馈信息发送给所述工控机的信号调理板。本发明实现了电器部件的测试系统对多种电器部件的测试。
",G01R31/00,北京经纬恒润科技有限公司,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G01R31/00,"实现, 包括, 测试, 方法, 系统",测试
CN102608993A,CN201210062419.0,"本发明公开了一种网络自动化测试方法、机柜及测试主机。其方法为：获取待测机动车的相关信息；录入获取ECU时的报文信息和执行ECU测试时所需的测试信息；依据报文信息和相关信息确定ECU并选取执行测试的测试用例；触发测试，将测试信息写入配置文件内，并根据配置文件执行对应各个测试项的测试用例；当测试结束时，获取各个测试项对应的测试结果并生成测试报告。本发明采用上述自动测试的过程，能够避免手动测试过程中对不同的ECU进行相同、重复测试及反复分析、判断和整理的重复过程，降低了技术人员的工作量；同时，还能够降低测试所耗费的时间，以及，避免手动测试时必然引入的人为误差，进一步提高了测试结果的准确性。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"生成, 方法, 测试, 配置","机动车, 测试, 网络"
CN101751033A,CN200810227836.X,"本发明提供一种车辆远程监测诊断系统，包括后台运营中心、车载终端，所述后台运营中心包括数据库服务器、WEB服务器、监控配置服务器、网关服务器和通讯子系统。所述WEB服务器可控制配置车辆的信息、处理车辆用户注册和定制信息，所述通讯子系统可读取预警条件，将预警条件和车载终端发送的数据进行对比，若达到预警条件，则将预警指令发送给网关服务器，网关服务器根据设定的预警形式，实时发送预警信息。本发明可以根据不同车辆及不同用户的特定需求，定制所需要诊断、预警的信息，可远程发出相关指令排除故障或控制车辆以避免发生故障或指示驾驶者进行维护或维修。
",G05B23/02,北京经纬恒润科技有限公司,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 包括, 处理, 控制, 系统, 配置","车辆, 驾驶, 诊断, 控制"
