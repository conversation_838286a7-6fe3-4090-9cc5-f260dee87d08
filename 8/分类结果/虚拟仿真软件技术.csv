﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN120494008A,CN202510621954.2,"本发明提供一种异构平台部署AI模型的方法及装置，获取待部署的AI模型的模型文件和待输入AI模型的图像数据；利用异构平台中的第一浮点数数字信号处理器，对图像数据进行预处理，得到预处理后的图像数据；基于异构平台中的第二浮点数数字信号处理器、模型文件和预处理后的图像数据进行推理，得到推理结果；利用异构平台中的第三浮点数数字信号处理器，对推理结果进行后处理；通过可视化节点展示后处理之后的推理结果和图像数据。在本方案中，调用异构平台上的各个硬件，执行不同的AI模型部署流程，相比于直接利用CPU执行部署流程的方式，实现了充分利用异构平台各个硬件的计算资源、降低CPU的计算负担的目的。
",G06N3/063,北京经纬恒润科技股份有限公司,虚拟仿真软件技术,2.0,IPC分类号匹配: G06N3/063,"提供, 平台, 实现, 模型, 装置, 方法, 处理, 计算",通用
CN120411330A,CN202510637875.0,"本申请公开了一种纹理映射变换方法、装置及渲染方法及装置，基于待处理像素点对应的屏幕空间坐标，确定与待处理像素点对应的片元坐标；根据与待处理像素点对应的图像信息，确定有向距离场；基于有向距离场以及每一待处理像素点对应的片元坐标，确定每一待处理像素点对应的有符号距离值；根据每一待处理像素点对应的有符号距离值，确定纹理映射区域；将纹理映射区域中对应的像素点的片元坐标进行纹理映射，得到纹理坐标，从而构建了适用于各种纹理映射变换需求的纹理映射方法，将通过着色器程序执行该纹理映射变换方法，在进行渲染的时候可以直接调用该着色器程序，在不增加硬件成本的前提下，提升了动态渲染效率和效果。
",G06T15/04,北京经纬恒润科技股份有限公司,虚拟仿真软件技术,2.0,IPC分类号匹配: G06T15/04,"方法, 处理, 装置",通用
CN119578359A,CN202411629322.2,"本发明提供一种PCB走线添加电容的方法、装置、电子设备和存储介质，计算基础电容值对应的PCB走线面积；将仿真PCB板导入仿真软件以添加PCB测试走线，根据PCB测试走线的测试电容不断调整PCB测试走线，直至最终PCB走线的测试电容等于基础电容值，若接收到包含待添加电容值的添加指令，计算待添加电容值对应的最终PCB走线的个数；在目标PCB板中添加对应个数的最终PCB走线，得到最终PCB板。通过预设基础电容值，结合仿真软件对PCB走线进行调试，确定最终PCB走线。通过在PCB板中线性叠加基础电容值即最终PCB走线，满足PCB板临时添加电容的需求，达到减少PCBA制造成本及PCB贴片生产时间的效果。
",G06F30/398,北京经纬恒润科技股份有限公司,虚拟仿真软件技术,3.0,关键词匹配: 仿真软件; IPC分类号匹配: G06F30/398,"提供, 测试, 装置, 仿真, 方法, 设备, 计算",测试
CN118690587A,CN202411173553.7,"本申请公开了一种重载线控底盘的能源消耗仿真方法及装置，所述方法包括：获取待仿真重载线控底盘的仿真工况以及实际数据；将仿真工况输入预先构建好的仿真能耗模型中，得到待仿真重载线控底盘对应的仿真能耗；其中，仿真能耗模型由预先构建的底盘模型与整车控制策略模型之间的联合仿真通信构成；底盘模型预先通过样本重载线控底盘的各个关键部位对应的元件进行组合；基于实际数据，计算重载线控底盘的实际能耗；基于仿真能耗以及实际能耗，确定仿真能耗模型的能耗仿真误差。从而通过底盘模型与整车控制策略模型之间的联合仿真通信构成的仿真模型，能够实现高精度地仿真出实际港口运营工况中的能耗，进而有效地降低了能耗仿真的误差结果。
",G06F30/20,经纬恒润(天津)研究开发有限公司,虚拟仿真软件技术,3.0,关键词匹配: 仿真模型; IPC分类号匹配: G06F30/20,"实现, 包括, 模型, 装置, 仿真, 方法, 控制, 计算","通信, 控制"
CN118260993A,CN202410346575.2,"本发明涉及电机技术领域，公开了一种温度场监测方法、系统、计算机设备及存储介质，本发明通过获取永磁同步电机的运行状态参数；基于运行状态参数，确定永磁同步电机的热源；采用数字孪生方式，根据热源对永磁同步电机的温度场进行在线模拟监测，生成温度场监测数据。本发明解决了现有永磁同步电机温度场仿真过程复杂，效率低，且仿真结果与真实环境不贴合的问题；实现了在电机运行过程中可以根据参数变化实时调整仿真模型精度的效果。
",G06F30/23,北京经纬恒润科技股份有限公司,虚拟仿真软件技术,4.0,"关键词匹配: 仿真模型, 数字孪生; IPC分类号匹配: G06F30/23","实现, 模型, 仿真, 方法, 生成, 设备, 系统, 计算, 模拟",通用
CN117537916A,CN202311374403.8,"本申请实施例的一种HUD噪声确定方法和装置，该方法包括：在声源中心与噪声预测点之间的距离大于预设距离的情况下，将等效点声源的噪声值以及HUD外壳的模型输入声学仿真软件，以输出HUD外壳的第一振动结果；基于第一振动结果和测试位置，得到测试位置的第一噪声值；在电机底座的激励施加位置施加激励施加值，得到HUD外壳的第二振动结果；基于第二振动结果和测试位置，得到测试位置的第二噪声值；对第一噪声值和第二噪声值进行叠加，得到测试位置的目标噪声值。通过对电机运行时本体噪声引起的HUD噪声和电机运行时电机底座振动引起的HUD噪声分别进行仿真，以在缺少HUD设计参数的情况下，仿真得到测试位置的噪声。
",G01H17/00,北京经纬恒润科技股份有限公司,虚拟仿真软件技术,1.0,关键词匹配: 仿真软件,"包括, 模型, 装置, 测试, 仿真, 方法",测试
CN117421922A,CN202311475109.6,"本申请公开了一种仿真模型生成方法及装置，其中方法包括：获取初始文件，初始文件包括用于进行仿真的多组仿真信息，每组仿真信息包括多个仿真子信息；对初始文件进行解析，得到中间文件，中间文件包括多组中间信息，每组中间信息包括一组仿真信息中的仿真子信息；从多组中间信息中选择目标中间信息；根据目标中间信息中的仿真子信息，构建仿真模型。上述中，对初始文件进行解析，以对初始文件记录的多个仿真子信息进行梳理，将属于相同组的多个仿真子信息记录在中间文件的相邻位置，便于用户在需要进行以太网信号仿真时，对相同组的多个仿真子信息进行查看和选择，提高了仿真效率。
",G06F30/20,北京经纬恒润科技股份有限公司,虚拟仿真软件技术,3.0,关键词匹配: 仿真模型; IPC分类号匹配: G06F30/20,"包括, 模型, 装置, 仿真, 方法, 生成",通用
CN117313241A,CN202311278121.8,"本申请提供了一种车架仿真方法、装置及设备，涉及车辆仿真试验技术领域，其中方法包括：搭建多个构件模型，每个所述构件模型对应待仿真车架中的一个构件；基于所述待仿真车架中的每个构件的连接关系，对多个所述构件模型进行转动自由度设置，获得车架模型，其中，所述连接关系包括运动副连接；将所述车架模型与预获取的轮胎模型进行仿真搭建，获得待仿真模型；对所述轮胎模型施加预设的载荷；基于所述载荷对所述待仿真模型进行仿真计算，获得所述待仿真模型的静强度仿真结果。采用上述步骤可以提高车架静强度仿真结果的准确性。
",G06F30/15,经纬恒润(天津)研究开发有限公司,虚拟仿真软件技术,3.0,关键词匹配: 仿真模型; IPC分类号匹配: G06F30/15,"提供, 包括, 模型, 装置, 仿真, 方法, 设置, 设备, 计算",车辆
CN117077465A,CN202310721525.3,"本申请提供一种电池包箱体结构的优化方法及装置，根据目标电池包的初始参数构建的电池包箱体数模中n个模组的排布方式确定质点坐标；利用仿真软件根据电池包箱体数模生成箱体总成的有限元模型，根据质点坐标确定质点在箱体总成的有限元模型中的位置；为箱体总成的有限元模型配置相应的材料参数，将赋予预设质点质量的质点分别耦合到每个加强肋上，得到目标箱体总成的有限元模型；根据预设工况参数和目标箱体总成的有限元模型进行目标工况的整车行驶模拟得到电池包箱体仿真强度；若仿真强度未达标，输出相应的提示信息；当检测到技术人员根据提示信息对电池包箱体数模的结构优化完成后，返回质点坐标确定步骤直至电池包箱体仿真强度达标为止。
",G06F30/23,经纬恒润(天津)研究开发有限公司,虚拟仿真软件技术,3.0,关键词匹配: 仿真软件; IPC分类号匹配: G06F30/23,"提供, 模拟, 模型, 装置, 仿真, 方法, 生成, 检测, 配置",检测
CN117058349A,CN202311013702.9,"本申请公开了一种数字孪生系统的集装箱显示方法、装置、设备及存储介质，涉及通信技术领域。其方法包括：在目标区域内设置有多个集装箱的情况下，根据各集装箱的目标特征参数信息，在数字孪生系统中生成各集装箱的多个集装箱模型，不同集装箱模型配置有不同的显示质量信息；获取各集装箱在数字孪生系统的数字孪生界面中的显示占比；根据各集装箱在数字孪生系统的数字孪生界面中的显示占比，在集装箱的多个集装箱模型中确定目标集装箱模型，目标集装箱模型的显示质量信息与显示占比对应；将各集装箱的目标集装箱模型显示于数字孪生界面。根据本申请实施例，不仅能够保证数字孪生系统流畅运行，还具有不错的显示效果。
",G06T19/20,北京经纬恒润科技股份有限公司,虚拟仿真软件技术,1.0,关键词匹配: 数字孪生,"包括, 模型, 装置, 具有, 方法, 设置, 生成, 设备, 系统, 配置",通信
CN115035227A,CN202210634379.6,"本申请公开了一种点云数据的处理方法、装置及电子设备，方法包括：获得第一点云数据；根据待检测对象对应的第一高度阈值和第二高度阈值，对第一点云数据进行筛选，以得到第二点云数据；根据待检测对象对应的几何形态参数，对第二点云数据进行筛选，以得到第三点云数据；依据第三点云数据与数据采集设备之间的距离，对第三点云数据进行下采样，以得到N个下采样点云数据；对N个下采样点云数据进行滤波处理，以得到第四点云数据；对第四点云数据采用随机抽样一致算法进行运算，以得到第五点云数据；对第五点云数据采用最远点采样算法进行运算，以得到第六点云数据。
",G06T15/00,北京经纬恒润科技股份有限公司,虚拟仿真软件技术,2.0,IPC分类号匹配: G06T15/00,"包括, 装置, 算法, 方法, 处理, 设备, 检测",检测
CN113962389A,CN202111447637.1,"本发明公开了一种神经网络压缩方法及系统，在待压缩神经网络中添加显著性表征网络；然后在保持待压缩神经网络的参数不变的情况下，训练添加显著性表征网络后的待压缩神经网络，训练完成后，利用与预设压缩需求对应的目标样本集得到用于表征待压缩神经网络中输出通道重要性的信息；最后基于用于表征待压缩神经网络中通道重要性的信息，对待压缩神经网络执行与预设压缩需求对应的操作得到压缩后的神经网络。本发明利用显著性表征网络，确定出待压缩神经网络中各通道的重要性，与人为或随机选择相比，可以更有效、全面、自动化的对各通道的重要性进行建模表达，避免了结构化剪枝过程中人为或随机选择造成的重要通道被剪枝的情况。
",G06N3/08,北京经纬恒润科技股份有限公司,虚拟仿真软件技术,2.0,IPC分类号匹配: G06N3/08,"系统, 方法",网络
CN113901752A,CN202111201483.8,"本发明实施例公开一种器件仿真模型的构建方法及装置，该方法包括：获得待构建器件的阻抗测量值序列；利用预设限制条件以及阻抗测量值序列，确定用于构建待构建器件对应的仿真模型的节点数量、模型器件数量及其类型，以及各类型模型器件之间的拓扑关系，以构建出待构建器件对应的仿真模型，其中，预设限制条件包含：所构建的仿真模型中节点数量与模型器件数量之间的限制条件，以实现对未知结构的器件的仿真模型的准确建立。
",G06F30/367,北京经纬恒润科技股份有限公司,虚拟仿真软件技术,3.0,关键词匹配: 仿真模型; IPC分类号匹配: G06F30/367,"实现, 包括, 模型, 装置, 仿真, 方法",通用
CN113792438A,CN202111110167.X,"本发明提供了一种整车电磁抗干扰性能评估方法及装置，其中，方法包括：建立整车电磁抗干扰仿真模型，整车电磁抗干扰仿真模型包括车身模型、测试环境模型、线束模型和终端部件模型，基于终端部件模型随机生成多个终端阻抗数据样本集，将多个终端阻抗数据样本集分别集成到整车电磁抗干扰仿真模型中，以得到多个整车抗干扰仿真模型样本，基于多个整车抗干扰仿真模型样本，评估整车电磁抗干扰风险。经由本发明提供的整车电磁抗干扰性能评估方案可获得整车电磁抗干扰风险，另外，本发明提供的整车电磁抗干扰性能评估方法无需获知整车零部件的详细特性，这使得本发明提供的整车电磁抗干扰性能评估方法具有较强的可操作性和实用性。
",G06F30/20,北京经纬恒润科技股份有限公司,虚拟仿真软件技术,3.0,关键词匹配: 仿真模型; IPC分类号匹配: G06F30/20,"提供, 包括, 模型, 装置, 测试, 具有, 仿真, 方法, 生成",测试
CN112036034A,CN202010895213.0,"本发明提供了一种电控悬架控制器母线电容容值选取方法及装置，方法包括生成电控悬架控制器的仿真模型，然后仿真模型根据用户设定的目标转速和目标电流进行运行，在仿真模型运行过程中，利用粒子群算法进行母线电容的容值选取。通过对电控悬架控制器的仿真模型的试验来研究母线电容的容值选取是否合适，相比于传统的通过实际样件进行试验以确定母线电容的容值，缩短了选取周期，降低了成本；且利用粒子群算法进行母线电容选取，极大提高了仿真效率。
",G06F30/20,北京经纬恒润科技有限公司,虚拟仿真软件技术,3.0,关键词匹配: 仿真模型; IPC分类号匹配: G06F30/20,"提供, 包括, 模型, 装置, 算法, 仿真, 方法, 生成, 控制",控制
CN111797475A,CN202010620848.X,"本发明提供V2X测试方法及系统。上述V2X测试系统包括仿真系统、车辆动力学模型系统和被测设备。方法包括：使用仿真系统搭建虚拟测试场景；虚拟测试场景中包含车道、被测设备对应的主车虚拟仿真车辆模型和基础设施设备模型；在测试阶段，仿真系统向被测设备输出主车信息和周边设备信息；主车信息和周边设备信息用于被测设备生成控制命令；仿真系统接收车辆动力学模型系统返回的车辆控制命令；车辆控制命令是车辆动力学模型系统将控制命令转化得到的；仿真系统根据车辆控制命令动态显示主车虚拟仿真车辆模型。使用本发明所提供的技术方案，并不需要实车参与，因此，不需要生产出样车才进行测试，缩短了车型研发周期，降低了成本。
",G06F30/15,北京经纬恒润科技有限公司,虚拟仿真软件技术,4.0,"关键词匹配: 虚拟仿真, 虚拟测试; IPC分类号匹配: G06F30/15","提供, 包括, 模型, 测试, 仿真, 方法, 生成, 设备, 控制, 系统","车辆, 测试, 控制"
CN109931937A,CN201910244062.X,"本发明提供高精导航信息模拟方法及系统，以对高精导航信息进行模拟。所述系统至少包括：仿真数据生成单元和高精导航仿真单元；所述方法包括：所述仿真数据生成单元向所述高精导航仿真单元输出目标位置，并实时输出虚拟测试场景中车辆的当前位置仿真数据；所述高精导航仿真单元使用接收的当前位置仿真数据、目标位置以及OpenDRIVE地图文件生成高精导航仿真信息。在本发明实施例中，采用仿真数据生成单元和高精导航仿真单元生成高精导航仿真信息，其中，高精导航仿真单元模拟了地图盒子的功能，而仿真数据生成单元对高精导航仿真单元所需的输入数据进行了仿真。
",G01C21/20,北京经纬恒润科技有限公司,虚拟仿真软件技术,1.0,关键词匹配: 虚拟测试,"提供, 包括, 测试, 仿真, 方法, 生成, 系统, 模拟","车辆, 测试, 导航"
CN109614732A,CN201811571165.9,"本发明提供了一种对象的电磁兼容建模方法及装置，方法包括：把待建模对象分解为电气连接和P个组件单元；将每个组件单元由体结构转换为由无厚度的表面组成的面结构，转换后的组件单元作为目标组件单元；确定每个目标组件单元对应的至少一个目标单元平面，并通过每个目标组件单元对应的至少一个目标单元平面构建每个组件的组件模型；构建电气连接的等效电路模型，作为电气连接的模型；组合所有组件单元的组件模型和电气连接的模型，组合后的模型作为待建模对象的电磁兼容仿真模型。本发明提供的对象的电磁兼容建模方法及装置能够确保仿真计算的准确性，还能提升仿真计算效率，并且建模过程的执行较简单，可操作性强，适合实际工程应用。
",G06F17/50,北京经纬恒润科技有限公司,虚拟仿真软件技术,1.0,关键词匹配: 仿真模型,"提供, 包括, 模型, 装置, 仿真, 方法, 计算, 电路",通用
CN106874616A,CN201710128073.2,"本发明公开一种参数优化调整方法及系统，该方法包括：构建待设计仿真模型；根据待设计仿真模型获取优化参数集；调用通用优化算法函数，将优化参数集作为输入代入到通用优化算法函数对待优化调整参数进行优化调整。本发明通过先确定待优化调整参数，将待优化调整参数从待设计仿真模型中引出作为待设计仿真模型的输入参数，将设计仿真模型的工程约束要求转换为约束条件，将设计指标要求转换为待优化调整参数的寻优优化指标，调用通用优化算法函数，进行自动寻优，从自动寻优过程数据中确定待优化调整参数的最终调整结果，其适用于多种应用场合，如：飞行轨迹参数和控制器参数的优化，且待优化调整参数个数不限定，可协助设计人员提高设计效率。
",G06F17/50,北京经纬恒润科技有限公司,虚拟仿真软件技术,1.0,关键词匹配: 仿真模型,"包括, 模型, 算法, 仿真, 方法, 控制, 系统",控制
CN106021750A,CN201610353894.1,"本申请公开了一种仿真场景及其重建单元的生成方法及生成系统，其中，所述仿真场景重建单元的生成方法包括：获取待重建场景的视频信息；提取所述视频信息中的特征物体信息及特征物体间相互关系，对所述特征物体信息及特征物体间相互关系进行处理获得数字特征信息；对所述数字特征信息进行处理，获得仿真场景重建单元。在获得所述仿真场景重建单元之后通过对所述仿真场景重建单元进行读取、渲染即可生成仿真场景。利用所述仿真场景重建单元的生成方法生成仿真场景重建单元之后，只需要利用仿真软件对所述仿真场景重建单元进行读取渲染操作即可完成待重建场景的仿真场景重建，极大地降低了对仿真设计者的仿真软件掌握程度的要求。
",G06F17/50,北京经纬恒润科技有限公司,虚拟仿真软件技术,1.0,关键词匹配: 仿真软件,"包括, 仿真, 生成, 方法, 处理, 系统",通用
CN105426639A,CN201510996652.X,"本申请公开了一种六自由度数学仿真模型的仿真系统及方法。该系统包括：源数据在线计算模型、控制模型、六自由度数学仿真模型以及仿真实验模型。其中，源数据在线计算模型和控制模型分别用于输出六自由度数学仿真模型仿真所需的源数据和控制量。进而，六自由度数学仿真模型利用源数据和控制量进行仿真，并输出仿真结果。仿真实验模型利用仿真结果进行仿真实验，并比较实际仿真实验结果和预设仿真实验结果是否一致，若不一致，将比较结果输入给源数据在线计算模型以及控制模型，以对源数据在线计算模型和控制模型的模型参数进行调整，从而完成对源数据的更新和控制量的调整。
",G06F17/50,北京经纬恒润科技有限公司,虚拟仿真软件技术,1.0,关键词匹配: 仿真模型,"包括, 模型, 仿真, 方法, 控制, 系统, 计算",控制
CN104197959A,CN201410455783.2,"本发明实施例公开了一种惯性导航系统设计参数获取方法及装置，依据选择指令选择功能模块仿真模型，并将所选择的功能模块仿真模型构建为惯性导航系统仿真模型，为所述系统误差仿真模型、所述随机误差仿真模型和所述解算算法仿真模型配置仿真参数，之后对所构建的惯导模型进行仿真，通过仿真结果确定惯性导航系统设计参数。通过本申请实施例提供的惯性导航系统设计参数获取方法及装置，设计人员可以实现惯性导航系统的反向设计，使得设计过程灵活度、自由度提高，提高了惯性导航系统的设计效率。
",G01C25/00,北京经纬恒润科技有限公司,虚拟仿真软件技术,1.0,关键词匹配: 仿真模型,"提供, 实现, 模型, 装置, 算法, 仿真, 方法, 系统, 配置",导航
CN103278788A,CN201310148379.6,"本申请公开了一种霍尔盘仿真模型，包括四个霍尔盘电阻、四个用于模拟霍尔电压的霍尔电压源和四个用于模拟失调电压的失调电压源；四个霍尔盘电阻依次首尾相接构成电阻桥，电阻桥的每个接点上串联有一个霍尔电压源和一个失调电压源；任一时刻，有且仅有两个不相邻接点上串接的失调电压源的电压为0.5*V<Sub>OS</Sub>、霍尔电压源的电压为0.5SIV(MF)，其中，I为产生霍尔效应所需的电流，V(MF)为与产生霍尔效应所需的磁场信号B相对应的等效电压信号。本申请通过在两个输出端分别设置失调电压源，并使其总输出电压为被模拟的霍尔盘的失调电压V<Sub>OS</Sub>，实现了对霍尔盘失调电压的模拟仿真，进而可获得霍尔盘的失调特性，解决了现有技术问题。
",G01R35/00,北京经纬恒润科技有限公司,虚拟仿真软件技术,1.0,关键词匹配: 仿真模型,"实现, 包括, 模型, 仿真, 设置, 模拟",通用
