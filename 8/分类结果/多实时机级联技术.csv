﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN120447969A,CN202510488223.5,"本申请公开了一种应用变量数据标定方法及相关装置，涉及数据处理领域，包括：预先将各个目标应用的变量描述信息统一写入变量信息描述文件，当需要对目标应用进行标定时，基于所述变量信息描述文件中存储的目标应用的各个变量的描述信息生成标定指令，当应用本方法的网关在获取到该标定指令时，对所述标定指令进行解析，基于解析结果确定各个变量在共享内存中的目标位置，然后再基于解析结果对所述目标位置进行标定测量，由此，只需配置一个变量信息描述文件就可以完成对所有目标应用的变量数据的维护，当多个目标应用需要更新时，只需对该变量信息描述文件进行更新即可，简化了变量信息描述文件的管理和维护的复杂度。
",G06F9/34,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/34,"包括, 装置, 方法, 生成, 处理, 配置",通用
CN120407173A,CN202510488068.7,"本申请公开了一种自定义显卡分配的方法、装置及系统。响应渲染进程启动命令，获取目标机器及其中的目标显卡，并获取目标显卡的显卡适配器接口。将与渲染进程启动命令对应的渲染进程中所包含的显卡适配器接口重定向为目标显卡的显卡适配器接口，再执行渲染进程启动命令，就能通过目标显卡完成渲染进程。从而实现脱离默认的显卡分配策略，自定义通过目标显卡完成渲染进程。本申请所述方法采用接口的重定向，操作简单、无需更改渲染进程的源代码。
",G06F9/50,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/50,"系统, 方法, 装置, 实现",通用
CN120234115A,CN202510297412.4,"本申请公开了一种任务队列的访问方法及装置，所述任务队列为单向链表，其中的每个节点包括存储第一智能指针的第一存储区和存储第二智能指针的第二存储区，第一智能指针用于存储任务数据单元指针，第二智能指针用于存储下一个节点的节点指针，方法包括：获得针对任务队列的访问请求；确定是否需要添加互斥锁；若需要，对任务队列的头部互斥锁加锁和/或尾部互斥锁加锁；获得头结点和/或尾节点，并进行与所述访问请求相关的访问操作；若不需要，直接基于访问请求执行访问操作。上述方案的任务队列的头部和尾部可以独立加锁，能够同时执行存储和获取数据的操作，提高了数据交互的速度；同时使用智能指针存储数据结构能够节省CPU和内存资源。
",G06F9/48,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/48,"方法, 包括, 装置",通用
CN120123055A,CN202510136305.3,"本申请公开了一种任务调度方法、装置及电机控制器，涉及车辆技术领域。任务调度方法包括：第一内核接收任务发送方发送的第一任务，其中，第一内核为车辆中具有多个内核的电机控制器中被配置为主内核的内核，第一内核中配置有用于存储任务的消息队列，任务发送方包括多个内核中除第一内核之外的第二内核或车辆电控单元；将第一任务存储于消息队列中；解析消息队列中的第一任务，得到第一任务对应的解析结果；将解析结果发送给相应的任务接收方。根据本申请公开的方案，能够提高电机控制器的内核资源利用率。
",G06F9/48,经纬恒润(天津)研究开发有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/48,"包括, 装置, 具有, 方法, 控制, 配置","车辆, 控制"
CN120085934A,CN202510149562.0,"本申请公开了一种域控制器的软件控制方法、装置及车辆，其中方法包括：获得软件启动指令；控制启动实时单元存储器中当前有效存储区的软件代码；在实时单元成功启动后，控制启动处理单元存储器中当前有效存储区的软件代码；处理单元成功启动改后，进行匹配性和一致性比对；若所述匹配性和一致性比对的各项比对结果均为相同，正常运行域控制器。上述方案在域控制器的实时单元和处理单元的软件运行中，能够主动进行软件匹配性和一致性比对，满足软件匹配性和一致性的要求，同时能够独立的对实时单元或处理单元进行启动和升级控制，满足不同场景下软件更新和启动的需求。
",G06F9/445,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"包括, 装置, 方法, 处理, 控制","车辆, 控制"
CN120066723A,CN202510184141.1,"本发明公开机器人操作系统的执行调度方法及相关装置，可以获得各ROS2节点的节点信息；根据各所述ROS2节点的执行时间和订阅发布信息，计算各所述ROS2节点至终点的最长路径的执行总时间；针对任一节点，根据所述ROS2节点的预设优先级和执行时间，计算所述ROS2节点新的优先级；根据各所述ROS2节点对应的执行总时间和新的优先级，确定各所述ROS2节点的执行顺序。本发明能够基于ROS2节点的节点信息，识别出最长路径和新的优先级，然后基于最长路径和新的优先级规划出确定的执行顺序，所确定的执行顺序不仅能最大限度得提供任务执行效率，而且具有非常明确的确定性，不会出现多个顺序的混乱情况。
",G06F9/48,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/48,"提供, 装置, 具有, 方法, 系统, 计算",通用
CN120074698A,CN202510216777.X,"本申请公开了一种散射参数信息的确定方法及装置。该方法通过获取主链路的信息；基于主链路的信息，将主链路拆分为多个子链路，并获取子链路内各个端口之间的第一端口连接关系信息以及相邻子链路的端口之间的第二端口连接关系信息；基于第一端口连接关系信息和第二端口连接关系信息，生成子链路对应的散射参数矩阵；将各个散射参数矩阵进行级联，得到主链路对应的目标散射参数矩阵，目标散射参数矩阵中包括主链路的主端口对应的端口散射参数信息。根据本申请实施例，能够提高链路散射参数提取的效率和适用性。
",H04B17/20,经纬恒润(天津)研究开发有限公司,多实时机级联技术,1.0,关键词匹配: 级联,"生成, 方法, 包括, 装置",通用
CN120029687A,CN202510121384.0,"本申请实施例公开了一种车载应用管理方法和相关装置，在获取用于请求启动目标车载应用的应用启动请求后，创建目标车载应用对应的状态记录进程和运行实例，运行实例用于运行目标车载应用，状态记录进程用于记录目标车载应用对应的应用状态，若删除状态记录进程则会终止运行实例，从而可以终止运行目标车载应用。若获取针对目标车载应用的终止请求，此时可以将状态记录进程中记录的应用状态变更为终止状态。基于状态记录进程中记录的应用状态为终止状态，处理设备可以删除该状态记录进程，从而可以终止目标车载应用的运行。由此可见，通过本申请的车载应用管理方法，可以实现对车载应用的有效启动和终止，能够及时响应用户对于车载应用的控制需求。
",G06F9/445,经纬恒润(天津)研究开发有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"实现, 装置, 方法, 处理, 设备, 控制",控制
CN120010950A,CN202510083976.8,"本申请公开了一种硬件资源配置方法及硬件资源配置装置，涉及嵌入式技术领域。该方法应用于实时操作系统RTOS系统，包括：获取目标YAML配置文件，所述目标YAML配置文件预先基于目标开发板的目标硬件特征参数编写得到；解析所述目标YAML配置文件，确定得到所述目标硬件特征参数对应的目标硬件描述信息；对所述目标硬件描述信息进行硬件抽象化描述，确定得到所述目标开发板的目标硬件抽象化描述结果；根据所述目标硬件抽象化描述结果，执行与所述目标开发板相关的硬件资源配置操作。根据本申请实施例，能够更为灵活、便捷地实现对不同系统的硬件描述与配置，从而达到轻松适应不同的硬件平台和需求变化的目的。
",G06F9/445,经纬恒润(天津)研究开发有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"平台, 实现, 包括, 装置, 方法, 系统, 配置",通用
CN119883371A,CN202510045320.7,"本申请公开了一种控制寄存器的刷新方法及相关装置，涉及汽车电子技术领域，方法包括：每当执行周期性的主任务时，统计从上一寄存器刷新时间开始至当前时刻主任务的执行次数；根据主任务的执行次数、预配置的寄存器刷新周期以及针对主任务预配置的任务执行周期，确定是否到达寄存器刷新时间；若到达寄存器刷新时间，则对目标控制寄存器标识对应的控制寄存器进行刷新；在完成对目标控制寄存器标识对应的控制寄存器的刷新后，将目标控制寄存器标识更新为下一待刷新的控制寄存器的标识。当控制寄存器中的数据因外界干扰发生变化时，通过本申请公开的方法能够将控制寄存器中的数据恢复成原本的数据，从而能够确保驱动芯片的功能正常。
",G06F9/30,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/30,"包括, 装置, 方法, 控制, 配置","汽车, 控制"
CN119576491A,CN202411620248.8,"本申请公开了一种实时操作系统定时器实现方法、装置及电子设备，能够根据实时操作系统中各个需要触发的定时动作的触发时刻来非周期性地更新硬件定时器中重载寄存器的重载值，这使得实时操作系统只会在需要触发相应定时动作的时刻进入硬件定时器中断，而不会每时间单位进入一次硬件定时器中断，从而达到节约系统资源的效果，以及可以避免由于频繁进行中断导致的系统负载率高的问题。
",G06F9/48,经纬恒润(天津)研究开发有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/48,"实现, 装置, 方法, 设备, 系统",通用
CN119473986A,CN202411496924.5,"本申请公开一种基于Autosar软件架构实现方法和装置，涉及车辆网络技术领域。包括：构建目标配置接口和目标通信机制。目标配置接口指示Autosar软件架构中每层的多个模块中每个模块需要配置的参数信息，目标通信机制可以实现Autosar软件架中各层间的信息共享，所以本申请中方案可以打通Autosar软件架构中各层之间的交互，解决了传统方案中获取目标文件的操作过程繁琐的问题，可以简化客户的操作流程，提高获得目标文件的工作效率。
",G06F15/163,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F15/163,"实现, 包括, 装置, 方法, 配置","车辆, 通信, 网络"
CN119440663A,CN202411515594.X,"本申请公开了一种访问码函数加载方法、装置、存储介质及设备，该方法包括：首先编写sct分散加载文件；其中，sct分散加载文件中定义了目标空闲ROM空间和目标空闲RAM空间的起始地址，然后将访问码函数加载至目标空闲ROM空间；接着将存放了访问码函数的目标空闲ROM空间中的数据拷贝至目标空闲RAM空间；进而通过运行目标空闲RAM空间中的访问码函数，执行用户挂起的擦写请求对应的擦写任务，并在任务完成时，对目标空闲RAM空间中的访问码函数进行卸载，实现资源回收。从而使得采用sct类型分散加载文件的编译器中可以正常使用访问码函数加载功能，实现了对访问码函数的加载方式的有效扩展，提高了其适用范围。
",G06F9/445,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"实现, 包括, 装置, 方法, 设备",通用
CN119206177A,CN202411296171.3,"本申请公开了一种三维目标检测方法及装置、电子设备、存储介质，所述方法包括：获取当前待检测图像；利用待检测图像上的图像特征元素在多个深度上的深度权重对图像特征元素进行加权，得到伪点云特征；预测伪点云特征对应的语义权重；通过将各个伪点云特征对应的深度权重和语义权重，与预设权重阈值对比，将伪点云特征划分为前景特征集合和背景特征集合；将前景特征和背景特征，分别转换为前景鸟瞰视角特征集合和背景鸟瞰视角特征集合，并在特征通道维度上将两者进行级联操作，得到级联鸟瞰视角特征集合；通过训练好的编码器对级联鸟瞰视角特征集合进行卷积处理，得到融合后的鸟瞰视角特征集合，并利用其进行目标检测，得到当前目标检测结果。
",G06V10/25,北京经纬恒润科技股份有限公司,多实时机级联技术,1.0,关键词匹配: 级联,"包括, 装置, 方法, 处理, 设备, 检测",检测
CN118916136A,CN202411017075.0,"本申请公开了一种中断的管理方法及装置，所述方法包括：获取硬件产生的中断信号；基于中断信号，利用偏移量寄存器从预先构建的中断向量表中获取中间软件层函数的指针，并将中间软件层函数的指针加载到中断活跃状态寄存器中；调用下层软件层函数在中断活跃状态寄存器和预先构建的子中断向量表中，确定中断服务函数的指针；对中断服务函数的指针进行有效性检查；若通过有效性检查，则通过中断服务函数的指针，调用中断服务函数执行中断操作。由于本申请是基于RAM框架，因此会将中断向量表以及子中断向量表存储在RAM中，而且RAM的存储相较于现有技术的FLASH的存储更加安全，因此中断向量表在RAM中不易发生被篡改的可能性。
",G06F9/48,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/48,"方法, 包括, 装置",通用
CN118916189A,CN202410977177.0,"本发明提供一种图编辑引擎节点的通信方法、图编辑引擎系统及存储介质，图编辑引擎系统包括图形节点，与图形节点连接的VUE节点实例，所有VUE节点实例均通过总线BUS进行连接，所述方法包括：与图形节点连接的VUE节点实例基于用户在图形节点上的行为操作生成待处理事件时，将待处理事件在BUS上进行广播；若其他的某VUE节点实例确定待处理事件验证通过，基于对应的处理函数对待处理事件进行处理，以实现多个图形节点的通信。本发明通过定义Vue节点实例所连接BUS，让Vue节点实例共享总线事件，通过总线进行监听和触发事件发送和接收，而不是通过层层冒泡的方式解决，降低了性能开销，从而提高节点通信效率。
",G06F9/54,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/54,"提供, 实现, 包括, 方法, 生成, 处理, 系统","通信, 验证"
CN118799412A,CN202410850761.X,"本发明提供一种摄像头参数的标定方法及装置，应用于PC机，PC机与控制器AVM连接，控制器AVM与摄像头连接，摄像头的拍摄范围中预先设置有棋盘格板，标定方法包括：接收控制器AVM发送的由摄像头采集的视频，播放视频，每隔预设帧数实时从正在播放的视频中提取并存储视频帧，直至视频播放完毕，并行检测视频帧中是否存在清晰度符合要求的棋盘格板，若是，确定视频帧为符合摄像头标定要求的目标图片，利用目标图片对摄像头进行标定，得到摄像头的参数。在本方案中，利用PC机并行处理视频帧提取过程和视频帧中清晰的棋盘格板检测过程，以实现快速检测出存在清晰棋盘格板的目标图像，进而提高摄像头标定效率的目的。
",G06T7/80,北京经纬恒润科技股份有限公司,多实时机级联技术,1.0,关键词匹配: 并行处理,"提供, 实现, 包括, 装置, 方法, 设置, 处理, 控制, 检测","检测, 摄像, 控制"
CN118796312A,CN202411035290.3,"本申请公开了一种实现跨位数调用动态链接库的方法及相关装置，方法包括：M位程序加载M位动态链接库，M位动态链接库创建N位辅助进程，M位动态链接库具有与N位动态链接库相同的接口；M位程序通知M位动态链接库加载N位目标动态链接库，M位动态链接库通知N位辅助进程加载N位目标动态链接库，N位辅助进程加载目标动态链接库后，将加载结果反馈给M位动态链接库；M位程序调用M位动态链接库中的目标接口，M位动态链接库通知N位辅助进程执行目标动态链接库中实现目标接口的目标函数，N位辅助进程执行目标函数后，将函数执行结果反馈给M位动态链接库。本申请通过引入M位动态链接库和N位辅助进程实现了M位程序调用N位动态链接库。
",G06F9/445,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"实现, 包括, 装置, 具有, 方法",通用
CN118672966A,CN202410939615.4,"本申请公开了一种多CPU间通信方法及相关装置，该方法包括通过向通信节点为从节点的第二CPU发送预设字节的第一时钟信号，然后接收第二CPU基于第一时钟信号发送的响应数据，其中响应数据指示第二CPU待发送的目标数据的数据长度，进一步向第二CPU发送第二时钟信号，其中第二时钟信号的字节数量基于数据长度确定，然后接收第二CPU基于第二时钟信号发送的目标数据。其中，通过在第一CPU与第二CPU建立通信后，由第一CPU向第二CPU发送用于传输目标数据的数据长度的第一时钟信号，以获取目标数据的数据长度，从而根据数据长度确定第二时钟信号的字节数量，避免时钟浪费，进而提高传输效率。
",G06F15/17,天津经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F15/17,"方法, 包括, 装置",通信
CN118093140A,CN202410381757.3,"本发明提供了一种内核级时间触发调度方法及装置，该方法为：调用时隙转移管理器向多个第一核心分配需运行的时隙任务，时隙转移管理器运行在第二核心上，第一核心为至少运行有时间触发调度器和截止期调度器的Linux内核，时间触发调度器的优先级高于截止期调度器，第二核心为未运行时间触发调度器的Linux内核；根据为第一核心分配的时隙任务，从多个第一核心中确定需重新调度的目标核心；利用时间触发调度器，重新为目标核心分配需运行的时隙任务。在Linux内核中使用基于时间触发的调度方式，可以有效减少延迟和抖动，从而满足现代应用程序所需要的检测级别。
",G06F9/48,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/48,"提供, 方法, 装置, 检测",检测
CN117909098A,CN202410083484.4,"本申请提供了一种数据传输方法及装置，在如车辆控制系统的网络通信场景下，本申请将连接PCIe总线的各设备的PCIe NTB的共享内存空间直接分配到上层的SoAd，第一套接字端口调用PCIe接口发送的负载数据，将直接写入第一设备中对应的第一内存区域，第一设备可以调用PCIe驱动，直接将第一内存区域存储的负载数据写入映射到的第二设备中的第二套接字缓冲区，避免了对负载数据的格式转换和数据包的拆分和重组，提高了数据传输的效率和速度，且不需要CPU参与数据传输过程，减少了CPU的占用率，提高了系统性能和效率，大大降低了大量数据传输场景下网络拥塞问题的发生概率，满足了实时性要求更高的应用场景。
",G06F9/54,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/54,"提供, 装置, 方法, 设备, 控制, 系统","数据传输, 控制, 车辆, 通信, 网络"
CN117827405A,CN202311763073.1,"本发明涉及OTA升级技术领域，公开了一种车辆应用的升级方法、装置、车辆及存储介质，该方法包括：获取至少一个车辆对应的应用升级包以及所述至少一个车辆的升级状态，所述应用升级包中携带有多个升级任务；基于各个所述车辆对应的所述升级状态，确定与所述各个车辆对应的所述升级任务的目标任务队列；基于所述目标任务队列对所述各个车辆对应的升级任务进行任务调度，生成任务调度结果；按照所述任务调度结果控制所述车辆应用的升级进程。通过实施本发明，在车辆需要同时执行大量升级活动的场景下，保证车辆云端升级活动的实时有序下发，提高了车辆的升级效率。
",G06F9/48,经纬恒润(天津)研究开发有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/48,"包括, 装置, 生成, 方法, 控制","车辆, 控制"
CN117785387A,CN202410033079.1,"本申请公开了一种融合架构的构建方法、融合处理方法及系统，在虚拟化框架的底层中设置虚拟机监视器和硬件，虚拟机监视器用于将各个HPC集群组织成资源池来实现硬件资源的整合，硬件用于通过预设连接方式将各个HPC集群进行连接以建立分布式共享内存，在虚拟化框架的中间层中将各个虚拟机镜像进行横向融合，得到融合后的虚拟机，在虚拟化框架中通过上层应用实现整车控制功能、智能驾驶功能和智能座舱功能，通过处理后的虚拟化框架完成融合架构的构建，通过融合架构和横向扩展的虚拟化技术，对各个物理机资源进行资源整合与分配，以实现在确保系统资源的充分利用的情况下进行整车控制、智能驾驶控制和智能座舱控制。
",G06F9/455,北京经纬恒润科技股份有限公司,多实时机级联技术,4.0,"关键词匹配: 分布式, 集群; IPC分类号匹配: G06F9/455","实现, 方法, 设置, 处理, 控制, 系统","驾驶, 控制"
CN117742814A,CN202311588741.1,"本申请公开了一种汽车开放系统架构的网络启动系统、方法和装置。该系统包括：二级启动程序，所述二级启动程序中集成有汽车开放系统架构的第一网络管理功能模块；所述二级启动程序启动后，加载所述第一网络管理功能模块启动，以使所述第一网络管理功能模块发送首帧网络管理报文，存储模块，用于存储所述第一网络管理功能模块发送各帧网络管理报文的时间戳信息，以及唤醒源对所述第一网络管理功能模块的网络请求连接状态。以快速启动AUTOSAR的网络管理功能，及时发出首帧网络管理报文。
",G06F9/445,经纬恒润(天津)研究开发有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"系统, 方法, 包括, 装置","汽车, 网络"
CN117608668A,CN202311372008.6,"本申请公开了一种电池管理系统通信方法、系统及电池管理系统，获得电池管理系统中主板的主板控制器输出的主板初始化指令；基于主板初始化指令向电池管理系统中从板的模拟前端芯片输出芯片类型判断数据；获得从板的模拟前端芯片基于芯片类型判断数据反馈的响应数据，基于响应数据确定从板的模拟前端芯片的当前芯片类型；调整主板中记录的从板的模拟前端芯片的芯片类型，以与确定的从板的模拟前端芯片的当前芯片类型匹配；基于调整记录的芯片类型后的主板与从板的模拟前端芯片进行通信。
",G06F9/4401,天津经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/4401,"系统, 模拟, 方法, 控制","通信, 控制"
CN117608881A,CN202311754325.4,"本申请实施例提供了一种跨进程通信方法、装置、设备及计算机可读存储介质，该方法包括：Binder驱动接收客户端进程发送的服务请求；在服务管理表中查找与服务请求对应的Binder引用，服务管理表存储于Binder驱动中；将Binder引用发送给客户端进程，以便客户端进程通过Binder引用获取服务端进程提供的服务。本申请实施例通过Binder驱动与客户端进程之间的交互就可以获得Binder引用，无需依赖ServiceMangaer，避免了因ServiceMangaer不能正常工作而导致通信失效的情况，提高了跨进程通信的安全性。
",G06F9/54,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/54,"提供, 包括, 装置, 方法, 设备, 计算",通信
CN117555604A,CN202311579071.7,"本申请公开了一种系统时间的计时方法及车辆控制器、电子设备、存储介质，所述方法包括：引导加载程序模块在初始化时，读取第一存储器中存储的系统时间，从该系统时间起开始计时，并在跳出时利用当前计时的系统时间对第一存储器存储的系统时间进行更新；应用程序模块在初始化时，判断本次启动类型；在判断出为软复位启动时，读取第一存储器中的系统时间；在判断出为休眠后唤醒时，读取第二存储器中的系统时间；从读取的系统时间起开始计时，并在每次更新系统时间后，利用计时的系统时间对第一存储器存储的系统时间进行更新，并在车辆控制器进入休眠时，将当前的系统时间更新至第二存储器中；第二存储器为车辆控制器休眠时掉电不丢失数据的存储器。
",G06F9/4401,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/4401,"包括, 方法, 设备, 控制, 系统","车辆, 控制"
CN117270672A,CN202311218339.4,"本申请公开了一种协同休眠唤醒管理方法及系统、相关装置、存储介质，所述方法应用于域控制器中的协同休眠唤醒管理模块，所述方法包括：实时监测各个当前唤醒源；当前唤醒源为当前存在唤醒需求的唤醒源；当监测到通过统一接口新增当前唤醒源时，针对新增的当前唤醒源对应的每个工作单元，判断该工作单元的当前状态是否处于正常工作模式；若其未处于正常工作模式，向其发送唤醒请求，以控制其切换至正常工作模式；当监测到任意一个当前唤醒源结束时，针对结束的当前唤醒源对应的每个工作单元，判断其是否不属于其他当前唤醒源对应的工作单元；若其不属于其他任意一个当前唤醒源对应的工作单元，向其发送休眠请求，以控制其切换至低功耗工作模式。
",G06F1/3296,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F1/3296,"包括, 装置, 方法, 控制, 系统",控制
CN116755853A,CN202310635910.6,"本申请公开了一种系统定时器中断处理方法、装置、设备及介质，涉及车辆技术领域。系统定时器中断处理方法包括：在累加计数寄存器的计数值与比较寄存器的比较值相等的情况下，将中断请求标志位置位；控制系统定时器中断，将中断请求标志位复位；将比较寄存器的比较值更新为第一比较值与重载值之和，其中，第一比较值为上一次控制系统定时器中断时比较寄存器的比较值，重载值用于决定系统定时器中断周期；将第一比较值更新为比较寄存器当前的比较值，继续执行在累加计数寄存器的计数值与比较寄存器的比较值相等的情况下，将中断请求标志位置位的步骤。根据本申请公开的方案，能够减少系统定时器中断周期误差，提高系统定时器中断周期的准确度。
",G06F9/48,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/48,"包括, 装置, 方法, 处理, 设备, 控制, 系统","车辆, 控制"
CN116680003A,CN202310558977.4,"本申请实施例提供一种前端插件化系统，该前端插件化系统包括插件加载和安装模块，所述插件加载和安装模块包括：插件管理模块，用于获取第一插件以及所述第一插件的基本信息，并向插件加载模块发送所述第一插件以及所述第一插件的基本信息；所述插件加载模块，用于全局挂载第一对象，并将所述第一插件的基本信息传入第一对象的register方法，并基于预设的模块化规范调用所述第一对象的register方法，以对所述第一插件进行注册安装。本申请实施例，能够避免在每次更新插件之后需要重新编译的情况。
",G06F9/445,经纬恒润(天津)研究开发有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"系统, 提供, 方法, 包括",通用
CN116627548A,CN202310604694.9,"本申请提供了一种多感性负载驱动控制方法、控制单元及负载驱动系统，其中方法包括获取N个负载的同时启动请求，响应于同时启动请求，为N个负载分配时序信息，根据N个负载的时序信息，将每个负载对应的启动信号发送至驱动单元，以使驱动单元依次启动N个负载。这样，在多个负载需要同时启动时，可以基于时序信息实现多个负载的错峰启动，从而避免多感性负载同时启动的瞬间电流过大，烧毁电路元件的问题。
",G06F9/448,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/448,"提供, 实现, 包括, 方法, 控制, 系统, 电路",控制
CN116520954A,CN202310504809.7,"本申请公开了一种散热机箱以及散热机箱温度控制系统，散热机箱用于板卡散热，包括箱体以及导流组件，箱体包括沿第一方向分布且相互连通的第一开口、容纳腔以及第二开口，第一开口设置有散热部件，散热部件用于将空气导入至容纳腔，导流组件设置于容纳腔，在第二方向上与箱体的侧壁固定连接，导流组件沿第一方向由第一开口向第二开口延伸，导流组件在第二方向上的横截面呈曲线形，其中，第一方向与第二方向相交。根据本申请实施的散热机箱，能够对板卡实现良好的散热效果。
",G06F1/18,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F1/18,"实现, 包括, 设置, 控制, 系统",控制
CN116340252A,CN202211725801.5,"本申请公开了一种信号采集系统及方法，涉及信号处理技术领域，所述信号采集系统包括：信号输入模块、RFSoC芯片、降噪模块以及信号输出模块；其中，信号输入模块用于获取差分输入信号，并将差分输入信号输入至RFSoC芯片；RFSoC芯片，用于根据差分输入信号对应的参数配置对差分输入信号进行信号采集，得到采样信号集合，并将采样信号集合发送至降噪模块；降噪模块，用于对采样信号集合进行线性累加平均计算和递推累加平均计算，得到降噪信号，并将降噪信号输入至信号输出模块；信号输出模块，用于将降噪信号输出至上位机。可见，本申请能够通过将降噪模块集成在信号采集系统中，能够对RFSoC芯片采集的信号进行即时降噪，从而提高系统的信号采集效率。
",G06F15/78,经纬恒润(天津)研究开发有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F15/78,"包括, 方法, 处理, 系统, 计算, 配置",通用
CN114661379A,CN202210344635.8,"本申请提供了一种车载摄像头系统的解串器驱动程序改造方法及相关装置，该方法先将解串器驱动程序中检测到摄像头模组未连接时直接返回失败并结束的判断条件去除，再在解串器驱动程序中添加预设注册判断逻辑；在车载摄像头系统启动且检测到摄像头模组未连接时预设注册判断逻辑将加串器驱动程序和摄像头驱动程序注册至解串器驱动程序中，在车载摄像头系统启动且检测到摄像头模组连接时预设注册判断逻辑将加串器驱动程序和摄像头驱动程序注册至解串器驱动程序中，并根据加串器驱动程序和摄像头驱动程序驱动相应的加串器和摄像头，解决了有关方案在摄像头驱动以从设备注册至解串器驱动过程中，要求系统启动时摄像头模组必须处于连接状态的问题。
",G06F9/445,经纬恒润(天津)研究开发有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"提供, 装置, 方法, 设备, 系统, 检测","检测, 摄像"
CN114610415A,CN202210288052.8,"本发明实施例提供了一种程序启动方法、系统、存储介质及电子设备。其中，方法应用于处理器，处理器中设置有第一BootLoader程序和第二BootLoader程序，方法包括：对第一BootLoader程序的第一启动配置信息进行验证；在第一启动配置信息验证未通过的情况下，对第二BootLoader程序的第二启动配置信息进行验证；在第二启动配置信息验证通过的情况下，从第二启动配置信息中解析获得第二BootLoader程序的启动地址，运行第二BootLoader程序的启动地址指向的引导信息。本发明解决了BootLoader程序出现问题时无法正常引导处理器运行应用程序这一问题。
",G06F9/445,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"提供, 包括, 方法, 设置, 处理, 设备, 系统, 配置",验证
CN114489982A,CN202210059423.5,"本申请公开了一种进程启动的控制方法和装置，该方法为：在检测到TBOX启动后，读取各个进程的配置文件，获得各个进程的配置信息；按照启动优先级由高到低的顺序，依次启动各个进程，并将每个进程的喂狗配置，纳入到每个进程的启动参数中；基于各个进程的喂狗配置，确定各个进程的运行状态，并将运行状态为异常的进程，标识为异常进程；解析异常进程的进程依赖配置，得到依赖于异常进程的其他进程；结束其他进程，并在重启异常进程后，重启其他进程。该方法能够确保重启异常进程之前，预先结束依赖于异常进程的其他进程，并在重启异常进程之后，重启依赖于异常进程的其他进程，以使其他进程能够正常启动，从而有效提高进程启动的可靠性。
",G06F9/48,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/48,"装置, 方法, 控制, 检测, 配置","检测, 控制"
CN114416222A,CN202210050145.7,"本发明公开一种应用程序调用驱动接口的方法及装置，方法包括：在驱动程序中搭建驱动框架，并针对所述驱动框架内每个功能模块创建系统接口，所述驱动框架内功能模块对应有执行不同驱动需求的驱动接口；针对所述系统接口封装实现不同驱动需求的接口逻辑，并将所述系统接口暴露至应用程序；响应于应用程序对目标驱动接口的调用请求，根据所述调用请求传递的命令参数将所述调用请求对接至针对目标驱动接口创建的系统接口，并执行相应系统接口中封装的接口逻辑。通过上述方法可以实现应用程序与驱动接口的解耦合，解决了现有技术中由于应用程序和驱动接口之间的耦合性较强而增加软件开发时间和开发成本的问题。
",G06F9/448,经纬恒润(天津)研究开发有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/448,"实现, 包括, 装置, 方法, 系统",通用
CN113986393A,CN202111282769.3,"本申请提供了一种电子控制单元启动方法和装置，该方法包括：在电子控制单元上电后，启动引导加载程序；在通过引导加载程序确认满足进入停留阶段的条件后，通过引导加载程序读取存储的程序异常运行次数，程序异常运行次数表征电子控制单元中的应用程序运行异常的次数；如程序异常运行次数未达到设定次数，通过引导加载程序启动应用程序；如程序异常运行次数达到设定次数，控制电子控制单元停留在引导加载程序阶段。本申请的方案可以提升电子控制单元的启动速度，提升启动性能。
",G06F9/445,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"提供, 包括, 装置, 方法, 控制",控制
CN113590205A,CN202110829207.X,"本发明公开一种SOC的休眠控制方法及装置，涉及汽车技术领域，能够降低多SOC整体休眠时间。该方法包括：获取每个系统级芯片SOC的最近一次入眠时间、休眠可靠性，以及当前应用场景对应的休眠可靠性占比权重，所述休眠可靠性是根据历史休眠事件的休眠成功率确定的；根据所述最近一次入眠时间、所述休眠可靠性以及所述休眠可靠性占比权重，计算所述SOC的休眠总性能；将休眠总性能最强的SOC确定为主SOC，将其他SOC确定为从SOC。
",G06F9/4401,北京经纬恒润科技股份有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/4401,"包括, 装置, 方法, 控制, 系统, 计算","汽车, 控制"
CN112084133A,CN202010995526.3,"本发明公开了一种车载双微处理器系统及控制方法，以实现在MCU1上电前，将所有与MCU1通信的IO口配置为输入悬空状态，在MCU1启动完成后，再将所有与MCU1通信的IO口由输入悬空状态切换为正常工作状态，因此本发明实现了MCU1的IO供电始终提前于MCU1的IO通信功能，从而解决了MCU1和MCU2之间IO灌电的问题，保证了MCU1上电后能够正常启动，进而保证了车载双微处理器系统的正常功能运行和响应能力。
",G06F15/17,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F15/17,"实现, 方法, 处理, 控制, 系统, 配置","通信, 控制"
CN111999347A,CN202010898735.6,"本申请提供一种确定热交换装置内部干度的方法及装置。该方法包括：获取热交换装置内部待测点的状态参数的参数值，该状态参数指示待测点的介电常数；基于待测点的状态参数的参数值和参考值的差值，确定待测点是否处于积液状态或蒸干状态。本申请提供的技术方案，能够实现对空间较小的热交换装置的内部干度的检测，并且，通过设置多个待测点，能够实现对热交换装置内部干度的分布式检测。
",G01N27/22,北京经纬恒润科技有限公司,多实时机级联技术,1.0,关键词匹配: 分布式,"提供, 实现, 包括, 装置, 方法, 设置, 检测",检测
CN111752692A,CN202010580398.6,"本发明公开了一种仿真环境下的多核任务处理方法及装置，方法包括：分别构建在目标仿真环境下能够使用的多核用户接口和周期任务用户接口，在目标仿真环境的模型文件中调用前述用户接口进行任务配置，将所配置的多个周期任务分配至至少两个不同的核；针对每一个核，将其中执行周期相同的周期任务作为一个OS任务，并为每一个OS任务配置一个任务接口函数，使其在运行时，一个周期任务中任务接口函数只被调用一次。上述实现中，能够将一个核中周期相同的任务合并在一起，并为其统一配置一个任务接口函数，这样在一个执行周期中调用一次任务接口函数就能够完成所有相同周期的周期任务，避免了周期任务被重复调用的情况。
",G06F9/48,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/48,"实现, 包括, 模型, 装置, 仿真, 方法, 处理, 配置",通用
CN111694602A,CN202010534148.9,"本申请公开了跨分区的数据处理方法及装置，当第一分区中的任务产生跨分区的函数调用请求时，将第一分区的内存访问权限切换为第二分区的初始内存访问权限，调用目标函数，调用完毕后，将第一分区的内存访问权限恢复至其初始内存访问权限。依据初始内存访问权限，每个分区中的任务仅可对该分区对应的内存空间进行写操作，因此，每个分区只需占用MPU中的一个寄存器资源，能够减少寄存器资源的消耗；调用目标函数时，将第一分区的内存访问权限切换为第二分区的初始内存访问权限，调用完成后，再将第一分区的内存访问权限恢复至其初始内存访问权限，能够防止目标函数非预期地访问第一分区对应的内存空间，降低了出现数据安全问题的可能性。
",G06F9/30,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/30,"方法, 处理, 装置",通用
CN111679923A,CN202010529980.X,"本发明实施例提供实时通信方法和系统。该系统包括MCU、定位模块和IMU；MCU运行的操作系统为非实时操作系统；非实时操作系统包括内核空间和用户空间；内核空间中部署有内核驱动kenel；该方法包括：上电后，kenel启动定时器(用于每隔预设周期触发进入中断)；每次进入中断后，kenel调用回调函数以记录时间戳和触发内核空间的实时读取线程；退出中断后，实时读取线程进行如下操作：读取外设设备的定位相关数据；其中，外设设备至少包括惯性测试单元；存储并通知定位进程读取标记时间戳后的定位相关数据。实时读取线程比定位进程优先级更高，且没有由用户空间切换到内核空间的过程，从而可以更稳定的周期读取IMU数据。
",G06F9/54,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/54,"提供, 包括, 测试, 方法, 设备, 系统","通信, 测试"
CN111475202A,CN202010243471.0,"本发明提供了基于异构多处理系统的核间通信方法及系统，对共有内存进行分块，每一对处理器内核在传输消息时使用其中的一个共有内存块，其它应用程序不能使用该共有内存块。发送端处理器内核将需要发送的消息存储至一个共有内存块中，并向接收端处理器内核通知该共有内存块的定位信息，以使接收端处理器内核从该共有内存块中读取该消息，该共有内存块中只有该接收端需要接收的消息，因此，接收端处理器内核从该共有内存块中读取的消息就是需要接收的消息。所以接收端处理器内核不需要将共有内存块中的数据拷贝到本地进行筛选后再进行处理，因此，提高了各个处理器内核之间的处理效率，同时，也降低了处理器内核处理数据所占用的系统资源。
",G06F9/30,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/30,"系统, 提供, 方法, 处理",通信
CN111400069A,CN202010208245.9,"本发明公开了一种KCF跟踪算法的实现方法、装置及系统，包括：接收上位机发送的第一待处理数据和第一触发信号；依据第一触发信号，基于待处理数据处理得到尺度计算所需参数；依据预设的尺度数量进行尺度计算的任务分配；在接收到上位机发送的第二待处理数据和第二触发信号后，主核和至少两个从核进行尺度计算；汇总自身和至少两个从核的尺度计算结果，并确定出最优尺度；基于最优尺度更新目标参数和尺度计算所需参数。上述方案将多尺度计算任务分配到DSP的不同内核上，多尺度计算并行处理，从而能够有效保证KCF跟踪算法执行的实时性；同时多尺度KCF跟踪算法的实施能够适应跟踪目标不同尺度的变化速率，提高尺度预测的准确度。
",G06F9/54,北京经纬恒润科技有限公司,多实时机级联技术,3.0,关键词匹配: 并行处理; IPC分类号匹配: G06F9/54,"实现, 包括, 装置, 算法, 方法, 处理, 系统, 计算",通用
CN111124562A,CN201911119754.8,"本发明公开了一种应用程序双屏显示方法及装置，该方法可应用于安卓车机系统，车机系统包括主屏和副屏，该方法包括：分别在主屏和副屏上创建应用程序窗口，其中，所创建的主屏的应用程序窗口和副屏的应用程序窗口内显示的内容一致；将所创建的副屏的应用程序窗口配置在副屏的前台应用程序窗口和背景桌面之间；配置副屏的前台应用程序窗口具有非零透明度，并将副屏的应用程序窗口与副屏的前台应用程序窗口中的内容进行叠加显示。本发明的上述技术方案，使用副屏通过沉降的方式来显示由主屏启动和控制的应用程序，而不会影响原本在副屏正在使用的应用程序，使得车辆驾驶员只观察一块屏幕即可获取需要的全部信息。
",G06F9/451,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/451,"包括, 装置, 具有, 方法, 控制, 系统, 配置","车辆, 驾驶, 控制"
CN109918139A,CN201910188245.4,"本发明公开了一种网络通信方法及装置，该方法包括：当第二MCU被唤醒后，判断第二MCU的唤醒源是否为通信模块；若为通信模块，判断车辆是否处于熄火状态；若处于熄火状态，通过通信模块与汽车远程服务提供商后台服务器建立通信连接，以实现对车辆的远程控制。本发明由第二MCU执行远程控制功能，由于第二MCU未运行操作系统，所以第二MCU的唤醒时间相较于运行操作系统的第一MCU的唤醒时间短，从而缩短了远程控制车辆的时间，提高了用户体验。
",G06F9/4401,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/4401,"提供, 实现, 包括, 装置, 方法, 控制, 系统","汽车, 控制, 车辆, 通信, 网络"
CN109783437A,CN201910079667.8,"本发明提供了一种芯片的控制方法及装置，该方法包括：获取MCU驱动的芯片的芯片信息；芯片信息包括芯片的传输通道的传输通道信息；芯片的一路输入通道以及至少一路输出通道作为一路传输通道；基于传输通道对应的传输通道信息，对传输通道进行驱动控制。通过本发明提供的芯片的控制方法及装置，抽象出MCU驱动每种芯片的传输通道，一路输入通道以及至少一路输出通道作为一路传输通道，不同种类的芯片的传输通道的驱动方式相同，进而可以采用同一套驱动程序驱动不同的芯片，减少冗余代码，提高复用性。
",G06F15/78,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F15/78,"提供, 包括, 装置, 方法, 控制",控制
CN106843979A,CN201710059891.1,"本发明公开了一种应用程序的更新方法及装置，该方法应用于通过CANFD总线与电子控制单元相连的重编程设备，该方法包括：向所述电子控制单元发送编程模式命令；接收所述电子控制单元响应所述编程模式命令所发送的第一响应信息；向处于所述编程模式的所述电子控制单元发送驱动数据下载命令；接收所述电子控制单元响应所述驱动数据下载命令所发送的第二响应信息；向所述电子控制单元发送与所述电子控制单元对应的驱动数据，所述驱动数据用于对所述电子控制单元中的应用程序进行更新本发明的方案通过重编程设备，以软件方式，实现了对电子控制单元的应用程序的更新，以此解决了以更换硬件的方式更新电子控制单元带来的成本高的问题。
",G06F9/445,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"实现, 包括, 装置, 方法, 设备, 控制",控制
CN106250118A,CN201610580112.8,"本发明提供了一种MATLAB图形用户界面的创建方法及系统，方法包括，预先生成一标准函数，再通过接口将java文件导入所述MATLAB，其中，所述java文件包括GUI控件布局信息，之后根据GUI基础信息，确定GUI界面的框架尺寸和标题属性，最后依据所述GUI控件布局信息以及基础控件对应的函数，生成GUI控件，进行数据交互。可见，本发明提供的一种MATLAB图形用户界面的创建方法，采用java控件，降低了GUI编程的复杂程度，且java控件种类多，布局较为美观，本发明还提供了一种MATLAB图形用户界面的创建系统。
",G06F9/44,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"提供, 包括, 方法, 生成, 系统",通用
CN106095454A,CN201610430814.8,"本申请提供了一种协处理器的软件更新方法、系统及主处理器，协处理器的软件更新方法包括：通过外部总线从下载设备中下载合成软件，并存储合成软件到主处理器的可用存储空间中；通过内部总线读取协处理器中当前应用程序的版本；比较当前应用程序的版本与待使用协处理器应用程序的版本；在当前应用程序的版本与所述待使用协处理器应用程序的版本不同时，通过内部总线发送待使用协处理器应用程序至协处理器，以使协处理器将当前应用程序替换为待使用协处理器应用程序。在本申请中，通过以上方式对协处理器的应用程序进行更新，不再需要从汽车上将协处理器所属的控制器拆卸下来，简化了操作，减少了工作量，从而提高了更新效率。
",G06F9/44,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"提供, 包括, 方法, 处理, 设备, 控制, 系统","汽车, 控制"
CN106055351A,CN201610342015.5,"本申请公开了一种控制系统的开发方法和装置。首先获取开发控制系统所需的图像信息。进而，将获取到的图像信息封装成可在Matlab工具下直接使用的S‑Function模块，并利用Matlab工具下的仿真模块对预先建立的控制策略模型进行仿真，初步验证控制策略模型的正确性。当确认建立的控制策略模型无误后，将上述S‑Function模块和上述控制策略模型进行集成，并基于Matlab工具提供的RTW模块将集成后的控制策略模型转换为C代码。进一步，对生成的C代码进行编译、链接生成可执行文件，以将上述可执行文件写入相关的控制器中完成控制系统的开发。
",G06F9/445,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"提供, 模型, 装置, 仿真, 方法, 生成, 控制, 系统, 工具","验证, 控制"
CN105700915A,CN201511032329.7,"本申请公开了一种兼顾看门狗功能和监测软件烧写功能的方法和装置。该方法根据软件编程器的运行状态信号，确定软件编程器的运行状态。当软件编程器处于内部软件加载状态时，则启动看门狗功能；当软件编程器处于程序烧写状态时，则不启动看门狗功能。与现有技术相比，本发明可根据软件编程器当前的运行状态决定是否启动看门狗功能，从而避免了在烧写软件过程中，看门狗对软件编程器进行复位，造成软件编程器烧写失败的情况。
",G06F9/445,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"方法, 装置",通用
CN105653242A,CN201511001181.0,"本发明公开了一种计时方法及装置，确定当前位的值；将电平置为与所述当前位的值对应的第一预设电平，并确定与所述第一预设电平对应的第一时间参数，所述第一时间参数用于表示所述第一预设电平的持续时长；当达到所述第一预设电平的持续时长时，进入中断程序，将电平置为与所述当前位的值对应的第二预设电平，并确定与所述第二预设电平对应的第二时间参数，所述第二时间参数用于表示所述第二预设电平的持续时长；当达到所述第二预设电平的持续时长时，使所述当前位的下一位作为当前位，并返回执行所述确定当前位的值的步骤，直至所述当前位为最后一个字节为止。通过上述方法及装置，在无需调整计时参数的前提下即可实现快速准确计时。
",G06F9/30,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/30,"方法, 装置, 实现",通用
CN105094991A,CN201510518787.5,"本发明实施例公开了一种堆栈容量的设置方法，为第一任务分配第一任务堆栈；第一任务堆栈为执行第一任务时使用的随机存取存储器RAM空间；在执行第一任务的过程中，检测第一任务堆栈是否发生堆栈溢出，并在第一任务堆栈没有发生堆栈溢出时，检测第一任务对于第一任务堆栈的第一实际使用量；以第一任务对于第一任务堆栈的第一实际使用量为基准，设置第一任务堆栈的第一堆栈容量；第一堆栈容量大于或等于第一任务对于第一任务堆栈的第一实际使用量。可见，在不发生堆栈溢出时，本申请方案能够使得第一堆栈容量与第一任务的实际使用量精确匹配，可以避免对于有限的RAM资源的过于浪费，保证任务能够在尽量减少资源浪费的情况下正常运行。
",G06F9/50,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/50,"检测, 方法, 设置",检测
CN104793975A,CN201510211875.0,"本发明实施例公开了一种电子控制单元中应用程序的更新方法及装置，应用于重编程设备，所述重编程设备具有组播地址，所述重编程设备通过网络总线与若干个相同的电子控制单元相连接，所述若干个相同的电子控制单元构成一个网段，在对该网段中的电子控制单元的应用程序进行更新时，通过组播地址向网段发送更新命令，使得网段中的各个电子控制单元都能接收到该更新命令，且网段中的所有电子控制单元相同，从而多个电子控制单元可以并行对重编程设备发送的同一更新命令进行响应，而不是一个电子控制单元更新完成再对另一个电子控制单元进行更新，从而缩短了对多个相同的电子控制单元中的应用程序进行更新所耗费的时间。
",G06F9/445,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"装置, 具有, 方法, 设备, 控制","网络, 控制"
CN104765616A,CN201510212001.7,"本发明公开了一种自动生成IO模型的方法，包括：获取控制器系统的硬线信号列表及总线数据文件；对硬线信号列表及总线数据文件进行解析，得到满足预设模型结构的数据结构体；根据数据结构体和预设的模型库，对目标的模型页面类进行页面实例化，使用页面类生成方法生成目标页面；完成所有页面的生成，形成控制器系统与虚拟模型之间的输入输出IO模型。实现了按照统一的搭建标准对IO模型进行自动搭建，提高了IO模型的可读性和搭建效率。本发明还公开了一种自动生成IO模型的系统。
",G06F9/44,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"实现, 包括, 模型, 方法, 生成, 控制, 系统",控制
CN104715486A,CN201510134249.6,"本申请提供一种仿真台架摄像机标定方法及实时机，通过建立的虚拟摄像机采集添加的标定参考物并成像，建立其任意一点与虚拟摄像机图像平面坐标之间的第一对应关系；由真实摄像机采集所述成像，建立标定参考物的所述任意一点在虚拟摄像机的成像中的成像点与真实摄像机图像平面坐标之间的第二对应关系；对两个对应关系进行求解，得到标定参考物的所述任意一点与真实摄像机图像平面坐标之间的第三对应关系，输出至FAS系统，使FAS系统得到真实摄像机图像平面坐标与实时机提供的测试场景数据之间的对应关系，消除了虚拟摄像机到真实摄像机之间存在的参数差异及空间相对关系，解决了实时机无法为FAS系统提供准确的测试场景数据的问题。
",G06T7/00,北京经纬恒润科技有限公司,多实时机级联技术,1.0,关键词匹配: 实时机,"提供, 测试, 仿真, 方法, 系统","测试, 摄像"
CN104216779A,CN201410508608.5,"本申请实施例提供了一种中断执行方法及装置，通过确定当前负载非最大的CPU核、以及负载非最大的时钟源的方式，由当前负载非最大的CPU核、以负载非最大的时钟为中断定时器，执行第一中断，所以，与现有技术相比，可以降低中断被延时响应的可能性。
",G06F9/48,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/48,"提供, 方法, 装置",通用
CN104199699A,CN201410437192.2,"本发明实施例公开了一种程序加载方法、芯片启动方法、装置及主控设备，主控设备和多处理器核心芯片预先进行交互确定主控设备内存空间与多处理器核心芯片的内存空间和寄存器空间的映射关系，从而当确定与每一个目标处理器核心相对应的待加载程序后，主控设备可以将与第i个目标处理器核心相对应的待加载程序写入多处理器核心芯片中与第i个目标处理器核心对应的内存中；并在确定与第i个目标处理器核心相对应的待加载程序在多处理器核心芯片的内存中的第一入口地址后，可以将第一入口地址写入与第i个目标处理器核心相对应的第二寄存器中。从而实现了独立对每一个处理器核心进行程序加载的过程，进而可以对不同的处理器核心加载不同的程序。
",G06F9/445,北京经纬恒润科技有限公司,多实时机级联技术,3.0,关键词匹配: 多处理器; IPC分类号匹配: G06F9/445,"实现, 装置, 方法, 处理, 设备",通用
CN103997543A,CN201410259107.8,"本申请提供一种信息交互方法及分布式仿真系统，通过在该分布式仿真系统中设置多个功能接口，且各个功能接口对应该分布式仿真系统中的不同仿真子系统，进而使得该分布式仿真系统中的DDS中间件通过与仿真子系统对应的功能接口，实现与仿真子系统之间的信息交互，本申请通过在分布式仿真系统中设置与仿真子系统对应的功能接口的方式、保证分布式仿真系统中各个仿真子系统与DDS中间件之间的信息交互。
",H04L29/08,北京经纬恒润科技有限公司,多实时机级联技术,1.0,关键词匹配: 分布式,"提供, 实现, 仿真, 方法, 设置, 系统",通用
CN103970547A,CN201410229740.2,"本发明提供变量生成方法和装置及数据处理方法和装置，通过确定预先定义的宏指定的对象总数量，并依据该对象总数量及预先构建的用于生成对象的变量的结构体数组，生成与对象总数量匹配的每个对象的变量，并在数据处理过程中，获取用户输入信息，并利用该用户输入信息及预先设置的调用函数为预先设置的函数主体中函数的形参赋值，最后通过该函数依据该形参的值确定待处理对象，并对该待处理对象的变量进行预设处理，本发明通过自动生成变量的方法以及在数据处理过程中，为函数主体中函数的形参赋值、进而根据形参的值进行数据处理的方法，解决了在数据处理过程中，实现该数据处理过程的代码量大以及后期维护不方便的问题。
",G06F9/44,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"提供, 实现, 装置, 方法, 设置, 生成, 处理",通用
CN103744712A,CN201410042175.9,"本申请提供了一种应用程序的更新方法及装置，应用于上位机，上位机能够与作为下位机的电子控制单元通信，方法包括：将下位机的重编程标识改写为有效值，以便下位机在执行完复位操作进入Bootloader之后，使Bootloader不将下位机的控制权交给应用程序；将第一应用程序的标识信息写入下位机的存储器；将第一应用程序数据写入下位机的存储器；对写入下位机的存储器中的第一应用程序数据进行校验，并在校验成功之后，将重编程标识更改为非有效值，以便下位机在执行完复位操作进入Bootloader之后，使Bootloader将下位机的控制权交给第一应用程序。本申请提供的应用程序的更新方法及装置，可通过汽车总线对具有Bootloader功能的电子控制单元中的应用程序进行更新。
",G06F9/445,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"提供, 包括, 装置, 具有, 方法, 控制","通信, 汽车, 控制"
CN103713932A,CN201410027192.5,"本发明提供了一种电子控制单元中应用程序的更新方法及装置，当主控制器接收到重编程设备的下载流程启动命令时，使从控制器进入准备编程模式；当接收到重编程设备发送的第一从控制器的驱动程序时，调用驱动程序中的初始化接口使第一从控制器进入编程模式；当接收到重编程设备发送的访问地址信息时，如果通过访问地址信息确定出待操作的从控制器为第一从控制器，调用第一从控制器的驱动程序的擦除接口擦除第一从控制器的存储器；当接收到重编程设备发送的应用程序数据时，通过第一从控制器的驱动程序的写操作接口将应用程序数据写入第一从控制器的存储器。由于重编程设备只与作为主控制器进行通信，因此，降低了重编程成本、节省了网络资源。
",G06F9/445,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"提供, 装置, 方法, 设备, 控制","通信, 网络, 控制"
CN103699393A,CN201410007141.6,"本发明提供一种打解包程序生成方法及装置，首先接收用户输入的包含有至少一条报文的ICD，然后分别对ICD中的每条报文定义位域结构体，并利用预先设置的打解包函数生成规则，分别生成与各个位域结构体相对应的打解包函数，最后利用各个位域结构体，对生成的分别与各个位域结构体相对应的打解包函数进行编译，生成打解包程序，在该过程中通过对接收到的用户输入的ICD中的各个报文定义位域结构体，并最终根据定义的位域结构体以及预先设置的打解包函数生成规则自动生成与该ICD相对应的打解包程序，使得在保证被测系统ICD安全性的基础上，满足打解包程序的通用性、提高打解包程序的即时适应变更能力。
",G06F9/44,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"提供, 装置, 方法, 设置, 生成, 系统",通用
CN103677827A,CN201310671563.9,"本申请提供了一种芯片配置方法及装置，应用于配置工具，该配置工具具有插件目录，方法包括：确定是否存在与配置工具的插件目录下的插件关联的芯片，配置工具的插件目录下的插件为协议栈中的至少一个配置模块的插件，配置模块与插件一一对应；当存在与插件目录下的插件关联的芯片时，显示第一交互界面，并在第一交互界面上显示与插件目录下的插件关联的芯片标识；基于用户在第一交互界面上选择的芯片标识显示第二交互界面，并在第二交互界面上显示插件目录下、与用户选择的芯片标识关联的插件对应的配置模块的标识；基于用户在第二界面上选择的配置模块的标识进行配置。
",G06F9/44,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"提供, 包括, 装置, 具有, 方法, 工具, 配置",通用
CN103677835A,CN201310689788.7,"本发明公开了一种软件集成方法及系统，通过将预先选择的至少一个基础软件函数模块连接至应用软件模型的相应位置，生成控制器软件模型；并接收输入的功能配置参数，在接收代码生成指令后，生成配置文件，应用软件的源代码和与各个基础软件函数模块相对应的，用于调用相应的基础软件代码的调用代码，将每个基础软件函数模块的调用代码添加至所述应用软件源代码的相应位置，得到应用软件初级代码；然后将调用编译器，将所述应用软件初级代码，各个基础软件代码，以及所述与各个基础软件相对应的配置文件添加至所述编译器中；控制所述编译器对所述编译器内的源代码进行编译，生成目标文件。实现了软件的自动集成，降低了软件集成难度，减少了工作量。
",G06F9/44,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"实现, 模型, 方法, 生成, 控制, 系统, 配置",控制
CN103677834A,CN201310689255.9,"本申请提供了一种信号操作处理方法及装置，所述方法包括：接收操作信号请求，获取所述操作信号请求中所携带的信号及所述信号对应的信号编号；依据预设的所述信号与信号存储区域的对应关系，获取所述信号的存储区域；依据所述信号编号，获取所述信号对应的操作函数；执行所述操作函数，对所述信号的存储区域进行与所述操作信号请求对应的操作。因此，提高了ECU运行的实时性，减少了ECU中的内存占用。
",G06F9/44,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"提供, 包括, 装置, 方法, 处理",通用
CN103646007A,CN201310700460.0,"本发明实施例提供了一种数据处理方法、装置及系统，该方法包括：接收第一天线的电容值以及第二天线的电容值；记录所述第一天线的电容值不大于第一预设阈值的第一开始时刻，所述第一天线的电容值不小于第二预设阈值的第一终止时刻；记录所述第二天线的电容值不大于第三预设阈值的第二开始时刻，所述第二天线的电容值不小于第四预设阈值的第二终止时刻；当所述第一开始时刻小于所述第二开始时刻且所述第一终止时刻大于所述第二终止时刻，且检测到智能钥匙在预设范围内时，生成控制所述车辆后备箱的车盖开启的指示。采用本发明实施例提供的方法、装置和系统不会产生对后备箱的误操作的现象。
",G06F15/163,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F15/163,"提供, 包括, 装置, 方法, 生成, 处理, 控制, 系统, 检测","车辆, 检测, 控制"
CN103412796A,CN201310389187.4,"本发明实施例公开了一种操作系统中任务的堆栈分配方法，在为任务分配堆栈时，为相互之间不会发生抢占的至少两个任务分配同一个堆栈，即将相互之间不会发生抢占的任务设置为共享一个堆栈。由于共享堆栈的任务之间不会发生抢占，因此，在某个时刻，共享堆栈的任务中只有一个任务在运行，也就是说，在一个时刻，共享堆栈的任务中只有一个任务在使用该共享堆栈，实现了多个任务共享一个堆栈，因此，在任务量一定的情况下，减少了堆栈的RAM使用量，降低了产品的研发成本。本申请还公开了一种操作系统中任务的堆栈分配装置。
",G06F9/50,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/50,"实现, 装置, 方法, 设置, 系统",通用
CN103197930A,CN201310102516.2,"本发明公开了一种汽车诊断软件自动生成的方法，包括：建立具有标准的文件格式定义的包含汽车诊断使用的通信协议和诊断服务信息的ODX文件；参照标准的文件格式定义提取所述ODX文件中的诊断服务信息；根据所述诊断服务信息获取该诊断服务信息需要发送的请求报文；根据所述ODX文件中的通信协议发送所述请求报文；接收响应所述请求报文的响应报文；解析所述响应报文的内容。本发明能够自动的生成汽车诊断软件，且能够大大降低汽车诊断软件的开发和维护时间。
",G06F9/44,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"具有, 生成, 方法, 包括","通信, 汽车, 诊断"
CN103186363A,CN201310099823.X,"本发明公开了一种数值编辑方法及系统，方法应用于系统中，包括：接收控制指令，控制指令包括：待编辑数据段、待编辑数据段的转换方式及其转换后的数据段对应的十进制数值，其中，待编辑数据段是预先由多个数据段构成的控制字中的待编辑数据段，依据待编辑数据段的转换方式将待编辑数据段进行转换，得到与控制指令对应的转换后的数据段，将转换后的数据段中的二进制码进行计算，得到十进制数值，在转换后的数据段中输入控制指令中的十进制数值，无论待编辑数据段进行多少次的转换，都会生成十进制数值，对待编辑数据段的转换可以实现对一个控制字N位的任意划分，且对任意划分的某一数据段进行直接输入十进制数值，提高了对控制字编辑的效率。
",G06F9/30,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/30,"实现, 包括, 方法, 生成, 控制, 系统, 计算",控制
CN103164228A,CN201310109485.3,"本发明实施例提供了一种现场可编程门阵列程序的生成方法及装置，该方法包括获得构建的包括封装有功能程序代码的功能模块的系统的信息；根据所述各个功能模块之间的连接关系以及所述各个功能模块的标识，获得模型声明文件和顶层文件；根据所述模型声明文件以及所述FPGA的资源信息获得接口逻辑文件，所述接口逻辑文件包括所述系统中的各个功能模块在所述FPGA中的连接线路信息；将所述顶层文件、所述模型声明文件以及所述接口逻辑文件进行预设操作生成比特流。采用本发明实施例提供的方法和装置可以加快程序代码的生成速度，使开发效率提高。
",G06F9/44,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"提供, 包括, 模型, 装置, 方法, 生成, 系统",通用
CN103164276A,CN201310102695.X,"本发明公开了一种对受保护对象进行嵌套处理的方法，为需要嵌套处理的受保护对象分配至少两个标准资源，每个标准资源有不同的天花板优先级，在进行嵌套处理时，通过占用天花板优先级更高的标准资源来对受保护对象进行嵌套处理，也就是说，本方案中的链接资源就是标准资源，即本方案将链接资源以普通标准资源的形式进行管理，在系统中不用设置专门用于控制链接资源的数据结构，也不用在控制标准资源（这里的标准资源是相对链接资源来说的）的数据结构中添加用于处理链接资源的属性，因此降低了资源管理的复杂度，提高了嵌入式实时操作系统在实际运行时，资源管理的效率。
",G06F9/48,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/48,"方法, 设置, 处理, 控制, 系统",控制
CN103135989A,CN201310075933.2,"本发明公开了一种回调函数代码生成方法及装置，所述方法包括获取配置文件，所述配置文件包括至少一个目标回调函数的标识参数信息；依据所述配置文件中的标识参数信息，在Simulink模型中设置一个回调函数模块；其中，所述回调函数模块包括至少一个与所述目标回调函数相对应的函数子模块对，每个所述函数子模块对包括一个函数名称子模块及一个函数内容子模块；若预设配置规则成立，对所述回调函数模块中的每个函数内容子模块添加其各自对应的函数内容；运行所述回调函数模块，生成与所述配置文件相对应的目标回调函数代码。
",G06F9/44,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"包括, 模型, 装置, 方法, 设置, 生成, 配置",通用
CN103116497A,CN201310070018.4,"本申请提供了一种基于软件平台的多电子控制单元构建方法，包括：在一个所述软件平台内建立模块库及系统配置输入文件；从所述系统配置输入文件中确定待设计的各种类型ECU，并从所述模块库中提取所述待设计的各种类型ECU所需要的模块；获取所述待设计的各种类型ECU各自所需要的模块之间的连接关系；对所述待设计的各种类型ECU所需要的模块进行编译链接，生成所述待设计的各种类型ECU各自对应的可执行文件。因此本申请同时从系统配置输入文件中确定待设计的各个ECU，对待设计的各个ECU进行软件设计，节省人力和时间，提供工作效率，保证获得的ECU的可靠性和安全性，同时降低因为车辆的程序存在缺陷而被召回的概率。
",G06F9/44,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"提供, 平台, 包括, 方法, 生成, 控制, 系统, 配置","车辆, 控制"
CN103116523A,CN201310062310.1,"本申请提供一种报警检测及响应方法、装置，报警检测及响应方法包括：当系统计数器加1时，获取当前报警就绪队列，当前报警就绪队列中的报警器为处于激活状态的报警器，排在当前报警就绪队列队首的报警器为最先要发生报警的报警器，判断当前报警就绪队列是否为空，如果是，则判断系统计数器的当前值与当前报警就绪队列中排在队首的报警器的报警时间是否一致，如果是，则触发当前报警就绪队列中排在队首的报警器发生报警，然后将排在队首的报警器移除。本申请提供的方法只需比较系统计数器的当前值与位于当前报警就绪队列队首的报警器的报警时间是否一致，从而决定是否触发报警，该方法大大提高了报警检测和响应速度，缩短了报警检测和响应时间。
",G06F9/48,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/48,"提供, 包括, 装置, 方法, 系统, 检测",检测
CN103019792A,CN201210564648.2,"本发明提供了一种应用程序的重编程方法及系统，本发明中重编程设备和至少一个电子控制单元相连接，所述电子控制单元可以包括：重编程程序模块和应用程序模块。在电子控制单元由断电状态变为上电状态的情况下，该电子控制单元中的重编程程序模块接收重编程设备发送的请求报文后，该重编程程序模块向重编程设备发送响应报文，并中断重编程程序模块对应用程序模块的引导过程，以使应用程序模块维持在未启动状态。当应用程序模块维持在未启动状态时，重编程程序模块就可以较长时间处于运行状态，从而保证重编程设备可以有足够的时间将保存的应用程序通过重编程程序模块下载到应用程序模块中，实现了应用程序的在线重编程。
",G06F9/445,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"提供, 实现, 包括, 方法, 设备, 控制, 系统",控制
CN102935805A,CN201210440063.X,"本发明公开了一种车身闭合系统的防夹判断方法及系统，所述方法通过对所述车身闭合系统的运行位置进行分段，重建每一分段的机械特性平均值，并利用所述分段中每个运行位置的实时机械特性值与该运行位置所处分段的重建机械特性平均值的差值作为判断是否发生挤夹的依据，解决了因环境因素（温度、电压等）和时间变化（变形、老化和磨损等）对车身闭合系统运行特性的影响所引起的复杂补偿问题，提高了系统的稳定性，并且提高了防夹判断的准确率。
",B60K28/10,北京经纬恒润科技有限公司,多实时机级联技术,1.0,关键词匹配: 实时机,"系统, 方法",通用
CN102819443A,CN201210279665.1,"本发明提供了一种PCI硬件应用程序兼容运行的方法，包括：当对PCI硬件进行操作的应用程序的个数为至少两个时，启动服务程序，应用所述服务程序得到PCI硬件的数据结构列表，并将数据结构列表发布给对PCI硬件进行操作的所有的应用程序，使所述应用程序通过数据结构列表中的目标PCI硬件的信息对目标PCI硬件进行操作。采用本发明的提供的一种PCI硬件应用程序兼容运行的方法和装置，解决了当多个应用程序同时操作PCI硬件时导致的异常，使得各个应用程序能够兼容运行。
",G06F9/445,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"提供, 方法, 包括, 装置",通用
CN102662692A,CN201210071649.3,"本发明提供了一种电子控制单元中应用程序的更新方法及系统，该系统包括远程服务器、重编程设备和电子控制单元网络，该方法包括：重编程设备向电子控制单元发送身份信息请求；电子控制单元对自己的身份信息进行加密并将加密的身份信息发送给重编程设备；重编程设备向远程服务器发送更新文件请求；远程服务器根据应用程序生成加密的更新文件，并将加密的更新文件传送给重编程设备，重编程设备将加密的更新文件数据发送给电子控制单元；电子控制单元解密加密的更新文件数据并将应用程序数据写入存储器。本发明提供的应用程序的更新方法和系统，由于对包括有应用程序的更新文件进行了加密处理，因此保证了更新文件的安全，即保证了应用程序的安全。
",G06F9/445,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"提供, 包括, 方法, 生成, 处理, 设备, 控制, 系统","网络, 控制"
CN102650939A,CN201210093055.2,"本发明公开了一种防止重编程程序对应用程序干扰的方法及装置，所述方法包括：初始化控制器的硬件环境和软件环境；获取重编程标识，并判断所述重编程标识是否有效；如果所述重编程标识有效，则运行所述重编程程序；如果所述重编程标识无效，则获取应用程序标识，并判断所述应用程序标识是否有效；如果所述应用程序标识有效，则反初始化所述控制器的硬件环境，并运行所述应用程序。本发明通过在从重编程模式向应用程序模式切换之前，对控制器的硬件环境和软件环境进行初始化，避免应用程序运行过程中出现错误，排除了重编程程序对应用程序造成干扰。
",G06F9/44,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"方法, 包括, 装置, 控制",控制
CN102629213A,CN201210041375.3,"本发明提供了一种C语言仿真模型的剖析方法，预先由文法文件生成C代码剖析方法类，C代码剖析方法类包括语法分析模块、词法分析模块和语法树生成模块，该方法包括：调用C代码剖析方法类中的词法分析模块对导入的C语言仿真模型的词法内容进行剖析，形成记号流；调用语法分析模块，根据记号流，对导入的C语言仿真模型的语法结构进行剖析；调用语法树生成模块根据分析得到的语法结构生成相应的语法树；从语法树中提取信息并将提取的信息保存到模型信息文件中。本发明还提供了一种C语言仿真模型的监控方法，该监控方法包括上述的剖析方法。本发明提供的剖析及监控方法填补了目前国内C语言仿真模型剖析及监控技术的空白，且实现过程简单。
",G06F9/45,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/45,"提供, 实现, 包括, 模型, 仿真, 方法, 生成",通用
CN102567018A,CN201110439647.0,"本发明公开了一种车载控制器执行的应用程序下载方法及系统，所述方法包括：A、获得待更新应用程序的待更新版本控制信息，以及电子控制单元ECU中初始版本控制信息；B、比较所述待更新版本控制信息与所述初始版本控制信息，如果满足条件，则进入步骤C；C、判断所述电子控制单元ECU中存储应用程序的容量是否不小于所述待更新应用程序占用字节数，如果是，则下载所述待更新应用程序。采用本发明提供的方法及系统，通过车载控制器的判断替代了人为判断，而且还保证了所述下载至所述ECU中应用程序的完整性，从而达到了准确的下载应用程序的目的。
",G06F9/44,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"提供, 包括, 方法, 控制, 系统",控制
CN102103511A,CN201110046811.1,"本发明公开了一种应用程序的刷新方法和系统。其中，该方法包括：电子控制单元获取来自下载工具的重编程指令；电子控制单元根据重编程指令，使得电子控制单元进入重编程模式；下载工具将新的应用程序下载并保存至电子控制单元。通过本发明，能够实现对ECU应用程序进行在线重编程。
",G06F9/445,北京经纬恒润科技有限公司,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"实现, 包括, 方法, 控制, 系统, 工具",控制
