﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN120475116A,CN202510645922.6,"本申请公开了一种数据传输方法及相关装置，涉及数据传输领域，数据传输设备包括：处理器、解串器和两个串行器；两个串行器分别与解串器连接，两个串行器和解串器均与处理器连接；解串器接收摄像头数据，在控制器单通模式下将摄像头数据传输至对应串行器，在控制器双通模式下对摄像头数据进行复制后分别传输至两个串行器，以使与串行器连接的控制器获得摄像头数据；处理器对控制器进行监控，获得第一监控结果；处理器根据第一监控结果反映的控制器状态判断是否需要切换控制器模式；在需要切换控制器模式的情况下，处理器对解串器和串行器进行配置，以实现控制器模式切换。本申请可以在保证系统可靠性的同时实现数据备份的目的。
",H04N5/268,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"实现, 包括, 装置, 方法, 处理, 设备, 控制, 系统, 配置","摄像, 数据传输, 控制"
CN120375355A,CN202510499462.0,"本发明提供一种点云数据处理方法及装置、存储介质及电子设备，应用于FPGA现场可编程逻辑门阵列，包括：对获取的各个簇点集中的各个点进行旋转映射，得到每个点在不同的旋转角度的映射坐标；基于每个簇点集的各个点的各个映射坐标，获取每个簇点集在每个旋转角度的拟合面积，对于每个簇点集，在该簇点集的各个拟合面积中确定目标拟合面积，然后应用在目标拟合面积拟合出簇点集的边框顶点坐标。整个运算过程简单，降低对芯片的要求，可兼容于各种性能较低的芯片，降低实现点云数据处理的成本，运算过程简单无需进行大量的计算，使用少量的系统算力即可实现，减少系统的运算压力，且无需应用不同的芯片处理数据，提高数据处理效率。
",G06V20/64,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"提供, 实现, 包括, 装置, 方法, 处理, 设备, 系统, 计算",通用
CN120314911A,CN202510546812.4,"本申请公开了一种数据处理方法及装置。该方法包括：获取自车的原始可行驶区域的多帧点集及自车的帧间位姿变化数据，点集形成对应帧的可行驶区域，帧间位姿变化数据由自车的运动信息推算得到；根据帧间位姿变化数据将多帧点集投影到当前帧的自车坐标系下，得到投影后的点集；将多帧投影后的点集映射到预设的栅格地图中，统计栅格地图中每个栅格的数据点数量；根据每个栅格的数据点数量从栅格地图中确定噪声栅格；从原始可行驶区域的多帧点集中滤除噪声栅格内的数据点，得到更新后的多帧点集；基于更新后的多帧点集构建自车的可行驶区域。本申请实施例所提供的方案能够准确识别出雪花噪声及真实障碍物，提升了雪天环境下可行驶区域的可用性。
",G01S7/48,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"提供, 包括, 装置, 方法, 处理",通用
CN120263697A,CN202510443061.3,"本申请提供了一种车载网关路由的测试方法、装置及存储介质，所述方法包括：基于车载网关路由的测试用例获取车载网关路由测试对应的测试步骤。按照测试步骤将车载网关路由的测试用例封装为功能模块，功能模块包括分流器控制模块、路由模块、测试数据发送模块、测试数据接收模块、测试数据处理模块和衔接模块。获取待测试车载网关路由的路由测试策略。将多个功能模块按照路由测试策略对应的测试规则生成对应于待测试车载网关路由的测试脚本。利用测试脚本对待测试车载网关路由进行测试得到测试报告。本申请提供的方法通过预先封装好的功能模块生成测试脚本，在车载网关路由的测试过程中更简单的生成测试脚本，从而减低车载网关路由的测试的难度。
",H04L43/0817,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"提供, 包括, 测试, 装置, 方法, 生成, 处理, 控制","测试, 控制"
CN120256427A,CN202510245228.5,"本申请公开了一种数据表格的生成方法、装置、存储介质及处理器。该方案中，获取用户输入的用于生成目标数据表格的XML标签；XML标签中包括用户定义的表格配置信息和Groovy脚本；基于XML标签中的表格样式信息，将XML标签解析为标签结构树；依次遍历标签结构树中的标签节点，执行与该标签节点相对应的Groovy脚本，从系统结构树中查询与标签节点相对应的目标数据；将目标数据添加至标签结构树中的对应标签节点中，并将标注数据后的标签结构树转换为目标数据表格。本申请技术方案通过允许用户依据个人需求自定义的XML标签来生成目标数据表格，提升了数据查询的灵活性和效率，从而提高了用户的体验感。
",G06F16/22,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/22,"包括, 装置, 方法, 生成, 处理, 系统, 配置",通用
CN120215985A,CN202510345020.0,"本申请提供了一种双Bootloader自更新方法、装置、设备及存储介质，涉及汽车软件控制技术领域，该方法先通过BootM组件的更新指令检测功能使得设备在启动初期即能识别并执行更新操作，当检测结果中包含更新指令时，通过设置跳转标志位的值确保更新过程的有序进行。若不包含更新指令，BootM会根据跳转标志位的当前值判断并准备进入相应的Bootloader更新流程。通过引入重编程有效标志BootM在进行更新时能够进一步确保更新的有效性和安全性。通过该方法能够实现设备启动过程中的双Bootloader根据实际情况进行自动化更新提高系统的稳定性和可靠性，同时降低维护成本，为用户带来更好的使用体验。
",G06F8/65,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"提供, 实现, 装置, 方法, 设置, 设备, 控制, 系统, 检测","检测, 汽车, 控制"
CN120215983A,CN202510322034.0,"本申请公开了一种OTA升级方法及相关装置，涉及通信技术领域，通过在车载终端的本车型配置文件中设置本车型支持的第二升级策略，从而在获取云端服务器下发升级包之后，对升级包中的第一升级策略与第二升级策略进行匹配，在第一升级策略与第二升级策略匹配且车辆状态符合第一升级策略的情况下，执行OTA升级，避免云端服务器下发的第一升级策略和车型支持的第二升级策略不匹配时升级导致的错误，保证OTA升级的稳定性和可靠性。
",G06F8/65,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"方法, 设置, 装置, 配置","车辆, 通信"
CN120162067A,CN202510422431.5,"本发明公开了一种车载电子控制单元升级方法、装置及移动终端，移动终端根据用户输入的APP登录信息跳转至功能展示页面，根据用户在功能展示页面选择的目标车型的待升级电子控制单元，获取待升级电子控制单元对应的电子控制单元升级软件包，将电子控制单元升级软件包发送至与移动终端通过车辆局域网连接的车端，并获取车端利用电子控制单元升级软件包对待升级电子控制单元进行离线升级后的升级结果。本发明使得车辆在下线后需要进行电子控制单元刷写时，无需借助价格昂贵的电子控制单元刷写工具，通过移动终端即可实现对不同车型下的不同电子控制单元的刷写升级操作，整个刷写过程操作便捷，并大大降低了电子控制单元升级成本。
",G06F8/65,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"实现, 装置, 方法, 控制, 工具","车辆, 控制"
CN120107636A,CN202510221269.0,"本申请公开了一种毫米波雷达点云数据的处理方法及装置。应用于FPGA中，该方法包括：依次接收点云数据帧所包含的多个点云数据；对多个点云数据并行执行多普勒补偿处理和杂波标注处理，得到多个处理后的点云数据；在完成点云数据帧的接收时，根据补偿后的点云数据确定点云数据帧所对应的目标动静点云判别阈值；通过杂波标识及动静点云判别阈值对多个处理后的点云数据并行执行动静点云标注和网格化处理，得到网格化处理后的点云数据帧；对网格化处理后的点云数据帧进行小波聚类，确定点云数据帧中每个点云数据归属的聚类簇。本申请所提出的方案能够降低毫米波雷达系统的数据处理复杂度，提升点云数据的处理效率。
",G06V10/762,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"包括, 装置, 方法, 处理, 系统",雷达
CN119861945A,CN202411931498.3,"本申请提供了一种波特率自适应程序刷写方法及装置，涉及数据传输技术领域。在执行所述方法时，先获取波特率切换列表，后根据波特率切换列表中的波特率，向下位机发送波特率切换命令，直至收到下位机发送的回复报文，然后，将回复报文对应的波特率确定为目标波特率，再根据目标波特率与下位机建立通信连接，最后，基于与下位机的通信连接进行程序刷写。这样，将上位机和下位机统一成目标波特率，使得上位机和下位机可以建立通信连接并进行程序刷写，进而提高波特率自适应程序刷写的效率。
",G06F8/65,北京经纬恒润科技股份有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据传输; IPC分类号匹配: G06F8/65,"提供, 方法, 装置","通信, 数据传输"
CN119690448A,CN202411719093.3,"本申请公开了一种文件解析处理方法及系统，针对各个版本的Arxml文件，通过预先配置的模型自研工具获取Arxml文件的头信息，其中，模型自研工具中存储着预设映射关系的配置项，映射关系由服务元素在目标版本的Arxml文件的引用对象确定，根据头信息确定依赖包，根据模型自研工具和依赖包，对Arxml文件进行解析，得到解析结果，若解析结果表示初始版本的Arxml文件的属性为不能复用，对不能复用的初始版本的Arxml文件进行兼容处理。
",G06F8/41,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/41,"模型, 方法, 处理, 系统, 工具, 配置",通用
CN119597251A,CN202411629093.4,"本申请公开了一种智能产线信息物理系统，包括：物理子系统、信息子系统和边缘设备；边缘设备包括用于与物理子系统进行数据交互的第一功能模块和用于与信息子系统进行数据交互的第二功能模块；边缘设备被配置为执行如下步骤：通过第一功能模块，控制物理子系统运行并获取物理子系统在运行过程中产生的实时数据；识别实时数据中需要信息子系统处理的第一数据；通过第二功能模块，向信息子系统发送第一数据。本申请实施例，通过分别设置与物理子系统交互的第一功能模块和与信息子系统交互的第二功能模块，实现了物理子系统的自动化和数字化，隔离了面向物理子系统和信息子系统的功能模块，提高了代码复用性与扩展性，降低开发及后续维护成本。
",G06F8/20,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/20,"实现, 包括, 设置, 处理, 设备, 控制, 系统, 配置",控制
CN119597775A,CN202411658775.8,"本申请公开了一种实现订单状态变更的方法、装置、电子设备及存储介质，该方法中，接收客户端发送的客户端标识符和客户端版本信息，查询客户端的第一商品集合的订单信息；将第一订单状态修改为第二订单状态；将第一商品标识集合发送至客户端；获取第二商品标识集合；对第一总商品标识和第二商品标识集合中商品标识进行异或运算，获得第一商品集合中商品的目标订单状态；将第一商品集合中商品的第二订单状态修改为目标订单状态。如此，利用二的整数次幂的二进制特性，根据商品标识和实际失效时间，即可实现订单状态变更，并且因为整个过程是商品标识和实际失效时间的交互，相较于现有技术而言，减少了数据库操作，能够实现快速实现订单状态变更。
",G06F16/23,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/23,"方法, 装置, 实现, 设备",通用
CN119536717A,CN202411604581.X,"本发明提供一种快速原型算法验证平台及方法，通过配置界面接收用户输入的配置参数，基于配置参数和根据AUTOSAR标准编写的脚本生成配置文件；基于用户选择的符合AUTOSAR标准的功能模块搭建算法模型；生成算法模型对应的模型代码；在底层工程中对配置文件和模型代码进行编译和链接得到hex文件。在本方案中，利用基于AUTOSAR标准编写的脚本，以及封装AUTOSAR标准中ECU抽象层接口得到的各个功能模块，得到符合AUTOSAR标准的配置文件和模型代码，并自动化集成到底层工程进行编译链接，从而在提高代码复用率的同时节省人力，进而实现缩短算法验证耗时的目的。
",G06F8/36,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/36,"提供, 平台, 实现, 模型, 算法, 方法, 生成, 配置",验证
CN119520609A,CN202411676926.2,"本申请实施例提供了一种数据传输方法、装置、设备及介质，涉及嵌入式软件技术领域。该方法包括：通过数据分发服务DDS，将服务端和客户端进行连接；在使用数据写入器将服务数据写入DDS时，利用服务接口中定义的事件、方法和字段，将服务数据映射到相应的DDS的主题上，以使数据读取器通过读取DDS的主题，对服务数据进行读取。由此，服务数据可以通过DDS协议进行传输，提供了QoS控制的数据共享，通过发布和订阅机制实现应用程序之间的通信，具有高效、实时和可靠的特点。
",H04L67/566,北京经纬恒润科技股份有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据传输; IPC分类号匹配: H04L67/566,"提供, 实现, 包括, 装置, 具有, 方法, 设备, 控制","通信, 数据传输, 控制"
CN119512616A,CN202411554613.X,"本发明提供一种基于建模支撑工具的文档生成方法及装置，在预设开发软件中新建类库，并在类库中进行属性配置和添加引用项内容，得到目标类库；在目标类库中写入预设代码，得到动态链接库文件；新建批处理文件并将动态链接库文件的存储路径写入批处理文件中，得到目标批处理文件；运行目标批处理文件以在预设建模支撑工具的前端界面创建文档生成菜单，其中，预设建模支撑工具预先根据计算机注册表完成配置；当文档生成菜单被触发时，根据预设建模支撑工具中的架构模型生成开发文档。通过对预设建模支撑工具进行优化，实现了快速生成架构开发流程各阶段的开发文档的目的，使生成开发文档的过程更加快捷，解决了模型复杂度高、可读性较差的问题。
",G06F8/73,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/73,"提供, 实现, 模型, 装置, 方法, 生成, 处理, 计算, 工具, 配置",通用
CN119520458A,CN202411677059.4,"本申请公开了一种消息的编码方法和消息的解码方法。在执行本申请实施例提供的方法时，首先可以获取待编码的文件内容和文本内容，并计算文件内容的文件内容长度和文本内容的初步文本内容长度。根据文件内容长度和初步文本内容长度计算消息总长度，并将初步文本内容长度进行数据类型转换得到文本内容长度。再将消息总长度、文本内容长度、文本内容以及文件内容依次拼接得到编码消息。本申请通过计算文件内容和文本内容的长度以及消息总长度，可以准确地确定消息的整体长度，简化了校验过程和额外的协议属性处理过程，使得消息处理更加高效。同时，明确区分了消息中的文本内容和文件内容部分，以提高消息处理的效率。
",H04L51/066,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据类型,"提供, 方法, 计算, 处理",通用
CN119484731A,CN202411620776.3,"本申请公开了一种仿真视频的注入方法及系统，注入系统包括：仿真视频渲染工作站、PCIe适配板、CPCIe适配板、CPCIe扩展机箱以及视频仿真板卡；PCIe适配板与仿真视频渲染工作站连接；PCIe适配板与CPCIe适配板连接；CPCIe适配板设置于CPCIe扩展机箱的内部，CPCIe适配板设置有多个CPCIe槽位；视频仿真板卡设置于CPCIe扩展机箱的CPCIe槽位中；视频仿真板卡与控制器连接。通过PCIe适配板与CPCIe适配板将图像数据传输给视频仿真板卡，再将图像数据注入控制器中，由显卡对图像进行处理，从而能够实现更丰富的图像后处理效果。
",H04N5/14,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,"关键词匹配: 数据注入, 数据传输","实现, 包括, 仿真, 方法, 设置, 处理, 控制, 系统","数据传输, 控制"
CN119479685A,CN202411595348.X,"本申请公开了一种音频数据处理方法、装置、存储介质和电子设备，该方法为：确定目标模型训练过程所需的音频数据；基于音频数据，确定目标频谱；对目标频谱进行窗口划分与拼接，以获得第一特征序列，第一特征序列包括按照指定时间顺序记录的多个第一特征，且时间上连续的两个第一特征之间存在第一重叠数据；基于第一特征序列，确定音频特征。该方法通过对目标频谱进行窗口划分与拼接，以实现对音频特征的数据增强，经由数据增强处理后，长序列的音频数据将被拆分为多个具有重叠部分的数据片段，能进行多次推理，且各个数据片段之间具关联性，能有效防止目标模型单次推理带来的误识别，提高目标模型的识别准确度。
",G10L25/30,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"实现, 包括, 模型, 装置, 具有, 方法, 处理, 设备",通用
CN119396461A,CN202411605247.6,"本发明提供了一种基于AUTOSAR的配置信息确定方法及装置，涉及自动化软件配置领域。基于配置需求信息以及AUTOSAR中的模块之间的模块依赖关系，确定配置项的功能描述信息，检索出所述配置项的功能描述信息对应的关联信息，利用所述配置项的功能描述信息以及所述关联信息，确定配置结果，生成所述配置结果对应的ARXML文件。本发明在确定配置信息时，考虑了AUTOSAR中的模块之间的模块依赖关系，使得确定的配置信息更加符合实际的模块依赖场景，提高AUTOSAR配置信息的准确度。并且本发明中，还会检索配置项的功能描述信息对应的关联信息，使得确定配置信息时使用的数据更全面，保证后续确定的配置结果准确度。
",G06F8/71,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/71,"提供, 装置, 生成, 方法, 配置",通用
CN119149078A,CN202410822891.2,"本申请公开了一种基于MCU升级switch固件的方法、装置、设备及介质。在上述方法中，MCU首先获取switch固件对应的数据包之后，通过串行外设接口SPI通信协议访问switch，并获得switch的信息；若switch的版本信息与数据包的版本信息不一致，则对switch的硬件信息与数据包进行匹配校验；若匹配校验通过，则将数据包写入switch的Flash中，并将switch进行复位。在此过程中，MCU通过SPI通信协议可以直接将数据包写入switch中，以实现switch固件的升级，无需借助外部硬件设备，减少了升级switch固件的成本，从而提高升级的便捷性和效率。
",G06F8/654,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/654,"方法, 装置, 实现, 设备",通信
CN119046240A,CN202411095653.2,"本发明提供一种运营数据的拉取方法及装置，应用于数据上传设备，方法包括：扫描数据采集设备得到数据文件，其中，数据采集设备实时采集运营数据生成数据文件，并限定数据文件个数和存储时间，将数据文件的文件名存储到对应的文件列表，响应于接收到云端服务器发送的查询任务，查询得到目标文件名并返回，响应于接收到云端服务器在港口无人运输车辆空闲时发送的拉取任务，上传拉取任务指示的数据文件。在本方案中，不改变网络和硬件架构，利用数据采集设备生成数据文件并限定文件个数和保存时间，利用数据上传设备响应云端服务器在港口无人车辆在空闲状态时发送的拉取任务上传数据文件，从而实现合理规划存储空间，高效拉取运营数据的目的。
",G06F16/16,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/16,"提供, 实现, 包括, 装置, 方法, 生成, 设备","车辆, 网络"
CN118963809A,CN202411348284.3,"本申请公开了一种升级软件是否启动成功的验证方法以及相关装置。首先在查询到芯片入口地址正常启动时，选取第一待启动分区，并从MCU的升级标志区中选取出第一待启动分区的分区可靠性标志位。当分区可靠性标志位为可靠时，基于分区可靠性标志位和升级软件的正常执行时长检查升级软件是否成功启动；当分区可靠性标志位为不可靠时，基于第一待启动分区的分区启动次数和升级软件的正常执行时长检查升级软件是否成功启动。本申请通过检测第一待启动分区的可靠性标志位来验证升级软件是否启动成功，以避免升级过程中出现各种异常情况造成系统崩溃或数据丢失的情况，从而提高升级的可靠性。
",G06F8/65,天津经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"系统, 检测, 方法, 装置","检测, 验证"
CN118860442A,CN202410939638.5,"本申请公开了一种产品软件的升级方法及系统，所述方法包括：获取多个待升级组件；判断各个待升级组件是否满足升级要求；若各个待升级组件均满足升级要求，则对各个待升级组件对应的升级包进行校验；其中，升级包预先通过对待升级组件的特性进行封装得到；当各个待升级组件对应的升级包均通过校验时，判断所有待升级组件的状态是否满足升级条件的状态；若所有待升级组件的状态满足升级条件的状态，则利用各个待升级组件对应的升级包，对各个待升级组件进行升级；当各个待升级组件均升级成功时，则将各个待升级组件的当前版本切换为升级版本。从而实现了多个组件可以采用统一的升级方法进行升级，并且能够快速迭代，降低成本。
",G06F8/65,天津经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"系统, 方法, 包括, 实现",通用
CN118838579A,CN202410866947.4,"本申请提供了一种软件构建方法及装置，通过在实时监测到代码仓库中存储的待构建软件的软件代码发生变更的情况下，获取代码仓库的仓库标识，代码仓库通过第一预设接口与持续集成平台通信，第一预设接口基于代码仓库的仓库属性确定；基于仓库标识，从预设置的数据库中获取与代码仓库相关联的目标流水线，目标流水线用于构建待构建软件；基于目标流水线中每个构建任务之间的执行顺序，以及目标构建引擎，构建待构建软件，目标构建引擎与待构建软件相关联，目标构建引擎通过第二预设接口与持续集成平台通信，第二预设接口基于目标构建引擎的引擎属性确定。本申请实施例能够提高构建软件的构建效率。
",G06F8/30,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/30,"提供, 平台, 装置, 方法, 设置",通信
CN118820343A,CN202410910936.1,"本申请公开了一种数据的导入、数据的导出方法。该数据导入方法包括：获取导入请求中第一模板ID对应的第一模板；从第一目标文件中获取第一页表格名称对应的第一页表格；获取第一页表格标题行中的第一标题名称和第一列号；获取第一页表格的导入数据，将与导入数据的第二列号相同的第一列号的第一标题名称作为导入数据的标题名称；将导入数据的标题名称转换为属性名，得到第一页表格中全部数据行的导入数据和导入数据的属性名；向第一业务服务模块发送第一额外参数、第一页表格中全部数据行的导入数据和导入数据的属性名。将数据导入服务和业务服务解耦，降低了后期业务服务模块的维护难度。
",G06F16/25,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/25,"方法, 包括",通用
CN118803084A,CN202410843815.X,"本申请公开了一种数据转换方法和装置。本申请实施例的数据转换方法和装置，该方法包括：在接收到第一报文的情况下，从所述第一报文中筛选出满足第一预设标识和第一预设长度的第二报文；按照第二预设长度对所述第二报文进行拆分，得到至少一个数据类型的第一信号片段；按照第三预设长度对所述至少一个数据类型的第一信号片段进行封装，得到第三报文；将预设报文头添加到所述第三报文的头部，得到第四报文。如此，通过对报文的拆分和重新封装，提供不同报文类型之间的数据转换方法，即使CAN总线数据传输时的帧结构与以太网数据传输时的帧结构不同，也能通过数据的转换，实现CAN总线与以太网之间的通信。
",H04L69/08,北京经纬恒润科技股份有限公司,数据注入类型支持技术,3.0,"关键词匹配: 数据类型, 数据传输, 数据转换","提供, 实现, 包括, 装置, 方法","通信, 数据传输"
CN118785123A,CN202410819142.4,"本申请提供了一种车辆的低功耗蓝牙控制方法、装置及设备。涉及车辆技术领域，本申请所述低功耗蓝牙包括主模块和与主模块连接的从模块，方法包括：获取车辆工况；基于所述车辆工况，配置低功耗蓝牙主模块的扫描模式和从模块的广播模式，便于所述主模块与从模块之间的数据传输；控制低功耗蓝牙的主模块的广播模式与所述车辆工况匹配。如此能够，低功耗蓝牙的运行模式不再仅仅根据低功耗蓝牙自身的工况来进行切换，而是根据整车运行工况进行匹配，从而降低整车系统的功耗。
",H04W4/80,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"提供, 包括, 装置, 方法, 设备, 控制, 系统, 配置","车辆, 数据传输, 控制"
CN118689477A,CN202410822504.5,"本申请提供了一种地图绘制方法及装置，所述方法包括：构建显示界面，所述显示界面为前端界面；根据待绘制地图的空间信息，在所述显示界面中构建地图画布，所述地图画布用于绘制所述待绘制地图；获取所述待绘制地图的道路参数信息，所述道路参数信息用于描述所述待绘制地图中不同道路类型的道路的特征；在所述地图画布中，根据所述道路参数信息绘制与所述道路类型对应的目标道路，得到目标地图；在所述显示界面显示所述目标地图。采用上述步骤可以能够降低OpenDrive地图的使用成本。
",G06F8/38,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/38,"提供, 方法, 包括, 装置",通用
CN118672622A,CN202410840608.9,"本申请公开了一种车载虚拟形象更新方法、装置及介质，该方法包括：在车辆行驶过程中实时监控车辆的当前所在位置；在当前所在位置位于N个区域范围中目标区域范围的情况下，获取与目标区域范围相匹配的目标虚拟资源数据，其中，目标虚拟资源数据包括目标区域范围关联的目标主题虚拟形象的资源数据；基于目标虚拟资源数据，将车辆的车载虚拟形象更新为当前所在位置的目标主题虚拟形象。根据本申请实施例，能够将地理位置信息与车载虚拟形象相结合，实现车载虚拟形象的动态更新，提升车载虚拟形象的趣味性。
",G06F8/65,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"方法, 包括, 装置, 实现",车辆
CN118656119A,CN202410836277.1,"本申请公开了一种数据刷写方法及装置，从上位机中读取配置文件；对配置文件进行解析，得到配置项和待刷写文件的文件路径；根据待刷写文件的文件路径查找待刷写文件；利用配置项从下位机中获取厂商信息；当厂商信息与预设厂商信息一致时，将待刷写文件刷写至控制器中。在本申请中，只需要进行更改配置文件即可实现刷写流程的更改，不需要更改源代码生成新的刷写程序，提升了刷写效率。还增加了对厂商信息核对的流程，减少在无核对的情况下刷错件的情况，减少了废件率。
",G06F8/71,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/71,"实现, 装置, 方法, 生成, 控制, 配置",控制
CN118585229A,CN202410653355.4,"本申请实施例记载了一种汽车远程升级方法及系统。在该方法中，接收远程升级后台下发的远程升级任务信息；获取目标车辆的车辆信息和车辆电池信息；基于车辆信息和车辆电池信息确定目标车辆满足预设的远程升级任务需求，则向远程升级后台返回升级方案，升级方案为根据车辆信息和车辆电池信息计算得到的；接收远程升级后台根据升级方案下发的远程升级任务；根据远程升级任务对电子控制单元进行升级。由此可见，利用本申请实施例提供的方案，可以更加精确地确定远程升级任务的升级方案，提高远程升级任务执行场景的灵活性，相比于人工计算远程升级任务的升级方案，可以大大提高计算效率，降低成本。
",G06F8/654,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/654,"提供, 方法, 控制, 系统, 计算","车辆, 汽车, 控制"
CN118550563A,CN202410675438.3,"本申请公开了一种电子控制单元刷写顺序确定方法及装置，该方法包括：从待刷写电子控制单元序列中筛选出节点属性为主节点的第一电子控制单元以及节点属性为非主节点的第二电子控制单元，得到主节点序列和非主节点序列；根据非主节点序列，构建键值对数据结构集合；根据键值对数据结构集合确定键对象集合；根据键对象集合得到第一刷写组；以第一刷写组为当前组；循环执行以下操作，直至第二电子控制单元均归属到对应的刷写组：遍历第一刷写组中电子控制单元对应的键值对数据结构，将第一刷写组中电子控制单元的值对象加入下一个刷写组；将下一个刷写组更新为当前组；根据电子控制单元的刷写约束条件，确定各个刷写组的刷写顺序，能够提高刷写效率。
",G06F8/65,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"方法, 包括, 装置, 控制",控制
CN118349612A,CN202310068017.X,"本发明提供了一种地图标记方法及装置，获取初始位置信息，并对所述初始位置信息进行筛选操作，得到中间位置信息，所述初始位置信息为在数据采集过程中产生的位置信息，对所述中间位置信息进行坐标转换操作，以转换成预设地图展示软件能够识别的目标位置信息，调用所述预设地图展示软件，以使所述预设地图展示软件在目标地图中对所述目标位置信息进行地图标记操作。即通过本发明，实现了数据采集领域的地图标记功能，进而能够使用地图标记指导路线规划、以及分析历史采集信息概况。
",G06F16/29,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/29,"提供, 方法, 装置, 实现",通用
CN118331940A,CN202410391488.9,"本申请公开了一种整车日志收集系统和方法，设置在每个网络边缘ECU上的第一日志收集模块，用于收集自身所在网络边缘ECU上的应用程序运行时产生的第一日志；设置在每个中间层ECU上的第二日志收集模块，与第一日志收集模块连接，用于收集自身所在中间层ECU上的应用程序运行时产生的第二日志，并接收来自与其连接的第一日志收集模块传输来的第一日志；设置在网络中心ECU上的第三日志收集模块，与第二日志收集模块连接，用于收集网络中心ECU上的应用程序运行时产生的第三日志，并接收每个第二日志收集模块传输来的第一日志和第二日志。基于此，降低了新车型(不同电子控制单元ECU网络结构或不同日志方案的ECU)整车日志收集开发的难度和时间成本。
",G06F16/18,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/18,"系统, 方法, 设置, 控制","网络, 控制"
CN118331580A,CN202410421735.5,"本申请公开了一种切换ECU生产状态和产品状态的方法、装置、设备及介质。在上述方法中，通过设计生产软件和产品软件存储在不同分区中，并通过读取分区标志块，可以实现系统在生产分区中启动生产软件进行对应的生产操作，并且完成生产操作后通过清除生产状态标志，使分区标志块为产品状态标志，从而实现将系统从生产状态切换到产品状态，以使产品软件进行分区同步操作。在此过程中，由于生产软件和产品软件是存储在不同分区的，因此可以杜绝两套软件的相互影响，并且通过读取分区标志块，可以快速完成生产操作、从生产状态切换至产品状态的操作，以及分区同步操作，从而极大地增加生产软件的稳定性和减小了产品生产测试周期。
",G06F8/41,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/41,"实现, 测试, 装置, 方法, 设备, 系统",测试
CN118296080A,CN202410465537.9,"本申请提供了一种数据录制回放方法、装置、电子设备及存储介质，该数据录制回放方法可应用于分布式系统，包括：确定分布式系统中每个节点的输出事件对应的元数据，所述元数据包括：影响本节点的输出事件的输入事件的标签、逻辑时间戳以及类型；依据各个节点的输出事件对应的元数据，确定数据录制过程中每个录制事件的发生顺序和事件类型，发生顺序为逻辑时间顺序；按照发生顺序和事件类型，对录制事件进行数据回放，由于发生顺序为逻辑时间顺序，能够保证数据回放时与录制时一致，解决了现有分布式系统中由于节点间通讯、时间同步等问题，导致数据的一致性和确定性无法保证的问题。
",G06F16/27,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/27,"提供, 包括, 装置, 方法, 设备, 系统",通用
CN118193204A,CN202410330105.7,"本申请公开了一种数据处理方法和装置，该方法应用于第一服务器，服务器位于车机端和云端之间，该方法包括：从服务器中的存储区域获取不同采集时刻下的第一数据类型的第一电参数，其中，第一电参数包括电压数据、电流数据和温度数据；将第一数据类型的第一电参数转换为第二数据类型的第一电参数；将第二数据类型的多个电压数据进行聚合，得到电压数据集；根据电压数据集中各电压数据的第一时间戳信息与第二数据类型的温度数据的第二时间戳信息和电流数据的第三时间戳信息，按照时间窗口将电压数据集和第二数据类型的温度数据和电流数据进行拼接，得到目标数据；将目标数据存储至存储区域或数据库中，以使云端从存储区域或数据库中获取目标数据进行处理，如此无需耗费云端计算资源即可实现对新能源汽车BMS数据的处理，节省了云端计算资源。
",G06F9/50,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,"关键词匹配: 数据类型, 数据处理","实现, 包括, 装置, 方法, 处理, 计算",汽车
CN118193023A,CN202410283890.5,"本申请公开了一种多电子控制单元刷写方法及装置，该方法包括：获取多电子控制单元ECU升级任务信息，N个ECU包括主节点及其关联的N‑1个ECU；通过对多ECU升级任务信息进行解析，将N个ECU划分至多个ECU分组；对多个ECU分组进行升级任务的串行刷写，其中，主节点的升级任务为最后一个刷写；对于每个ECU分组，至少一个ECU的预编程任务为并行刷写，至少一个ECU的后编程任务为并行刷写，对应不同刷写通道的至少两个ECU的主编程任务为并行刷写，对应同一刷写通道的至少两个ECU的主编程任务为串行刷写。根据本申请，能够优化多ECU的刷写方式，提升多ECU的刷写效率。
",G06F8/654,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/654,"方法, 包括, 装置, 控制",控制
CN118092872A,CN202410381618.0,"本申请公开了一种ARXML文件生成方法、系统、设备及介质。在上述方法中，利用建模工具建立模型之后，将建模数据进行导出，获得XML文件，再利用通信设计工具读取校验建模工具导出的XML文件；若校验XML文件通过，将XML文件进行数据解析，获得架构数据，再对架构数据进行承接及映射处理，封装在自定义数据模型中，最后将数据进行通信设计，并根据通信设计后的数据生成ARXML文件。在此过程中，利用建模工具进行架构建模，并获得XML文件，再利用通信设计工具将XML文件进行通信设计，获得ARXML文件，可以导出不同格式的数据库文件，兼容下游工具链进行软件开发，解决了逻辑层架构设计与通信层设计数据不互通的问题。
",G06F8/20,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/20,"模型, 方法, 生成, 处理, 设备, 系统, 工具",通信
CN118034735A,CN202410171874.7,"本申请公开了一种网关的性能升级方法及系统，该方法包括：通过第二引导加载程序判断电子控制单元是否存在第一升级请求，所述第一升级请求为所述第一引导加载程序的升级任务；当所述电子控制单元存在所述第一升级请求时，通过第二引导加载程序初始化所述第二引导加载程序的刷写资源；通过所述第二引导加载程序根据刷写协议对所述第一引导加载程序进行刷写，以使得所述第一引导加载程序进行性能升级，其中，所述刷写协议为所述第一引导加载程序对应的整车厂提供的刷写协议。本申请中，网关采用双BootLoader的设计模块来进行自升级，进而提升网关的性能。
",G06F8/65,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"提供, 包括, 方法, 控制, 系统",控制
CN118035141A,CN202410137583.6,"本申请公开了一种中断处理方法、设备及介质，涉及多核异构技术领域。中断处理方法包括：当第一内核产生通用型之输入输出GPIO中断时，第一内核通过目标通信通道向第二内核发送GPIO中断对应的中断信息，其中，第一内核为电子设备包括的两个内核中的任意一个内核，第二内核为两个内核中除第一内核之外的内核，目标通信通道用于第一内核和第二内核之间的通信；第二内核接收第一内核发送的中断信息，基于第一内核发送的中断信息进行中断处理。根据本申请实施例，能够跨核处理GPIO中断。
",G06F13/24,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F13/24,"方法, 包括, 处理, 设备",通信
CN117950699A,CN202410176614.9,"本申请公开了一种系统升级方法、装置以及硬件平台，包括：检测当前系统的状态运行信息和升级资源信息是否满足自升级条件，得到第一检测结果；在第一检测结果为满足自升级条件的情况下，将第一检测结果发送至OTA模块，以使OTA模块发送返回成功指令；根据升级资源信息对当前系统中的第一当前系统分区进行升级，得到原始升级系统，将升级的升级进度发送至OTA模块，以使OTA模块在升级进度达到预设值的情况下，发送确定指令；根据升级资源信息对升级系统分区进行检测，得到第二检测结果；在第二检测结果满足检测条件的情况下，确定原始升级系统为目标升级系统。基于此，减少了系统的升级时间，提高了系统升级效率。
",G06F8/65,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"平台, 包括, 装置, 方法, 系统, 检测",检测
CN117911808A,CN202410082338.X,"本申请提供一种模型训练方法、数据处理方法和装置，确定当前目标域数据的数量，若当前目标域数据的数量符合预设训练条件，通过判别器筛选出目标域数据，通过预训练模型组对目标域数据进行数据推理和伪标签融合，得到融合后的伪标签数据，通过预设模型训练方式和伪标签数据，对预训练模型组和判别器进行模型微调，并返回执行确定当前目标域数据的数量，直至当前目标域数据的数量不符合预设训练条件，得到目标模型，通过目标模型，对点云数据进行处理，得到推理结果，通过预设排序方式将推理结果进行排序，得到排序结果，通过预设选取方式从排序结果中选取得到伪标签数据，将伪标签数据反馈至车辆的决策端，以实现自动驾驶的感知、决策和控制执行。
",G06V10/774,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"提供, 实现, 模型, 装置, 方法, 处理, 控制","车辆, 驾驶, 控制"
CN117877148A,CN202410046245.1,"本申请公开了一种唤醒方法及系统。所述唤醒方法包括：在存在用户对远程钥匙的触发输入的情况下，通过远程钥匙内部的发射端的微处理控制器生成数据帧，并根据连接的同步时钟控制信号线对应的时钟周期确定数据传输速率，以通过发射端的发射芯片在该传输速率下依次发送唤醒帧以及至少三帧相同的数据帧至汽车控制器的接收芯片；从而，使得接收芯片使用中断方式捕捉唤醒帧，以在检测到唤醒帧的情况下汽车控制器的微处理控制器查询数据帧，进而根据查询结果由休眠状态切换至唤醒状态。根据本申请实施例，能够解决高低温和晶振差异等带来的数据波特率误差从而能够提升数据发送精度，并实现在保持唤醒灵敏度的条件下降低汽车控制器功耗。
",G07C9/00,天津经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"实现, 包括, 方法, 生成, 处理, 控制, 系统, 检测","检测, 汽车, 数据传输, 控制"
CN117851298A,CN202410031395.5,"本申请公开了一种基于PCIe的车辆资源共享方法、装置、设备、介质及车辆，车辆中的第一控制器中创建初始PCIe设备，第一控制器通过NTB获取第二控制器中第一PCIe设备的配置信息，第一PCIe设备为第一控制器和第二控制器需要共享使用的PCIe设备，第一控制器基于配置信息对初始PCIe设备进行配置，得到目标PCIe设备，第一控制器通过目标PCIe设备访问第一PCIe设备。根据本实施例，可以实现第一控制器与第二控制器之间的PCIe设备共享，从而减少了资源配置的成本，也降低了布线的成本，而且第一控制器在访问第二控制器中第一PCIe设备时，访问方式接近本地访问，无需进行协议转换和解析，从而有效降低了数据传输的延迟。
",G06F13/10,北京经纬恒润科技股份有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据传输; IPC分类号匹配: G06F13/10,"实现, 装置, 方法, 设备, 控制, 配置","车辆, 数据传输, 控制"
CN117827251A,CN202410175486.6,"本申请提供了一种车辆ECU版本升级方法、系统及相关设备，该车辆ECU版本升级方法可应用在云端服务平台，该方法在车辆上线之后，获取车辆的整车信息；基于整车信息和预先创建拉齐基线集合中的目标拉齐基线，判断车辆是否存在未升级到目标版本的ECU，拉齐基线集合包括多个拉齐基线，每个拉齐基线包括车辆中各个ECU需要升级至的目标版本；若判断出车辆存在未升级到目标版本的ECU，则依次发送每一个未升级的目标版本的ECU对应的升级包至车辆对应的车端客户端，以便车端客户端依次基于接收到的升级包为对应的未升级到目标版本的ECU进行升级，解决了现有对于产线车辆拉齐ECU版本方法存在做包过程耗时大、升级网络状态要求高导致的升级成功率低的问题以及存在升级文件冗余下发导致网络带宽耗费严重的问题。
",G06F8/65,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"提供, 平台, 包括, 方法, 设备, 系统","车辆, 网络"
CN117729255A,CN202311542679.2,"本申请提供了一种车载数据上传方法及装置，通过获取多个车载设备的初始运行数据；根据预设置的数据格式集对初始运行数据中的每个数据种类的数据分别进行格式调整，得到每个数据种类对应的目标运行数据，数据格式集包括多种数据种类，以及每种数据种类对应的数据格式；采用每个所述数据种类对应的上传模式向服务器上传每个数据种类对应的目标运行数据。本申请实施例能够保障上传的车载数据的数据格式的统一性，便于后续对车载数据进行查询。
",H04L67/565,北京经纬恒润科技股份有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据格式; IPC分类号匹配: H04L67/565,"提供, 包括, 装置, 方法, 设置, 设备",通用
CN117687634A,CN202311684769.5,"本申请提供了一种服务编译方法、装置和电子设备，包括：接收服务请求；若所述服务请求是获得动态库服务请求，基于所述服务请求获得目标算法模型；生成所述目标算法模型对应的编译配置文件；基于目标实现代码修改所述编译配置文件得到目标编译配置文件，所述目标实现代码是与目标算法模型的目标接口对应；编译所述目标编译配置文件得到动态库文件。
",G06F8/41,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/41,"提供, 实现, 包括, 模型, 装置, 算法, 方法, 生成, 设备, 配置",通用
CN117667861A,CN202311674495.1,"本申请提供一种文件传输的方法和装置，方法包括，显示文件选择界面；响应于使用者在文件选择界面的操作，通过目标组件获得待传输的目标文件的目标文件路径，目标组件具有获取本地文件的真实存储路径的权限；根据目标文件路径，计算构成目标文件的文件分片的哈希值，作为第一哈希值；向服务器发送每一文件分片对应的二进制大型对象，每一文件分片的第一哈希值以及第二哈希值；其中，第二哈希值根据目标文件中全部文件分片的第一哈希值确定，文件分片对应的二进制大型对象根据文件分片对应的数据视图对象转换得到，文件分片对应的数据视图对象根据文件分片转换得到。
",G06F16/16,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/16,"提供, 包括, 装置, 具有, 方法, 计算",通用
CN117648464A,CN202311665806.8,"本发明提供一种XML文件的解析方法及装置，获取XML文件中与根节点的首个子节点同级的目标节点；在目标节点对应的目标数据结构层级中，解析目标节点得到解析数据并存储到对应的层级存储变量；当目标节点解析完毕时，将各个层级存储变量中的解析数据存储到数据结构；若目标节点存在子节点，将子节点作为目标节点，返回继续解析，直到XML文件中的所有节点解析完毕。在本方案中，逐层级对XML文件中的节点在对应的数据结构层级中进行解析，得到的解析数据存储在数据结构层级对应的层级存储变量中，最终将层级存储变量中的解析数据复制到数据结构中，以实现同一套解析逻辑对不同数据结构类型的XML文件解析，提高开发效率的目的。
",G06F16/81,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/81,"提供, 方法, 装置, 实现",通用
CN117573168A,CN202311609085.9,"本发明提供了一种基于整车版本管理下的ECU升级方法及系统，该方法包括：显示与客户端的升级任务模块所对应的菜单界面；响应在菜单界面触发的操作，显示待升级的第二ECU对应的第二详情信息。当检测到用户触发的提交操作时，根据各个第二ECU对应的第二详情信息，判断是否满足升级条件，以确保升级级别为高的第二ECU的升级顺序优先于升级级别为低的第二ECU，以及确保类型为主节点的第二ECU的升级顺序排在最后；若满足升级条件，将升级任务下发至车辆端，使车辆端依序升级各个第二ECU，这样就能够避免因ECU升级顺序错误而导致的ECU升级失败问题，进而提高ECU升级效率。
",G06F8/65,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"提供, 包括, 方法, 系统, 检测","车辆, 检测"
CN117573174A,CN202311440132.1,"本发明提供一种基于芯片的更新Bootloader的方法及装置，若第一标志位中的校验值有效，且待更新Bootloader程序通过完整性校验，通过待更新Bootloader程序擦除第一标志位中的校验值；擦除存储空间中的Bootloader程序；将Bootloader数据存储至存储空间；若Bootloader数据通过完整性校验，在第一标志位中写入校验值并执行复位操作。芯片上电后校验第一标志位中的校验值，以避免芯片跑飞的情况出现；通过Bootloader程序存储待更新Bootloader程序节省存储空间，通过待更新Bootloader程序进行Bootloader数据的更新缩短更新时间，提高更新效率。
",G06F8/654,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/654,"提供, 方法, 装置",通用
CN117556161A,CN202311367786.6,"本发明提供了一种主题切换方法及装置，该方法包括：获取目标主题色值；将目标主题色值替换掉CSS文件中的原主题色值，CSS文件基于主题来源文件生成，主题来源文件中的CSS属性或SCSS变量由公共样式文件中的SCSS主题样式变量进行赋值，主题来源文件至少包含二次封装组件库；从公共样式文件中获取与目标主题色值对应的目标背景图片的URL；根据目标背景图片的URL，将前端项目的原背景图片切换为目标背景图片。本方案中，通过公共样式文件统一管理和维护主题来源文件中的样式变量，降低维护难度；通过给SCSS样式变量动态赋值就能够实现主题色系和背景图片的切换，不需要引入多套SCSS样式变量，降低文件打包体积。
",G06F16/958,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/958,"提供, 实现, 包括, 装置, 方法, 生成",通用
CN117492819A,CN202311490554.X,"本申请公开了一种应用服务封装方法及装置，涉及汽车软件技术领域。应用服务封装方法包括：获取待开发车辆应用的通信矩阵；根据通信矩阵，生成待开发车辆应用对应的通过UML描述的软件集成框架；基于软件集成框架，检测SOME/IP服务接口的实现类是否能够正常运行；在实现类能够正常运行的情况下，基于软件集成框架，生成待开发车辆应用对应的代码，得到目标车辆应用。通过本申请公开的方案，能够提高车辆应用的开发效率。
",G06F8/71,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/71,"实现, 包括, 装置, 生成, 方法, 检测","车辆, 检测, 汽车, 通信"
CN117453238A,CN202311433146.0,"本申请提供了一种数据刷写方法及装置，通过获取初始刷写文件，初始刷写文件包括刷写数据和配置数据，刷写数据用于对车辆的电子控制单元ECU进行刷写，配置数据用于描述刷写数据；将刷写数据转换为第一预设格式，得到第一数据；将配置数据转换为第二预设格式，得到第二数据；通过预设软件，分别对第一数据和第二数据进行解析，提取得到目标刷写文件；基于目标刷写文件，通过预设软件对ECU进行刷写工作。本申请实施例能够提高数据刷写的效率。
",G06F8/61,北京经纬恒润科技股份有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据转换; IPC分类号匹配: G06F8/61,"提供, 包括, 装置, 方法, 控制, 配置","车辆, 控制"
CN117440024A,CN202311303080.3,"本申请公开了一种数字孪生系统及其请求方法，该系统包括：网络服务器，用于在接收到客户端发送的目标请求的情况下，为客户端分配对应的三维程序，并向与目标请求匹配的数据处理站转发目标请求，目标请求包括画面交互请求、回放请求、仿真请求中的任意一项；数据处理站，用于接收网络服务器转发的目标请求，并从内部数据存储模块中获取目标请求对应的请求数据；三维程序，用于对请求数据进行三维可视化处理，得到场景画面的视频流，并将视频流实时推送至三维程序对应的客户端，以使客户端显示与目标请求匹配的场景画面。根据本申请实施例，能够提升数字孪生系统的适用性，更大程度上满足用户需求。
",H04L67/131,北京经纬恒润科技股份有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据处理; IPC分类号匹配: H04L67/131,"包括, 仿真, 方法, 处理, 系统",网络
CN117271830A,CN202311214818.9,"本申请公开了一种数字孪生数据的录制与回放方法及装置，涉及通信技术领域。其方法包括：在数字孪生系统完成录制的情况下，响应于用户输入的回放指令，生成起始批次号和结束批次号，回放指令包括回放开始时间和回放结束时间，起始批次号为回放开始时间对应的批次号，结束批次号为回放结束时间对应的批次号；根据起始批次号和结束批次号，在数据库中确定目标数字孪生数据，数据库包括数字孪生系统录制的数字孪生数据及批次号，相邻批次号之间相差预设的固定时间周期，目标数字孪生数据为从起始批次号至结束批次号对应的数字孪生系统录制的数据；创建与目标数字孪生数据关联的播放游标；基于播放游标，播放目标数字孪生数据。
",G06F16/71,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/71,"包括, 装置, 生成, 方法, 系统",通信
CN117234533A,CN202311251875.4,"本申请实施例公开了一种虹膜算法模型的部署方法及装置，其中，方法包括：将IRIS算法模型中的所有模型均转换为第一格式的模型，所述第一格式的模型为可编辑处理的模型；将第一格式的模型中经过第一平台的工具链处理后误差超过设定值的算子或工具链不支持的第一类型的算子进行算子替换，替换的算子与被替换算子能够实现相同功能；将第一格式的模型中第一平台的工具链不支持的算子删除；将处理后的第一格式的模型进行量化处理，并部署到所述第一平台。上述方案可以基于第一平台工具链的特点对IRIS算法模型进行修改编辑，从而使得完成训练的IRIS算法模型能够部署在第一平台上，并可以使用第一平台搭载的相关处理器实现模型推理过程的硬件加速。
",G06F8/60,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/60,"平台, 实现, 包括, 模型, 装置, 算法, 方法, 处理, 工具",通用
CN117194718A,CN202311088005.X,"本申请公开了一种开放式诊断数据交换文件的处理方法、装置及相关设备，涉及汽车技术领域。其方法包括：获取车辆的开放式诊断数据交换ODX文件集，以及获取目标ODX文件，ODX文件集包括至少一个ODX文件，各ODX文件用于描述对应电子控制单元ECU的诊断规范，各ODX文件包括对应ECU的诊断服务信息和诊断数据结构信息；根据ODX文件集，获取各ODX文件的诊断数据，及获取各ECU的诊断服务信息、诊断数据结构信息及诊断服务继承关系信息；将各ECU的诊断服务信息和诊断数据结构信息，按照各诊断服务继承关系信息整合至目标ODX文件中，得到整合后的目标ODX文件，整合后的目标ODX文件用于诊断测试。
",G06F16/81,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/81,"包括, 测试, 装置, 方法, 处理, 设备, 控制","测试, 汽车, 诊断, 控制, 车辆"
CN117093249A,CN202310927097.X,"本申请公开了一种看门狗喂狗方法、装置、设备及介质，涉及计算机技术领域。看门狗喂狗方法包括：获取看门狗定时器的定时时长；根据定时时长，确定看门狗喂狗周期；在执行目标程序的过程中，当看门狗喂狗周期到期时，调用中断函数对看门狗执行喂狗操作。根据本申请实施例，能够提高代码维护效率。
",G06F8/70,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/70,"包括, 装置, 方法, 设备, 计算",通用
CN117082475A,CN202311185424.5,"本申请公开了一种数据传输方法、装置、设备、存储介质及程序产品。该方法包括：向第二控制器发送第一数据包，其中，所述第一数据包为按照预设顺序排列的多个数据包中的任意一个，所述多个数据包用于对车辆上的目标应用程序进行升级；在接收到所述第一数据包的确认报文的情况下，向所述第二控制器发送第二数据包，其中，所述第二数据包为所述多个数据包中所述第一数据包的下一数据包，所述第一数据包的确认报文用于表征所述第一数据包被所述第二控制器所接收。根据本申请实施例，能够提高软件刷写效率。
",H04W4/44,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"包括, 装置, 方法, 设备, 控制","车辆, 数据传输, 控制"
CN117032687A,CN202310650855.8,"本申请公开了一种可视化路线关系图确定方法和装置，该方法包括：获取待绘制路线关系图中各节点的第一参数信息，以及连接两个节点之间的边的第二参数信息；确定画布的宽度，基于所述待绘制路线关系图中各路径的长度信息，计算各所述节点在画布中的第一位置信息；基于各所述边的目标状态，以及预先设置的边的状态与边的类型之间的对应关系，确定各所述边的目标类型；基于各所述节点在画布中的第一位置信息，以及各所述边的目标类型，绘制所述待绘制路线关系图，得到目标路线关系图。通过本申请的可视化路线关系图确定方法，可以实现将复杂路径关系清晰显示的效果。
",G06F8/38,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/38,"实现, 包括, 装置, 方法, 设置, 计算",通用
CN117032733A,CN202310959927.7,"本申请公开了一种电子控制单元刷写方法及装置，通过获取约定格式刷写文件，根据各所述约定格式刷写数据的摘要信息与电子设备中的各电子控制单元进行匹配，得到匹配结果。在所述匹配结果指示所述目标约定格式刷写数据所关联的电子控制单元为从控制单元的情况下，通过所述主控制单元将所述目标约定格式刷写数据转发至目标从控制单元，以使目标从控制单元基于所述目标约定格式刷写数据进行刷写。如此，能够在无需专业烧写设备或外部诊断设备的情况下，对具有协作模式的从属控制单元进行直接刷写，降低电子控制单元的刷写方法的局限性。
",G06F8/61,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/61,"装置, 具有, 方法, 设备, 控制","诊断, 控制"
CN117033840A,CN202310943043.2,"本申请公开了一种报文信号Layout渲染方法、装置、设备、存储介质及程序，在网页端初始化包含多个单元格的Layout画布，根据画布中各单元格的索引位置为各单元格绑定唯一标识，并为各单元格添加第一监听事件，其中第一监听事件包括拖动进入事件，如此可以实现对针对单元格中拖动操作的监听和处理，基于画布对报文中的信号进行渲染，以使信号的布局结构在画布中显示，并为各信号元素添加第二监听事件，第二监听事件包括拖动开始事件和拖动结束事件，如此可以实现对信号元素拖动的监听和处理。根据本实施例，在通过上述方式对报文信号Layout进行渲染后，可以基于第一监听事件和第二监听事件在网页端渲染的Layout中实现对报文信号的实时拖拽效果。
",G06F16/958,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/958,"实现, 包括, 装置, 方法, 处理, 设备",通用
CN117009299A,CN202310901149.6,"本申请提供了一种字节对齐方法、装置及设备，方法包括：运行预设的启动文件，所述启动文件包括文件路径；根据所述文件路径，确定待处理文件；获取所述待处理文件对应的多个数据块，每个所述数据块包括多个数据行；对每个数据块中的每个数据行按照预设字节长度进行填充，获得对齐文件。采用上述步骤可以提高对文件进行字节对齐时的处理效率。
",G06F16/16,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/16,"提供, 包括, 装置, 方法, 处理, 设备",通用
CN117008944A,CN202310907445.7,"本申请公开了一种引导加载程序Bootloader更新方法、装置、介质及控制器，Bootloader包括第一程序和第二程序，包括：跳转至第一启动模块运行第一程序，跳转至第二启动模块运行第二程序；在检测到第二程序待更新的情况下，下载目标应用程序，并跳转至目标应用程序，更新第二程序；在控制器复位后，再次跳转至第一启动模块运行第一程序，并检测第二程序的更新状态；在更新状态用于表征第二程序更新完成的情况下，通过跳转至第二启动模块运行更新后的第二程序；在更新状态用于表征第二程序未更新完成的情况下，通过跳转至目标应用程序，继续更新第二启动模块中的第二程序。根据本申请实施例，能够解决控制器变砖的问题，保证BootLoader完成更新。
",G06F8/658,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/658,"包括, 装置, 方法, 控制, 检测","检测, 控制"
CN116931987A,CN202310848326.9,"本申请公开了一种车载控制器更新方法及装置，涉及汽车技术领域。车载控制器更新方法包括：获取多个待更新软件包信息、软件包与车载控制器的对应关系以及多个软件包；根据多个待更新软件包信息和对应关系，向多个车载控制器更新控制单元分别发送多个软件包中相应的软件包，以用于多个车载控制器更新控制单元更新相应车载控制器中的软件。根据本申请实施例，能够对车辆中所有需要软件更新的车载控制器中的软件进行更新，实现了对整车的车辆控制器的更新。
",G06F8/65,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"实现, 包括, 装置, 方法, 控制","车辆, 汽车, 控制"
CN116932594A,CN202310890408.X,"本申请公开了一种数据获取方法，属于数据数据处理技术领域。获取携带数据标识的查询请求，从缓存区内查找与该数据标识对应的目标数据。若查找到该目标数据，则响应于该查询请求，从该缓存区内返回该目标数据。若未查找到该目标数据，则优先将存储于后端设备处的包含目标数据的数据批次，复制至该缓存区内。再响应于该查询请求，从该缓存区内返回该目标数据。可见，能够通过前端设备获取目标数据。在获取目标数据之前，将部分目标数据存储至前端设备的缓存区内。获取特定数据标识的目标数据时，可优先从缓存区内查找该目标数据，并基于查找的结果决定是否从后端设备获取该目标数据，无需频繁与后端设备交互，提高效率。
",G06F16/2455,北京经纬恒润科技股份有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据处理; IPC分类号匹配: G06F16/2455,"方法, 处理, 设备",通用
CN116853132A,CN202310955643.0,"本申请公开一种车辆装置，车身设置有第一安装区、第二安装区以及第三安装区，第一安装区与车头相邻设置，第三安装区与车尾相邻设置；感知模块包括分别设置在车头和车尾的至少两组感知单元；数据处理模块包括设置于第一安装区的第一处理单元、设置于第二安装区的控制单元以及设置于第三安装区的第二处理单元；本申请数据处理模块的各部件分布集成于中间的三个安装区内，三个安装区可根据所处位置设计相应的功能实现模块化设计，结构分布清晰明了，并且将第一处理单元和第二处理单元分别对应设置于与感知单元相邻的一端上，能够减短第一处理单元和第二处理单元与感知单元线连接所需的线束。
",B60R11/00,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"实现, 包括, 装置, 设置, 处理, 控制","车辆, 控制"
CN116755737A,CN202310658044.2,"本申请公开了一种汽车软件OTA升级方法、装置、设备、存储介质及程序，能够预先在汽车的存储器中划分出第一分区和第二分区，在OTA应用进行升级时，OTA应用从第一分区和第二分区中确定出有效分区，并将新的应用程序数据从上位机下载到有效分区外的另一个分区，也即目标分区，在新的应用程序数据成功下载到目标分区后，将之前的有效分区设置为无效分区，并将目标分区设置为有效分区。如此完成对OTA应用的应用数据更新。根据本实施例，基于两个分区进行OTA升级，即使升级失败OTA应用仍可以基于另一个分区内的应用数据进行运行，能够较为简便的实现在线的嵌入式软件升级，提高了软件系统的可靠性。
",G06F8/65,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"实现, 装置, 方法, 设置, 设备, 系统",汽车
CN116643771A,CN202310564732.2,"本发明提供一种控制器的升级方法及装置，该方法包括：获取待检查车辆识别号、控制器编号和控制器版本号；对于每个控制器编号，判断控制器编号对应的控制器版本号是否为预期版本号；若否，将当前控制器版本号及对应的待检查车辆识别号加入待升级名单；根据待升级名单确定目标控制器；下载目标控制器对应的升级包；安装符合验证要求的升级包；若安装成功，发送升级成功信息，根据待升级名单确定新的目标控制器。根据车辆识别号、控制器编号和控制器版本号确定待升级名单，根据待升级名单自动对目标控制器进行升级，解放了人工操作，降低升级成本；根据待升级名单实现对多个控制器同时进行升级，提高升级效率。
",G06F8/65,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"提供, 实现, 包括, 装置, 方法, 控制","车辆, 验证, 控制"
CN116400931A,CN202310212849.4,"本申请提供了一种S19文件流式刷写方法和装置，涉及文件刷写技术领域，方法包括：获取待刷写的S19文件，将获取的文件作为待刷写文件；建立记录待刷写文件包含的连续行的地址和数据长度的地址信息变量；根据地址信息变量将待刷写文件包含的连续数据行中的记录数据刷写到ECU。本申请针对待刷写文件包含的连续行，建立了记录连续行的地址和数据长度的地址信息变量，从而在刷写阶段仅根据地址信息变量从对应连续行中读取记录数据，并将读取的记录数据刷写至ECU即可，无需额外生成Bin文件，节省了空间资源，降低了文件读写次数，提高了刷写效率。
",G06F8/61,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/61,"提供, 包括, 装置, 生成, 方法",通用
CN116186345A,CN202211708510.5,"本申请公开了一种置信度等级评估方法和装置。该方法包括：构建N位数的二进制字符串的重复排列，根据重复排列构建十进制值与置信度等级的第一映射关系，第一映射关系中的十进制值与重复排列中的2<Sup>N</Sup>个二进制字符串一一对应，N为大于或等于2的整数，获取待评估数据，转换待评估数据为十进制比对值，待评估数据为N位数的二进制数据，根据第一映射关系确定与十进制比对值相同的十进制值，设置与该十进制值对应的置信度等级为待评估数据的置信度等级。根据本申请实施例，置信度等级生成过程简单，置信度等级输出的实时性好。
",G06F16/903,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/903,"包括, 装置, 生成, 方法, 设置",通用
CN116089382A,CN202211686528.X,"本申请公开了一种文件存储方法、装置、设备、介质及产品，涉及自动驾驶技术领域。文件存储方法包括：获取目标MDF4文件的第一变量对应的至少两条数据记录，其中，第一变量为目标MDF4文件对应的至少两个变量中的任意一个变量；将至少两条数据记录按照数据采集时间进行排序；将排序后的数据记录存储于数据块中；将数据块写入目标MDF4文件中；将数据块的地址索引信息存储于第一变量对应的数据链表块中；当数据采集结束时，将数据链表块写入目标MDF4文件中。根据本申请实施例，能够提高MDF4文件解析效率。
",G06F16/172,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/172,"方法, 包括, 装置, 设备",驾驶
CN116088914A,CN202211624895.7,"本申请公开了一种多核异构片上系统升级方法及装置，涉及汽车电子技术领域。方法包括：在片上系统处于正常工作状态的情况下，微控制单元选择非易失闪存包括的内容相同的第一存储插槽和第二存储插槽分别作为工作存储插槽和备份存储插槽，微处理器选择嵌入式多媒体卡包括的两个内容不同的存储插槽中当前运行的存储插槽作为工作存储插槽，将另一个存储插槽作为备份存储插槽；在接收到升级指令的情况下，将微控制单元和微处理器各自的升级镜像分别写入各自的备份存储插槽；重启片上系统；在片上系统重启成功的情况下，将第一存储插槽与第二存储插槽进行同步，片上系统升级完成。根据本申请实施例，微控制单元和微处理器能够采用不同升级策略进行升级。
",G06F8/654,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/654,"包括, 装置, 方法, 处理, 控制, 系统","汽车, 控制"
CN116069355A,CN202211608124.9,"本申请实施例提供一种电子控制单元升级系统，该系统包括：云端设备、主控电子控制单元ECU和多个ECU，主控ECU包括OTA程序，云端设备与OTA程序通信连接，OTA程序分别与多个ECU通信连接，云端设备，用于向OTA程序发送升级包的升级信息，升级信息包括ECU标识和升级方式，OTA程序，用于在升级方式为ECU独立升级方式的情况下，从多个ECU中匹配到与ECU标识对应的目标ECU，并向目标ECU发送升级包的升级信息，目标ECU，用于根据升级包的升级信息，下载并安装升级包。本申请实施例，提高了ECU的升级效率。
",G06F8/65,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"提供, 包括, 设备, 控制, 系统","通信, 控制"
CN116047460A,CN202310118309.X,"本申请公开了一种雷达测角方法，包括：根据雷达的多输入多输出MIMO信号，得到初始协方差矩阵；对所述初始协方差矩阵进行滑动滤波，获得目标协方差矩阵；基于所述目标协方差矩阵进行特征值分解，得到噪声子空间；利用导向矢量在所述噪声子空间匹配，得到信源的目标角度。通过上述步骤，可以能够在保障数据处理实时性的同时，提高雷达测角的精度。
",G01S7/41,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"方法, 包括, 处理",雷达
CN116028406A,CN202211638481.X,"本申请公开了一种元数据的交互方法、装置、设备及存储介质，涉及软件通信技术领域。其应用于车载终端，方法包括：在数据接收对象接收到目标协议数据单元PDU的至少一个元数据项的情况下，在预先配置的缓存对象中，查找与目标PDU存在对应关系的目标元数据配置地址，缓存对象包括N个PDU中各PDU与元数据配置地址的对应关系，不同PDU对应不同的元数据配置地址，N为正整数；控制缓存对象将目标PDU的元数据项存入缓存对象中目标元数据配置地址的缓存区；控制目标数据发送对象从目标元数据配置地址的缓存区中读取元数据项，目标接收对象与目标PDU的元数据项对应。根据本申请实施例，能够使得元数据的交互变得简单。
",G06F13/38,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F13/38,"包括, 装置, 方法, 设备, 控制, 配置","通信, 控制"
CN116028079A,CN202211620016.3,"本申请实施例提供了一种车辆应用程序的升级方法、装置，软件远程升级服务引擎包括状态机管理器和每个应用程序对应的状态机，状态机管理器用于接收服务发送的消息，并分发至各应用程序对应的状态机，状态机用于根据消息对应的驱动行为对各应用程序进行驱动，该方法包括在接收到状态机管理器发送的目标应用的升级消息的情况下，检测目标应用的当前状态，接收在当前状态下的事件信息，事件信息包括升级目标应用的过程中产生的事件的信息，在目标应用的当前状态下处理事件时，将目标应用从当前状态迁移至目标状态，以用于完成对目标应用的升级。根据本申请实施例，可以将应用作为升级客体，更精细地控制升级对象，更精准、高效地实现应用的SOTA流程。
",G06F8/65,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"提供, 实现, 包括, 装置, 方法, 处理, 控制, 检测","车辆, 检测, 控制"
CN115982254A,CN202211513933.1,"本申请公开了一种数据库文件的转换方法、装置、系统、设备及存储介质，能够由服务器获取浏览器发送的第一格式的第一数据库文件，然后解析第一数据库文件获得其中的总线数据，由服务器将总线数据生成第二格式的第二数据库文件，从而实现对数据库文件的格式转换。根据本实施例，可以实现对数据库文件的线上格式转换，可以随时随地在任意联网终端上通过自带的浏览器将需要转换的第一数据库文件上传至服务器进行转换，不需要再安装和运行任何专业软件，相比传统利用专业软件的转换方法，大大降低了转换成本。
",G06F16/25,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/25,"实现, 装置, 方法, 生成, 设备, 系统",通用
CN115934125A,CN202211526467.0,"本申请公开了一种车身控制方法和车身控制器，在通过车身控制器中的无线通信模块接收客户端发送的程序更新指令，将重编程标志位设置为有效，将第二分区中存储的应用数据拷贝到第三分区进行备份，在备份完成后，通过无线通信模块接收客户端发送的升级数据，并将升级数据存储至第二分区，从而实现对车身控制器的程序更新。根据本申请实施例，可以实现对车身控制器的远程程序更新，相比于技术工程师携带升级工具进行现场更新，消耗的人力和时间更少，而且，本实施例中在程序更新时，先利用第三分区对第二分区内之前存储的应用数据进行备份，如此，即使程序更新失败，仍然可以获取到原来的应用执行代码，从而保证车身控制器可以正常运行。
",G06F8/65,天津经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"实现, 方法, 设置, 控制, 工具","通信, 控制"
CN115930976A,CN202211342405.4,"本说明书公开一种数据融合方法及感知融合系统，该方法包括：接收激光雷达、毫米波雷达和相机从环境中获取的当前帧目标信息，将毫米波雷达和相机的目标信息数据转换到激光雷达的坐标系下，将所述当前帧目标信息通过链表进行存储；将所述当前帧目标信息与前一帧目标信息进行关联匹配，所述关联匹配包括同一传感器的相邻两帧数据的匹配以及不同传感器之间相邻两帧数据的匹配；在相邻两帧数据的匹配结果为同一目标的情况下，按照卡尔曼滤波算法将该目标的当前帧目标信息与前一帧目标信息进行数据融合；根据融合得到的数据更新所述链表中各目标的目标信息，将链表中的目标信息转化成预设的格式，以预设的格式将目标信息发送给服务器。
",G01C21/28,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据转换,"系统, 方法, 包括, 算法","雷达, 传感器, 激光"
CN115782894A,CN202211574209.X,"本申请提供电路板及驾驶员监控装置，电路板包括第一部分和第二部分，上设置有电源接口、数据处理单元和控制单元，第二部分上设置有拍摄单元，拍摄单元与数据处理单元电连接；其中，电源接口设置于第一部分上沿第二方向的端部，第二部分与第一部分上第一方向的一侧相连接，控制单元的至少部分设置于第一部分上沿第二方向的中心线上，数据处理单元设置于控制单元的远离电源接口的一侧，并靠近控制单元，第二方向与第一方向相垂直。通过将拍摄单元与数据处理单元集成于同一电路板上，提高驾驶员监控系统的监控精度。
",B60W40/08,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"提供, 包括, 装置, 设置, 处理, 控制, 系统, 电路","驾驶, 控制"
CN115730027A,CN202211370890.6,"本申请提供了地图实时更新方法和装置，涉及智能驾驶领域，方法包括：接收目标车辆上的控制器在判断第二地图数据与第一地图数据不一致的情况下上传的车辆定位数据和第二地图数据；将云端地图数据库中与车辆定位数据匹配的云端地图数据作为第三地图数据；在第二地图数据和第三地图数据一致的情况下，基于第二地图数据或第三地图数据对第一地图数据进行更新；在第二地图数据和第三地图数据不一致的情况下，基于多个车辆上的控制器上传的与车辆定位数据匹配的第二地图数据，确定待更新地图数据，并基于待更新地图数据对第三地图数据和第一地图数据进行更新。本申请节省了地图更新过程中的人力、物力和成本，且保证了地图更新的实时性。
",G06F16/29,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/29,"提供, 包括, 装置, 方法, 控制","车辆, 驾驶, 控制"
CN115675348A,CN202211481477.7,"本申请公开了一种车辆落水漂浮控制装置。该装置包括：热气球模块，位于目标车辆顶部，包括热气球和绳索，热气球通过绳索与目标车辆连接，数据处理模块，与控制模块通信连接，用于判断目标车辆的车身入水深度是否达到入水深度阈值，得到目标判断结果，并将目标判断结果发送至控制模块，控制模块，与热气球模块通信连接，用于在目标判断结果表征车身入水深度达到入水深度阈值的情况下，控制热气球模块启动，使热气球上升。采用本申请提供的车辆落水漂浮控制装置，可以减小目标车辆的车身入水深度，更大程度上保障车内人员的生命安全。
",B60R21/00,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"提供, 包括, 装置, 处理, 控制","车辆, 通信, 控制"
CN115658087A,CN202211137846.0,"本申请实施例提供了一种车载应用程序的安装、运行方法、设备、座舱管理设备，该车载应用程序的安装方法应用于安装管理设备，包括获取车载应用程序的摘要文件，解析摘要文件，得到车载应用程序的配置信息，配置信息包括车载应用程序中多个分布式组件的部署位置，基于每个分布式组件的部署位置安装分布式组件。根据本申请实施例，可以将车载应用程序的分布式组件部署到整车各设备节点上，提高了车载应用程序对整车情况的感知，且分布式部署方式有利于高效利用整车中各个系统的资源。
",G06F8/61,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/61,"提供, 包括, 方法, 设备, 系统, 配置",通用
CN115618169A,CN202211033218.8,"本申请提供了一种轨迹点拟合方法、装置、电子设备和可读存储介质，基于轨迹点集合中的任意相邻轨迹点间的直线距离作为路程增量，基于该轨迹点集合以及路程增量生成多组三次样条曲线，该三次样条曲线的端点满足特定的终端倒数连续条件，而非端点的其他轨迹点的三次样条曲线在一阶导数连续和二阶倒数连续，在保证最终曲线平滑的前提下，数据处理量较小，提高车辆跟踪响应速度。
",G06F17/12,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"提供, 装置, 生成, 方法, 处理, 设备",车辆
CN115374069A,CN202210904510.6,"本申请实施例提供了一种数据保存和读取的方法、装置，包括获取值和键的对应关系信息，值表示存放变量数据的第一数据块的名称，键表示变量的名称；在目标文件中写入值和键的对应关系信息；将变量的数据存储至数据缓存区的第二数据块；当检测到数据缓存区的变量中目标变量的数据大小满足条件时，将变量的数据写入目标文件，其中，条件包括数据缓存区的大小与键和值的对应关系信息中变量个数的比值；基于第三数据块的名称，以及存储于第三数据块的变量的名称的对应关系信息，增添写入至目标文件的值和键的对应关系信息；其中，第三数据块为第二数据块中除第一数据块之外的数据块。根据本申请实施例，通过数据分块化存储提高了保存和读取数据的效率。
",G06F16/172,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/172,"提供, 包括, 装置, 方法, 检测",检测
CN114996332A,CN202210667668.6,"本发明公开了一种数据回放方法、装置及电子设备，方法包括：基于显示器的显示参数确定待回放数据的采样点数；基于所述待回放数据的数据点个数和所述采样点数确定所述待回放数据的采样周期；在每个所述采样周期内，从对应的待回放数据中提取出该采样周期的采样点数据，采样周期的总数量与所述采样点数的数量相同；基于所有的采样周期对应的采样点数据得到所述待回放数据的波形图。上述方案根据显示器的显示参数进行待回放数据采样周期的计算和采样点的提取，能够快速回放仿真过程中的数据文件，能够在避免回放数据内存溢出的同时，显示出回放数据的完整的包络曲线，满足仿真数据的分析场景需求。
",G06F16/2458,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/2458,"包括, 装置, 仿真, 方法, 设备, 计算",通用
CN114859320A,CN202210509769.0,"本发明实施例提供了一种激光雷达点云数据处理方法及系统。其中，方法包括：获取激光雷达扫描到目标检测区域内待检标志物上的点云，通过设置ROI区域选取包含待检标志物的点云；对激光雷达的各条扫描线进行单根激光扫描线尺度上的点云特征提取和匹配，得到候选点云；对候选点云进行点云整体尺度上的点云特征提取和匹配，得到降噪和细化后的候选点云；对降噪和细化后的候选点云进行RANSAC操作，滤除被误检的点云，得到标志物点云。本发明可以提高标志物检测的时效性，适应于在车规级芯片上进行标志物检测的操作。
",G01S7/48,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"提供, 包括, 方法, 设置, 处理, 系统, 检测","雷达, 检测, 激光"
CN114780476A,CN202210394811.9,"本发明提供一种支持多主多从的SPI分时复用电路，其SPI片选CS复用模块用于在多个主节点与多个从节点中，选取将要通信的任一主节点与相应的从节点；数据通道使能模块依据选定的相应主节点和相应的从节点，产生相应的数据通道使能信号，同时，当相应的主节点通过片选信号占用总线后，总线将被锁定，其余主节点将无法建立数据通道，当相应的主节点通过取消占用所述总线、总线释放后，总线可由任一主节点锁定；数据通道切换模块依据数据通道使能信号执行相应的开启和关断的动作，建立相应主节点与相应从此节点之间的数据通道，以使相应的主节点与相应的从节点进行数据传输；实现了多主从节点的分时复用通信、节省了从节点的IO资源。
",G06F13/42,北京经纬恒润科技股份有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据传输; IPC分类号匹配: G06F13/42,"提供, 实现, 电路","通信, 数据传输"
CN114662954A,CN202210332451.X,"本发明提供了一种车辆性能评估系统，系统的数据采集装置采集车辆的原始行驶报文信号数据；系统的数据处理装置对原始行驶报文信号数据进行挖掘处理，获得数据处理结果，系统的性能评估装置包括实车路谱单元、竞品车型对标单元、理论性能计算单元以及整车效率分析单元中的至少一种；实车路谱单元根据数据处理结果构建实车路谱；竞品车型对标单元根据数据处理结果得到车辆与对标车型的对标分析报告；理论性能计算单元利用数据处理结果对整车理论模型进行校正，利用校正后的整车理论模型输出理论性能数据；整车效率分析单元根据数据处理结果得到整车效率计算分析报告。该系统能够快速准确地对车辆性能进行评估，提高车辆性能的评估效率。
",G06Q10/06,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"提供, 包括, 模型, 装置, 处理, 系统, 计算",车辆
CN114579161A,CN202210334681.X,"本申请公开一种应用程序更新方法及ECU。ECU包括通过总线连接的MPU和MCU，MPU向MCU发送与当前的备份区匹配的应用数据，MCU利用接收到的应用数据对备份区进行更新，将更新状态标识设置为第一标识，在完成更新后，MCU将当前的备份区设置为工作区，将另一分区设置为备份区，进行系统复位操作；每次复位启动后，若复位原因为上电复位或系统复位，更新状态标识为第一标识，且本次复位启动前的备份区完成更新，那么MCU启动当前的工作区，向MPU发送正常启动升级成功消息。本申请公开的方案，对应用程序升级的过程不需要占用ECU外部的CAN总线，也不需要静默车辆的CAN总线上的其他节点，不会影响车辆的其他功能。
",G06F8/65,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"系统, 方法, 包括, 设置",车辆
CN114546463A,CN202210298683.8,"本发明提供了一种软件更新方法及装置，通过预先对新版本软件与旧版本软件进行分段差分处理得到多个第一差分子文件，在软件更新过程中，每次仅需将第一存储器中的一个待处理第一差分子文件以及用于生成待处理第一差分子文件的旧版本软件数据读取到第二存储器中，即可根据已读取的待处理第一差分子文件以及旧版本软件数据生成新版本软件数据，在第一存储器中完成旧版本软件数据擦除以及新版本软件数据写入开始处理下一个第一差分子文件，直到完成软件版本更新。也就是说，本发明利用分段差分技术实现分段软件版本更新，相对于现有技术需要将整个差分文件与旧版本软件全部读取到第二存储器中，有效降低了对第二存储器空间的占用量。
",G06F8/658,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/658,"提供, 实现, 装置, 方法, 生成, 处理",通用
CN114489713A,CN202111591348.9,"本发明公开一种域控制器的程序处理方法，方法应用于域控制器，域控制器包括至少两个M核、至少一个A核、第一存储介质和第二存储介质，第一存储介质包括域控制器的引导程序，第二存储介质包括M核应用程序、域控制器的配置文件和A核相关程序，方法包括：A核检查第二存储介质中各程序是否能够读写；若第二存储介质中各程序均能够读写，则A核基于配置文件和下载的更新文件对第二存储介质中的待更新程序进行更新；若存在至少一个程序不能读写，则至少两个M核中第一M核执行域控制器的引导程序，通过域控制器的引导程序格式化被损坏的程序，并基于配置文件和下载的更新文件对待更新程序进行更新。
",G06F8/65,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"包括, 方法, 处理, 控制, 配置",控制
CN114461552A,CN202210091268.5,"本发明公开了一种数据存储方法及装置。该方法包括：接收数据写入请求，其中，数据写入请求包括待写入数据的起始写入地址、数据总长度、源地址和目标校验值，根据起始写入地址、数据总长度和源地址计算得到参考校验值，当参考校验值与目标校验值相同时，检测直接存储器访问DMA模块是否被占用，如果未被占用，通过DMA模块根据起始写入地址、数据总长度和页的预设数据长度将待写入数据写入带电可擦可编程只读存储器EEPROM，其中，页为进行读取数据或者写入数据的最基本单位。本发明中，在DMA模块未被占用时，通过DMA模块将待写入数据写入EEPROM，此时未占用MCU，降低了MCU的占用率。
",G06F13/28,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F13/28,"包括, 装置, 方法, 检测, 计算",检测
CN114461860A,CN202210065142.0,"本发明实施例公开一种SysML图的实现方法及装置，该方法包括：将每一SysML图中的信息划分为图形元素信息、逻辑关系信息和元素属性信息，其中，图形元素信息包括图外框；据图形元素信息、逻辑关系信息、元素属性信息，生成用于存储SysML图信息的树形图，其中，树形图的顶节点与图外框相对应，顶节点关联第一节点和第二节点，第一节点用于关联图形元素节点，第二节点用于关联逻辑关系节点，图形元素节点的子节点是根据图形元素节点对应的逻辑关系节点确定的，树形图中的每个节点存储与该节点相应的元素属性信息。应用本公开可以将MBSE和基于文档的方法进行有效的融合。
",G06F16/901,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/901,"实现, 包括, 装置, 生成, 方法",通用
CN114327557A,CN202111656269.1,"本发明公开了一种车辆OTA升级时间评估方法及装置，确定当前电子控制单元中单位数据量升级标准时间；在满足预设升级能耗要求的情况下，确定当前电子控制单元中单位数据量升级实际时间；基于单位数据量升级实际时间对单位数据量升级标准时间进行自学习更新，得到单位数据量升级目标时间；基于单位数据量升级目标时间对当前电子控制单元进行升级时间评估。上述过程，在每次升级时间评估过程中，会基于单位数据量升级实际时间对单位数据量升级标准时间进行自学习更新，得到单位数据量升级目标时间，基于单位数据量升级目标时间评估当前电子控制单元进行升级时间，不再采用固定的单位数据量升级标准时间进行计算，提高了升级时间评估的准确性。
",G06F8/65,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"方法, 计算, 装置, 控制","车辆, 控制"
CN114327560A,CN202111660357.9,"本申请公开了一种车辆OTA升级的能耗估算方法、装置和电子设备，该方法和装置具体为当车辆接收到OTA升级包后，对OTA升级包进行解析，从中确定出待升级的多个ECU的个数以及每个ECU所对应的升级包的数据量；根据每个升级包的数据量、单位数据量升级时间计算每个ECU进行升级所需预估时间；根据每个所述ECU的升级方式、所需功率和所述升级所需预估时间计算升级总功耗。通过上述方案可以得到车辆进行OTA升级所需的预估功耗，在此基础上可以根据其他约束条件对判断当前低压蓄电池的SOC是否满足升级需要，并根据判断结果采取必要措施，从而能够避免发生OTA升级失败。
",G06F8/65,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"方法, 计算, 装置, 设备",车辆
CN114327544A,CN202111560099.7,"本发明公开一种ECU版本管理方法，应用于升级路径管理库的方法包括：获取指定车型的新整车基线版本基础信息；将新ECU版本的ECU版本基础信息添加到目标ECU版本列表；根据目标ECU版本列表中旧ECU版本的ECU版本升级信息、ECU版本升级类型以及差分文件路径，更新旧ECU版本的ECU版本升级信息；获取新整车基线版本的整车基线版本号，并根据新整车基线版本的整车基线版本号和新整车基线版本的整车版本升级类型，更新ECU版本总表的各ECU版本列表中最新ECU版本的整车基线版本参考信息；根据各ECU版本列表中最新ECU版本、新整车基线版本的整车版本升级类型和新整车基线版本的整车基线版本号确定新整车基线版本，并将新整车基线版本添加到指定车型对应的整车基线版本列表中。
",G06F8/65,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"方法, 包括",通用
CN114296693A,CN202111531627.6,"本发明公开了一种多核异构处理器的模型开发方法及装置，该方法中，根据各类处理器核心的模型配置脚本与代码生成规则生成各类处理器核心的抽象模型；将各类处理器核心对应的驱动模块、核间通信模块和外界通信模块进行封装生成各类处理器核心对应的模块库；根据所读取的配置文件中的信息、各个处理器核心的抽象模型以及模型引用方法生成多核异构处理器的抽象模型；调用各个处理器核心的抽象模型以及各个处理器核心所属的类别对应的模块库得到各个处理器核心的抽象模型生成的代码，根据所生成的代码以及多核异构处理器的基础工程框架得到多核异构处理器对应的多核工程。采用本发明的方法，无需人工编码，减少开发人员工作量，提高了效率。
",G06F8/20,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/20,"模型, 装置, 方法, 生成, 处理, 配置",通信
CN114265035A,CN202111475271.9,"本申请公开一种激光雷达数据的处理方法及系统，方法包括：获取由激光雷达原始数据转换成的多帧二维深度图以及根据激光雷达的横向角度分辨率和纵向角度分辨率的比值确定的滤波器；根据滤波器和预设平面拟合算法对每帧二维深度图进行帧内压缩，获得多帧帧内压缩后的二维深度图；对于一个二维深度图分组，根据二维深度图分组中第二帧帧内压缩后的二维深度图至最后一帧帧内压缩后的二维深度图分别与第一帧帧内压缩后的二维深度图之间的差异，对二维深度图分组进行帧间压缩；在对所有二维深度图分组进行帧间压缩后，对帧间压缩后的所有二维深度图进行整体压缩，获得整体压缩后的二维深度图。本申请可以从多角度实现数据压缩，从而减小了数据量。
",G01S7/48,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据转换,"实现, 包括, 算法, 方法, 处理, 系统","雷达, 激光"
CN114253578A,CN202111564935.9,"本发明公开了一种刷写环境的设计方法及装置，包括：预先编写通用刷写流程，其中，所述通用刷写流程包括:预编程配置、编程配置和后编程配置，其中，所述预编程配置、所述编程配置和所述后编程配置中包含至少一个诊断服务；在接收到对目标控制器的刷写环境的设计请求的情况下，识别所述通用刷写流程中与所述目标控制器匹配的各个已选定诊断服务；针对所述各个已选定诊断服务，确定所述目标控制器的第一目标刷写流程。上述过程，针对不同的目标控制器，可以配置与其匹配的不同的第一目标刷写流程，刷写流程可变，实现了对不同控制器的刷写流程的可配置，避免了现有技术中由于刷写流程是固定的、不可配置的，导致的变更周期长的问题。
",G06F8/65,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"实现, 包括, 装置, 方法, 控制, 配置","诊断, 控制"
CN114170195A,CN202111508843.9,"本申请公开一种点云数据的处理方法、装置、系统和边缘计算单元，本申请借助辅助检测设备采集的路况数据，确定是否有移动目标进入路侧点云采集设备的感兴趣区域，在有的情况下，才触发对点云采集设备回传的点云数据的处理，或触发对云采集设备回传的点云数据及图像采集设备回传的图像的处理，通过该数据处理将点云数据或点云数据与图像传输至应用端，而在移动目标未处于点云采集设备感兴趣区域期间，则直接对应的采集设备采集的数据丢弃。从而，本申请滤除了在没有移动目标经过期间点云采集设备采集到的重复数据，可实现准确、高效地基于V2X路侧设备采集足够多的点云数据作为训练集，同时降低了路侧设备对存储容量和上传带宽的要求。
",G06T7/00,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"实现, 装置, 方法, 处理, 设备, 系统, 检测, 计算",检测
CN114138889A,CN202111476536.7,"本发明公开了一种内存数据库及仿真测试系统，内存数据库包括五个数据库表单，分别为总体信息列表单元、变量名称数据字典单元、变量数值列表偏移数据字典单元、模型变量信息列表单元、IO通道信息列表单元和模型变量数值列表单元，仿真测试所需的仿真监控子系统、车辆动力学模型及IO任务子系统通过与内存数据库中对应的数据库表单进行交互实现仿真测试。本发明中仿真监控子系统、IO任务子系统和车辆动力学模型能够直接访问内存数据库中的内存地址，提高了数据库的访问性能，通过在内存数据库中设置数据库表单，使得仿真监控子系统、车辆动力学模型及IO任务子系统可通过各个数据库表单完成数据交互，从而满足仿真测试的功能要求。
",G06F16/25,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/25,"实现, 包括, 模型, 测试, 仿真, 设置, 系统","车辆, 测试"
CN114115850A,CN202111414238.5,"本发明提供了一种代码生成方法及装置，通过预先利用嵌入式平台提供的优化专用算法函数封装为自定义的Simulink算法模块，优化了由Simulink自带的系统函数生成的算法模块，在使用自定义算法模块对应的自定义TLC文件生成代码后，可得到代码量较小的C代码文件，大大提高了C代码文件在嵌入式平台的运行速度，降低了对嵌入式平台CPU的占用时间，满足任务的实时性要求。
",G06F8/34,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/34,"提供, 平台, 装置, 算法, 方法, 生成, 系统",通用
CN114115946A,CN202111414467.7,"本申请公开一种动态调整刷写流程的升级方法和装置，该方法及装置预先通过整合多个不同部件(如整车ECU)分别对应的不同升级刷写流程得到涵盖不同部件的不同升级刷写流程且无重复步骤的通用升级刷写流程；在此基础上，针对待升级的目标部件，通过在其升级包中携带该通用升级刷写流程及目标部件特有的升级配置信息，使得可基于目标部件特有的升级配置信息对通用升级刷写流程进行配置，得到适配于目标部件的目标升级刷写流程并完成升级。从而，针对不同部件，如整车ECU，本申请减少了ECU开发复杂度，一定范围内降低了整车ECU开发周期；且，整个升级方案更具有灵活性，更具有平台效果。
",G06F8/65,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"平台, 装置, 具有, 方法, 配置",通用
CN114124842A,CN202111328471.1,"本发明提供一种数据传输方法、系统、电子设备及存储介质，数据传输系统包括网关和诊断仪，网关将诊断仪发送的目标ECU的第一下载数据包发送给目标ECU；判断是否在预设时间段内接收到其他ECU的下载数据包；若是，将其他ECU的下载数据包发送给其他ECU，返回判断是否在预设时间段内接收到其他ECU的下载数据包；若否，向诊断仪模拟发送目标肯定响应；诊断仪基于目标肯定响应，检测是否存在目标ECU的待发送下载数据包；若是，向网关发送一个目标ECU的待发送下载数据包；网关将待发送下载数据包发送给目标ECU，返回判断是否在预设时间段内接收到其他ECU的下载数据包。本发明可以降低数据传输的时间。
",H04L47/43,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"提供, 包括, 方法, 设备, 系统, 检测, 模拟","检测, 数据传输, 诊断"
CN114035779A,CN202111315314.7,"本申请公开一种报文数据的解析处理方法和装置，本申请在预定的图形化开发平台获取报文数据的数据文件，并识别该数据文件中的压缩及非压缩格式数据，其中报文数据携带在压缩格式数据中；之后，在图形化开发平台利用基于预定编程语言封装的解析处理文件对压缩格式数据进行解析，得到解析后的报文数据，最终在图形化开发平台对解析后的报文数据和非压缩格式数据进行筛选、分类得到目标数据，并对目标数据中的至少部分数据进行可视化显示处理。本申请结合了图形化开发平台的快速、丰富的图形化界面处理功能及预定编程语言强大的数据处理能力，可加快报文数据的数据解析过程及图形界面处理过程，提升了报文数据的数据解析与可视化显示效率。
",G06F8/30,北京经纬恒润科技股份有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据处理; IPC分类号匹配: G06F8/30,"方法, 处理, 装置, 平台",通用
CN113971673A,CN202111276868.0,"本发明公开了一种点云分割方法及装置，方法包括：获取激光点云数据，所述激光点云数据包括三维的点云数据；使用点云分割网络对所述激光点云数据进行粗粒度预测先验，得到粗粒度先验结果；将所述粗粒度先验结果降维转化为一维的降维点云数据；从所述降维点云数据中提取物体局部点云数据；对所述物体局部点云数据进行聚类分割，得到点云分割物体。上述实现方案将粗粒度先验结果降维转化为一维的降维点云数据，在保留三维数据特征的前提下能够大大减少后续的数据计算量，提升数据处理速度；后续利用降维后的点云数据快速提取出物体局部数据，并对物体局部数据进行聚类分割，提高激光点云物体的分割准确性。
",G06T7/10,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"实现, 包括, 装置, 方法, 处理, 计算","网络, 激光"
CN113888748A,CN202111133410.X,"本发明实施例公开一种点云数据处理方法及装置，该方法包括：将各点云簇的特征信息进行融合，得到特征矩阵；其中，所述各点云簇是对原始点云数据经过聚类处理后得到的，所述特征信息包括形状特征信息和位置特征信息；基于训练完成的注意力机制网络模型，对所述特征矩阵中的点云簇进行加权处理，使得相同物体对应的点云簇特征之间的相似度增加，不同物体对应的点云簇特征之间的相似度减少；对于经过加权处理后的点云簇进行聚类处理，确定属于同一物体的点云簇。通过采用上述技术方案，解决了点云聚类过程中过分割的问题，提高了聚类结果的有效性。
",G06V10/44,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"包括, 模型, 装置, 方法, 处理",网络
CN113885907A,CN202111086128.0,"本发明实施例公开一种固件升级系统及方法，该系统包括：云端服务器针对每一第一固件，对该第一固件进行加密，得到目标加密固件；生成目标加密固件对应的目标指纹；对目标指纹进行加密，得到加密后指纹；基于每一第一固件的目标加密固件、目标指纹、加密后指纹及指定私钥所对应公钥，确定升级包；待升级车载终端获得升级包，若第一固件不包含指定引导固件；利用运行的当前引导固件、升级包和分区激活状态表，确定待升级车载终端的待升级固件；利用预设验证过程，对升级包中相应的升级数据进行验证；并在验证通过后，对各升级数据中目标加密固件解密，得到相应的第一固件并升级，以实现提高固件升级的安全性。
",G06F8/65,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"实现, 包括, 生成, 方法, 系统",验证
CN113609059A,CN202110925667.2,"本发明提供了一种通信系统及通信方法，通过在Cortex‑A处理器创建虚拟can设备的网络节点，并在Cortex‑A处理器与Cortex‑M4处理器之间创建rpmsg通道，使应用层能利用Cortex‑A处理器的Linux网络框架，实现应用层与Cortex‑M4处理器之间的can报文通信，在不需要开发网络框架的基础上，保证can报文的实时性。
",G06F13/42,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F13/42,"提供, 实现, 方法, 处理, 设备, 系统","通信, 网络"
CN113590162A,CN202110976378.5,"本发明实施例提供的一种数据的升级方法及系统，数据升级方法应用于汽车的网关，升级方法首先接收诊断设备通过第一通信协议发送的待升级设备的升级指令；然后确定待升级设备所在汽车通信网络中的第二通信协议；若第二通信协议与第一通信协议不同，则确定诊断设备与待升级设备对应的目标路由；其中，目标路由用于将第一通信协议转换成第二通信协议；最后触发诊断设备对待升级设备进行升级的升级操作；其中诊断设备与多个待升级设备通过每一待升级设备对应的目标路由进行并行数据传输可以匹配新的整车网络架构，充分利用新的整车网络架构，并行对待升级设备进行数据升级，大大缩短了整车数据更新时间。
",G06F8/65,北京经纬恒润科技股份有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据传输; IPC分类号匹配: G06F8/65,"系统, 提供, 方法, 设备","汽车, 数据传输, 诊断, 通信, 网络"
CN113590160A,CN202110928975.0,"本发明提供了一种软件在线升级方法及多核ECU，通过将应用程序运行在第二处理核，将引导加载程序运行在第三处理核，实现将应用程序功能与升级功能分离，并通过第一处理核实现外部诊断仪与第三处理核之间的通信，通过外部诊断仪、第一处理核与第三处理核之间的交互实现软件在线升级，在软件升级过程中不涉及第二处理核，第二处理核仍然能正常运行应用程序的功能，不影响车辆的正常使用，提升了用户体验。
",G06F8/65,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"提供, 方法, 处理, 实现","车辆, 通信, 诊断"
CN113452520A,CN202110713829.6,"本发明公开了一种通讯数据处理方法、装置及通讯系统，在该方案中网关生成随机数并加密，使得控制器对获取到的加密报文进行解密，获得随机数；基于随机数和待传输报文的标识信息，生成排序数；基于排序数和待传输报文的信号数量，确定待传输报文的各个信号的排序序号；基于各个信号的排序序号对待传输报文进行处理，获得目标传输报文。由于控制器接收到的随机数一致，相同的标识信息的报文排序结果也是一致的，保证了不同控制器之间总线数据库的一致性，并且在车辆每次上电，网关每次都生成不同的随机数，每个不同的标识信息的报文的信号都会重新排列，即使被破解也无法将总线数据库应用在其他车辆，保证了车辆的信息安全性。
",H04L9/08,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"装置, 生成, 方法, 处理, 控制, 系统","车辆, 控制"
CN113434788A,CN202110768848.9,"本发明实施例公开了一种建图方法、装置、电子设备及车辆，获得当前时刻车辆的地理位置，以及当前时刻车辆上的目标超声波传感器探测到的距离值；根据车辆的地理位置和上述距离值，确定当前时刻目标区域内的各个栅格的初始空闲概率；根据当前时刻目标区域内的各个栅格的初始空闲概率，以及前一时刻目标区域内的各个栅格的空闲概率，确定当前时刻目标区域内的各个栅格的空闲概率；将当前时刻目标区域内的各个栅格的空闲概率二值化，得到目标地图。实现了基于超声波传感器的自动建图。
",G06F16/9537,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/9537,"方法, 装置, 实现, 设备","车辆, 传感器"
CN113282286A,CN202110600207.2,"本发明公开了一种算法开发方法及装置，其中该方法包括：在获取了FPGA模型后，添加至少一个AXI总线模块，根据FPGA模型和与FPGA模型相连接的AXI总线模块，生成第一FPGA代码模块；第一FPGA代码模块包括：FPGA模型对应的第二FPGA代码、AXI总线模块对应的AXI总线代码以及第二FPGA代码与AXI总线代码之间的连接关系，其中，在AXI总线代码生成的过程中，采用不同的标识符对不同的AXI总线代码中的AXI信号进行标记。由此，通过对AXI总线模块生成的AXI总线代码中的AXI信号进行区分，实现了通过多路AXI总线进行数据传输的目的。
",G06F8/35,北京经纬恒润科技股份有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据传输; IPC分类号匹配: G06F8/35,"实现, 包括, 模型, 装置, 算法, 方法, 生成",数据传输
CN113094546A,CN202110454025.9,"本发明提供了一种仿真动画模型加载方法、装置及仿真设备，由于预先依据用户外部构建的仿真动画文件构建了索引文件，仿真动画文件中包括仿真动画模型文件，每个仿真动画模型文件中包括有子模型文件。从而在根据索引计算公式以及观测目标的当前位置，确定当前位置所属的当前包围盒索引值之后，能够利用预先建立的索引文件，确定当前包围盒索引值的周边包围盒索引值；最后实现对仿真动画文件中与当前包围盒索引值和周边包围盒索引值所对应的子模型文件的加载。通过将用户外部构建的仿真动画文件转化成索引文件的方式，通过索引文件实现对仿真动画文件中的子模型文件的加载，即实现了对仿真动画模型的外部加载。
",G06F16/71,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/71,"提供, 实现, 包括, 模型, 装置, 仿真, 方法, 设备, 计算",通用
CN112667265A,CN202110075061.4,"本申请提出一种引导程序更新方法及装置，该方法包括：获取引导程序更新代码，所述引导程序更新代码包括用于对引导程序进行更新的程序代码，以及新版本的引导程序代码；运行引导程序存储区中的引导程序，将所述引导程序更新代码写入应用程序存储区；运行所述用于对引导程序进行更新的程序代码，对所述引导程序存储区中的引导程序进行更新。上述处理过程实现了对控制器引导程序的远程自更新，大大提高了对控制器引导程序更新的便捷性，提高了引导程序更新效率。
",G06F8/65,北京经纬恒润科技股份有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"实现, 包括, 装置, 方法, 处理, 控制",控制
CN112614194A,CN202110122268.2,"本发明实施例公开一种图像采集设备的数据处理方法、系统及装置，该方法包括：基于第一图像中各特征点的图像位置信息，确定畸变参数的初始值；针对每一第二图像，利用该第二图像中各特征点的图像位置信息以及去畸变操作，确定第二图像中各特征点在该第二图像对应的图像物理坐标系下的去畸变位置信息；基于棋盘格图卡中各空间点在预设空间坐标系下的空间位置信息，确定各空间点在各第二图像对应的图像物理坐标系下的投影位置信息；利用各第二图像中各特征点的去畸变位置信息、各空间点的投影位置信息及畸变参数的初始值，确定待标定设备的标定参数的标定值，以实现得到精度较高的设备内外参数，以提高后续设备使用过程中的计算结果的精度。
",G06T7/80,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"实现, 包括, 装置, 方法, 处理, 设备, 系统, 计算",通用
CN112511536A,CN202011367624.9,"本申请公开一种通信方法及通信系统。基于本申请公开的方案，数据接收方能够确定是否正确接收数据发送方发送的目标数据，数据发送方也能够确定数据接收方是否正确接收本方发送的目标数据，在确定数据接收方正确接收本方发送的目标数据后，数据发送方才会继续向数据接收方发送后续的目标数据，能够提高点对点通信过程中数据传输的可靠性。
",H04L29/06,北京经纬恒润科技股份有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"系统, 方法","通信, 数据传输"
CN112346773A,CN202011222965.7,"本发明提供了一种ECU升级方法及装置，方法包括将差分升级包发送至ECU；在ECU侧利用差分还原工具对差分升级包和ECU当前软件版本进行还原处理，得到升级数据，对ECU进行升级。在ECU侧进行还原处理，只需要将差分升级包发送到ECU即可，相比于将全量升级包发送到ECU，减少了数据传输时间，进而提高了ECU升级效率。进一步的，对于支持更新差异部分的ECU，在ECU侧仅烧写差异部分，减少了烧写时间，进一步提高了ECU升级效率。以及对于支持流式刷写的ECU，边还原边升级，更进一步提高了ECU升级效率。
",G06F8/658,经纬恒润(天津)研究开发有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据传输; IPC分类号匹配: G06F8/658,"提供, 包括, 装置, 方法, 处理, 工具",数据传输
CN112230966A,CN202011230038.X,"本发明提供了一种OTA升级方法及装置，应用于OTA服务器，通过根据每个已注册版本的升级方式，对每个已注册版本进行合理的升级路径规划，得到路径信息表，在接收到车机端发送的携带有当前版本的升级请求后，在已注册版本中确定最新版本，并根据路径信息表为车机端推送本次升级所对应的目标版本，避免当前版本与最新版本之间存在可跳过版本时，按照注册版本先后顺序依次升级导致的升级效率低下的问题，并避免当前版本与最新版本之间存在不可跳过版本时，由当前版本直接升级到目标版本导致的升级错误，提高了OTA升级效率。
",G06F8/65,天津经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"提供, 方法, 装置",通用
CN111968272A,CN202010866092.7,"本发明提供了一种遥控门禁系统数据传输方法及装置，方法包括检测遥控钥匙的按键状态；并在遥控钥匙的按键状态符合预设的触发条件时，依次发出包含唤醒数据的唤醒帧、包含同步数据的同步帧以及包含按键状态的数据帧，唤醒帧的持续时间大于车门控制器的接收芯片的自唤醒周期，避免出现接收芯片在休眠状态下接收唤醒帧而无法被唤醒的情况出现，进而保障了每次唤醒车门控制器的接收芯片的可靠性。且采用唤醒帧唤醒方式相比于现有唤醒方式逻辑简单。进一步的，通过在连续多次采集到按键状态为有效时，才发出数据帧，提高了发出的数据帧中按键状态的准确性。降低了电磁干扰等原因造成的按键误有效，发出错误的指令。
",G07C9/00,天津经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"提供, 包括, 装置, 方法, 控制, 系统, 检测","检测, 数据传输, 控制"
CN111901076A,CN202010915271.5,"本申请提供了一种车辆诊断数据转发方法、装置及网关，其中，网关中维护第一计时器和第二计时器，第一计时器用于控制网关主动向诊断设备发送等待响应报文的时机，第二计时器用于监控被诊断设备是否在线。网关根据实际情况控制两个计时器的运行状态，并根据运行状态转发相应数据。避免由于网关或被诊断设备对数据的处理产生的延迟，使诊断设备误判被诊断设备的状态而重新发起诊断请求这种现象发生，最终提高了诊断数据传输的准确率。
",H04L1/18,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"提供, 装置, 方法, 处理, 设备, 控制","车辆, 数据传输, 诊断, 控制"
CN111880780A,CN202010742110.0,"本申请实施例公开了一种MCU的引导加载程序开发方法、装置及平台。利用整车厂配置需求文件得到配置需求信息，将配置需求信息、MCU配置类型、通信协议配置类型、硬件配置信息和核心代码配置信息填充到预设代码框架中生成配置代码，并将配置代码和核心代码进行集成、编译，得到引导加载程序。该过程中，OEM的配置需求是直接基于其配置需求文件得到的，配置代码在配置需求信息、MCU配置类型、通信协议配置类型、硬件配置信息和核心代码配置信息这些信息确定后填充到预设代码框架中得到，同时将配置代码和核心代码进行集成、编译得到最终的引导加载程序，整个过程不需要进行手动需求分析、代码设计等工作，提升了引导加载程序的开发周期。
",G06F8/30,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/30,"平台, 装置, 生成, 方法, 配置",通信
CN111831310A,CN202010690332.2,"本发明公开了一种软件更新方法及系统，方法包括：获取通过软件注入设备注入的供应商软件，其中，供应商软件至少包括供应商的APP软件，运行所述供应商软件，进行整车下线测试；在测试完成后，发送命令使得程序指针跳转到供应商软件自毁BT区域执行；利用供应商的自毁BT下载客户的软件到供应商的APP软件的存储空间，擦除供应商的自毁BT。本发明在更新客户的软件时，能够保证原有的生产工序不变，且能够将供应商软件擦除，保证在更新客户的软件后控制器中只存在客户的软件。
",G06F8/654,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/654,"包括, 测试, 方法, 设备, 控制, 系统","测试, 控制"
CN111797098A,CN202010622247.2,"本发明提供了一种报文解析方法、装置及车载终端，接收待解析报文和其报文标识；将报文标识作为查询索引，从DBC索引文件中查询与报文标识对应的报文描述信息的存储位置，依据存储位置在DBC索引文件中查询与待解析报文对应的报文描述信息，利用查询到的报文描述信息对待解析报文进行解析。由于DBC索引文件中预先将报文描述信息以索引的方式进行存储，在查询的过程中，利用报文标识作为查询索引，直接查询报文描述信息，无需对整个DBC索引文件中的内容都进行遍历，直接利用索引，便可准确、快速定位到报文描述信息，能够尽快利用查询到的报文描述信息对待解析报文进行解析，提高报文解析速度。
",G06F16/22,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/22,"提供, 方法, 装置",通用
CN111796839A,CN202010645813.1,"本发明实施例提供控制器程序管理方法和装置。上述方法包括：在基于硬件供应商提供的下线检测EOL程序执行下线检测后，通过硬件供应商提供的第一BootLoader程序将EOL程序擦除，其中，第一BootLoader程序和EOL程序于裸板阶段被烧写至控制器非易失性内存中；通过第一BootLoader程序将产品供应商提供的第二BootLoader程序及application文件刷写进非易失性内存中；通过第一BootLoader程序将预设的擦除代码存储至预设内存，通过擦除代码对第一BootLoader程序执行擦除操作，以保证控制器中无硬件供应商程序残余。
",G06F8/61,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/61,"提供, 包括, 装置, 方法, 控制, 检测","检测, 控制"
CN111752895A,CN202010599459.3,"本发明公开了一种多系统级芯片之间的日志存储方法及装置，应用于多系统级芯片系统中的第一处理芯片，其中第一处理芯片部署有eMMC，第一处理芯片之外的其他处理芯片不设置对应的eMMC，方法包括：与第一处理芯片之外的其他处理芯片建立数据连接；通过数据连接接收其他处理芯片发送的日志报文并归档存储至eMMC。上述方法及装置应用的多系统级芯片系统中仅包含一个部署有eMMC的处理芯片，该处理芯片能够将其他处理芯片的日志报文统一收集到自身进行归档存储，该实现方案仅需要在系统中部署一个eMMC，相对于现有技术大大降低了成本，充分利用了eMMC存储资源，而且便于系统的日志数据的统一管理，能够提升用户的使用体验。
",G06F16/11,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/11,"实现, 包括, 装置, 方法, 设置, 处理, 系统",通用
CN111459518A,CN202010237024.4,"本发明提供了一种车辆ECU升级方法及系统，方法应用于TBOX，通过获取ECU升级包；确定ECU升级包对应的待升级ECU；向待升级ECU发送升级指令，以触发待升级ECU进入升级模式；在确定待升级ECU进入升级模式的情况下，将ECU升级包发送至进入升级模式的待升级ECU，ECU升级包是进入升级模式的待升级ECU进行升级的基础。通过TBOX及时获取ECU升级包，进而在获取到ECU升级包之后，及时将ECU升级包发送至车辆中的待升级ECU，实现对车载ECU的及时升级，从而满足智能网联时代汽车软件快速更新迭代的要求，提高车辆ECU升级的效率。
",G06F8/65,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"系统, 提供, 方法, 实现","车辆, 汽车"
CN111459508A,CN202010231994.3,"本发明公开了一种BootLoader自升级的方法及系统，方法包括：运行开始启动程序区域中的程序，判断BT标志是否为0或1；当BT标志为1时，将备份BT程序区域中的代码拷贝至正常BT程序区域，将运行标志区域中的BT标志设置为0，运行正常BT程序区域中的程序；判断升级标志是否为0或1；当升级标志为1时，判断升级区域是否为BT的flash区域和/或应用程序的flash区域；当为BT的flash区域时，擦除备份BT程序区域的flash区域，并写入新的BT程序代码；将BT标志设置为1，将升级标志设置为0，返回再次运行开始启动程序区域中的程序。本发明能够简单快捷的实现BT的自升级。
",G06F8/61,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/61,"实现, 包括, 方法, 设置, 系统",通用
CN111459519A,CN202010238099.4,"本发明公开了一种MCU升级方法及装置，应用于多MCU架构下的主MCU，采用在各个MCU的APP层和驱动层之间，单独增加一层中间接口层的策略，通过中间接口层屏蔽MCU的底层硬件以及不同的通信方式，来最大程度的复用APP层的上层业务，实现升级功能的快速移植。主MCU对从MCU的升级包进行格式化统一处理得到升级数据，当从MCU由工作模式跳转至升级模式后，将升级数据发送至对应的从MCU进行升级。本发明通过对各个从MCU的升级包进行格式化统一处理，使得各个从MCU均接收到相同格式的数据包，从而解决了各个从MCU的升级包格式差异的问题，简化了升级流程，提高了MCU的升级效率。
",G06F8/65,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"方法, 处理, 装置, 实现",通信
CN111338323A,CN202010208281.5,"本发明实施例公开了一种按键触发器，包括信息处理电路以及分别与信息处理电路连接的电源接口、数据传输接口和多个触发按键，每一个触发按键对应一个唯一标识；信息处理电路接收用户按下触发按键产生的触发信号，并将触发信号通过数据传输接口发送至上位机。还公开了一种基于上述按键触发器实现的数据处理方法，在用户按下代表特定场景的按键后，生成对应的待发送信息并发送给上位机，使得上位机依据预设的对应关系确定当前进入的场景。实现过程中用户只需要一键触发传输对应信息，即可实现场景切换记录；相对于现有技术，减少了测试员打字记录场景的繁琐操作，能够保证场景切换记录的及时性。
",G05B23/02,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,"关键词匹配: 数据处理, 数据传输","实现, 包括, 测试, 方法, 生成, 处理, 电路","测试, 数据传输"
CN111104410A,CN201911358296.3,"本发明提供局部道路信息提取方法及装置，基于预先建立的道路地图索引列表、二进制文件索引列表和二进制道路文件；道路地图索引列表包括：虚拟网格位置ID与地图文件中的道路ID间的关联关系；地图文件中的道路信息以二进制形式存储在二进制道路文件中；二进制文件索引列表包括：道路ID与存储位置间的关联关系。上述方法包括：接收搜索位置和搜索范围；根据搜索位置及搜索范围，在道路地图索引列表中确定搜索道路ID；根据搜索道路ID，在二进制文件索引列表内查询与搜索道路ID对应的存储位置；查询到的存储位置为目标存储位置；在二进制道路文件的目标存储位置处获取二进制形式的道路信息。
",G06F16/22,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/22,"提供, 方法, 包括, 装置",通用
CN110688136A,CN201910911301.2,"本发明提供一种应用程序更新方法及装置，在控制器上电复位后监测控制器的报文接收情况，如果监测到从控制器上电复位之后的预设时间内接收到特定类型报文，控制控制器停留在引导加载程序阶段，获取待更新的应用程序，并在引导加载程序阶段对待更新的应用程序进行更新，以从由具备UDS功能的应用程序控制进入引导加载程序阶段更改为由特定类型报文控制进入引导加载程序阶段，这样即使具备UDS功能的应用程序运行异常，在接收到特定类型报文后仍能够对待更新的应用程序进行更新，从而实现在不依赖具备UDS功能的应用程序的情况下完成应用程序更新。
",G06F8/65,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"提供, 实现, 装置, 方法, 控制",控制
CN110519377A,CN201910807400.6,"本申请公开了一种通信数据传输方法及装置，包括：利用目标通信总线接收整车通信数据，目标通信总线的带宽大小至少满足目标通信总线实时传输整车通信数据的需求；依据预设的打包规则，将整车通信数据进行打包，得到整车通信数据包；依据预设存储规则，将整车通信数据包交替存储在预先设置的至少两个存储区域中，并将存储区域中存储的整车通信数据包发送至具有联网功能的网络节点。由于本申请中的目标通信总线的带宽大小至少满足目标通信总线实时传输整车通信数据的需求，因此，采用目标通信总线来传输整车通信数据，能够不受带宽限制，实时传输全部的整车通信数据。
",H04L29/08,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"包括, 装置, 具有, 方法, 设置","通信, 数据传输, 网络"
CN110362327A,CN201910631831.1,"本发明提供了一种应用程序更新方法、装置及系统，其中方法应用于电子控制单元，方法包括：判断已有应用程序是否出现异常；在确定已有应用程序出现异常的情况下，将应用程序有效标志位设置为无效状态；控制电子控制单元进入下载模式，以从上位机下载最新应用程序；利用最新应用程序更新已有应用程序。本发明不再使用已有技术中由应用程序主导的更新过程，而是由BT程序控制ECU直接进入下载模式，以便从上位机下载最新应用程序更新已有应用程序，进而消除应用程序的异常情况，保证ECU可以继续被刷新、继续可以使用。
",G06F8/65,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"提供, 包括, 装置, 方法, 设置, 控制, 系统",控制
CN109976737A,CN201910237678.4,"本发明提供了一种RTE代码的生成方法及装置，在应用层将所有的汽车电子功能抽象为虚拟SWC；依据RTE与BSW的接口调用关系，利用BSW配置工具生成满足BSW应用需求的所有RTE代码，通过RTE代码实现应用层与BSW之间的通信。本发明不需要设计真实的SWC，直接通过BSW配置工具生成所有的RTE代码，简化了开发流程，降低了开发成本。
",G06F8/30,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/30,"提供, 实现, 装置, 方法, 生成, 工具, 配置","通信, 汽车"
CN109947371A,CN201910238138.8,"本发明提供一种数据记录方法、装置、存储器及T‑BOX，该方法基于包含两片内存的T‑BOX，将数据流直接写入内存，相比写入磁盘的速度快，且两片内存的设置保证了数据流的写入不会被中断，减少了数据丢失，提高了数据记录的完整性。将数据压缩后比没有压缩的数据量小，因此将数据压缩后再写入磁盘，缩短了数据写入磁盘的时间；以及将内存中数据直接压缩，相比文件压缩，减少了从磁盘读出数据的过程，减小了系统开销。进一步的，将数据格式转换为二进制格式，提高了压缩效率，减小了磁盘占用。以及将数据实时上传，可以及时了解车辆运行状态。
",G06F3/06,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据格式,"提供, 装置, 方法, 设置, 系统",车辆
CN109947454A,CN201910250102.1,"本申请公开了一种数据处理方法及系统，该方法包括：获取产品更新指令，若产品更新指令为升级程序更新指令，则将初始应用程序替换为升级工具程序，依据升级工具程序将初始升级程序更新为目标升级程序，根据目标升级程序将升级工具程序替换为第一目标应用程序，实现将初始应用程序替换为第一目标应用程序。本方案中通过将初始应用程序替换为升级工具程序，以实现通过升级工具程序对初始升级程序的更新，并进一步实现通过目标升级程序对初始应用程序的更新，实现了对已经生产出的产品的内部程序的更新，使得已生产出的产品可适应不同平台、不同厂家或不同功能的需求，提高了产品利用率，降低了资源浪费的问题。
",G06F8/65,北京经纬恒润科技有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据处理; IPC分类号匹配: G06F8/65,"平台, 实现, 包括, 方法, 处理, 系统, 工具",通用
CN109947465A,CN201910251562.6,"本发明提供了一种数据处理方法及系统，根据待刷写的车型开发时的网络架构，确定网络架构中待刷写的电子控制单元ECU，基于预先编写的自动化程序，调用预先建立的初始化配置ini文件中对应待刷写的ECU的配置信息，其中，配置信息包括网络节点配置信息和控制器功能配置信息，基于网络节点配置信息和控制器功能配置信息对待刷写的ECU进行刷写。通过上述方法，基于自动化程序对待刷写的ECU的网络节点配置信息和控制器功能配置信息进行刷写，实现提高刷写ECU效率的目的。
",G06F8/71,北京经纬恒润科技有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据处理; IPC分类号匹配: G06F8/71,"提供, 实现, 包括, 方法, 处理, 控制, 系统, 配置","网络, 控制"
CN109871225A,CN201910132299.9,"本发明公开了一种电子控制单元ECU升级方法及ECU，ECU中包括一个第一处理器以及至少一个第二处理器，至少一个第二处理器中与外部升级设备连接的第二处理器为直连处理器；第一处理器接收外部升级设备通过直连处理器传输的升级包文件；根据升级包文件确定待升级的处理器；当待升级的处理器包括第一处理器以及第二处理器时，先对第二处理器进行升级，并在第二处理器升级完成后，再进行自升级。基于上述方法及ECU，对外部升级设备来说，仅执行一次刷写流程即可对重量级ECU中的多个处理器进行升级操作。
",G06F8/65,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"包括, 方法, 处理, 设备, 控制",控制
CN109871657A,CN201910216819.4,"本发明公开了一种数据处理方法及装置，读取电子数据表；电子数据表预先填写了用于定义软件模型中数据对象和总线对象的属性信息；根据属性信息和预设对应关系定义数据对象和总线对象；预设对应关系用于表征属性信息与数据对象属性和总线对象属性的对应关系；将定义的数据对象和总线对象存储在本地文件。本发明通过预先填写了用于定义软件模型中的数据对象和总线对象的属性信息的电子数据表来管理数据，在面对新的设计需求时，可以对电子数据表中相应的属性信息的属性值批量修改，提高软件开发和维护效率，且通过数据表格式管理数据，不再需要开发人员记忆特定的数据格式即可进行数据的填写和修改，进一步提高了软件开发和维护效率。
",G06F17/50,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,"关键词匹配: 数据处理, 数据格式","处理, 方法, 模型, 装置",通用
CN109839281A,CN201910251652.5,"本发明提供了一种实车测试采集系统和方法，该实车测试采集系统包括供电模块、数据处理模块和数据获取模块，供电模块、数据处理模块和数据获取模块三者相互连接且分区域集成设置；数据处理模块的数据采集端口设置在实车测试采集系统的设备面板上。通过上述设置，实车测试采集系统的内部结构简单，各元件分区域集成，布线简单。并且数据处理模块能够基于采集的数据和车辆运行数据，对车辆性能进行分析，即能够同时实现数据采集和分析功能。
",G01M17/007,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"提供, 实现, 包括, 测试, 方法, 设置, 处理, 设备, 系统","车辆, 测试"
CN109800330A,CN201910096645.2,"本发明提供一种数据处理方法及装置，该方法包括：获取待处理数据，确定用于对待处理数据进行解析操作的目标关键字，基于目标关键字，对待处理数据进行解析，得到解析结果。通过本发明提供的方法及装置，在获取待处理数据之后，会确定用于对该待处理数据进行解析操作的目标关键字，即对于不同的Arxml文件，使用相适应的目标关键字进行解析，解决了使用同一个关键字不能解析不同的Arxml文件的问题。
",G06F16/80,北京经纬恒润科技有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据处理; IPC分类号匹配: G06F16/80,"提供, 包括, 装置, 方法, 处理",通用
CN109740005A,CN201811642727.4,"本发明提出一种图像目标标注方法及装置，该方法包括：确定待标注内容在待标注图像中的显示参数；其中，显示参数包括显示位置信息和/或尺寸信息；根据待标注内容在待标注图像中的显示参数，确定标注信息绘制参数；根据标注信息绘制参数，在待标注图像中绘制标注信息。上述处理过程只针对待标注内容所在图像区域的图像像素进行处理，并且是直接在原始的待标注图像中进行目标标注，其处理像素数量更少，因此图像目标标注效率更高。
",G06F16/51,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F16/51,"方法, 包括, 处理, 装置",通用
CN109491681A,CN201811226445.6,"本发明实施例公开了一种汽车内MCU的升级方法及装置，该方法包括：接收上位机发送的升级总包，升级总包中包括MCU升级包和升级信息文件，MCU升级包的数量等于待升级MCU的数量且一一对应，升级信息文件包括MCU升级包和待升级MCU之间的对应关系；根据对应关系确定每个待升级MCU对应的MCU升级包；控制每个待升级MCU利用其对应的MCU升级包进行升级。本发明实施例的技术方案升级总包中所包括MCU升级包的数量等于待升级MCU的数量且一一对应，然后基于对应关系确定每个待升级MCU对应的MCU升级包并控制每个待升级MCU进行升级，能够实现MCU的批量升级。
",G06F8/65,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"实现, 包括, 装置, 方法, 控制","汽车, 控制"
CN109413762A,CN201811219836.5,"本发明公开了一种车载Tbox的唤醒方法及装置，车载Tbox在将进入低功耗状态时，建立与服务器之间的长连接，进而通过所建立的长连接向服务器发送长连接心跳包，在服务器接收到业务数据后触发生成第一唤醒数据并发送给车载Tbox，车载Tbox基于第一唤醒数据，由低功耗状态进入唤醒状态，并关闭与服务器之间的长连接。上述车载Tbox的唤醒方法及装置，服务器通过车载Tbox发送长连接心跳包的长连接对车载Tbox进行唤醒，由于网络数据传输非常快，因此相对于振铃唤醒方式，能够有效提升车载Tbox的唤醒速度，进而提升远程业务处理速度。且通过网络数据进行唤醒的成功率相对于振铃方式唤醒车载Tbox的成功率更高。
",H04W76/25,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"生成, 方法, 处理, 装置","数据传输, 网络"
CN109190306A,CN201811222046.2,"本发明公开了一种数据回灌仿真方法及装置，该方法包括：获取待注入数据；搭建与ADAS的控制逻辑相同的算法仿真模型；将待注入数据注入算法仿真模型，并获得算法仿真模型对待注入数据处理后的第一仿真结果；利用第一仿真结果优化算法仿真模型的参数；将待注入数据注入优化后的算法仿真模型，并获得优化后的算法仿真模型对待注入数据处理后的第二仿真结果；当第二仿真结果相对于第一仿真结果符合预期要求时，根据优化后的算法仿真模型优化ADAS的参数。本发明提供的方法快速、简便，跟反复多次通过实际道路试验优化ADAS相比，减少了人力、财力和物力成本，同时极大地降低了时间的消耗，能够有效的辅助评价ADAS可靠性。
",G06F17/50,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,"关键词匹配: 数据注入, 数据处理","提供, 包括, 模型, 装置, 算法, 仿真, 方法, 处理, 控制",控制
CN108810000A,CN201810637635.0,"本发明提出一种生成应用程序编程接口的方法及装置，该方法包括：按照SomeIpXf规范生成基础开发模板库；其中，所述基础开发模板库包括用于对SomeIpXf报文首部进行序列化和反序列化处理的第一API对，和用于对各个基本数据类型的数据进行序列化和反序列化处理的各个第二API对；根据所述基础开发模板库生成用于对各个复杂数据类型的数据进行序列化和反序列化处理的各个第三API对，和用于对各个远程过程调用类型的数据进行序列化和反序列化处理的各个第四API对。上述方法及装置可以用来作为生成结构化数据的序列化和反序列化API的参考，将其应用到SomeIpXf开发中可以提高SomeIpXf开发效率。
",H04L29/06,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据类型,"包括, 装置, 生成, 方法, 处理",通用
CN108647176A,CN201810461856.7,"本发明提供的车联网设备数据传输方法及车联网设备，应用于汽车通信技术领域，所述方法包括第一微控制器将获得的待传输数据写入RAM的目标存储区域中，并将该目标存储区域的状态标记为不可写状态，之后向第二微控制器发送目标存储区域的地址信息和待传输数据的长度，以使第二微控制器读取存储在目标存储区域中的待传输数据，并在读取完毕后，向第一微控制器发送通知信号，以使第一微控制器根据该通知信号将目标存储区域的状态标记为可写状态，本发明提供的方法，不再依靠SPI接口进行数据传输，利用RAM读写速度快，且可与微控制器直接进行数据传输的特点，提高第一微控制器与第二微控制器之间的数据传输速率，满足实际应用中对数据传输的需求。
",G06F13/42,北京经纬恒润科技有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据传输; IPC分类号匹配: G06F13/42,"提供, 包括, 方法, 设备, 控制","通信, 汽车, 数据传输, 控制"
CN108228185A,CN201711469751.8,"本发明公开了一种嵌入式软件模块化生成方法和装置。该方法包括：在确定各个软件模块的功能、配置选项以及基本API接口集后，对各个软件模块的功能以及基本API接口集进行功能细化处理，从而得到多个子功能，以及多个子功能对应的API接口。再根据各个子功能的实现过程，对该软件模块进行编码调试，以实现该软件模块的所有功能，并生成该软件模块对应的二进制执行文件。进一步的，完成各个软件模块的联合调试，并根据调试结果更新各个软件模块对应的二进制执行文件。本发明还公开了一种嵌入式软件模块化生成装置，可以防止生成的软件的代码泄漏，并实现对软件的模块化处理。
",G06F8/41,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/41,"实现, 包括, 装置, 方法, 生成, 处理, 配置",通用
CN108121553A,CN201711381372.3,"本发明提出一种更新应用程序的方法及装置。本发明所提出的更新应用程序的方法包括：电子控制单元接收上位机发送的更新应用程序指令；依据更新应用程序指令，判断所述电子控制单元的预设地址中的原有协议标识是否与更新应用程序指令指定的更新协议相对应；若不对应，则删除原有协议标识，写入与更新应用程序指令指定的更新协议相对应的协议标识，并依据更新应用程序指令指定的更新协议更新应用程序；若对应，则直接依据更新应用程序指令指定的更新协议更新应用程序。采用上述方法及装置更新既支持基于UDS协议更新应用程序，又支持基于CCP协议更新应用程序的电子控制单元的应用程序，可以提高上述电子控制单元更新应用程序的效率。
",G06F8/65,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"方法, 包括, 装置, 控制",控制
CN108052338A,CN201711459534.0,"本发明公开了一种软件刷新方法及装置，该方法应用于控制器中的主MCU，包括：接收外部设备发送的安全机制校验请求；响应安全机制校验请求，判断是否具有对从MCU的刷新权限、以及外部设备是否具有对主MCU的刷新权限；若是，接收外部设备发送的传输请求，传输请求携带目标MCU的标识，目标MCU为至少两个MCU中的任一MCU；当目标MCU为主MCU时，控制从MCU进入低功耗状态，并对主MCU进行刷新；当目标MCU属于从MCU时，控制从MCU中、除待刷新从MCU外的其他从MCU进入低功耗状态，并对待刷新从MCU进行刷新。由于控制不被刷新的从MCU进入低功耗状态，因此相对于现有技术降低了能源浪费。
",G06F8/65,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/65,"包括, 装置, 具有, 方法, 设备, 控制",控制
CN107861746A,CN201711275100.5,"本发明公开了一种车辆电子控制单元的刷新方法及系统，该方法通过对待下载文件添加数据校验区，数据校验区设置有校验项，在下载待下载数据之前，先提取签名校验项和适用控制单元校验项进行校验，当均校验通过后，再将待刷新数据下载至待刷新地址范围内，实现下载前对待下载文件进行合法性以及适用性的校验，防止不合法及不适用文件下载至控制单元，以及准确确定每次下载需要擦写的地址空间，在待下载文件下载完成后，提取摘要校验项进行校验，校验通过后，则置位下载文件有效标志，实现下载完成后对电子控制单元内文件的完整性进行校验，防止下载过程中出错。
",G06F8/654,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/654,"实现, 方法, 设置, 控制, 系统","车辆, 控制"
CN107607795A,CN201710993505.6,"本申请提供了一种射频电磁场相位的测量方法及系统，频谱分析仪确定待测信号的幅度，功率合成器将待测信号和参考信号进行矢量合成，获得第一合成信号，频谱分析仪确定第一合成信号的第一幅度，数据处理设备基于待测信号的幅度、参考信号的幅度和第一幅度计算待测信号相对参考信号的相位的绝对值，功率合成器将待测信号和带偏移的参考信号进行矢量合成，获得第二合成信号，频谱分析仪确定第二合成信号的第二幅度，数据处理设备基于待测信号的幅度、参考信号的幅度、待测信号相对参考信号的相位的绝对值、第二幅度和预设相位偏移确定待测信号相对参考信号的相位。本申请实现电磁场相位测量的成本较低，且适用场合比较广泛。
",G01R29/08,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"提供, 实现, 方法, 处理, 设备, 系统, 计算",通用
CN105978862A,CN201610265417.X,"本申请提供了一种数据处理方法及系统，所述方法包括：接收设备发送的操作信息；判断所述操作信息是否为关键信息；若所述操作信息为关键信息，则对所述关键信息进行解密操作；其中，在所述操作信息为关键信息的情况下，所述操作信息为经所述设备执行加密操作后发送的；获取解密后的解密信息。本申请可以在网关与设备之间传输关键的操作信息进行加解密处理，从而在不影响网关实时性的基础上，提高设备与网关之间通信的安全性，继而增加车辆的安全性能。
",H04L29/06,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"提供, 包括, 方法, 处理, 设备, 系统","车辆, 通信"
CN105893279A,CN201610192173.7,"本申请公开了一种基于循环队列的数据传输方法及系统，创建循环队列；所述循环队列中设置有多个出列指针；为每个数据读取端分配至少一个对应的所述出列指针；控制每个所述数据读取端通过对应的所述出列指针读取所述循环队列中存储的数据。可见，由于本申请提供的循环队列中设置有多个出列指针，因此在需要采用一对多的数据传输方式时，可以为每个数据读取端分配至少一个对应的出列指针，控制每个数据读取端通过对应的出列指针读取循环队列中存储的数据，从而可以利用一个循环队列实现一对多的数据传输，无需将数据发送端所发送的数据复制到多个循环队列中，可以减少对数据存储空间的占用，拓宽循环队列的应用范围。
",G06F12/12,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"提供, 实现, 方法, 设置, 控制, 系统","数据传输, 控制"
CN105677592A,CN201511032625.7,"本申请公开了一种总线通信方法及系统，通过一级缓存接收总线数据；在DMA数据传输启动时，将一级缓存接收的总线数据保存到二级缓存中，并将二级缓存中的总线数据保存到三级缓存中；三级缓存的容量大于二级缓存的容量；在将三级缓存存满时，中断DMA数据传输，向CPU发送DMA中断请求，以使CPU在接收到DMA中断请求后将三级缓存中保存的总线数据提取到本地磁盘中。本申请提供的方案，在二级缓存后增加了三级缓存，且三级缓存的容量大于二级缓存的容量，在三级缓存的容量存满时再向CPU发送DMA中断请求，可以降低CPU转移总线数据的频率，从而降低CPU负载和磁盘负载，便于及时处理其他线程任务，提高设备性能。
",G06F13/16,北京经纬恒润科技有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据传输; IPC分类号匹配: G06F13/16,"提供, 方法, 处理, 设备, 系统","通信, 数据传输"
CN105653490A,CN201511001271.X,"本发明提供一种基于地址控制的数据处理方法及装置，在获取到存储装置的地址空间中存储的各个地址之后，可以将所获取的各个地址存储在智能芯片的存储装置中；当确定与数据对应的当前操作后，可以基于各个地址在数据空间中连续执行当前操作，这样智能芯片和电子设备在交互数据时，智能芯片则无需等待电子设备反馈的响应信息，就可以连续的与电子设备进行数据交互，提高数据传输效率。而数据传输效率的提高使得一定时间内传输的数据量相应提高，如之前等待电子设备反馈响应信息的时间也被用来进行数据交互，因此使得传输的有效带宽得到提高，这样一定时间内传输的数据量增加，从而降低数据丢失的概率。
",G06F13/42,北京经纬恒润科技有限公司,数据注入类型支持技术,4.0,"关键词匹配: 数据处理, 数据传输; IPC分类号匹配: G06F13/42","提供, 装置, 方法, 处理, 设备, 控制","数据传输, 控制"
CN104573135A,CN201410815379.1,"本发明公开了一种基于反射内存网与中间件技术的实时数据采集方法及装置，其采集方法包括构建反射内存网；采集所述反射内存网的数据并写入预先配置的本地缓存空间；按照设定周期，解析本地缓存空间的数据并写入预先配置的环形缓存空间；读取环形缓存空间中的数据并写入中间件；读取中间件数据并写入数据监控节点；本发明采用反射内存网，保证试验网络中各个节点的数据传输高效性和准确度；设定周期，保证数据采集周期的精度；数据监控节点与反射内存网的数据传输使用中间件技术，保证底层数据上传的可靠性；解决多个试验节点同时传输数据的实时性问题，确保整个试验的准确性，避免因查找监控界面中数据未及时更新的原因而增加试验的复杂性。
",G06F17/40,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"方法, 包括, 装置, 配置","数据传输, 网络"
CN104298739A,CN201410527974.5,"本申请公开了一种数据处理方法和装置，获取待处理数据，判断所述待处理数据的数据量是否大于预设阈值；如果所述待处理数据的数据量大于预设阈值，将所述待处理数据划分为多个数据段，且每个数据段中的数据量均不大于所述预设阈值；从所述多个数据段中选择至少一个样本数据，利用选择出的至少一个样本数据构建用于全局浏览的数据子集，所述数据子集中的数据量不大于所述预设阈值。这样，可以将海量的待处理数据划分为多个小的数据段，然后从多个数据段中选择多个样本数据组成数据子集，数据段和数据子集中的数据量均不大于预设阈值，可供现有的数据查看软件浏览和查看，从而通过浏览构建的数据子集即可实现海量数据的全局浏览。
",G06F17/30,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"方法, 处理, 装置, 实现",通用
CN103970698A,CN201410072302.X,"本申请提供了一种串行通信的波特率识别方法、装置、微控制器及系统，方法包括：获取脉冲宽度数据，脉冲宽度数据包括八个脉冲宽度时间；确定脉冲宽度数据是否满足第一预设条件；当脉冲宽度数据满足第一预设条件时，从脉冲宽度数据中确定出最大的脉冲宽度时间和最小的脉冲宽度时间；计算除最大的脉冲宽度时间和最小的脉冲宽度时间外的其它脉冲宽度时间的平均脉冲宽度时间；基于平均脉冲宽度时间确定脉冲宽度数据是否满足第二预设条件；当脉冲宽度数据满足第二预设条件时，通过平均脉冲宽度时间确定串行通信的波特率。本申请提供方法、装置、微控制器及系统，可解决由干扰造成的波特率计算错误的问题，能够提高串行通信的可靠性。
",G06F13/38,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F13/38,"提供, 包括, 装置, 方法, 控制, 系统, 计算","通信, 控制"
CN103973407A,CN201410230910.9,"本申请提供一种远程数据传输系统的数据传输速率匹配方法及装置，该远程数据传输系统包括发端和收端，收端包括多个更新单元及对应的缓冲区，各个更新单元按照设定的定时更新周期执行以下过程：计算与该更新单元对应的缓冲区中数据量的阈值范围，判断与该更新单元对应的缓冲区中的数据量是否满足阈值范围，当否时，计算调整值，并根据调整值，调整该更新单元的定时更新周期，以使得调整后的定时更新周期更趋近发端中、与该更新单元对应的采集器的定时采集周期，使得在远程数据传输过程中，保证发端数据采集与收端数据更新的速率相匹配、避免信号畸变、传输延时的不确定性以及数据丢失等问题。
",H04L1/00,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"提供, 包括, 装置, 方法, 系统, 计算",数据传输
CN103646427A,CN201310690352.X,"本发明公开了一种采集图像数据的方法及装置，首先生成仿真图像，在生成仿真图像的过程中，获取已生成的仿真图像数据，并将所述已生成的仿真图像数据传输至预先设定的存储器。基于上述方法，能够实时在线采集图像数据，且实现过程简单，适用于采集三维视景仿真图像数据。
",G06T19/00,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"实现, 装置, 仿真, 生成, 方法",数据传输
CN103646001A,CN201310682944.7,"本发明公开了一种DMA数据传输控制方法，DMA控制器集成在硬件板卡上，通过CPU访问硬件板卡中的控制寄存器实现对DMA数据传输的控制，因此，本申请实施例提供的DMA数据传输控制方法，只需要将硬件板卡中的第一BAR寄存器所占用的空间映射到主机板的内存即可，而不需要将硬件板卡中DMA数据的BAR空间映射到主机板的内存中，而控制寄存器所占用的空间远远小于DMA数据所占用的空间，因此，本申请实施例提供的DMA数据传输控制方法减少了映射到主机板内存中的空间，从而提高了PCIE总线可以负载的硬件板卡的数量。本发明还提供一种DMA数据传输控制系统。
",G06F13/28,北京经纬恒润科技有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据传输; IPC分类号匹配: G06F13/28,"提供, 实现, 方法, 控制, 系统","数据传输, 控制"
CN103616867A,CN201310611783.2,"本申请公开了一种数据处理方法及装置，所述方法包括：确定每个测量量的测量周期值及字节数值；依据每个所述测量量的测量周期值及字节数值，填充DAQ列表。通过本申请实施例可以避免现有技术中在将测量量随机配置到其对应测量周期的DAQ列表中时，会导致DAQ列表被填充满，使得DAQ列表的使用率较低的情况，本申请实施例能够在考虑到测量量的测量周期值的同时，在填充DAQ列表时，能够将测量量的字节数值考虑在内，增加DAQ表的填充率，提高DAQ表的使用率。
",G05B19/418,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"包括, 装置, 方法, 处理, 配置",通用
CN103514337A,CN201310512379.X,"本发明公开了仿真系统，该系统包括N个物理设备仿真模型、数据共享模块、通信服务模块及M个硬件接口，数据共享模块包括第一、第二数据共享单元，在通信服务模块检测到第二数据共享单元存储有物理设备仿真模型发送的第一格式的数据时，从中获取与物理设备需求的第二格式的数据对应的第一格式的数据，将其转换为第二格式的数据，之后将第二格式的数据发送至物理设备；在通信服务模块接收到物理设备的第二格式的数据时，将第二格式的数据转换为第一格式的数据，并将第一格式的数据发送至第一数据共享单元，以供各物理设备仿真模型获取。本发明公开的仿真系统具有较高的通用性，可以提高仿真效率，降低设计变更成本。本发明还公开了相应的仿真方法。
",G06F17/50,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据转换,"包括, 模型, 具有, 仿真, 方法, 设备, 系统, 检测","检测, 通信"
CN103294632A,CN201310253550.X,"本申请提供了一种总线载板、数据交互系统、数据处理方法及装置，其中，反射内存板设置于总线载板上，总线载板包括：底板，以及设置于底板上的处理器，处理器用于读取反射内存板的第一数据，将第一数据发送给实时仿真计算机，并且，读取实时仿真计算机的第二数据，将第二数据写入反射内存板。本申请提供的总线载板、数据交互系统、数据处理方法及装置，可读取反射内存板的数据，并将反射内存板的数据通过总线提供给实时仿真计算机，也可获取实时仿真计算机的数据，并将该数据写入反射内存板，实现了实时仿真计算机对反射内存板的读写操作，从而解决了基于特殊总线的实时仿真计算机应用反射内存板进行通信的问题。
",G06F13/38,北京经纬恒润科技有限公司,数据注入类型支持技术,3.0,关键词匹配: 数据处理; IPC分类号匹配: G06F13/38,"提供, 实现, 包括, 装置, 仿真, 方法, 设置, 处理, 系统, 计算",通信
CN103268201A,CN201310138191.3,"本申请公开了一种数据存储方法、存储装置及读取方法，该方法首先接收待写入数据，然后确定待写入数据的类型，最后在与该数据类型相对应的存储空间中依次写入待写入数据，该方法在数据存储过程中，通过在存储空间中依次写入待写入数据，不需要每次写入待写入数据时，均进行一次擦除操作，可以解决现有技术在每次进行数据存储的过程中，均需要对上一个有效的SECTOR中的数据进行擦除操作，使得擦除操作频繁，进而导致闪存寿命降低的问题。
",G06F3/06,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据类型,"方法, 装置",通用
CN103148741A,CN201310057202.5,"本申请公开了一种实现红外制导数字化仿真的方法及系统，基于由红外仿真平台和制导控制系统仿真平台构成的闭环系统，该方法通过红外仿真平台和制导控制仿真平台联合闭环实现红外制导数字化仿真，红外仿真平台对红外图像进行红外图像处理，得到红外图像信息，然后，对红外图像信息进行目标识别，得到含有目标信息的像素位置值的目标识别信息，使得红外仿真平台和制导控制仿真平台两个平台之间的接口传输数据量小，红外仿真平台和制导控制仿真平台采用网络通讯握手机制的方式，实现两个仿真平台仿真数据的同步，通信方式灵活，确保了数据传输的实时性，从而提高了系统的运行效率。
",F41G3/32,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"平台, 实现, 仿真, 方法, 处理, 控制, 系统","通信, 数据传输, 网络, 控制"
CN103078908A,CN201210576547.7,"本申请公开了一种数据传输方法及装置，应用于与网络相连接的PC机上，并且所述PC机上设置有多个IO接口，该方法通过建立与网络中其它PC机中的应用软件之间的通讯链路,然后通过该通讯链路控制该通讯链路中的其他PC机上的IO接口实现数据发送功能，解决了现有技术中不能实现应用软件与底层硬件之间数据传输的灵活应用的问题。
",H04L29/08,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"实现, 装置, 方法, 设置, 控制","数据传输, 网络, 控制"
CN102902217A,CN201210430018.6,"本发明实施例公开了一种基于步进电机控制的数据处理方法、装置、控制器和系统，其中方法包括：接收采集的负载监测量；解析所述负载监测量并与当前需求控制量进行比对，得到比对差量；查找所述比对差量匹配的步进电机步进值；发送携带针对步进电机步进值的控制指令。本发明实施例中的基于步进电机控制的数据处理方法、装置、控制器和系统，通过处理针对负载采集的监测量，并将当前需要针对负载的控制量，通过与电机的步进量进行匹配后转换为对电机的控制量，克服了现有技术中由于直接采集及控制电机步进量，引入来源于电机和传动装置的误差所带来的精确度不高的技术缺陷，达到了控制器直接处理和控制负载反馈量而对负载控制精确度提高的技术效果。
",G05B19/04,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"包括, 装置, 方法, 处理, 控制, 系统",控制
CN102651674A,CN201210091440.3,"本发明公开了一种反射内存网数据传输方法，包括：将反射内存卡的板载内存划分为全局变量区和节点数据区；为每个节点划分数据存储区；为每个节点数据存储区划分对应的数据位置记录区；在节点向板载内存写数据时，将数据写入与节点对应的数据存储区中已有数据后，并通过数据的存储位置更新为位置偏移量；当节点读取数据时，先读取目标节点的当前位置偏移量，将当前位置偏移量与目标节点的位置偏移量不同时，更新位置偏移量，并读取目标节点的数据。本发明实施例中，节点在板载内存写入数据时可以避免覆盖未被读出的数据，以及，在读出目标节点的数据时可以判断是否为重复数据，从而避免了数据传输过程中数据的混乱，进而提高了数据的传输效率。
",H04L1/00,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"方法, 包括",数据传输
CN102143185A,CN201110080897.X,"本发明提供了一种数据传输方法和数据传输装置，用以解决现有技术中分别使用ARINC429与AFDX两种协议的装置之间数据传输的效率较低的问题。该方法包括：接收第一种格式的数据帧；将接收的一个或多个所述第一种格式的数据帧中的净荷数据以及该净荷数据的传输目的地标识添加到第二种格式的数据帧中；发送所述第二种格式的数据帧。采用本发明的技术方案，有助于解决ARINC429与AFDX两种协议的装置之间数据传输的效率较低的问题，从而实现航空电子数据的高效转换，提高了系统性能和带宽的利用率。
",H04L29/06,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"提供, 实现, 包括, 装置, 方法, 系统",数据传输
CN101873196A,CN201010192923.3,"本发明公开了一种用于高速传输数据的方法、系统及板卡，用于板卡内或板卡之间的数据传输，其中，板卡具有发送端和接收端，包括：发送端获取训练数据序列并发送训练数据序列；接收端根据训练数据序列的相位延迟值获取中间相位延迟值；发送端根据中间相位延迟值传输数据。通过本发明，能够实现低误码率的高速数据传输。
",H04L1/00,北京经纬恒润科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"实现, 包括, 具有, 方法, 系统",数据传输
CN101819556A,CN201010135630.1,"本发明提供了一种信号处理板，包括：多个FPGA处理节点，多个FPGA处理节点通过互连总线按全连通的拓扑结构互连，该互连总线用于传输高速数据信号；FPGA主控模块，通过共享总线与多个FPGA处理节点互连，该共享总线用于传输控制信号；PCI接口模块，通过局部总线与FPGA主控模块相连；时钟模块，与FPGA主控模块相连，并由主控模块控制，用于提供信号处理板的工作时钟；电源模块，用于提供信号处理板所需电压。该信号处理板的多个FPGA处理节点的拓扑结构灵活，可以根据具体的应用而进行互联结构重构。
",G06F13/40,北京经纬恒润科技有限公司,数据注入类型支持技术,2.0,IPC分类号匹配: G06F13/40,"提供, 包括, 处理, 控制",控制
