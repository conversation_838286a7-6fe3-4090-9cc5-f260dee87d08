﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN120030675A,CN202510111724.1,"本申请公开了一种确定悬置系统中衬套参数的方法及装置。该方法包括：获取目标动力总成的属性参数和目标悬置系统的悬置参数，目标悬置系统用于悬置目标动力总成；基于属性参数和悬置参数，针对目标悬置系统中衬套的刚度参数的多个优化目标，分别构建多个目标函数，每个目标函数对应一个优化目标；融合多个目标函数，得到综合目标函数；基于预设算法，对综合目标函数进行求解，确定目标悬置系统中衬套的最优刚度参数。该方法，通过融合多目标优化的思路，简化了求解过程，提高了确定衬套参数的效率和质量。
",G06F30/15,经纬恒润(天津)研究开发有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/15,"包括, 装置, 算法, 方法, 系统",通用
CN119882482A,CN202411966331.0,"本申请公开了一种自动驾驶仿真方法、自动驾驶仿真装置和设备。该方法包括：获取地图文件，基于地图文件生成测试地图；获取导航仿真配置文件，基于导航仿真配置文件生成在测试地图中的导航路径；获取场景仿真文件，基于场景仿真文件生成与导航路径适配的路径仿真场景；基于所述路径仿真场景和待测自动驾驶算法，仿真车辆沿所述导航路径行驶。根据本申请实施例，本申请提供的自动驾驶仿真方法可以仿真导航路径，实现沿导航路径自动驾驶的仿真测试，降低测试成本。
",G05B17/02,经纬恒润(天津)研究开发有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"提供, 实现, 包括, 测试, 装置, 算法, 仿真, 方法, 生成, 设备, 配置","车辆, 驾驶, 测试, 导航"
CN119670255A,CN202411748560.5,"本申请公开了一种制动系统的摩擦片建模方法及相关装置，涉及车辆动力学技术领域，确定制动系统在当前时刻下的第一制动状态；在第一制动状态为抱死状态的情况下，基于连接关系为弹簧阻尼系统的假设，计算摩擦片模型与车轮模型之间的第一摩擦扭矩，第一摩擦扭矩为制动系统在抱死状态下的第一制动扭矩。车轮抱死时，摩擦片的静摩擦力约等于车轮的滚动阻力，而由于摩擦片与车轮并非绝对刚性、受力会产生轻微形变，因此摩擦片的静摩擦力会受车轮阻力而上下波动、但不会突变，对此本申请在建模时在摩擦片模型与车轮模型之间使用弹簧阻尼系统达到类似效果，以此能够准确模拟车辆抱死状态下的制动扭矩，对制动控制系统算法仿真测试能够提供极大帮助。
",G06F30/15,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/15,"提供, 测试, 模型, 装置, 算法, 仿真, 方法, 控制, 系统, 计算, 模拟","车辆, 测试, 控制"
CN119538584A,CN202411733814.6,"本申请公开了一种车辆仿真控制方法及相关装置，涉及车辆仿真技术领域，应用于部署有车体建模模型的仿真控制设备，该车体建模模型用于表征车辆的车体中的前车体和后车体之间的动力学关系，在动力学关系中后车体只能相对前车体绕目标方向转动。该方法包括：在车辆的仿真测试过程中，确定待施加到前车体和后车体上的受力数据；确定当前时刻前车体的第一状态数据和后车体的第二状态数据；基于第一状态数据、第二状态数据和受力数据，利用该动力学关系，确定前车体的第三状态数据和后车体的第四状态数据。本申请可以在车辆仿真测试过程中，更为准确地仿真出车辆中车体的状态，减少由于车体的状态数据存在较大误差而影响到车辆仿真的精准度的情况。
",G06F30/20,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"包括, 模型, 装置, 测试, 仿真, 方法, 设备, 控制","车辆, 测试, 控制"
CN119024721A,CN202411129716.1,"本发明提供一种汽车网联模块的硬件在环仿真方法、装置及系统，启动仿真场景软件并解析仿真静态数据，初始化总线机柜、GNSS模拟器和V2X信号仿真模块，以使GNSS模拟器、V2X信号仿真模块和被测控制器进行时间同步；当完成时间同步时获取仿真动态数据。从处理后的仿真静态数据和仿真动态数据中获取所需目标仿真数据信息并发送至总线机柜、GNSS模拟器和V2X信号仿真模块以搭建仿真环境；通过仿真环境进行被测控制器的仿真测试；监控被测控制器反馈的信息，通过虚拟ECU控制仿真环境根据信息进行变化，实现被测控制器与仿真环境的交互。有效提高汽车网联模块测试的仿真效率，提升仿真环境的拟真度，缩短汽车网联模块的开发周期。
",G05B17/02,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,4.0,"关键词匹配: 硬件在环, 模拟器; IPC分类号匹配: G05B17/02","提供, 实现, 测试, 装置, 仿真, 方法, 处理, 控制, 系统, 模拟","测试, 汽车, 控制"
CN118940464A,CN202410921518.2,"本申请公开一种混合固态激光雷达仿真方法、装置、存储介质及电子设备，方法包括：获取混合固态激光雷达对应的角度分布表，所述角度分布表中包含所述混合固态激光雷达扫描一帧点云发射的各条光线的角度参数；根据所述角度分布表，确定所述混合固态激光雷达发射的各条光线的出射角度；读取仿真场景中所述混合固态激光雷达的属性信息，并基于所述属性信息和所述出射角度，生成所述混合固态激光雷达对应的激光线束；确定所述激光线束击中目标对象表面时生成的点云数据；将所述点云数据渲染到仿真场景中，以实现所述混合固态激光雷达的点云模拟。本申请能够实现对混合固态激光雷达的点云模拟。
",G06F30/20,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"实现, 包括, 装置, 仿真, 方法, 生成, 设备, 模拟","雷达, 激光"
CN118839497A,CN202410873879.4,"本发明提供了一种高精地图数据播发的仿真方法及相关设备，该方法包括：获取车辆实时位置和地图编号；确定地图编号对应的目标数据库文件和目标二进制文件；目标数据库文件中存储有道路信息、切片信息和车道信息；目标二进制文件中存储有车道的坐标数据；依据目标数据库文件和目标二进制文件，对车辆实时位置进行定位，获得定位信息；基于定位信息，在目标数据库文件和目标二进制文件中进行数据搜索，得到路径地图数据；基于路径地图数据进行播发数据封装，获得待播发地图数据；将待播发地图数据发送给数据接收端，实现数据播发仿真。应用本发明的方法，需利用不同格式的地图文件时，无需对仿真程序进行变更，可减少工作量，提高效率。
",G06F30/20,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"提供, 实现, 包括, 仿真, 方法, 设备",车辆
CN118690586A,CN202411172255.6,"本申请公开了一种车辆能耗参数的能耗敏感度确定方法和装置，包括：获取车辆中能耗参数对应的特征值；根据参数测试值和参数平均值，确定能耗参数的优化价值因子；根据第一能耗参数值的能耗仿真结果，以及第一能耗参数值对应的能耗实际值，确定能耗参数的能耗变化因子；根据与能耗参数相关联的能耗系统的成本信息，确定能耗参数的成本因子；根据优化价值因子，能耗变化因子和成本因子，确定能耗参数的能耗敏感度。基于此，通过从多个方面进行综合考量得到能耗敏感度，提高了能耗敏感度确定的准确性，从而为车辆能耗参数的优化提供了科学、有效的决策支持。这有助于降低车辆能耗、提高能效，促进节能减排和可持续发展目标的实现。
",G06F30/20,经纬恒润(天津)研究开发有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"提供, 实现, 包括, 测试, 装置, 仿真, 方法, 系统","车辆, 测试"
CN118690536A,CN202410692251.4,"本申请公开了一种风险等级评估方法及装置。所述方法包括：获取车辆功能故障和车辆行驶环境；根据车辆功能故障和车辆行驶环境，确定车辆危害事件特征；将所述危害事件特征输入到预先构建的车辆动力学模型中，确定危害事件对应的严重度等级和可控性等级；根据所述严重度等级和可控性等级，确定所述危害事件的风险等级。根据本申请实施例，能够有效提高风险等级评估的客观性和准确性。
",G06F30/20,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"方法, 包括, 模型, 装置",车辆
CN118277033A,CN202410437297.1,"本申请实施例公开了一种仿真调度方法、装置、设备及介质，涉及仿真技术领域。该方法包括：通过配置调度信息和任务信息，构建配置文件，调度信息包括时间片信息、任务数量信息和主调度周期信息，任务信息包括任务开始运行的时间片偏移信息、任务运行时间片数量信息和任务周期信息；通过读取配置文件，对配置文件对应的所有任务进行仿真调度。由此，通过对调度信息和任务信息进行配置，使得每一个任务的起始运行时间和可运行的时间段都是确定的，有助于确保任务在仿真环境中按照预定的调度计划执行，从而保证了仿真的准确性和可靠性。
",G06F9/455,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F9/455,"包括, 装置, 仿真, 方法, 设备, 配置",通用
CN118171394A,CN202410446247.X,"本申请公开了一种目标文件的生成方法、装置、设备及存储介质，涉及车辆仿真技术领域。其方法包括：获取目标路面的三维模型，所述目标路面包括至少一个目标对象；对所述三维模型进行解析，得到各所述目标对象的空间分布信息，所述空间分布信息用于表征各所述目标对象在预设网格坐标系中的分布状况；根据所述至少一个目标对象的空间分布信息，生成所述目标路面的目标文件，所述目标文件用于描述所述目标路面中至少一个目标对象的微观特征。根据本申请实施例，能够提升路面模型的仿真精度。
",G06F30/15,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/15,"包括, 模型, 装置, 仿真, 方法, 生成, 设备",车辆
CN117725719A,CN202311390534.5,"本申请公开了一种模型仿真推演的仿真方法及装置，涉及通信技术领域。其方法包括：在体系建模工具端与仿真推演工具端建立访问连接的情况下，体系建模工具端获取待建模仿真装备的装备体系模型的模型参数；将模型参数发送至仿真推演工具端；仿真推演工具端根据模型参数进行仿真想定初始化，得到仿真物理模型，对仿真物理模型进行推演，生成与装备体系模型关联的仿真推演数据，并向体系建模工具端发送仿真推演数据，仿真物理模型与装备体系模型相互映射；体系建模工具端基于预设函数库中的数据调用函数，从仿真推演工具端中调用并显示仿真推演数据。
",G06F30/20,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"包括, 模型, 装置, 仿真, 方法, 生成, 工具",通信
CN117421920A,CN202311470399.5,"本发明公开了一种TAS传感器的仿真方法及装置，包括：采集EPS控制器助力扭矩，将助力扭矩发送给整车动力学模型，得到目标数据，其中，目标数据包括：转向管柱手力矩和方向盘转角，EPS控制器包括：待仿真TAS传感器和执行器；将目标数据发送给伺服电机进行物理加载，获取伺服电机的物理加载量，将物理加载量传递给TAS传感器；获取TAS传感器输出的仿真力矩；将仿真力矩发送给EPS控制器，获取执行电机输出的仿真助力扭矩，将仿真助力扭矩发送给整车动力学模型。上述过程，实现了在无法获取待仿真TAS传感器协议信息的情况下，对待仿真TAS传感器进行仿真，实现了对EPS控制器的测试验证。
",G06F30/20,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"实现, 包括, 模型, 装置, 测试, 仿真, 方法, 控制","测试, 传感器, 验证, 控制"
CN117331324A,CN202311280209.3,"本申请提供了一种车辆转向系统的仿真控制方法和装置，该方法包括：确定用户为仿真出的转向系统模型选择的目标转向驱动模式，转向系统模型配置有多种转向驱动模式，不同转向驱动模式驱动车轮转向的驱动方式不同，目标转向驱动模式属于多种转向驱动模式；基于目标转向驱动模式，确定转向系统模型中执行转向驱动的目标驱动部件，目标驱动部件为转向系统模型中仿真出的转向节或者是转向液压缸；按照目标转向驱动模式对应的驱动方式，控制目标驱动部件执行用于驱动车轮转向的驱动动作，得到转向系统模型的转向仿真结果。本申请的方案能够根据用户需求进行不同模式的车辆转向仿真。
",G05B17/02,经纬恒润(天津)研究开发有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"提供, 包括, 模型, 装置, 仿真, 方法, 控制, 系统, 配置","车辆, 控制"
CN117290944A,CN202311145875.6,"本申请公开了一种电气模块的布置方法、装置、设备、车辆底盘及车辆，涉及车辆检测技术领域，包括：获取多个电气模块的电磁兼容性参数和预设位置信息，以及电气模块之间的预设连接线路，根据各电磁兼容性参数，在多个电气模块中确定干扰电气模块和敏感电气模块，对干扰电气模块、敏感电气模块以及预设连接线路进行仿真分析，评估车辆底盘中各电气模块的电磁兼容性，在多个电气模块的电磁兼容性满足预设条件的情况下，根据位置参数和预设连接线路，将多个电气模块布置在车辆底盘上。本申请使得布置有大量电气模块的车辆底盘可以满足电磁兼容性，提高了车辆底盘中各个电气模块布置的准确性。
",G06F30/15,经纬恒润(天津)研究开发有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/15,"包括, 装置, 仿真, 方法, 设备, 检测","车辆, 检测"
CN116819980A,CN202310409094.7,"本申请公开了一种车辆电气负载仿真方法及装置，其中方法包括：获取车辆的多个负载的电气特性信息，所述电气特性信息包括每个所述负载在多个工作电压下的负载特性；根据所述电气特性信息，构建每个负载的负载仿真模型；根据预获取的所述车辆的车辆模型，对所述负载仿真模型进行仿真，其中，所述车辆模型用于为所述负载仿真模型提供仿真电压，所述负载仿真模型用于向所述车辆模型输出仿真电流。通过上述过程，可以基于每个负载在多个工作电压下的负载特性进行仿真，从而实现对负载在动态工况下的仿真，提高对负载进行仿真的准确性。
",G05B17/02,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"提供, 实现, 包括, 模型, 装置, 仿真, 方法",车辆
CN116520264A,CN202310457616.0,"本申请公开了一种多毫米波雷达同步测试系统及方法，仿真子系统对多个被测毫米波雷达对应的测试场景进行仿真，得到多个被测毫米波雷达对应的目标信息，被测毫米波雷达发射电磁波，雷达模拟器机箱子系统根据接收到的多个被测毫米波雷达对应的多个目标信息，控制对应的回波信号模拟模块和频综模块对多个被测毫米波雷达发射的电磁波信号进行处理，生成多个被测毫米波雷达对应的回波模拟信号，被测毫米波雷达接收对应的回波模拟信号，并根据回波模拟信号得到对应的测试信息，将测试信息传输给ADAS控制器，ADAS控制器基于多个被测毫米波雷达对应的测试信息生成车辆状态控制请求。根据本申请实施例，实现了对多个雷达协同检测的测试。
",G01S7/40,经纬恒润(天津)研究开发有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"实现, 测试, 仿真, 方法, 生成, 处理, 控制, 系统, 检测, 模拟","测试, 雷达, 控制, 车辆, 检测"
CN116522613A,CN202310407275.6,"本申请实施例提供了一种场景文件模板的生成方法、装置，场景文件模板的生成方法包括：获取功能场景信息，从功能场景信息中提取功能场景的多个第一场景要素，根据每个第一场景要素的预设取值范围，分别对第一场景要素进行赋值，得到每个第一场景要素的至少一个取值，基于预设算法对多个设置有取值的第一场景要素进行排列组合，得到多个场景要素组合，根据第一场景要素组合以及功能场景信息，生成符合预设规范的第一场景文件，第一场景要素组合为多个场景要素组合中的任意一个，将第一场景文件中的第一场景要素的取值替换为预设字符，得到场景文件模板。根据本申请实施例，能够得到可用于自动生成场景文件的场景文件模板。
",G06F30/20,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"提供, 包括, 装置, 算法, 方法, 设置, 生成",通用
CN116382121A,CN202310584591.0,"本申请公开了一种车辆仿真方法、装置、存储介质和设备，该方法为：采集摇杆机械响应用户触发的操作行为所生成的控制信号；基于控制信号，确定车控变量；基于车控变量，以及仿真车辆的当前车速、当前转角，生成动力学仿真信号；从高精度地图中，获取与当前位置坐标对应、且与当前姿态角对应的路面信息；将路面信息和动力学仿真信号，导入到仿真车辆的动力学模型中，得到动力学模型输出的最新车辆状态信息；基于高精度地图，以及最新车速、最新转角、最新位置坐标、最新姿态角，生成仿真车辆行驶过程的仿真动画，并通过预设界面向用户展示仿真动画。该方法可针对不同路面场景进行仿真，从而确保实现针对车辆摇杆控制算法持续迭代的车辆仿真。
",G05B17/02,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"实现, 模型, 装置, 算法, 仿真, 方法, 生成, 设备, 控制","车辆, 控制"
CN116341185A,CN202211655877.5,"本申请公开了一种高精地图仿真方法和系统，该方法包括：基于地图处理模块对高精地图进行仿真，并将仿真后的高精地图发送至融合模块和本地场景生成模块；本地场景生成模块在高精地图的仿真场景中，模拟仿真智能车辆在测试用例场景中的交通场景信息和行驶的驾驶行为信息，并将交通场景信息和驾驶行为信息分别发送至融合模块和自动驾驶域控制器；融合模块将交通场景信息和驾驶行为信息融合至高精地图中，并将融合后的信息发送给数据播发模块；数据播发模块将融合后的信息发送至自动驾驶域控制器；自动驾驶域控制器基于融合后的信息进行仿真测试，以提升仿真高精地图构建的精确性和高效性，且为SOA新架构车型的高级智能驾驶算法验证提供有力保障。
",G06F30/20,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"提供, 包括, 测试, 算法, 仿真, 方法, 生成, 处理, 控制, 系统, 模拟","驾驶, 测试, 验证, 控制, 车辆"
CN116305744A,CN202211606741.5,"本申请实施例提供了一种传感器控制系统及传感器的监听方法，包括传感器管理模块，传感器管理模块包括真实传感器模型和虚拟传感器模型：传感器管理模块用于获取目标应用程序的注册请求，在生成注册成功的消息的情况下，读取目标传感器的传感器数据，真实传感器模型用于在目标传感器为真实传感器的情况下，读取目标传感器的传感器数据，虚拟传感器模型用于在目标传感器为虚拟传感器的情况下，通过与虚拟传感器关联的真实传感器读取虚拟传感器的数据，真实传感器模型包括第一引用计数单元，虚拟传感器模型包括第二引用计数单元。根据本申请实施例，将不同传感器抽象成相同的模型，使用统一的控制方法进行控制。
",G06F30/20,经纬恒润(天津)研究开发有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"提供, 包括, 模型, 方法, 生成, 控制, 系统","传感器, 控制"
CN115906481A,CN202211462917.4,"本申请公开了一种电磁阀故障检测方法和装置。该电磁阀故障检测方法包括：基于电磁阀的电路原理，构建电磁阀模型，对电磁阀模型进行简化处理，得到电磁阀模型对应的非线性电磁阀模型，将故障数据输入至非线性电磁阀模型中，得到电磁阀的异常检测量，其中，故障数据包括第一线圈电阻和第一弹簧刚度，且第一线圈电阻和第一弹簧刚度均不为0，将雅可比矩阵和异常检测量输入至预先构建的卡尔曼滤波器中，得到电磁阀的故障结果，其中，故障结果包括：发生故障的部件的参数信息，雅可比矩阵为将非线性电磁阀模型进行线性化处理得到的。以有限的可以实时测量到的数据实现对电磁阀的故障状态、故障位置和故障程度的快速准确检测的效果。
",G06F30/20,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"实现, 包括, 模型, 装置, 方法, 处理, 检测, 电路",检测
CN115329461A,CN202211033207.X,"本发明提供了一种车辆建模方法、装置、设备和存储介质，可以分别获取铰接在一起的每个车体的动力学方程，然后基于主动力矩和空间耦合力的关系对第二车体的动力学方程进行处理，得到第一车体和第二车体的第一关联方程，其中主动力矩为第一车体运行时对第二车体所产生力矩。再基于第一车体和第二车体间的运动关系对第一关联方程进行处理，得到第一车体和第二车体的第二关联方程。最后将第二关联方程和第一车体的动力学方程合成为一个动力学方程进而得到第一车体的加速度向量。基于第一车体的加速度向量对第二车体的动力学方程进行求解，得到第二车体的加速度向量。能够大幅减少计算量和程序的代码量，有效减少模型开发人员的工作量。
",G06F30/15,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/15,"提供, 模型, 装置, 方法, 处理, 设备, 计算",车辆
CN115033991A,CN202210668660.1,"本发明实施例公开一种用于智能驾驶汽车仿真的毫米波雷达模拟方法，该方法包括：获取仿真场景中的环境数据；环境数据中至少包括毫米波雷达感知范围内的所有物体的材质网格参数；根据雷达方向图，确定毫米波雷达在发射角度上的发射强度；将雷达方向图转换为灰度图；根据灰度图中的每个像素点和毫米波雷达的发射强度，发射毫米波；基于光线追踪算法获取仿真场景中，通过像素点的毫米波与物体相交的各击中点的结构体信息；根据各结构体信息，获取各击中点对应的击中点信息；根据击中点信息，得到模拟毫米波雷达输出的目标信息；目标信息至少包括目标距离、目标速度、目标方位角、目标峰值功率、置信度以及距离信噪比。
",G06F30/15,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/15,"包括, 算法, 仿真, 方法, 模拟","雷达, 驾驶, 汽车"
CN114357616A,CN202111642202.2,"本发明提供一种交通流仿真方法和相关设备，在交通流仿真过程中，通过场景状态量实时调整仿真车辆在下一仿真步长的运动状态，并且基于车辆控制参数以及预设的车辆运动状态限制条件对车辆下一仿真步长的运动状态进行校正，使得车辆的运动状态更加符合实际车辆，提高了交通流仿真结果的可靠性。
",G06F30/15,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/15,"提供, 仿真, 方法, 设备, 控制","车辆, 控制"
CN114089294A,CN202111345065.6,"本发明实施例公开一种雷达的同步测试系统和方法，该系统包括：仿真控制器、目标模拟器和至少两个待测雷达；其中，仿真控制器，用于将同一时刻各虚拟雷达探测到的仿真目标信息同时发送给目标模拟器；目标模拟器，包括：模拟控制单元和目标模拟单元；其中，模拟控制单元，用于接收仿真目标信息，对于任意一个虚拟雷达探测到的不同目标物体，将其对应的不同仿真目标信息发送到对应的目标模拟单元；目标模拟单元，用于根据接收到的仿真目标信息和对应待测雷达的发射信号，对虚拟雷达探测到的目标物体进行模拟，并将模拟得到的模拟目标信息发送给对应的待测雷达，以对待测雷达进行功能测试。通过采用上述技术方案，实现了对多个雷达的同步测试。
",G01S7/40,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"实现, 包括, 测试, 仿真, 方法, 控制, 系统, 模拟","雷达, 测试, 控制"
CN113946956A,CN202111202343.2,"本申请提供一种代客泊车仿真方法及装置，通过将仿真开放停车场地图实时地逆向转化为泊车控制器可识别的目标特征，进而基于目标模拟车辆的定位位置将其所在区域内的目标特征播发至泊车控制器并在该区域内对播发的目标特征实时绘制重构，以此获得目标模拟车辆在其所在区域内的通用开发停车场地图，将该通用开发停车场地图下载更新至泊车控制器，以供泊车控制器路径规划使用。这就可以解决L4级代客泊车系统如何在仿真系统中进行算法验证的问题，为L4级代客泊车系统的算法验证提供可靠的保障。
",G06F30/20,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"提供, 装置, 算法, 仿真, 方法, 控制, 系统, 模拟","车辆, 验证, 控制"
CN113032963A,CN202110216136.6,"本发明提供了一种Simulink模型仿真加速方法及装置，方法包括预先生成可在计算机操作系统中运行的可执行程序文件，该可执行程序文件包含参数赋值代码、数据记录代码、程序调度代码和Simulink模型的代码；根据预先设定的Simulink模型任务与计算机多核CPU核心的对应关系，使Simulink模型的各个任务运行在计算机多核CPU的对应核心中。通过将一个规模较大的Simulink模型拆分为多个任务，并生成可在计算机操作系统中运行的可执行程序文件，在多核CPU中进行并行运算，相比于采用并行工具箱在Simulink环境下进行Simulink模型仿真加速的方式，可显著加快Simulink模型多次仿真速度。
",G06F30/20,北京经纬恒润科技股份有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"提供, 包括, 模型, 装置, 仿真, 方法, 生成, 计算, 系统, 工具",通用
CN111930026A,CN202010842387.0,"本发明公开了一种测试方法及装置，该方法包括：在预设条件下触发智能驾驶孪生算法，智能驾驶孪生算法与智能驾驶算法的算法模型一致但触发条件不同；获取实车传感器数据输入车辆模型和实车；基于实车传感器数据，采用智能驾驶孪生算法对车辆模型进行驱动；基于车辆模型的运行情况和实车情况生成测试结果。该测试方法及装置，采用真实的实车场景和传感器作为功能验证数据的来源，使其具备有优于仿真测试验证的准确性，且其实现不需要构造实车场景，避免了大量人力、物力的投入；该实现可部署在量产的车辆控制器产品中，然后收集量产车辆实际功能的执行情况，得到测试结果，具备成本小且准确性高的优点。
",G05B17/02,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"实现, 包括, 模型, 装置, 算法, 测试, 仿真, 方法, 生成, 控制","驾驶, 测试, 验证, 控制, 车辆, 传感器"
CN111859612A,CN202010512095.0,"本发明公开了一种激光雷达仿真方法以及装置，该方法包括：S10，根据场景模型的第一动态属性数据从场景模型中筛选出与激光碰撞的场景模型；S20，通过场景模型的静态属性数据和数据索引结构遍历与激光相交的场景模型的网格以计算与激光相关联的碰撞点，其中，数据索引结构用于管理场景模型中的网格，静态属性数据包括场景模型中的网格之间的相对位置。上述技术方案至少能够提高激光雷达仿真算法的运行效率。
",G06F30/20,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"包括, 模型, 装置, 算法, 仿真, 方法, 计算","雷达, 激光"
CN111797526A,CN202010624033.9,"本发明提供了一种仿真测试场景构建方法及装置，本发明中构建仿真测试场景所需的预设时间段内的车辆运行信息、车辆所在场景信息、车辆模型初始运行状态信息以及被测算法初始执行状态信息都是从实际车辆运行目标场景中提取的，本发明是基于实际车辆运行中的场景数据孪生构建仿真测试场景，提高了仿真测试场景与实际车辆运行场景的匹配度，构建的仿真测试场景更加符合实际行车状况，从而在利用仿真测试场景进行测试的过程中，能够减小仿真测试场景失真对测试结果的影响，提高仿真测试结果的准确性。
",G06F30/20,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"提供, 测试, 模型, 装置, 算法, 仿真, 方法","车辆, 测试"
CN111674373A,CN202010573370.X,"本申请公开了一种制动踏板感模拟器及制动踏板感调节方法。该方法包括：在启动常规制动后，控制第一电磁阀导通；获取制动踏板行程期望值，如果制动踏板行程期望值大于预设的行程阈值，则根据当前制动踏板感模式，确定制动踏板行程期望值对应的目标踏板力，进而确定与目标踏板力对应的目标压力，根据第二腔体的实际压力和目标压力生成控制信号，基于控制信号调整第二电磁阀的导通占空比，以调整第二腔体内的液压力，从而产生与用户需求匹配的踏板感。本申请提供的方案，在不同的制动踏板感模式下，基于控制信号控制第二电磁阀的导通占空比，使得第二腔体的实际压力得到调节，实现主动调节制动踏板感，满足用户个性化驾驶体验需求。
",B60T7/04,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"提供, 实现, 包括, 方法, 生成, 控制, 模拟","驾驶, 控制"
CN111597636A,CN202010411652.X,"本发明公开了一种控制器变体识别方法及系统，方法包括：在控制器上电时，判断控制器的第一存储器中是否存储有控制器的变体信息，若否，则：获取控制器的第一配置引脚信息；根据预先配置的变体信息与引脚信息之间的对应关系，确定与第一配置引脚信息对应的变体信息；将与第一配置引脚信息对应的变体信息确定为控制器的变体信息。本发明能够通过对控制器进行变体识别，实现在不更改软件的情况下，使控制器在一辆车中可以多次使用，以及在一辆车中使用多个控制器，有效解决的现有控制器软件管理复杂度高、控制器壳体模具设计复杂性高以及成本高的问题。
",G06F30/15,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/15,"实现, 包括, 方法, 控制, 系统, 配置",控制
CN111474865A,CN202010242698.3,"本发明提供了一种智能车载终端控制器休眠唤醒测试系统，包括直流电源、仿真平台和上位机；仿真平台为待测智能车载终端控制器提供唤醒控制信号和休眠控制信号，并采集其电流；上位机接收用户输入的测试参数，并在接收用户输入的开始测试命令后，根据待测智能车载终端控制器的电流确定其所处状态，且根据其所处状态控制模拟休眠唤醒源的状态，以按照用户输入的测试参数对待测智能车载终端控制器进行休眠唤醒测试。本发明提供的上述智能车载终端控制器休眠唤醒测试系统，只需要用户输入测试参数和开始测试命令，则对待测试智能车载终端控制器进行休眠唤醒的自动测试，相比于手动测试，缩短了测试时间，降低了人工成本，以及避免了人工测试的错误。
",G05B17/02,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"提供, 平台, 包括, 测试, 仿真, 控制, 系统, 模拟","测试, 控制"
CN111123740A,CN201911319738.3,"本发明公开了一种硬件在环测试方法以及装置，该方法包括：接收车辆位置信号；根据预设的车辆行驶前瞻距离和车辆位置信号确定测试所需的虚拟地图信号；将测试所需的虚拟地图信号和车辆仿真信号发送至下位机，以使下位机对待测件进行测试；其中，上位机中运行的功能模块包括地图信号模拟模块，所地图信号模拟模块为预先配置；测试所需的虚拟地图信号记录于地图信号模拟模块中。通过本发明的技术方案，至少能够不依赖于地图盒子，不需要实车在真实道路上进行测试，从而降低成本。
",G05B17/02,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,3.0,关键词匹配: 硬件在环; IPC分类号匹配: G05B17/02,"模拟, 包括, 测试, 装置, 仿真, 方法, 配置","车辆, 测试"
CN109991477A,CN201910313148.3,"本发明实施例提供自放电电阻检测方法和装置，以同时确定多节(N节)电池的自放电电阻，扩大适用范围。在本发明实施例中，基于对称环形电路结构对N节电池进行自放电电阻的检测，在检测时会获取N节电池的电动势和内阻，流经每一电池的分支电流，以及对称环形电路结构中正极环路和负极环路之间的电压U<Sub>0</Sub>，根据分支电流、电压U<Sub>0</Sub>，以及N节电池的电动势和内阻计算各电池的自放电电阻。由于可计算出各电池的自放电电阻，那么无论是一节还是一节以上的电池存在自放电现象，都可以根据自放电电阻确定出存在自放电现象的电池，与现有的自放电检测方式相比，扩大了适用范围。
",G01R27/08,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 环路,"提供, 装置, 方法, 检测, 计算, 电路",检测
CN109782630A,CN201910228709.X,"本发明提供自动泊车仿真测试方法及系统，以降低测试成本、提高工作效率。在本发明实施例中，利用动画仿真平台搭建测试场景，利用自动测试平台搭建测试脚本，在自动测试阶段，由自动测试平台根据测试脚本和泊车控制器的车辆控制命令，通过人机交互平台对车辆动力学模型的运行参数进行控制，并生成测试报告，可实现仿真测试的自动化。使用本发明实施例所提供的技术方案，并不需要实车参与，同时测试过程是由自动测试平台自动执行的，因此可在降低测试成本的同时，提高工作效率。
",G05B17/02,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"提供, 平台, 实现, 测试, 模型, 仿真, 方法, 生成, 控制, 系统","车辆, 测试, 控制"
CN109031345A,CN201810607382.2,"本发明提供了一种MEMS微镜扫描激光雷达系统及其扫描方法，包括激光发射器、激光接收器、环路器、MEMS微镜和时序控制与测量模块，激光发射器发射激光；环路器将激光传输至MEMS微镜；MEMS微镜将环路器输出的激光反射至目标区域，将目标区域内障碍物的反射光反射回环路器；环路器将MEMS微镜反射的障碍物的反射光传输至激光接收器；激光接收器根据接收到的障碍物的反射光生成回波信号；时序控制与测量模块根据激光接收器生成的回波信号和MEMS微镜反馈的角度信息生成扫描结果，从而可以在实现激光雷达系统高分辨率的基础上，减少激光雷达系统中激光接收器的个数，降低激光雷达系统的成本。
",G01S17/93,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 环路,"提供, 实现, 包括, 方法, 生成, 控制, 系统","雷达, 激光, 控制"
CN108327694A,CN201810174303.3,"本发明公开了一种踏感模拟器及线控液压制动系统中的电动助力器，踏感模拟器包括壳体，其包括同轴设置且依次连接的第一腔体和第二腔体；滑动设置于第一腔体内的第一活塞，滑动设置于第二腔体内的第二活塞；第一活塞和第二活塞同轴设置，第一活塞的一端为用于与制动踏板顶杆连接的连接端，第二活塞的一端为能够推动制动主缸顶杆的推动端，第一活塞的另一端与第二活塞的另一端相互靠近且具有间隙L；设置于第一活塞与第二活塞之间的第一压缩弹簧；设置于第二腔体内用于支撑第二活塞的第二压缩弹簧；第二压缩弹簧的预紧力大于第一压缩弹簧的预紧力，第二活塞的另一端抵压于第一腔体和第二腔体的连接处。踏感模拟器提高能量回收率及舒适性。
",B60T8/40,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"包括, 具有, 设置, 系统, 模拟",通用
CN105912751A,CN201610203214.8,"本发明公开了一种模型联合仿真的方法及系统，在PC机中进行ControlBuild模型仿真，并将该模型中所有的数据都映射到对应的共享内存上，在根据共享内存上的描述文件对Simulink模型进行配置后，将配置完成的Simulink模型下载至实时仿真机构，通过在PC机中建立服务端接口，在实时仿真机构中建立与服务端接口通信的客户端接口，实现ControlBuild模型和Simulink模型之间的信息交互。由于ControlBuild模型运行在作为非实时系统的PC机中，Simulink模型运行在作为实时系统的实时仿真机构中，因此，通过服务端接口和客户端接口间的数据交互实现实时仿真和非实时仿真的有效结合。
",G06F17/50,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 实时仿真,"实现, 模型, 仿真, 方法, 系统, 配置",通信
CN105677941A,CN201510996518.X,"本申请提供了一种多通道目标和干扰模型仿真的方法及模拟器，接收实际场景中包含的运动信息和待生成的仿真模型的仿真参数，再根据所述运动信息建立仿真类型和运动类型；识别得到每个通道对应的仿真类型和运动类型的种类要求，以及每个通道对应的仿真设置参数；将接收到的所述仿真参数和建立的所述仿真类型和运动类型，依据每个通道对应的仿真类型和运动类型的种类要求，以及每个通道对应的仿真设置参数，为每个通道进行分配；利用分配得到的仿真类型、运动类型和仿真设置参数对每个通道进行仿真。本发明的仿真方法及模拟器可以同时对多个通道进行仿真工作，以及对多个单通道仿真模型进行快速修改，与现有技术相比，减少了工作量，提高了仿真效率。
",G06F17/50,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"提供, 模型, 仿真, 方法, 设置, 生成, 模拟",通用
CN105425614A,CN201510998479.7,"本发明实施例提供一种目标跟踪系统的测试验证方法、装置及系统，该方法包括：对目标及背景进行动态仿真，形成动态仿真结果；对运动载体进行运动仿真，确定相应的运动参数，以便物理效应模拟设备进行相应运动；对所述动态仿真结果进行成像；在目标跟踪系统探测成像中的目标及背景信息，识别目标及背景信息中的目标信息，及感应物理效应模拟设备的运动信息，且目标跟踪系统根据目标信息和运动信息生成控制指令，并根据控制指令控制探测方向维持对准成像中的目标后，测试所述目标跟踪系统对目标信息的识别准确度，及维持探测方向对准目标的准确度，得出测试验证结果。本发明实施例提升了目标跟踪系统的跟踪性能的测试验证结果的准确度。
",G05B17/02,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"提供, 包括, 测试, 装置, 仿真, 方法, 生成, 设备, 控制, 系统, 模拟","测试, 验证, 控制"
CN104615008A,CN201410708701.0,"本发明提供一种自动紧急刹车AEB系统的测试结果分析方法和系统，方法应用于AEB系统中，AEB系统包括电子控制单元ECU，还包括硬件回路HIL台架，HIL台架与ECU连接，用于对ECU进行仿真测试，方法包括：获取HIL台架中保存的，对ECU仿真测试完成后得到的仿真数据；对仿真数据进行预处理，获得有用数据；依据欧盟新车认证程序/中心Euro NCAP的评分规则和获得的有用数据，计算AEB系统的得分。因此，本发明基于HIL完成对ECU的仿真测试，进而依据HIL完成仿真测试后保存的仿真数据进行AEB系统评分，减少了实车路测的次数，无需专业的测试场地及测试设备，相较于现有技术有效地降低了测试成本。
",G05B17/02,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,3.0,关键词匹配: HIL; IPC分类号匹配: G05B17/02,"提供, 包括, 测试, 仿真, 方法, 处理, 设备, 控制, 系统, 计算","测试, 控制"
CN104504203A,CN201410817616.8,"本发明公开了飞行仿真模拟器的信息处理方法及装置，在检测到驾驶杆移动时，不仅确定驾驶杆在当前移动方向上的偏转角度，还要确定驾驶杆的动作频率，当驾驶杆的动作频率超出预设的频率区间时，确定驾驶杆的动作为误操作，不再执行后续控制舵机运行的操作，从而降低操控人员做出误操作对飞行造成不良影响的概率；当驾驶杆的动作频率位于预设的频率区间时，进一步判断驾驶杆的偏转角度是否在预设的角度区间内，如果驾驶杆的偏转角度超出了频率区间，则相应的依据该频率区间的上限值或者下限值控制舵机的运行，避免出现飞机的飞行姿态迅速发生较大变化的情况，提高飞机的稳定性。
",G06F17/50,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"装置, 仿真, 方法, 处理, 控制, 检测, 模拟","驾驶, 检测, 控制"
CN104460349A,CN201410705507.7,"本申请提供了一种实车在回路仿真测试方法、实时仿真机及系统，包括：实时仿真机仿真模拟交通环境；接收汽车定位装置获取的实车的第一运动状态信息；依据第一运动状态信息、模拟交通环境，计算出控制器传感器信号；发送控制器传感器信号至实车内的被测控制器；获取被测控制器针对控制器传感器信号输出的控制信号、实车控制器响应控制信号后输出的信号，接收实车的第二运动状态信息和交通环境等信息，作为被测控制器的性能测试的分析依据。本申请提供的实车在回路仿真测试方法与传统的HIL测试相比提高了被测控制器的性能测试结果的精确度，与实车测试相比，节约成本，降低测试风险，增强了测试的复现性。
",G05B17/02,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,4.0,"关键词匹配: HIL, 实时仿真; IPC分类号匹配: G05B17/02","提供, 包括, 测试, 装置, 仿真, 方法, 控制, 系统, 计算, 模拟","测试, 汽车, 传感器, 控制"
CN104317640A,CN201410594514.4,"本申请公开了一种航电设备的仿真系统和仿真方法，包括：DD模型、POP模型和数据交互网络，DD模型中预定义有第一数据接口，POP模型中预定义有数据接口，DD模型通过第一数据接口与数据交互网络相连接，POP模型通过第二数据接口与数据交互网络相连接。这样，DD模型将逻辑数据发布到数据交互网络，POP模型从数据交互网络中读取逻辑数据，DD模型和POP模型不再直接对接，不再是强耦合的整体，数据接口变化时只需要修改数据接口程序，对DD模型和POP模型进行修改时，也不会对数据接口造成影响，可以增强DD模型和POP模型的通用性和复用性，增强联合仿真的扩展性和灵活性，并且并行开发，提高开发效率。
",G06F9/455,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F9/455,"包括, 模型, 仿真, 方法, 设备, 系统",网络
CN104238372A,CN201410460674.X,"本发明公开了一种多个模型并行仿真的调度方法和装置。所述方法包括：设置所述多个模型中每个模型的调度周期；计算所述多个模型中所有模型的调度周期的最大公约数和最小公倍数；计算所述最小公倍数对所述最大公约数的商，作为总帧数；所述多个模型中的每个模型的调度周期分别对所述最大公约数做商，得到每个模型的第一参数；从第一帧到最后一帧，分别用当前帧的帧序列号分别对每个模型的第一参数进行取余计算，如果结果为0，则将对应的模型的信息放置到当前帧中。本发明实现了对并行仿真的多个模型的合理调度，实现了数据的实时仿真功能。
",G05B17/02,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,3.0,关键词匹配: 实时仿真; IPC分类号匹配: G05B17/02,"实现, 包括, 模型, 装置, 仿真, 方法, 设置, 计算",通用
CN103645933A,CN201310648104.9,"本申请公开了一种控制方法及装置，所述方法包括：获取飞行目标的预设飞行剖面数据；依据所述飞行剖面数据，生成所述飞行目标的运动类型模块库；其中，所述运动类型模块库中包括多个运动类型模块，所述运动类型模块之间顺次相连，且相邻两个运动类型模块之间具有运动切换条件；依次控制所述飞行目标按照每个所述运动类型模块的运动参数执行对应的动作；当所述飞行目标执行动作的当前动态参数满足其当前运动类型模块与下一运动类型模块之间的运动切换条件时，控制所述飞行目标按照所述当前运动类型模块的下一运动类型模块的运动参数执行对应的动作；依据所述飞行目标执行的动作，生成所述飞行目标的飞行场景模型。
",G06F9/455,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F9/455,"包括, 模型, 装置, 具有, 方法, 生成, 控制",控制
CN103499453A,CN201310503779.4,"本发明实施例公开了一种电子稳定程序ESP液压制动系统的建模方法，包括：采用电磁阀建模方法对所述ESP液压制动系统中的各个进压阀、泄压阀、吸入阀和各个导向阀中的比例电磁阀分别进行建模，采用溢流阀建模方法对所述各个导向阀中的限压溢流阀分别进行建模，采用单向阀建模方法对所述ESP液压制动系统的单向阀分别进行建模，采用液压泵建模方法对所述ESP液压制动系统的液压泵分别进行建模，采用液压容腔建模方法对所述ESP液压制动系统的连接管道分别进行建模，采用可变体积液压容腔建模方法对所述ESP液压制动系统的制动轮缸和低压蓄能器分别进行建模，以建立一种与所述ESP液压制动系统相符的实时仿真模型。
",G01M17/007,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 实时仿真,"包括, 模型, 仿真, 方法, 系统",通用
CN103413008A,CN201310381897.2,"本发明公开了一种基于分布式I/O接口的实时仿真系统，包括：主机系统和两个或两个以上的I/O接口系统；其中：主机系统通过可远程传输的总线与I/O接口系统连接。本发明通过采用可远程传输的总线将主机系统和I/O接口系统连接，能够实现在仿真系统中只需一个主机系统，无需对原有的物理系统的数字模型进行拆分和改造；能够实现一个主机系统与多个I/O接口系统连接，方便对I/O接口系统进行扩展；能够实现将I/O接口系统靠近外部设备端完成信号的处理和转换，提高信号的质量。
",G06F17/50,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 实时仿真,"实现, 包括, 模型, 仿真, 处理, 设备, 系统",通用
CN103020401A,CN201310003880.3,"本发明公开了一种待测ECU的测试方法，用于HIL测试设备，还提供了一种ECU测试装置，及一种ECU测试系统。本发明将测试人员采用模拟软件的方式将需要对需求文档进行大量的分析计算过程集成仿真ECU中，通过将模拟同一测试环境的信号输入至仿真ECU和待测ECU对测试环境，得到待测ECU和仿真ECU输出的实时测试数据，将两者的实时测试数据进行对比，并得到最终的对比结果，避免了测试人员对需求文档中由输入条件至需求结果的数据进行大量的分析计算，解决了测试人员在进行HIL测试时工作量大，难于得出HIL测试的结果的问题。
",G06F17/50,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: HIL,"提供, 测试, 装置, 仿真, 方法, 设备, 系统, 计算, 模拟",测试
CN102929159A,CN201210469542.4,"本发明公开了一种仿真模型状态控制方法与装置，应用于仿真系统中，其中仿真系统包括上位机、下位机和实现上位机和下位机通信的ORB（Object Request Broker，对象请求代理），该方法包括：接收上位机发送的状态控制命令；从状态控制命令中获取下位机参数，依据下位机参数，匹配与上位机通信的下位机；发送状态控制命令至匹配到的下位机。在本发明中，上位机与下位机之间的通讯通过ORB实现，多个上位机发送状态控制命令控制多个下位机动作时，只需利用ORB将状态控制命令发送至与状态控制命令中下位机参数相匹配的下位机，便完成发送状态控制命令至下位机的过程，不再拘束于上位机与下位机之间的通讯协议，实现多个上位机同时控制多个下位机的功能。
",G05B17/02,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"实现, 包括, 模型, 装置, 仿真, 方法, 控制, 系统","通信, 控制"
CN102799114A,CN201210303652.3,"本发明公开了一种信号调理设备，包括：控制计算机，与控制计算机相连的主控制器模块，至少两个调理模块，调理模块内对应不同信号调理类型的调理板卡，所述主控制器模块与所述调理模块设置于背板上，背板上还设置有控制总线，主控制器模块通过控制总线与至少两个调理模块相连。所述控制计算机接收用户下发的调理指令，发送给主控制器模块，主控制器模块接收并解析所述调理指令，发送给预先配置好的与所述调理指令相对应的调理模块，该调理模块接收调理指令，并进行相应的信号调理。本发明中，计算机根据实际需要对信号调理板卡实时进行模式切换和参数设置，实现了在设备不断电的情况下实现动态的信号调理功能。
",G05B17/02,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"实现, 包括, 设置, 设备, 控制, 计算, 配置",控制
CN102622924A,CN201210084696.1,"本发明公开了一种方向盘限位装置，用于驾驶模拟器，包括：底座；用于连接方向盘和力反馈电机的转轴；设置于所述转轴径向的拨杆；可转动地设置于所述底座上的限位旋转盘，所述限位旋转盘的圆周面上开设有若干与所述拨杆拨动配合的拨槽，所述拨槽的数量为所述方向盘的目标旋转圈数减一，位于左侧的波槽的左侧形成第一限位边界，位于右侧的波槽的右侧形成第二限位边界。本发明实施例提供的方向盘限位装置，当方向盘旋转角度达到极限时，拨杆被卡在第一限位边界或第二限位边界，从而使方向盘不能继续旋转，达到了限位的目的。本发明还公开了一种具有上述方向盘限位装置的驾驶模拟器。
",G09B9/04,北京经纬恒润科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"提供, 包括, 装置, 具有, 设置, 模拟",驾驶
