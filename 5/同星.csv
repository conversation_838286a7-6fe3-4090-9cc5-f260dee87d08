﻿公开(公告)号,申请号,摘要(中文),原始申请人
CN120499168A,CN202510846108.0,"本发明属于软件部署技术，具体涉及一种桌面软件在软件平台中的跨平台交互方法及系统，所述跨平台交互方法包括：将软件平台作为服务端，在服务端构建独立的宿主服务进程，所述宿主服务进程提供HTTP超文本传输协议服务；所述宿主服务进程将桌面软件的运行界面显示在所述宿主服务进程设置的界面框架的子窗口位置区域中；所述宿主服务进程还为子窗口设置截屏缓冲区，以保存所述子窗口的截图，并将子窗口的截图发送至客户端，以在客户端通过网页显示并发布；网页显示的截图被触发时，所述宿主服务进程通过客户端获取触发行为，以执行截图对应的运行界面的操作。
",上海同星智能科技有限公司
CN120498437A,CN202411359402.0,"本发明属于PWM信号输出控制技术，具体涉及一种基于浮地驱动的信号输出控制电路、装置和信号输出控制方法，其中基于浮地驱动的信号输出控制电路包括：构成推挽电路的MOS管高压输出单元和MOS管低压输出单元；其中MOS管高压输出单元，由第一电源供电，并对接入的PWM信号依次进行浮地隔离、浮地放大，以在PWM信号的低电平区间使得PMOS管导通，从而持续输出与第一电源电压大小相近的高压信号；以及MOS管低压输出单元，设置有接地端，并对接入的PWM信号依次进行隔离、放大，以在PWM信号的高电平区间使得NMOS管导通，从而持续输出低压信号；MOS管高压输出单元与MOS管低压输出单元的输出端相连以输出放大后的PWM信号。
",上海同星智能科技有限公司
CN120492056A,CN202510540552.X,"本发明涉及计算机软件技术，具体涉及一种兼容多平台多语言的解密库文件调用方法及系统，其中解密库文件调用方法包括：宿主进程作为调用方，在触发解密函数调用后，先判断该解密函数所对应的函数库是DotNet类型的库文件还是Win32类型的库文件；若所述函数库是DotNet类型的库文件，则宿主进程将通过启动第一代理进程来调用解密函数，且第一代理进程调用完成后将结果反馈至宿主进程；以及若所述函数库是Win32类型的库文件，则再判断宿主进程当前运行平台和函数库的版本是否匹配，若匹配则宿主进程直接调用解密函数，否则宿主进程将通过启动第二代理进程或第三代理进程来调用解密函数，且第二代理进程或第三代理进程调用完成后将结果反馈至宿主进程。
",上海同星智能科技有限公司
CN120499169A,CN202510846114.6,"本发明涉及软件部署技术，具体涉及一种软件平台运行窗口的跨平台映射与交互方法及系统，软件平台运行窗口的跨平台映射与交互方法包括：在服务端构建独立的服务进程，该服务进程提供HTTP超文本传输协议服务；所述服务进程与至少一个软件进程进行实时通信，以获取软件进程中运行窗口的相关信息；所述服务进程为所述运行窗口设置截屏缓冲区，以保存运行窗口的截屏图像，并将运行窗口的截屏图像发送至客户端，以在客户端通过网页显示并发布；网页显示的截屏图像被触发时，所述服务进程通过客户端获取触发行为，并生成相应的操作指令至软件进程，以通过软件进程执行截屏图像对应的运行窗口的操作。
",上海同星智能科技有限公司
CN120492390A,CN202410896044.0,"本发明属于总线设备与计算机设备之间的通信技术领域，具体涉及一种总线设备与计算机设备之间的通信方法及系统，其中总线设备与计算机设备之间的通信方法包括：根据总线设备对外的物理接口类别和数量在总线设备内部枚举多个USB‑HID接口；构建各USB‑HID接口与各物理接口之间的分配关系；当计算机设备与总线设备连接后，识别各USB‑HID接口，并根据分配关系在各USB‑HID接口收发相应物理接口的数据。
",上海同星智能科技有限公司
CN120491932A,CN202510199589.0,"本发明涉及一种嵌入式代码模块的内存协商方法及协商系统，其中嵌入式代码模块的内存协商方法，包括：步骤S01，设置代码包；所述代码包包括：内存池管理模块、模块管理器和至少一个代码模块；其中设置代码模块的方法包括为代码模块配置内存协商回调函数；步骤S02，所述内存池管理模块通过所述模块管理器将内存池初始化参数传输至各所述代码模块；以及步骤S03，所述代码模块通过所述内存协商回调函数对内存池初始化参数和自身所需要的内存池参数进行协商，若协商成功，则将协商后的内存池参数反馈至所述模块管理器，若协商失败，则重复步骤S02和步骤S03，以进行下一轮协商。
",上海同星智能科技有限公司
CN120489147A,CN202510370192.3,"本发明属于惯性测量技术领域，具体涉及一种惯性测量单元量程自适应调整方法及量程自适应调整系统，惯性测量单元量程自适应调整方法包括：实时采集惯性测量数据，并根据惯性测量数据获得各惯性测试数据的矢量和；设置各惯性测试数据的矢量和的阈值；以及将各惯性测试数据的矢量和与相应的阈值进行比较，并根据比较结果执行相应的量程切换操作。
",上海同星智能科技有限公司
CN120499029A,CN202411535045.9,"本发明属于以太网报文抓包技术，具体涉及一种基于FPGA的以太网抓包装置、以太网仿真测试系统和方法，其中基于FPGA的以太网抓包装置包括：第一PHY收发器，被配置为接收来自第一通信设备发送的第一以太网信号，并将第一以太网信号经第二PHY收发器发送至第二通信设备；第二PHY收发器，被配置为接收来自第二通信设备发送的第二以太网信号，并将第二以太网信号经第一PHY收发器发送至第一通信设备；FPGA模块，被配置为至少包括第一MAC控制器和第二MAC控制器；第一MAC控制器，仅与第一PHY收发器对应，并经第一PHY收发器抓取第一通信设备发送的第一以太网信号；以及第二MAC控制器，仅与第二PHY收发器对应，并经第二PHY收发器抓取第二通信设备发送的第二以太网信号。
",上海同星智能科技有限公司
CN120499035A,CN202510122626.8,"本发明属于通讯测试技术领域，具体涉及一种总线报文干扰的方法及基于FPGA的干扰仪，其中总线报文的干扰方法包括：设定报文干扰精度的脉宽时间为T；判断待干扰位置的前一个脉宽时间T的报文的显隐性状态；以及根据显隐性判断结果对待干扰位置的报文执行相应干扰操作。
",上海同星智能科技有限公司
CN120495459A,CN202510139125.0,"本发明属于车用软件开发技术领域，具体涉及图形绘制方法及系统、存储介质、电子设备及计算机程序产品，其中图形绘制方法包括：将绘图区域按物理水平像素划分成至少一个物理像素单列；针对每个物理像素单列，从所述物理像素单列内存在的所有用于绘制图形的信号数据点中，选择相应信号数据点作为信号接入点、信号接出点、信号值最高点和信号值最低点；以及将信号接入点配置为与上一相邻物理像素单列中的信号接出点相连，将信号值最高点配置为与同一物理像素单列中的信号值最低点相连。
",上海同星智能科技有限公司
CN120491916A,CN202510092729.4,"本发明属于车用软件开发技术领域，具体涉及图形绘制系统及电子设备，其中所述图形绘制系统，包括：计算机装置，所述计算机装置被配置为包括：划分模块，被配置为将绘图区域按物理水平像素划分成至少一个物理像素单列；选择模块，被配置为针对每个物理像素单列，从所述物理像素单列内存在的所有用于绘制图形的信号数据点中，选择相应信号数据点作为信号接入点、信号接出点、信号值最高点和信号值最低点；连接模块，被配置为将信号接入点与上一相邻物理像素单列中的信号接出点相连，将信号值最高点与同一物理像素单列中的信号值最低点相连。
",上海同星智能科技有限公司
CN120498917A,CN202510513670.1,"本发明涉及汽车总线诊断技术，具体涉及一种兼容多总线的虚拟诊断方法及虚拟诊断接口层架构系统、电子设备，其中兼容多总线的虚拟诊断方法包括：在诊断服务层与数据传输层之间创建抽象接口层，其中所述抽象接口层包括：用于传输与总线类型无关的诊断服务数据的通用接口模块和用于传输与总线类型强相关的辅助数据的辅助接口模块；所述诊断服务层根据总线类型调用所述通用接口模块，或通用接口模块和辅助接口模块，以向所述数据传输层收发诊断服务数据，或诊断服务数据和辅助数据。
",上海同星智能科技有限公司
CN120499014A,CN202510519240.0,"本发明涉及车载网络仿真技术，具体涉及一种剩余总线仿真校验方法及系统、电子设备，其中剩余总线仿真校验方法包括：解析车载网络数据库，以提取校验信号相关信息；根据校验信号相关信息将校验信号与校验算法库中的相应函数进行关联；以及在RBS仿真引擎调度周期内自动调用相应函数，以更新校验信号并将更新后的校验信号注入至待发送报文。
",上海同星智能科技有限公司
CN120496693A,CN202510122873.8,"本发明属于热解动力学技术领域，具体涉及一种固体材料热解动力学参数获取方法及获取系统、电子设备，其中固体材料热解动力学参数获取方法包括：采集热解物样本的质量损失百分比随温度或时间变化的实验曲线；构建动力学机理函数库，并遍历各动力学机理函数，以获取各动力学机理函数对应的质量损失百分比随温度或时间变化的仿真曲线；分别计算各质量损失百分比随温度或时间变化的仿真曲线与质量损失百分比随温度或时间变化的实验曲线的均方根差RMSE；以及对各均方根差RMSE进行排序，将最小均方根差RMSE对应的动力学机理函数作为固体材料热解动力学参数。
",上海同星智能科技有限公司
CN120386645A,CN202510046053.5,"本发明涉及一种嵌入式代码消息路由架构的运行方法及架构系统；其中嵌入式代码消息路由架构的运行方法包括：设置至少一个路由管理模块和至少一个队列模块；所述路由管理模块包括：路由内存申请函数和路由内存发送函数；所述路由管理模块通过路由内存申请函数向内存管理模块申请内存，以获取用于路由消息数据的内存空间；以及所述路由管理模块还通过路由内存发送函数将需要发送的消息数据发送至各队列模块。
",上海同星智能科技有限公司
CN118192845B,CN202410413426.3,"本发明属于测量技术领域，具体涉及一种测量数据同步显示系统及其执行方法，其中测量数据同步显示系统，包括：至少一个总线适配器，被配置为从ECU中获取多个测量数据；至少一台计算机装置，所述计算机装置包括：处理器、与处理器通信以呈现图形界面的显示器、可读存储介质、通讯总线和通信接口；其中所述处理器、所述可读存储介质和所述通信接口通过所述通讯总线实现与总线适配器相互间的通信；所述可读存储介质被配置为存储指令程序；所述处理器被配置为在获得多个测量数据后，执行所述指令程序以使得处理器执行测量数据同步显示操作。
",上海同星智能科技有限公司
CN223067331U,CN202422091684.2,"本实用新型属于总线设备技术领域，具体涉及一种外壳及总线设备，其中外壳包括：大体呈U型的第一壳体，其一侧设有母连接部，另一侧设有公连接部；第二壳体，与第一壳体结构相同，且所述第二壳体的公连接部适于插入并卡接于所述第一壳体的母连接部内，所述第一壳体的公连接部适于插入并卡接于所述第二壳体的母连接部内；本实用新型的外壳通过采用分体式的第一壳体和第二壳体，且第一、第二壳体通过卡接的方式连接，不仅降低了模具制造的难度，且装配方式简单，极大的提高了壳体的通用性，另外由于第一、第二壳体结构相同，还大大降低了开模成本。
",上海同星智能科技有限公司
CN223067109U,CN202421489301.0,"本实用新型属于总线通讯技术，具体涉及一种CAN接口电路和总线设备，所述CAN接口电路包括：总线收发模块、总线控制模块和逻辑处理模块；其中所述总线控制模块适于接收总线收发模块输出的反馈信号，以输出ACK信号；逻辑处理模块，与总线控制模块和总线设备的处理器模块电性连接，以接收并逻辑与处理来自所述处理器模块的第一总线报文信号及所述ACK信号；以及所述总线收发模块与逻辑处理模块电性连接，以接收所述逻辑处理模块逻辑与处理后输出的第二总线报文信号，从而向外部设备输出第三总线报文信号；本实用新型的CAN接口电路具备自发ACK能力，能够实现总线接口的主动式ACK响应，确保总线收发模块正确发出总线报文，从而确保CAN总线报文的持续发送。
",上海同星智能科技有限公司
CN223051434U,CN202421853931.1,"本实用新型属于EMC检测技术，具体涉及一种总线适配器以及EMC测试系统，总线适配器包括：控制单元和光转换单元；以及所述控制单元和光转换单元通过光纤进行连接；本总线适配器在传统的总线适配器设计基础上增加了光转换单元，光转换单元与控制单元中间通过光纤进行连接；通过使用光纤可以实现控制单元的远程设置，从而可以极大的减小总线适配器作为辅助测试设备时控制单元的电磁辐射对待测设备测试的影响。
",上海同星智能科技有限公司
CN117724719B,CN202410028558.4,"本发明属于车用软件开发技术领域，具体涉及基于软件平台的用户界面动态可扩展开发方法及系统，基于软件平台的用户界面动态可扩展开发方法包括：在软件平台运行过程中，界面管理器动态抽取软件平台运行库中的窗体相关的类型信息，并继承所述类型信息以形成动态派生基础窗体，同时由界面管理器生成软件平台导入代码，根据软件平台导入代码中的类型信息生成一次窗体动态创建用代码，将软件平台导入代码和一次窗体动态创建用代码存入一次派生代码文件中。
",上海同星智能科技有限公司
CN118450013B,CN202410302893.9,"本发明属于总线报文处理技术领域，具体涉及一种CAN XL总线数据与ETH总线数据互转方法及基于FPGA的转换器，其中CAN XL总线报文与ETH总线报文互转方法包括：步骤S101，对CAN XL总线报文预处理，得到待转换CAN XL总线报文；步骤S102，提取待转换CAN XL总线报文，并放入到ETH总线报文的相应字段中实现转换；或步骤S201，对ETH总线报文预处理，得到待转换ETH总线报文；步骤S202，提取待转换ETH总线报文，并放入到CAN XL总线报文的相应字段中实现转换。
",上海同星智能科技有限公司
CN222672195U,CN202421416612.4,"本实用新型属于电阻模拟技术，具体涉及电阻模拟电路和电阻模拟器板卡，其中电阻模拟电路包括：串联设置的若干基准电阻；若干磁保持继电器，分别与相应基准电阻并联设置；以及各磁保持继电器适于接收处理器模块的导通或断开控制信号以触发相应基准电阻断开或者接入，以调整电阻模拟电路的输出阻值；本实用新型的电阻模拟电路通过磁保持继电器替代传统的继电器，只需在调整输出阻值时让相应的磁保持继电器导通或断开，而不需要持续给磁保持继电器供电，因此无需消耗电能，故可以在多路、大阻值应用场景下的电阻板卡中显著降低电阻板卡的功耗。
",上海同星智能科技有限公司
CN222621355U,CN202421120388.4,"本实用新型涉及电子设备装配技术领域，具体提供了一种控制器用壳体、控制器及控制器组件，其中所述壳体上设置有卡接结构；所述卡接结构包括：卡接母件，所述卡接母件包括：设有定位孔的定位内壁；与所述定位内壁间隔设置的定位导向块；所述定位内壁与所述定位导向块形成用于容纳卡接部的定位空间；当所述卡接部连接的球形柱塞嵌入所述定位孔中，所述卡接部限位在定位空间内。
",上海同星智能科技有限公司
CN309153418S,CN202430247818.8,"1.本外观设计产品的名称：信号采集控制器。
2.本外观设计产品的用途：将采集到的模拟信号或数字信号转换成特定格式的模拟或数字信息，且不同模块之间能够级联。
3.本外观设计产品的设计要点：在于形状。
4.最能表明设计要点的图片或照片：立体图。
",上海同星智能科技有限公司
CN117743480B,CN202311780520.4,"本发明属于测量技术领域，具体涉及一种测量数据同步显示方法、装置及计算机可读存储介质，其中测量数据同步显示方法，包括：为多个测量数据显示窗口分别设定同步标识和同步函数；当任一暂停状态的测量数据显示窗口的显示时间跨度被更改时，其余暂停状态的测量数据显示窗口的同步函数被调用，在所述同步函数中将该暂停状态的测量数据显示窗口与其余暂停状态的测量数据显示窗口的同步标识进行匹配，若匹配一致，则执行同步操作。
",上海同星智能科技有限公司
CN118158132B,CN202410291493.2,"本发明属于通讯测试技术领域，具体涉及一种总线报文干扰的方法及基于FPGA的干扰仪，其中总线报文的干扰方法包括：设定报文干扰精度的脉宽时间为T；判断待干扰位置的前一个脉宽时间T的报文的显隐性状态；以及根据显隐性判断结果对待干扰位置的报文执行相应干扰操作。
",上海同星智能科技有限公司
CN309070162S,CN202430384669.X,"1.本外观设计产品的名称：一致性测试设备。
2.本外观设计产品的用途：用于CAN FD/CAN总线的一致性测试设备，集成有电源、示波器、干扰仪、CAN接口卡、一致性板卡等硬件设备，在TSMaster软件中编写测试用例；能够覆盖CAN FD/CAN节点物理层测试、数据链路层测试、通信测试、AUTOSAR/OSEK网络管理测试、诊断测试、刷写测试、路由测试等相关内容。
3.本外观设计产品的设计要点：在于形状。
4.最能表明设计要点的图片或照片：立体图。
",上海同星智能科技有限公司
CN309071076S,CN202430384671.7,"1.本外观设计产品的名称：总线接口设备（TE1051）。
2.本外观设计产品的用途：用于采集车载以太网的数据，将车载以太网的数据通过以太网接口或者USB接口传输到PC或其他设备。
3.本外观设计产品的设计要点：在于形状。
4.最能表明设计要点的图片或照片：立体图。
",上海同星智能科技有限公司
CN113867720B,CN202111147972.X,"本发明为一种软件平台用第三方程序库函数互调方法及互调系统，其中软件平台用第三方程序库函数互调方法包括：构建多个第三方程序库；检验第三方程序库的合法性；以及多个第三方程序库进行互相调用，实现了通过软件平台实现一个通用的调用框架，使得第三方程序库在软件平台中注册的函数可以在所有加载的第三方程序库之间共享，在当前第三方程序库需要调用其他第三方程序库时，无需加载其他第三方程序库，只要使用软件平台分享给当前第三方程序库的包含其他第三方程序库的函数表，即可实现对其他第三方程序库的访问，当其他第三方程序库没有加载时，软件平台和当前第三方程序库都能得知这一情况，这使得当前第三方程序库能顺利加载并提供部分功能。
",上海同星智能科技有限公司
CN308912326S,CN202430247816.9,"1.本外观设计产品的名称：浪涌保护器。
2.本外观设计产品的用途：用于总线接口设备等设备前端防雷抗电涌保护。
3.本外观设计产品的设计要点：在于形状。
4.最能表明设计要点的图片或照片：立体图。
",上海同星智能科技有限公司
CN117834464B,CN202311775184.4,"本发明属于数据过滤技术领域，具体涉及一种测量数据过滤器的实现方法、存储介质及电子设备，其中测量数据过滤器的实现方法包括：为每一个测量窗口设定上游数据流输入端口、下游数据流输出端口和过滤功能；每一个测量窗口上游数据流输入端口连接自身测量窗口以外的一个测量窗口下游数据流输出端口或数据源，形成层级连接；各层测量窗口均经其上游数据流输入端口接收来自上层测量窗口的数据，并对接收的数据过滤后经其下游数据流输出端口流出至下层测量窗口。
",上海同星智能科技有限公司
CN118714418A,CN202410479992.4,"本发明属于车用软件开发技术领域，具体涉及一种视频信息与总线报文信息的时间同步系统，包括：至少一个总线适配器，被配置为收发总线报文；至少一个视频信息采集器，被配置为采集视频信息；至少一台计算机装置，所述计算机装置包括：处理器、与处理器通信以呈现图形界面的显示器、可读存储介质、通讯总线和通信接口；其中所述视频信息采集器通过通信接口与处理器通信；以及所述处理器、所述可读存储介质和所述通信接口通过所述通讯总线实现与总线适配器相互间的通信；所述可读存储介质被配置为存储指令程序；所述处理器被配置为执行所述指令以使得处理器执行时间同步操作；所述显示器通过图形界面显示时间同步的总线报文和视频信息。
",上海同星智能科技有限公司
CN117290127B,CN202311235403.X,"本发明属于信号标定处理技术领域，具体涉及一种多地协同远程标定方法、服务器、请求端及应答端，其中多地协同远程标定方法，包括：服务器、请求端和应答端；其中通过服务器存储标定数据库且接收并存储来自应答端发送的标定信号；所述请求端从服务器调用标定信号以读取标定信号值；和/或所述请求端向服务器发送标定信号写入命令，以通过服务器将写入目标值发送至应答端，由应答端完成在线标定，以及所述请求端向服务器申请读取写入后的标定信号。
",上海同星智能科技有限公司
CN308841627S,CN202430120572.8,"1.本外观设计产品的名称：测试系统机箱。
2.本外观设计产品的用途：用于ECU和车辆功能测试。
3.本外观设计产品的设计要点：在于形状。
4.最能表明设计要点的图片或照片：立体图。
",上海同星智能科技有限公司
CN116974222B,CN202310755109.5,"本发明属于车用软件开发技术领域，具体涉及一种汽车标定信号自动化读写方法及汽车标定系统，其中汽车标定信号自动化读写方法包括：对标定模块中的各标定信号分别创建对应的映射系统变量；对映射系统变量赋初值；对于定义为观测量的标定信号，标定模块实时读取标定信号值，并存入相应的映射系统变量；在读取该映射系统变量时，即读出映射系统变量的最后存入值；本发明汽车标定信号自动化读写方法可以将任意标定信号映射至标定模块内部的映射系统变量中，从而实现像使用映射系统变量一样读取和写入标定信号，从而解决标定信号的互操作问题。
",上海同星智能科技有限公司
CN221487747U,CN202323599422.9,"本实用新型属于EMC检测技术，具体涉及一种CAN总线分析仪以及EMC测试系统，其中所述CAN总线分析仪包括：高频单元和低频单元；以及所述高频单元与所述低频单元通过光纤进行连接；本CAN总线分析仪将传统的CAN总线分析仪中的高频单元与低频单元分成两个设备，中间通过光纤进行连接；通过使用光纤将高频单元和低频单元进行隔离，几乎可以完全杜绝通过导体传播的电路噪声，再加上光纤可以拉上百米长度，也可以极大的减小电磁辐射对被测件测试的影响。
",上海同星智能科技有限公司
CN118349155A,CN202410479991.X,"本发明属于车用软件开发技术领域，具体涉及一种标定信号用面板控件关联系统，包括：至少一个总线适配器，被配置为从调试设备中读取标定信号；至少一台计算机装置，所述计算机装置包括：处理器、与处理器通信以呈现面板设置界面的显示器、可读存储介质、通讯总线和通信接口；其中所述处理器、所述可读存储介质和所述通信接口通过所述通讯总线实现与总线适配器相互间的通信，以从调试设备中读取标定信号；所述可读存储介质被配置为存储指令程序；所述处理器被配置为在读取标定信号后，执行所述指令以使得处理器执行标定信号用面板控件关联操作；所述显示器被配置为通过面板设置界面显示至少一个面板控件以及该面板控件上所显示的值。
",上海同星智能科技有限公司
CN221352084U,CN202322906575.7,"本实用新型属于车用传感器模拟技术，具体涉及SENT模拟电路、SENT模拟器和汽车底盘硬件在环测试系统，其中SENT模拟电路包括：开关控制电路和分压电路；其中所述开关控制电路适于控制一微处理器的SENT信号输出，以输出所需的SENT模拟信号；以及所述分压电路适于对所述SENT模拟信号进行分压处理后，输出至微处理器的SENT信号输入端；本实用新型的SENT模拟电路结构简单，能够代替真实传感器输出SENT模拟信号，满足汽车底盘零部件的硬件在环测试需求，并且能够监听输出的SENT模拟信号。
",上海同星智能科技有限公司
CN118312069A,CN202410413424.4,"本发明属于信号处理技术领域，具体涉及一种信号跟踪观察系统及方法，其中信号跟踪观察系统包括：至少一台计算机装置，所述计算机装置包括：处理器、与处理器通信以呈现图形界面的显示器、可读存储介质、通讯总线和通信接口；所述可读存储介质被配置为存储指令程序；所述处理器被配置为执行所述指令程序以使得处理器执行信号的跟踪观察操作；所述显示器通过图形界面显示至少一个信号观察窗口。
",上海同星智能科技有限公司
CN117149466B,CN202311239344.3,"本发明属于信号标定处理技术领域，具体涉及一种汽车多地协同远程标定系统及其标定方法，其中汽车多地协同远程标定系统包括：服务器，被配置为存储标定数据库且接收并存储来自应答端发送的标定信号；请求端，被配置为从服务器调用标定信号以读取标定信号值；和/或向服务器发送标定信号写入命令，并向服务器发送读取写入后标定信号的命令；应答端，被配置为向服务器发送标定信号；以及还被配置为用于接收服务器发送的写入的目标值，并对车辆完成在线标定。
",上海同星智能科技有限公司
CN308701107S,CN202330648999.0,"1.本外观设计产品的名称:双通道控制器（TC1013）。
2.本外观设计产品的用途:将车辆/零部件里面通过 CANFD 总线传输的信息通过 USB 传输到电脑上。
3.本外观设计产品的设计要点:在于形状与图案的结合。
4.最能表明设计要点的图片或照片：立体图。
",上海同星智能科技有限公司
CN221224773U,CN202322906560.0,"本实用新型属于车用传感器模拟技术，具体涉及一种汽车轮速监听电路、汽车轮速监听器和汽车轮速测试系统；其中汽车轮速监听电路包括：电压采样电路、比较电压提供电路和电压比较电路；其中所述电压采样电路适于采集轮速传感器的输出电压，并将该输出电压连接至所述电压比较电路；所述比较电压提供电路适于输出比较电压至所述电压比较电路；以及所述电压比较电路适于对轮速传感器的输出电压与比较电压进行比较，以输出轮速传感器检测的轮速信号的变化时刻和变化值；本汽车轮速监听电路结构简单，能够准确、稳定地监听轮速信号的变化时刻和变化值，以简单的方式和较低的成本即能满足汽车ECU计算的轮速信号是否正确的评价需求。
",上海同星智能科技有限公司
CN117171044B,CN202311211564.5,"本发明属于车用软件开发技术领域，具体涉及一种基于汽车标定信号自动化读写方法的开发调试系统，包括：计算机装置和总线适配器；其中所述计算机装置包括：处理器、可读存储介质、通讯总线和通信接口；其中所述处理器、所述可读存储介质和所述通信接口通过所述通讯总线实现与总线适配器相互间的通信；所述可读存储介质被配置为存储指令程序；所述处理器被配置为执行所述指令以使得处理器执行汽车标定信号自动化读写方法的操作；以及所述总线适配器被配置从ECU中读取标定信号，以及还配置为将处理器执行所述操作后生成的写入值下发至ECU。
",上海同星智能科技有限公司
CN118113397A,CN202410135867.1,"本发明属于车用软件开发技术领域，具体涉及一种标定信号用面板控件关联方法，包括：对标定系统中的各标定信号分别创建对应的映射系统变量；选定需显示和/或修改的标定信号所对应的映射系统变量，并设置已选定的映射系统变量关联至少一个面板控件，以使该映射系统变量的值在面板控件上显示和/或在面板控件上修改该映射系统变量的值；当标定信号发生变化时，与之对应的映射系统变量的值发生变化；当映射系统变量的值发生变化时，所有与之关联的面板控件上所显示的值相应变化。
",上海同星智能科技有限公司
CN118113325A,CN202410073686.0,"本发明属于车用软件开发技术领域，具体涉及一种信号数据自适应更新方法，包括：设定源信号与目标信号之间的映射表达式为线性类型，并配置映射参数：映射放大因子、偏移量和映射方向；或设定源信号与目标信号之间的映射表达式为非线性类型，并配置映射表达式的参数；其中配置映射表达式的参数的方法包括：根据源信号的数量n创建自变量x1、x2…xn；设定映射表达式y＝f(x1、x2…xn)；将各自变量与系统中的信号或表达式结果y进行关联；以及将映射表达式、映射参数或映射表达式的参数存入一映射执行容器内以创建源信号与目标信号的映射关系，并激活映射关系以实现信号数据自适应更新。
",上海同星智能科技有限公司
CN221039863U,CN202322945815.4,"本实用新型属于车用传感器模拟技术，具体涉及一种PSI5信号模拟电路和PSI5信号模拟装置，其中PSI5信号模拟电路包括：PSI5信号触发电路和PSI5模拟信号输出电路；其中所述PSI5信号触发电路与ECU的供电输出端相连，以触发PSI5脉冲信号输出；所述PSI5模拟信号输出电路根据输出的PSI5脉冲信号MCU IO_out输出PSI5模拟信号至ECU的模拟信号接收端；以及所述ECU的供电输出端与ECU的模拟信号接收端为同一个端口；本实用新型的PSI5信号模拟电路通过将电路的供电端与电路的输出端设为同一个端口，不仅电路结构简单，且能够代替真实传感器输出PSI5模拟信号，满足汽车ECU相关仿真测试需要。
",上海同星智能科技有限公司
CN118075157A,CN202410203884.4,"本发明属于数据过滤技术领域，具体涉及一种测量数据过滤系统及其过滤方法、计算机程序产品，其中测量数据过滤系统包括至少一个总线适配器，被配置为从ECU中获取数据源；至少一台计算机装置，所述计算机装置包括：处理器、与处理器通信以呈现图形界面的显示器、可读存储介质、通讯总线和通信接口；其中所述处理器、所述可读存储介质和所述通信接口通过所述通讯总线实现与总线适配器相互间的通信；所述可读存储介质被配置为存储指令程序；所述处理器模块被配置为在获得数据源后，执行所述指令以使得处理器执行测量数据过滤器的实现操作。
",上海同星智能科技有限公司
CN118072881A,CN202410203886.3,"本发明属于热解动力学技术领域，具体涉及一种固体材料热解动力学参数获取方法及获取系统、电子设备，其中固体材料热解动力学参数获取方法包括：采集热解物样本的质量损失百分比随温度或时间变化的实验曲线；构建动力学机理函数库，并遍历各动力学机理函数，以获取各动力学机理函数对应的质量损失百分比随温度或时间变化的仿真曲线；分别计算各质量损失百分比随温度或时间变化的仿真曲线与质量损失百分比随温度或时间变化的实验曲线的均方根差RMSE；以及对各均方根差RMSE进行排序，将最小均方根差RMSE对应的动力学机理函数作为固体材料热解动力学参数。
",上海同星智能科技有限公司
CN117408060B,CN202311405199.1,"本发明属于车用软件开发技术领域，具体涉及一种整车模型仿真性能优化方法、存储介质和电子设备，其中所述整车模型仿真性能优化方法包括：创建用于执行整车模型实时仿真任务的实时内核程序；将实时内核程序与一宿主软件通过共享内存的方式实现跨进程通讯，以通过所述宿主软件读写车辆仿真信号和通过代码程序自动控制实时内核程序执行仿真命令，以及记录实时内核程序反馈的仿真数据。
",上海同星智能科技有限公司
CN220913153U,CN202322424417.8,"本实用新型属于车用传感器模拟技术，具体涉及一种汽车测试用轮速传感器信号模拟电路以及轮速测试装置；其中轮速传感器信号模拟电路，包括：开关控制电路、分压电路以及恒流源电路；其中所述开关控制电路包括两路控制输出端，该两路控制输出端分别与所述分压电路的相应分压端电性连接，以使分压电路的输出端输出相应电压信号；所述分压电路的输出端与恒流源电路的输入端电性连接，以将相应电压信号转换为对应电流输出。本轮速传感器信号模拟电路能够快速根据需要切换输出的恒流大小，以满足轮速仿真测试需要，并且通过简单的电路构成了三种恒流精准输出。
",上海同星智能科技有限公司
CN117408061B,CN202311407711.6,"本发明属于车用软件开发技术领域，具体涉及一种整车模型仿真性能优化系统及计算机装置，其中所述整车模型仿真性能优化系统包括：计算机装置，所述计算机装置被被配置为执行实时内核程序和宿主软件；其中实时内核程序，被配置为宿主软件的守护进程，以执行整车模型实时仿真任务；宿主软件，被配置为与实时内核程序通过共享内存的方式实现跨进程通讯，以读写车辆仿真信号和通过代码程序自动控制实时内核程序执行仿真命令，以及记录实时内核程序反馈的仿真数据。
",上海同星智能科技有限公司
CN117939208A,CN202311863274.9,"本发明属于车用软件开发技术领域，具体涉及一种视频信息与总线报文信息的时间同步方法及电子设备，其中视频信息与总线报文信息的时间同步方法包括：定义视频信息的时间戳为基础时间戳；启动记录时，获取视频信息的首帧时间戳T0，并将总线报文的首帧时间戳与视频信息的首帧时间戳T0对齐，并记录对齐后总线报文的各帧时间戳Ty；停止记录时，获取视频信息的尾帧时间戳T1和总线报文的尾帧时间戳T2；设定比例系数k＝(T1‑T0)/(T2‑T0)；遍历所有已记录的总线报文，并修改保存其各帧时间戳Ty为Tx，Tx＝T0+(Ty‑T0)*k，即总线报文的各帧时间戳Tx以视频信息的时钟为依据被记录。
",上海同星智能科技有限公司
CN117784977A,CN202311798920.8,"本发明属于信号处理技术领域，具体涉及一种信号跟踪观察方法、计算机可读存储介质及电子设备，其中信号跟踪观察方法，包括：设置多个信号观察窗口，各信号观察窗口分别被配置为以相应信号呈现属性显示信号；设置信号数据库，并从信号数据库中选择待跟踪观察信号，添加并显示在其中一个信号观察窗口内，该信号观察窗口内显示待跟踪观察信号的区域为待跟踪观察信号区；从待跟踪观察信号区选中所需待跟踪观察信号至其余各信号观察窗口，以按相应信号呈现属性呈现待跟踪观察信号，且各信号观察窗口独立呈现。
",上海同星智能科技有限公司
CN117762610A,CN202311589289.0,"本发明涉及一种嵌入式代码内存架构的执行方法及内存架构系统；其中执行方法包括：模块管理器调用中断开关接口和原子变量锁定解锁操作接口执行中断开关和原子变量锁定操作时，关闭中断开关，对原子变量尝试加锁，并恢复中断开关，检查原子变量是否锁定；若被锁定则将MCU时间片资源分配给各代码模块以执行任务，并在相应代码模块的任务执行完毕后解锁原子变量。
",上海同星智能科技有限公司
CN308531261S,CN202330396169.3,"1.本外观设计产品的名称：显示屏幕面板的汽车总线检修图形用户界面。
2.本外观设计产品的用途：用于汽车总线检修软件以显示信息、运行程序。
3.本外观设计产品的设计要点：在于显示图形用户界面的界面内容。
4.最能表明设计要点的图片或照片：设计1主视图。
5.显示屏幕面板的其他视图为惯常设计，省略其他视图。
6.指定设计1为基本设计。
7.图形用户界面的用途：该图形用户界面用于汽车总线检修软件，显示汽车总线检修软件工作时的参数信息。
8.图形用户界面的人机交互方式：设计1主视图用于分析数据，通过选择相应按钮进行其对应的操作；设计2至设计8的交互方式与设计1相同。
9.其他需要说明的情形其他说明：该图形用户界面可用于台式电脑、笔记本电脑、平板电脑及手机。
",上海同星智能科技有限公司
CN220650695U,CN202322424423.3,"本实用新型属于车用传感器模拟技术，具体涉及一种轮速传感器信号模拟电路以及装置，该轮速传感器信号模拟电路包括稳压模块、恒流源电路和开关控制电路；其中所述恒流源电路包括至少两个恒流输出子电路，所述稳压模块的输出一恒压值以使各恒流输出子电路输出恒定电流；所述开关控制电路控制相应恒流输出子电路的恒定电流输出，以使总电流为一个恒定电流或多个恒流电路叠加输出。本轮速传感器信号模拟电路能够快速根据需要切换输出的恒流大小，以满足轮速仿真测试需要，并且通过简单的电路构成了三种恒流精准输出。
",上海同星智能科技有限公司
CN117608740A,CN202311360067.1,"本发明属于车用软件开发技术领域，具体涉及一种硬件在环仿真中非实时系统实现硬实时的方法及电子设备，其中硬件在环仿真中非实时系统实现硬实时的方法包括：创建实时模型任务，并形成任务程序；任务程序自动读取计算机配置并判断当前计算机装置的处理器内核数量Z，当内核数量Z不超过X个时，设定线程数量n＝1以执行实时模型任务，否则设定线程数量为n＝(Z‑X)/Y以执行实时模型任务，其中Y表示处理器的一个物理核心的线程数量。
",上海同星智能科技有限公司
CN117608741A,CN202311360358.0,"本发明属于车用软件开发技术领域，具体涉及一种硬件在环仿真中非实时系统实现硬实时的系统及计算机，其中硬件在环仿真中非实时系统实现硬实时的系统包括：计算机装置，所述计算机装置被配置为执行任务创建模块和任务线程设定模块；其中任务创建模块，被配置为创建实时模型任务并形成任务程序；任务线程设定模块，被配置为使任务程序自动读取计算机配置并判断当前计算机装置的处理器内核数量Z，当内核数量Z不超过X个时，设定线程数量n＝1以执行实时模型任务，否则设定线程数量为n＝(Z‑X)/Y以执行实时模型任务，其中Y表示处理器的一个物理核心的线程数量。
",上海同星智能科技有限公司
CN116737135B,CN202310753619.9,"本发明属于车用软件开发技术领域，具体涉及一种图形程序用参数配置方法及系统，其中图形程序用参数配置方法包括配置执行单元的参数，并对该参数所关联的参数配置字符串进行序列化操作，以及将序列化后的字符串显示在该参数所关联的参数对应表中；运行图形程序时对所述序列化后的字符串进行反序列化操作，得到图形程序中执行单元所需的实际参数值；本发明实现了用户可以直接通过字符串对参数进行配置使得参数配置的过程简化成对文本形式的参数进行配置，有效提升了图形程序的开发效率。
",上海同星智能科技有限公司
CN117421839A,CN202311468719.3,"本发明属于汽车仿真测试程序领域，具体涉及一种仿真测试过程中的程序步骤可视化实现方法，包括：为图形程序中的各执行框分别设定与相应执行状态对应的激活状态和非激活状态的标记参数；当执行框的执行状态发生变化时，该执行框的显示标记变为新执行状态对应的激活状态标记；以及当执行框在当前执行状态保持一段预设时间不变时，该执行框的显示标记从激活状态标记变为非激活状态标记。
",上海同星智能科技有限公司
CN117319224A,CN202311402885.3,"本发明属于车用软件开发技术领域，具体涉及一种汽车总线虚拟通道的实现方法及实现系统，其中汽车总线虚拟通道的实现方法包括：设计单实例服务端进程，启动后通过UDP协议监听本地计算机的固定端口；设计多实例客户端进程，启动后分别通过UDP协议监听本地计算机的随机端口；客户端进程向服务端进程发送注册请求数据包，服务端进程成功接收注册请求数据包后反馈成功接收信号至客户端进程，客户端进程接收到反馈后则按特定间隔时间向服务端进程发送心跳包，并等待服务端进程对该心跳包的应答；客户端进程向服务端进程发送汽车总线报文。
",上海同星智能科技有限公司
CN220254522U,CN202322031761.0,"本实用新型属于汽车以太网数据交互领域，具体涉及一种可以在不同场景下根据网络接口的类型使用不同以太网端口进行数据传输与交互电路及以太网通讯系统，其中车载以太网与普通以太网通讯切换电路，包括：信号切换逻辑控制模块，其包括若干多路复用芯片，其中各多路复用芯片中的两通道端分别连接车载以太网和普通以太网的相关信号，各多路复用芯片中的输出端分别连接MCU模块的相应通讯输入端；以及各多路复用芯片的通道切换端均与MCU模块的通道选择控制端电性连接，以解决两以太网的数据报文可以在同一个设备下根据需要进行切换使用，避免在使用单独的媒体转换介质设备进行数据的桥接转发时硬件切换的繁琐步骤。
",上海同星智能科技有限公司
CN117171042A,CN202311203983.4,"本发明属于车用软件开发技术领域，具体涉及一种基于图形程序用参数配置方法的开发调试系统，包括：计算机装置、总线适配器或烧写器；其中所述计算机装置包括：处理器、可读存储介质、通讯总线和通信接口；其中所述处理器、所述可读存储介质和所述通信接口通过所述通讯总线实现与总线适配器相互间的通信；所述可读存储介质被配置为存储指令程序；所述处理器被配置为执行所述指令以使得处理器执行图形程序用参数配置方法的操作；所述处理器还被配置为在执行所述操作后生成文本代码，并编译所述文本代码中至少一条执行代码；所述总线适配器被配置为将编译后的执行代码写入一调试设备；或所述烧写器被配置为将编译后的执行代码写入一调试设备。
",上海同星智能科技有限公司
CN116663502B,CN202310640840.3,"本发明属于车用软件开发技术领域，具体涉及一种汽车开发用文本代码与图形代码的转换方法及转换系统，其中汽车开发用文本代码与图形代码的转换方法包括：将图形代码转换为文本代码，并在目标文本代码生成的同时将当前图形代码中各执行单元的配置信息插入到目标文本代码的注释区域；以及将目标文本代码转换回为图形代码时，提取目标文本代码注释区域的配置信息，并加载该配置信息还原图形代码。
",上海同星智能科技有限公司
CN114448854B,CN202210059599.0,"本发明属于通信测试技术领域，具体涉及一种通信链路运行特征测试系统及测试方法，其中通信链路运行特征测试系统包括：继电器组合电路板，模拟组件，所述模拟组件设置在所述继电器组合电路板上；接口组件，所述接口组件设置在所述继电器组合电路板上，所述接口组件适于连接被测件；通过开闭继电器组合电路板上的各继电器，将被测件与模拟组件中所需的器件连接，以在模拟组件模拟的工作环境下对被测件进行测试，实现了应对不同的工况条件，测试验证被测件多种信号特征和各工况下运行特性，可针对多种差分通信技术，避免手工测试带来的遗漏。
",上海同星智能科技有限公司
CN308191181S,CN202330197816.8,"1.本外观设计产品的名称：控制器（TC1034）。
2.本外观设计产品的用途：汽车级 CAN 总线开发、测试、标定、诊断需求。
3.本外观设计产品的设计要点：在于形状与图案的结合。
4.最能表明设计要点的图片或照片：立体图。
",上海同星智能科技有限公司
CN115499268A,CN202211061631.5,"本发明属于汽车总线适配器技术领域，具体涉及一种用于汽车总线的映射管理器及总线适配器连接系统，其中总线适配器连接系统，包括：应用程序模块、映射管理器和总线适配器；映射管理器适于将应用程序模块中将每一个逻辑通道与总线适配器的硬件通道关联后连接，以进行数据通信；应用程序模块中应用程序针对所支持的汽车总线类型，每种类型带单个或多个逻辑通道，逻辑通道的数量和种类是用户配置的，实现了一个应用程序访问总线适配器资源的通用架构，使用该架构的应用程序可以任意配置其所需要连接的总线适配器型号和硬件通道，在每次更改配置后，映射关系立即生效，而不需要修改用户的软件，从而提高了应用程序开发效率，降低出错的可能性。
",上海同星智能科技有限公司
CN114253887A,CN202111563868.9,"本发明属于适配器技术领域，具体涉及一种阻抗匹配的适配器、阻抗匹配系统及方法，其中阻抗匹配的适配器包括：内部电路，以及与内部电路电性连接CAN接口模块和阻抗匹配模块；所述CAN接口模块适于连接目标网络；所述阻抗匹配模块与所述CAN接口模块连接；所述内部电路适于控制阻抗匹配模块以调节CAN接口模块两接口之间的电阻阻值，实现了用户可通过软件自动搭建硬件电路，以实现阻抗可配置的总线适配器，这对于通用的总线适配器的使用带来了效率的提升。
",上海同星智能科技有限公司
CN114138268A,CN202111504046.3,"本发明属于软件派生技术领域，具体涉及一种基于原生系统的派生软件构建方法、系统及设备，其中基于原生系统的派生软件构建方法包括：编辑派生软件的用户界面，并保存成派生软件界面配置文件；以及根据派生软件界面配置文件生成用户设计的界面，实现了软件平台在随后加载用户模块的过程中，能区分不同用户模块的依赖项，选择性加载所需的内核模块，节省了系统的CPU、内存消耗，提升了用户使用软件平台的效率。
",上海同星智能科技有限公司
CN114116498A,CN202111447914.9,"本发明属于汽车测试系统技术领域，具体涉及一种基于Excel文件加载的测试方法、系统及设备，其中基于Excel文件加载的测试方法包括：编辑Excel测试用例；以及对Excel测试用例进行测试，实现了将测试脚本通过Excel文件来实现，修改测试脚本只要修改Excel文件，从而避免更改脚本的过程中需要频繁修改程序，提升测试系统参数化能力。
",上海同星智能科技有限公司
CN114064322A,CN202111395571.6,"本发明属于汽车技术领域，具体涉及一种软件宿主构建方法、构建系统、软件宿主及仿真设备，其中软件宿主构建方法包括：软件平台根据外部软件的通信方法与外部软件进行通信；以及软件平台与外部软件通信后将外部软件作为软件平台的附属软件，以构成软件宿主，实现了通过软件宿主统一管理多种行业软件，可自动运行、自动关闭、自动配置这些行业软件，可自动控制这些行业软件之间的通信，从而确保了依赖这些软件的联合仿真等需求得到有效的满足。
",上海同星智能科技有限公司
CN113867719A,CN202111147971.5,"本发明属于计算机技术领域，具体涉及一种软件平台用第三方程序库函数拆解调用方法及调用系统，其中软件平台用第三方程序库函数拆解调用方法包括：构建第三方程序库；检验第三方程序库的合法性；以及调用第三方程序库，实现了针对第三方程序库函数的拆解调用系统的自动导入解决方案，无论是新增第三方程序库，还是现有的第三方程序库函数做了改动，软件平台只要加载第三方程序库，就能自动识别函数接口，而无需用户手动更新调用程序。这种方法可保证软件平台架构的稳定性，以及减少因软件平台反复修改而带来的稳定性下降等问题。
",上海同星智能科技有限公司
CN113608781A,CN202110878595.0,"本发明属于适配器兼容技术领域，具体涉及一种多适配器兼容库文件模块、调用方法、调用系统及设备，其中多适配器兼容用调用方法包括：构建各厂家的库文件模块；应用程序通过库文件模块获取设备的获取报文信息；以及在报文信息获取后断开设备的连接，实现了在上层APP中实现适配器的抽象，上层APP导出一套通用的API接口，针对每种厂家的适配器编写库文件，通过调用厂家适配器的API，来实现上层APP的API接口，APP无需了解厂家适配器的任何细节，仅仅加载库文件即可使用适配器的功能，不但可支持市面上所有的厂家的适配器，同时还可通过增加库文件的方式支持任何一款新的厂商的适配器，极大地提高了应用程序的开发效率。
",上海同星智能科技有限公司
CN211784238U,CN202020429967.2,"本实用新型属于天窗试验领域，具体涉及一种天窗风载加载系统及天窗风载试验平台。本天窗风载加载系统包括：随天窗玻璃板同步运动的随动加载装置；所述随动加载装置适于向天窗玻璃板加或卸载风载力；所述随动加载装置包括：加载机构、同步机构、加载平移驱动机构；所述同步机构包括：控制模块、与控制模块电性相连的位移传感器；所述位移传感器适于检测天窗玻璃板的位移；所述控制模块适于控制加载平移驱动机构驱动加载机构与天窗玻璃板同步运动。本实用新型的有益效果是：本实用新型的随动加载装置随天窗玻璃板同步运动，可以在天窗运动过程中施加稳定，且能够保持和天窗玻璃垂直或其他特定施力角度的模拟风荷载。
",上海同星智能科技有限公司
CN211015073U,CN201922436313.2,"本实用新型涉及一种CAN总线接口设备，本CAN总线接口设备包括：处理器模块、与所述处理器模块电性相连的USB接口模块、CAN总线接口模块；其中所述处理器模块适于通过USB接口模块连接上位机；以及所述处理器模块还适于通过CAN总线接口模块连接CAN总线，即实现上位机与CAN总线之间信息交互；本实用新型通过处理器模块作为中转站分别通过USB接口模块连接上位机和CAN总线接口模块连接CAN总线，能够将CAN总线报文及信号呈现在上位机，或者通过上位机将所需的报文、信号发送到CAN总线上，或者在网络节点开发时模拟整个网络环境，进行虚拟仿真、半实物仿真、实物仿真。
",上海同星智能科技有限公司
CN210776277U,CN201922323241.0,"本实用新型涉及一种同步升降桌控制电路、同步升降桌及同步升降桌系统，本同步升降桌控制电路包括：处理器模块、与所述处理器模块电性相连的驱动模块、通信模块，若干设置在相应桌体上且与所述驱动模块电性相连的驱动机构和若干分别与各驱动机构固定相连的升降机构；其中所述处理器模块适于通过通信模块接收由外部设备发送的控制信号，并根据控制信号控制驱动模块驱动相应驱动机构进行动作，以带动升降机构升降相应桌体；本实用新型通过通信模块接受远程控制信号，并由处理器模块控制设置在桌体上的驱动机构带动升降机构升降桌体，实现了远程操纵各升降桌进行同步升降的功能，满足了智能化操作需求。
",上海同星智能科技有限公司
CN210776676U,CN201922440261.6,"本实用新型涉及一种CANFD总线接口设备，本CANFD总线接口设备包括：处理器模块、USB接口模块、CANFD控制器和CANFD总线接口模块；其中所述处理器模块适于通过USB接口模块连接上位机，以进行数据传输；所述CANFD总线接口模块适于连接CANFD总线，并通过CANFD控制器将CANFD总线信号后输送至处理器模块；以及所述处理器模块还适于通过CANFD控制器将CANFD总线信号发送至CANFD总线接口模块进行输出，即实现上位机与CANFD总线之间信息交互；本实用新型能够将CANFD总线报文及信号呈现在上位机上，或者通过上位机将所需的报文、信号发送到CANFD总线上，或者在网络节点开发时模拟整个网络环境，进行虚拟仿真、半实物仿真、实物仿真。
",上海同星智能科技有限公司
CN210536666U,CN201922440285.1,"本实用新型涉及一种FastLIN总线接口设备，本FastLIN总线接口设备包括：处理器模块、与所述处理器模块电性相连的USB接口模块、FastLIN总线接口模块；其中所述处理器模块适于通过USB接口模块连接上位机；以及所述处理器模块还适于通过FastLIN总线接口模块连接FastLIN总线，即实现上位机与FastLIN总线之间信息交互；本实用新型通过处理器模块作为中转站分别通过USB接口模块连接上位机和FastLIN总线接口模块连接FastLIN总线，能够将FastLIN总线报文及信号呈现在上位机上，或者通过上位机将所需的报文、信号发送到FastLIN总线上，或者在网络节点开发时模拟整个网络环境，进行虚拟仿真、半实物仿真、实物仿真，能够克服传统LIN总线传输速率满足不了需求的问题。
",上海同星智能科技有限公司
CN210518379U,CN201922436344.8,"本实用新型涉及一种LIN总线接口设备，本LIN总线接口设备包括：处理器模块、与所述处理器模块电性相连的USB接口模块、LIN总线接口模块；其中所述处理器模块适于通过USB接口模块连接上位机；以及所述处理器模块还适于通过LIN总线接口模块连接LIN总线，即实现上位机与LIN总线之间信息交互；本实用新型通过处理器模块作为中转站分别通过USB接口模块连接上位机和LIN总线接口模块连接LIN总线，能够将LIN总线报文及信号呈现在PC端软件，或者通过上位机将所需的报文、信号发送到LIN总线上，或者在网络节点开发时模拟整个网络环境，进行虚拟仿真、半实物仿真、实物仿真。
",上海同星智能科技有限公司
CN305552784S,CN201930361449.4,"1.本外观设计产品的名称：通讯接口。
2.本外观设计产品的用途：用于连接通信系统。
3.本外观设计产品的设计要点：在于形状。
4.最能表明设计要点的图片或照片：设计1立体图。
5.指定设计1为基本设计。
",上海同星智能科技有限公司
CN209860749U,CN201920978314.7,"本实用新型涉及一种电机控制板及带有电机控制板的电机。电机控制板包括：控制模块、总线收发模块以及电机驱动模块；所述控制模块分别与所述总线收发模块以及所述电机驱动模块电性连接；所述控制模块适于通过所述总线收发模块接收或发送通讯信息给一根通讯线LDN与控制设备进行通讯；所述控制模块还适于接收通讯信息，并依据通讯信息控制电机驱动模块带动电机转动。通过三根线束组成一条通讯线，精简了传统通讯线包括至少四根，如电源线、接地线和两条通讯线（CAN总线方式），减少了电机与控制设备之间的线束数量，优化了电机控制板的电路结构。
",上海同星智能科技有限公司
CN209858985U,CN201920986394.0,"本实用新型涉及一种基于LDN的分布式控制系统、装置及智能家具，其中，控制系统包括主节点控制模块以及至少一个从节点控制模块；所述主节点控制模块分别与至少一个所述从节点控制模块由同一条通讯线并联通信；所述主节点控制模块适于发送控制通讯信号给多个所述从节点控制模块对后端装置进行控制。将原有的集中式控制方式改变成分布式控制方式，减少了从节点与主节点之间的线束约束，电路结构简单，单片机性能要求降低，无需外部晶振、单片机内部RC振荡器即可满足通讯精度要求，同时，通过三根线束组成一条通讯线精，简化了传统通讯线包括至少四根，如电源线、接地线和两条通讯线（CAN总线方式）。
",上海同星智能科技有限公司
CN305503366S,CN201930361447.5,"1.本外观设计产品的名称：通讯接口外壳。
2.本外观设计产品的用途：用于通讯接口的外壳使用。
3.本外观设计产品的设计要点：在于形状。
4.最能表明设计要点的图片或照片：立体图。
",上海同星智能科技有限公司
CN110275468A,CN201910564654.X,"本发明涉及一种基于LDN的分布式控制系统、方法、通讯协议、装置及家具，其中，控制系统包括主节点控制模块以及至少一个从节点控制模块；所述主节点控制模块分别与至少一个所述从节点控制模块由同一条通讯线并联通信；所述主节点控制模块适于发送控制通讯信号给多个所述从节点控制模块对后端装置进行控制。将原有的集中式控制方式改变成分布式控制方式，减少了从节点与主节点之间的线束约束，电路结构简单，单片机性能要求降低，无需外部晶振、单片机内部RC振荡器即可满足通讯精度要求，同时，通过三根线束组成一条通讯线精，简化了传统通讯线包括至少四根，如电源线、接地线和两条通讯线（CAN总线方式）。
",上海同星智能科技有限公司