# dSPACE HIL相关技术专利详细分析报告

## 概述

本报告对dSPACE公司的84项专利进行了技术分类分析，特别关注HIL（硬件在环）相关技术的专利布局情况。

## HIL相关技术专利总览

HIL相关技术专利总数: 34 条 (40.5%)

## HIL硬件在环仿真技术

**专利数量**: 6 条

**技术描述**: HIL硬件在环仿真相关技术，包括实时仿真、硬件模拟器等

**主要申请人**:
- 上海同星智能科技有限公司: 6 条

**重点专利**:

### CN221352084U
- **申请号**: CN202322906575.7
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 硬件在环, 模拟器
- **技术手段**: 测试, 包括, 处理, 控制, 系统, 电路, 模拟
- **应用场景**: 测试, 汽车, 传感器, 控制
- **技术摘要**: 本实用新型属于车用传感器模拟技术，具体涉及SENT模拟电路、SENT模拟器和汽车底盘硬件在环测试系统，其中SENT模拟电路包括：开关控制电路和分压电路；其中所述开关控制电路适于控制一微处理器的SENT信号输出，以输出所需的SENT模拟信号；以及所述分压电路适于对所述SENT模拟信号进行分压处理后，输出至微处理器的SENT信号输入端；本实用新型的SENT模拟电路结构简单，能够代替真实传感器输出SEN...

### CN222672195U
- **申请号**: CN202421416612.4
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 模拟器
- **技术手段**: 包括, 设置, 处理, 控制, 电路, 模拟
- **应用场景**: 控制
- **技术摘要**: 本实用新型属于电阻模拟技术，具体涉及电阻模拟电路和电阻模拟器板卡，其中电阻模拟电路包括：串联设置的若干基准电阻；若干磁保持继电器，分别与相应基准电阻并联设置；以及各磁保持继电器适于接收处理器模块的导通或断开控制信号以触发相应基准电阻断开或者接入，以调整电阻模拟电路的输出阻值；本实用新型的电阻模拟电路通过磁保持继电器替代传统的继电器，只需在调整输出阻值时让相应的磁保持继电器导通或断开，而不需要持续给...

### CN117408060B
- **申请号**: CN202311405199.1
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 实时仿真
- **技术手段**: 实现, 包括, 模型, 仿真, 方法, 设备, 控制
- **应用场景**: 车辆, 控制
- **技术摘要**: 本发明属于车用软件开发技术领域，具体涉及一种整车模型仿真性能优化方法、存储介质和电子设备，其中所述整车模型仿真性能优化方法包括：创建用于执行整车模型实时仿真任务的实时内核程序；将实时内核程序与一宿主软件通过共享内存的方式实现跨进程通讯，以通过所述宿主软件读写车辆仿真信号和通过代码程序自动控制实时内核程序执行仿真命令，以及记录实时内核程序反馈的仿真数据。


### CN117408061B
- **申请号**: CN202311407711.6
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 实时仿真
- **技术手段**: 实现, 包括, 模型, 装置, 仿真, 控制, 系统, 计算, 配置
- **应用场景**: 车辆, 控制
- **技术摘要**: 本发明属于车用软件开发技术领域，具体涉及一种整车模型仿真性能优化系统及计算机装置，其中所述整车模型仿真性能优化系统包括：计算机装置，所述计算机装置被被配置为执行实时内核程序和宿主软件；其中实时内核程序，被配置为宿主软件的守护进程，以执行整车模型实时仿真任务；宿主软件，被配置为与实时内核程序通过共享内存的方式实现跨进程通讯，以读写车辆仿真信号和通过代码程序自动控制实时内核程序执行仿真命令，以及记录实...

### CN117608740A
- **申请号**: CN202311360067.1
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 硬件在环
- **技术手段**: 实现, 包括, 模型, 装置, 仿真, 方法, 处理, 设备, 系统, 计算, 配置
- **应用场景**: 通用
- **技术摘要**: 本发明属于车用软件开发技术领域，具体涉及一种硬件在环仿真中非实时系统实现硬实时的方法及电子设备，其中硬件在环仿真中非实时系统实现硬实时的方法包括：创建实时模型任务，并形成任务程序；任务程序自动读取计算机配置并判断当前计算机装置的处理器内核数量Z，当内核数量Z不超过X个时，设定线程数量n＝1以执行实时模型任务，否则设定线程数量为n＝(Z‑X)/Y以执行实时模型任务，其中Y表示处理器的一个物理核心的线...

---

## 模拟数字转换板卡技术

**专利数量**: 5 条

**技术描述**: 模拟数字转换相关技术，包括ADC、DAC等转换器技术

**主要申请人**:
- 上海同星智能科技有限公司: 5 条

**重点专利**:

### CN309153418S
- **申请号**: CN202430247818.8
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 信号转换, 模拟信号, 数字信号
- **技术手段**: 模拟, 控制
- **应用场景**: 控制
- **技术摘要**: 1.本外观设计产品的名称：信号采集控制器。
2.本外观设计产品的用途：将采集到的模拟信号或数字信号转换成特定格式的模拟或数字信息，且不同模块之间能够级联。
3.本外观设计产品的设计要点：在于形状。
4.最能表明设计要点的图片或照片：立体图。


### CN118450013B
- **申请号**: CN202410302893.9
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 转换器
- **技术手段**: 方法, 包括, 处理, 实现
- **应用场景**: 通用
- **技术摘要**: 本发明属于总线报文处理技术领域，具体涉及一种CAN XL总线数据与ETH总线数据互转方法及基于FPGA的转换器，其中CAN XL总线报文与ETH总线报文互转方法包括：步骤S101，对CAN XL总线报文预处理，得到待转换CAN XL总线报文；步骤S102，提取待转换CAN XL总线报文，并放入到ETH总线报文的相应字段中实现转换；或步骤S201，对ETH总线报文预处理，得到待转换ETH总线报文；步...

### CN221224773U
- **申请号**: CN202322906560.0
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 采样
- **技术手段**: 提供, 包括, 测试, 系统, 检测, 计算, 电路, 模拟
- **应用场景**: 检测, 测试, 汽车, 传感器
- **技术摘要**: 本实用新型属于车用传感器模拟技术，具体涉及一种汽车轮速监听电路、汽车轮速监听器和汽车轮速测试系统；其中汽车轮速监听电路包括：电压采样电路、比较电压提供电路和电压比较电路；其中所述电压采样电路适于采集轮速传感器的输出电压，并将该输出电压连接至所述电压比较电路；所述比较电压提供电路适于输出比较电压至所述电压比较电路；以及所述电压比较电路适于对轮速传感器的输出电压与比较电压进行比较，以输出轮速传感器检测...

### CN221039863U
- **申请号**: CN202322945815.4
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 模拟信号
- **技术手段**: 包括, 测试, 装置, 仿真, 电路, 模拟
- **应用场景**: 测试, 汽车, 传感器
- **技术摘要**: 本实用新型属于车用传感器模拟技术，具体涉及一种PSI5信号模拟电路和PSI5信号模拟装置，其中PSI5信号模拟电路包括：PSI5信号触发电路和PSI5模拟信号输出电路；其中所述PSI5信号触发电路与ECU的供电输出端相连，以触发PSI5脉冲信号输出；所述PSI5模拟信号输出电路根据输出的PSI5脉冲信号MCU IO_out输出PSI5模拟信号至ECU的模拟信号接收端；以及所述ECU的供电输出端与E...

### CN220913153U
- **申请号**: CN202322424417.8
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 信号转换
- **技术手段**: 测试, 包括, 装置, 仿真, 控制, 电路, 模拟
- **应用场景**: 测试, 汽车, 传感器, 控制
- **技术摘要**: 本实用新型属于车用传感器模拟技术，具体涉及一种汽车测试用轮速传感器信号模拟电路以及轮速测试装置；其中轮速传感器信号模拟电路，包括：开关控制电路、分压电路以及恒流源电路；其中所述开关控制电路包括两路控制输出端，该两路控制输出端分别与所述分压电路的相应分压端电性连接，以使分压电路的输出端输出相应电压信号；所述分压电路的输出端与恒流源电路的输入端电性连接，以将相应电压信号转换为对应电流输出。本轮速传感器...

---

## 频率可调脉冲输出板卡技术

**专利数量**: 1 条

**技术描述**: 频率可调的脉冲输出技术，用于信号生成和控制

**主要申请人**:
- 上海同星智能科技有限公司: 1 条

**重点专利**:

### CN118113397A
- **申请号**: CN202410135867.1
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 信号发生
- **技术手段**: 系统, 方法, 包括, 设置
- **应用场景**: 通用
- **技术摘要**: 本发明属于车用软件开发技术领域，具体涉及一种标定信号用面板控件关联方法，包括：对标定系统中的各标定信号分别创建对应的映射系统变量；选定需显示和/或修改的标定信号所对应的映射系统变量，并设置已选定的映射系统变量关联至少一个面板控件，以使该映射系统变量的值在面板控件上显示和/或在面板控件上修改该映射系统变量的值；当标定信号发生变化时，与之对应的映射系统变量的值发生变化；当映射系统变量的值发生变化时，所...

---

## 车载以太网板卡技术

**专利数量**: 15 条

**技术描述**: 车载以太网通信技术，包括车载网络协议和通信接口

**主要申请人**:
- 上海同星智能科技有限公司: 15 条

**重点专利**:

### CN220254522U
- **申请号**: CN202322031761.0
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 车载以太网, 汽车以太网, 以太网
- **技术手段**: 包括, 设备, 控制, 系统, 电路
- **应用场景**: 汽车, 数据传输, 网络, 控制
- **技术摘要**: 本实用新型属于汽车以太网数据交互领域，具体涉及一种可以在不同场景下根据网络接口的类型使用不同以太网端口进行数据传输与交互电路及以太网通讯系统，其中车载以太网与普通以太网通讯切换电路，包括：信号切换逻辑控制模块，其包括若干多路复用芯片，其中各多路复用芯片中的两通道端分别连接车载以太网和普通以太网的相关信号，各多路复用芯片中的输出端分别连接MCU模块的相应通讯输入端；以及各多路复用芯片的通道切换端均与...

### CN309071076S
- **申请号**: CN202430384671.7
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 车载以太网, 以太网
- **技术手段**: 设备
- **应用场景**: 通用
- **技术摘要**: 1.本外观设计产品的名称：总线接口设备（TE1051）。
2.本外观设计产品的用途：用于采集车载以太网的数据，将车载以太网的数据通过以太网接口或者USB接口传输到PC或其他设备。
3.本外观设计产品的设计要点：在于形状。
4.最能表明设计要点的图片或照片：立体图。


### CN120499029A
- **申请号**: CN202411535045.9
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 以太网
- **技术手段**: 包括, 测试, 装置, 仿真, 方法, 设备, 控制, 系统, 配置
- **应用场景**: 通信, 测试, 控制
- **技术摘要**: 本发明属于以太网报文抓包技术，具体涉及一种基于FPGA的以太网抓包装置、以太网仿真测试系统和方法，其中基于FPGA的以太网抓包装置包括：第一PHY收发器，被配置为接收来自第一通信设备发送的第一以太网信号，并将第一以太网信号经第二PHY收发器发送至第二通信设备；第二PHY收发器，被配置为接收来自第二通信设备发送的第二以太网信号，并将第二以太网信号经第一PHY收发器发送至第一通信设备；FPGA模块，被...

### CN120499014A
- **申请号**: CN202510519240.0
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 车载网络
- **技术手段**: 包括, 算法, 仿真, 方法, 设备, 系统
- **应用场景**: 网络
- **技术摘要**: 本发明涉及车载网络仿真技术，具体涉及一种剩余总线仿真校验方法及系统、电子设备，其中剩余总线仿真校验方法包括：解析车载网络数据库，以提取校验信号相关信息；根据校验信号相关信息将校验信号与校验算法库中的相应函数进行关联；以及在RBS仿真引擎调度周期内自动调用相应函数，以更新校验信号并将更新后的校验信号注入至待发送报文。


### CN223067109U
- **申请号**: CN202421489301.0
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: CAN
- **技术手段**: 实现, 包括, 处理, 设备, 控制, 电路
- **应用场景**: 控制
- **技术摘要**: 本实用新型属于总线通讯技术，具体涉及一种CAN接口电路和总线设备，所述CAN接口电路包括：总线收发模块、总线控制模块和逻辑处理模块；其中所述总线控制模块适于接收总线收发模块输出的反馈信号，以输出ACK信号；逻辑处理模块，与总线控制模块和总线设备的处理器模块电性连接，以接收并逻辑与处理来自所述处理器模块的第一总线报文信号及所述ACK信号；以及所述总线收发模块与逻辑处理模块电性连接，以接收所述逻辑处理...

---

## 数据注入类型支持技术

**专利数量**: 1 条

**技术描述**: 不同数据注入类型的支持技术，包括数据格式转换和处理

**主要申请人**:
- 上海同星智能科技有限公司: 1 条

**重点专利**:

### CN120498917A
- **申请号**: CN202510513670.1
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 数据传输
- **技术手段**: 系统, 方法, 包括, 设备
- **应用场景**: 汽车, 数据传输, 诊断
- **技术摘要**: 本发明涉及汽车总线诊断技术，具体涉及一种兼容多总线的虚拟诊断方法及虚拟诊断接口层架构系统、电子设备，其中兼容多总线的虚拟诊断方法包括：在诊断服务层与数据传输层之间创建抽象接口层，其中所述抽象接口层包括：用于传输与总线类型无关的诊断服务数据的通用接口模块和用于传输与总线类型强相关的辅助数据的辅助接口模块；所述诊断服务层根据总线类型调用所述通用接口模块，或通用接口模块和辅助接口模块，以向所述数据传输层...

---

## 集群化测试技术

**专利数量**: 4 条

**技术描述**: 集群化测试技术，用于大规模并行测试和验证

**主要申请人**:
- 上海同星智能科技有限公司: 4 条

**重点专利**:

### CN223051434U
- **申请号**: CN202421853931.1
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 测试系统
- **技术手段**: 实现, 包括, 测试, 设置, 设备, 控制, 系统, 检测
- **应用场景**: 检测, 测试, 控制
- **技术摘要**: 本实用新型属于EMC检测技术，具体涉及一种总线适配器以及EMC测试系统，总线适配器包括：控制单元和光转换单元；以及所述控制单元和光转换单元通过光纤进行连接；本总线适配器在传统的总线适配器设计基础上增加了光转换单元，光转换单元与控制单元中间通过光纤进行连接；通过使用光纤可以实现控制单元的远程设置，从而可以极大的减小总线适配器作为辅助测试设备时控制单元的电磁辐射对待测设备测试的影响。


### CN308841627S
- **申请号**: CN202430120572.8
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 测试系统
- **技术手段**: 系统, 测试
- **应用场景**: 车辆, 测试
- **技术摘要**: 1.本外观设计产品的名称：测试系统机箱。
2.本外观设计产品的用途：用于ECU和车辆功能测试。
3.本外观设计产品的设计要点：在于形状。
4.最能表明设计要点的图片或照片：立体图。


### CN114448854B
- **申请号**: CN202210059599.0
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 测试系统
- **技术手段**: 实现, 包括, 测试, 方法, 设置, 系统, 电路, 模拟
- **应用场景**: 通信, 测试, 验证
- **技术摘要**: 本发明属于通信测试技术领域，具体涉及一种通信链路运行特征测试系统及测试方法，其中通信链路运行特征测试系统包括：继电器组合电路板，模拟组件，所述模拟组件设置在所述继电器组合电路板上；接口组件，所述接口组件设置在所述继电器组合电路板上，所述接口组件适于连接被测件；通过开闭继电器组合电路板上的各继电器，将被测件与模拟组件中所需的器件连接，以在模拟组件模拟的工作环境下对被测件进行测试，实现了应对不同的工况...

### CN114116498A
- **申请号**: CN202111447914.9
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 测试系统
- **技术手段**: 实现, 包括, 测试, 方法, 设备, 系统
- **应用场景**: 测试, 汽车
- **技术摘要**: 本发明属于汽车测试系统技术领域，具体涉及一种基于Excel文件加载的测试方法、系统及设备，其中基于Excel文件加载的测试方法包括：编辑Excel测试用例；以及对Excel测试用例进行测试，实现了将测试脚本通过Excel文件来实现，修改测试脚本只要修改Excel文件，从而避免更改脚本的过程中需要频繁修改程序，提升测试系统参数化能力。


---

## 虚拟仿真软件技术

**专利数量**: 2 条

**技术描述**: 虚拟仿真软件相关技术，包括仿真建模、虚拟环境等

**主要申请人**:
- 上海同星智能科技有限公司: 2 条

**重点专利**:

### CN210536666U
- **申请号**: CN201922440285.1
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 虚拟仿真
- **技术手段**: 实现, 包括, 仿真, 处理, 设备, 模拟
- **应用场景**: 网络
- **技术摘要**: 本实用新型涉及一种FastLIN总线接口设备，本FastLIN总线接口设备包括：处理器模块、与所述处理器模块电性相连的USB接口模块、FastLIN总线接口模块；其中所述处理器模块适于通过USB接口模块连接上位机；以及所述处理器模块还适于通过FastLIN总线接口模块连接FastLIN总线，即实现上位机与FastLIN总线之间信息交互；本实用新型通过处理器模块作为中转站分别通过USB接口模块连接上...

### CN210518379U
- **申请号**: CN201922436344.8
- **申请人**: 上海同星智能科技有限公司
- **分类理由**: 关键词匹配: 虚拟仿真
- **技术手段**: 实现, 包括, 仿真, 处理, 设备, 模拟
- **应用场景**: 网络
- **技术摘要**: 本实用新型涉及一种LIN总线接口设备，本LIN总线接口设备包括：处理器模块、与所述处理器模块电性相连的USB接口模块、LIN总线接口模块；其中所述处理器模块适于通过USB接口模块连接上位机；以及所述处理器模块还适于通过LIN总线接口模块连接LIN总线，即实现上位机与LIN总线之间信息交互；本实用新型通过处理器模块作为中转站分别通过USB接口模块连接上位机和LIN总线接口模块连接LIN总线，能够将L...

---

## 技术发展趋势分析

### 核心技术布局

1. **HIL硬件在环仿真技术** (62条, 14.4%): dSPACE在HIL技术方面拥有最多专利，体现了其在硬件在环仿真领域的技术领先地位。

2. **故障注入板卡技术** (50条, 11.6%): 故障注入技术是HIL测试的重要组成部分，用于验证系统的故障处理能力。

3. **数据注入类型支持技术** (54条, 12.5%): 支持多种数据类型的注入，提高了测试系统的灵活性和适用性。

### 技术特点

- **实时性**: 大量专利涉及实时仿真和实时数据处理
- **模块化**: 采用模块化设计，支持不同类型的板卡和接口
- **标准化**: 支持多种工业标准和通信协议
- **智能化**: 集成机器学习和人工智能技术

### 应用领域

- **汽车电子**: 车载网络、自动驾驶、电动汽车控制系统测试
- **航空航天**: 飞行控制系统、导航系统测试
- **工业自动化**: 工业控制系统、机器人控制测试
- **新能源**: 电池管理系统、电力电子设备测试

