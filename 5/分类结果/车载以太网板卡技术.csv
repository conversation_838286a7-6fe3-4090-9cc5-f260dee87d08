﻿公开(公告)号,申请号,摘要(中文),原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN120499029A,CN202411535045.9,"本发明属于以太网报文抓包技术，具体涉及一种基于FPGA的以太网抓包装置、以太网仿真测试系统和方法，其中基于FPGA的以太网抓包装置包括：第一PHY收发器，被配置为接收来自第一通信设备发送的第一以太网信号，并将第一以太网信号经第二PHY收发器发送至第二通信设备；第二PHY收发器，被配置为接收来自第二通信设备发送的第二以太网信号，并将第二以太网信号经第一PHY收发器发送至第一通信设备；FPGA模块，被配置为至少包括第一MAC控制器和第二MAC控制器；第一MAC控制器，仅与第一PHY收发器对应，并经第一PHY收发器抓取第一通信设备发送的第一以太网信号；以及第二MAC控制器，仅与第二PHY收发器对应，并经第二PHY收发器抓取第二通信设备发送的第二以太网信号。
",上海同星智能科技有限公司,车载以太网板卡技术,1.0,关键词匹配: 以太网,"包括, 测试, 装置, 仿真, 方法, 设备, 控制, 系统, 配置","通信, 测试, 控制"
CN120499014A,CN202510519240.0,"本发明涉及车载网络仿真技术，具体涉及一种剩余总线仿真校验方法及系统、电子设备，其中剩余总线仿真校验方法包括：解析车载网络数据库，以提取校验信号相关信息；根据校验信号相关信息将校验信号与校验算法库中的相应函数进行关联；以及在RBS仿真引擎调度周期内自动调用相应函数，以更新校验信号并将更新后的校验信号注入至待发送报文。
",上海同星智能科技有限公司,车载以太网板卡技术,1.0,关键词匹配: 车载网络,"包括, 算法, 仿真, 方法, 设备, 系统",网络
CN223067109U,CN202421489301.0,"本实用新型属于总线通讯技术，具体涉及一种CAN接口电路和总线设备，所述CAN接口电路包括：总线收发模块、总线控制模块和逻辑处理模块；其中所述总线控制模块适于接收总线收发模块输出的反馈信号，以输出ACK信号；逻辑处理模块，与总线控制模块和总线设备的处理器模块电性连接，以接收并逻辑与处理来自所述处理器模块的第一总线报文信号及所述ACK信号；以及所述总线收发模块与逻辑处理模块电性连接，以接收所述逻辑处理模块逻辑与处理后输出的第二总线报文信号，从而向外部设备输出第三总线报文信号；本实用新型的CAN接口电路具备自发ACK能力，能够实现总线接口的主动式ACK响应，确保总线收发模块正确发出总线报文，从而确保CAN总线报文的持续发送。
",上海同星智能科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"实现, 包括, 处理, 设备, 控制, 电路",控制
CN309070162S,CN202430384669.X,"1.本外观设计产品的名称：一致性测试设备。
2.本外观设计产品的用途：用于CAN FD/CAN总线的一致性测试设备，集成有电源、示波器、干扰仪、CAN接口卡、一致性板卡等硬件设备，在TSMaster软件中编写测试用例；能够覆盖CAN FD/CAN节点物理层测试、数据链路层测试、通信测试、AUTOSAR/OSEK网络管理测试、诊断测试、刷写测试、路由测试等相关内容。
3.本外观设计产品的设计要点：在于形状。
4.最能表明设计要点的图片或照片：立体图。
",上海同星智能科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"测试, 设备","通信, 测试, 网络, 诊断"
CN309071076S,CN202430384671.7,"1.本外观设计产品的名称：总线接口设备（TE1051）。
2.本外观设计产品的用途：用于采集车载以太网的数据，将车载以太网的数据通过以太网接口或者USB接口传输到PC或其他设备。
3.本外观设计产品的设计要点：在于形状。
4.最能表明设计要点的图片或照片：立体图。
",上海同星智能科技有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载以太网, 以太网",设备,通用
CN221487747U,CN202323599422.9,"本实用新型属于EMC检测技术，具体涉及一种CAN总线分析仪以及EMC测试系统，其中所述CAN总线分析仪包括：高频单元和低频单元；以及所述高频单元与所述低频单元通过光纤进行连接；本CAN总线分析仪将传统的CAN总线分析仪中的高频单元与低频单元分成两个设备，中间通过光纤进行连接；通过使用光纤将高频单元和低频单元进行隔离，几乎可以完全杜绝通过导体传播的电路噪声，再加上光纤可以拉上百米长度，也可以极大的减小电磁辐射对被测件测试的影响。
",上海同星智能科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"包括, 测试, 设备, 系统, 检测, 电路","检测, 测试"
CN308701107S,CN202330648999.0,"1.本外观设计产品的名称:双通道控制器（TC1013）。
2.本外观设计产品的用途:将车辆/零部件里面通过 CANFD 总线传输的信息通过 USB 传输到电脑上。
3.本外观设计产品的设计要点:在于形状与图案的结合。
4.最能表明设计要点的图片或照片：立体图。
",上海同星智能科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,控制,"车辆, 控制"
CN220254522U,CN202322031761.0,"本实用新型属于汽车以太网数据交互领域，具体涉及一种可以在不同场景下根据网络接口的类型使用不同以太网端口进行数据传输与交互电路及以太网通讯系统，其中车载以太网与普通以太网通讯切换电路，包括：信号切换逻辑控制模块，其包括若干多路复用芯片，其中各多路复用芯片中的两通道端分别连接车载以太网和普通以太网的相关信号，各多路复用芯片中的输出端分别连接MCU模块的相应通讯输入端；以及各多路复用芯片的通道切换端均与MCU模块的通道选择控制端电性连接，以解决两以太网的数据报文可以在同一个设备下根据需要进行切换使用，避免在使用单独的媒体转换介质设备进行数据的桥接转发时硬件切换的繁琐步骤。
",上海同星智能科技有限公司,车载以太网板卡技术,3.0,"关键词匹配: 车载以太网, 汽车以太网, 以太网","包括, 设备, 控制, 系统, 电路","汽车, 数据传输, 网络, 控制"
CN308191181S,CN202330197816.8,"1.本外观设计产品的名称：控制器（TC1034）。
2.本外观设计产品的用途：汽车级 CAN 总线开发、测试、标定、诊断需求。
3.本外观设计产品的设计要点：在于形状与图案的结合。
4.最能表明设计要点的图片或照片：立体图。
",上海同星智能科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"测试, 控制","测试, 汽车, 诊断, 控制"
CN114253887A,CN202111563868.9,"本发明属于适配器技术领域，具体涉及一种阻抗匹配的适配器、阻抗匹配系统及方法，其中阻抗匹配的适配器包括：内部电路，以及与内部电路电性连接CAN接口模块和阻抗匹配模块；所述CAN接口模块适于连接目标网络；所述阻抗匹配模块与所述CAN接口模块连接；所述内部电路适于控制阻抗匹配模块以调节CAN接口模块两接口之间的电阻阻值，实现了用户可通过软件自动搭建硬件电路，以实现阻抗可配置的总线适配器，这对于通用的总线适配器的使用带来了效率的提升。
",上海同星智能科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"实现, 包括, 方法, 控制, 系统, 电路, 配置","网络, 控制"
CN211015073U,CN201922436313.2,"本实用新型涉及一种CAN总线接口设备，本CAN总线接口设备包括：处理器模块、与所述处理器模块电性相连的USB接口模块、CAN总线接口模块；其中所述处理器模块适于通过USB接口模块连接上位机；以及所述处理器模块还适于通过CAN总线接口模块连接CAN总线，即实现上位机与CAN总线之间信息交互；本实用新型通过处理器模块作为中转站分别通过USB接口模块连接上位机和CAN总线接口模块连接CAN总线，能够将CAN总线报文及信号呈现在上位机，或者通过上位机将所需的报文、信号发送到CAN总线上，或者在网络节点开发时模拟整个网络环境，进行虚拟仿真、半实物仿真、实物仿真。
",上海同星智能科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"实现, 包括, 仿真, 处理, 设备, 模拟",网络
CN210776676U,CN201922440261.6,"本实用新型涉及一种CANFD总线接口设备，本CANFD总线接口设备包括：处理器模块、USB接口模块、CANFD控制器和CANFD总线接口模块；其中所述处理器模块适于通过USB接口模块连接上位机，以进行数据传输；所述CANFD总线接口模块适于连接CANFD总线，并通过CANFD控制器将CANFD总线信号后输送至处理器模块；以及所述处理器模块还适于通过CANFD控制器将CANFD总线信号发送至CANFD总线接口模块进行输出，即实现上位机与CANFD总线之间信息交互；本实用新型能够将CANFD总线报文及信号呈现在上位机上，或者通过上位机将所需的报文、信号发送到CANFD总线上，或者在网络节点开发时模拟整个网络环境，进行虚拟仿真、半实物仿真、实物仿真。
",上海同星智能科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"实现, 包括, 仿真, 处理, 设备, 控制, 模拟","数据传输, 网络, 控制"
CN209860749U,CN201920978314.7,"本实用新型涉及一种电机控制板及带有电机控制板的电机。电机控制板包括：控制模块、总线收发模块以及电机驱动模块；所述控制模块分别与所述总线收发模块以及所述电机驱动模块电性连接；所述控制模块适于通过所述总线收发模块接收或发送通讯信息给一根通讯线LDN与控制设备进行通讯；所述控制模块还适于接收通讯信息，并依据通讯信息控制电机驱动模块带动电机转动。通过三根线束组成一条通讯线，精简了传统通讯线包括至少四根，如电源线、接地线和两条通讯线（CAN总线方式），减少了电机与控制设备之间的线束数量，优化了电机控制板的电路结构。
",上海同星智能科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"包括, 设备, 电路, 控制",控制
CN209858985U,CN201920986394.0,"本实用新型涉及一种基于LDN的分布式控制系统、装置及智能家具，其中，控制系统包括主节点控制模块以及至少一个从节点控制模块；所述主节点控制模块分别与至少一个所述从节点控制模块由同一条通讯线并联通信；所述主节点控制模块适于发送控制通讯信号给多个所述从节点控制模块对后端装置进行控制。将原有的集中式控制方式改变成分布式控制方式，减少了从节点与主节点之间的线束约束，电路结构简单，单片机性能要求降低，无需外部晶振、单片机内部RC振荡器即可满足通讯精度要求，同时，通过三根线束组成一条通讯线精，简化了传统通讯线包括至少四根，如电源线、接地线和两条通讯线（CAN总线方式）。
",上海同星智能科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"包括, 装置, 控制, 系统, 电路","通信, 控制"
CN110275468A,CN201910564654.X,"本发明涉及一种基于LDN的分布式控制系统、方法、通讯协议、装置及家具，其中，控制系统包括主节点控制模块以及至少一个从节点控制模块；所述主节点控制模块分别与至少一个所述从节点控制模块由同一条通讯线并联通信；所述主节点控制模块适于发送控制通讯信号给多个所述从节点控制模块对后端装置进行控制。将原有的集中式控制方式改变成分布式控制方式，减少了从节点与主节点之间的线束约束，电路结构简单，单片机性能要求降低，无需外部晶振、单片机内部RC振荡器即可满足通讯精度要求，同时，通过三根线束组成一条通讯线精，简化了传统通讯线包括至少四根，如电源线、接地线和两条通讯线（CAN总线方式）。
",上海同星智能科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"包括, 装置, 方法, 控制, 系统, 电路","通信, 控制"
