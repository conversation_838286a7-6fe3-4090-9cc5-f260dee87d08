﻿公开(公告)号,申请号,摘要(中文),原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN222672195U,CN202421416612.4,"本实用新型属于电阻模拟技术，具体涉及电阻模拟电路和电阻模拟器板卡，其中电阻模拟电路包括：串联设置的若干基准电阻；若干磁保持继电器，分别与相应基准电阻并联设置；以及各磁保持继电器适于接收处理器模块的导通或断开控制信号以触发相应基准电阻断开或者接入，以调整电阻模拟电路的输出阻值；本实用新型的电阻模拟电路通过磁保持继电器替代传统的继电器，只需在调整输出阻值时让相应的磁保持继电器导通或断开，而不需要持续给磁保持继电器供电，因此无需消耗电能，故可以在多路、大阻值应用场景下的电阻板卡中显著降低电阻板卡的功耗。
",上海同星智能科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"包括, 设置, 处理, 控制, 电路, 模拟",控制
CN221352084U,CN202322906575.7,"本实用新型属于车用传感器模拟技术，具体涉及SENT模拟电路、SENT模拟器和汽车底盘硬件在环测试系统，其中SENT模拟电路包括：开关控制电路和分压电路；其中所述开关控制电路适于控制一微处理器的SENT信号输出，以输出所需的SENT模拟信号；以及所述分压电路适于对所述SENT模拟信号进行分压处理后，输出至微处理器的SENT信号输入端；本实用新型的SENT模拟电路结构简单，能够代替真实传感器输出SENT模拟信号，满足汽车底盘零部件的硬件在环测试需求，并且能够监听输出的SENT模拟信号。
",上海同星智能科技有限公司,HIL硬件在环仿真技术,2.0,"关键词匹配: 硬件在环, 模拟器","测试, 包括, 处理, 控制, 系统, 电路, 模拟","测试, 汽车, 传感器, 控制"
CN117408060B,CN202311405199.1,"本发明属于车用软件开发技术领域，具体涉及一种整车模型仿真性能优化方法、存储介质和电子设备，其中所述整车模型仿真性能优化方法包括：创建用于执行整车模型实时仿真任务的实时内核程序；将实时内核程序与一宿主软件通过共享内存的方式实现跨进程通讯，以通过所述宿主软件读写车辆仿真信号和通过代码程序自动控制实时内核程序执行仿真命令，以及记录实时内核程序反馈的仿真数据。
",上海同星智能科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 实时仿真,"实现, 包括, 模型, 仿真, 方法, 设备, 控制","车辆, 控制"
CN117408061B,CN202311407711.6,"本发明属于车用软件开发技术领域，具体涉及一种整车模型仿真性能优化系统及计算机装置，其中所述整车模型仿真性能优化系统包括：计算机装置，所述计算机装置被被配置为执行实时内核程序和宿主软件；其中实时内核程序，被配置为宿主软件的守护进程，以执行整车模型实时仿真任务；宿主软件，被配置为与实时内核程序通过共享内存的方式实现跨进程通讯，以读写车辆仿真信号和通过代码程序自动控制实时内核程序执行仿真命令，以及记录实时内核程序反馈的仿真数据。
",上海同星智能科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 实时仿真,"实现, 包括, 模型, 装置, 仿真, 控制, 系统, 计算, 配置","车辆, 控制"
CN117608740A,CN202311360067.1,"本发明属于车用软件开发技术领域，具体涉及一种硬件在环仿真中非实时系统实现硬实时的方法及电子设备，其中硬件在环仿真中非实时系统实现硬实时的方法包括：创建实时模型任务，并形成任务程序；任务程序自动读取计算机配置并判断当前计算机装置的处理器内核数量Z，当内核数量Z不超过X个时，设定线程数量n＝1以执行实时模型任务，否则设定线程数量为n＝(Z‑X)/Y以执行实时模型任务，其中Y表示处理器的一个物理核心的线程数量。
",上海同星智能科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 硬件在环,"实现, 包括, 模型, 装置, 仿真, 方法, 处理, 设备, 系统, 计算, 配置",通用
CN117608741A,CN202311360358.0,"本发明属于车用软件开发技术领域，具体涉及一种硬件在环仿真中非实时系统实现硬实时的系统及计算机，其中硬件在环仿真中非实时系统实现硬实时的系统包括：计算机装置，所述计算机装置被配置为执行任务创建模块和任务线程设定模块；其中任务创建模块，被配置为创建实时模型任务并形成任务程序；任务线程设定模块，被配置为使任务程序自动读取计算机配置并判断当前计算机装置的处理器内核数量Z，当内核数量Z不超过X个时，设定线程数量n＝1以执行实时模型任务，否则设定线程数量为n＝(Z‑X)/Y以执行实时模型任务，其中Y表示处理器的一个物理核心的线程数量。
",上海同星智能科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 硬件在环,"实现, 包括, 模型, 装置, 仿真, 处理, 系统, 计算, 配置",通用
