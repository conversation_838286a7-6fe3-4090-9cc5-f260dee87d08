﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,当前法律状态
US20250260405A1,US19051914,"一种可编程门阵列，其被设置为执行功能，并且当执行该功能时，由该功能的至少一个功能部分使用至少一个信号。门阵列具有至少一个检测元件，该检测元件被设置为使用对信号的改变来确定当执行该功能时该至少一个功能部分是否在门阵列上运行，并且根据该确定来提供至少一个检测值。还提供了具有这种门阵列的计算机装置、这种计算机装置的使用、具有这种计算机装置的测试设备、以及用于生成用于对这种门阵列编程的配置数据的方法。
",H03K19/173,
EP4597303A1,EP24155284.3,"该发明涉及一种计算机实现的方法和系统（1），用于识别至少一个用于生成虚拟构件（10）的代码更改（14），特别是用于测试和/或验证车辆的预定功能，尤其是自动化驾驶功能的虚拟控制器，包括以下步骤：提供（S1）至少一个源代码段（12）以测试和/或验证车辆的预定功能；应用（S2）机器学习算法（A）到所述至少一个源代码段（12）以识别至少一个所需的代码更改（14）；以及输出（S3）一个数据集（16），包含生成虚拟构件（10）以测试和/或验证车辆的预定功能所需的代码更改（14）的信息。此外，本发明还涉及一种计算机实现的方法，用于提供机器学习算法（A）。
",G06F8/30,
EP3812885B1,EP19205062.3,"根据本发明，从模型生成模拟代码和生产代码作为源代码。所述模型包括一个或多个块，所述块规定了程序、尤其是控制程序的期望行为。所述块中的至少一个用模拟代码属性来标记。针对包含模拟代码属性的那些块生成模拟代码。针对所有其他的块生成生产代码。所生成的源代码包括模拟代码部分和生产代码部分两者。模拟代码部分被包含在源代码中，以使得它们可以与生产代码部分分离。
",G06F8/35,
EP3783452B1,EP19192743.3,"本发明涉及一种用于近似设备的虚拟测试的测试结果以至少部分自主引导机动车的计算机实现的方法。本发明还涉及一种用于提供经训练的人造神经网络的计算机实现的方法、一种测试单元(1)、一种计算机程序以及一种计算机可读的数据载体。
",G06N3/084,
EP4592886A1,EP24221364.3,"该发明涉及一种计算机实现的方法用于将模拟与测试同步该模拟根据输入数据生成输出数据包括以下步骤：S1）将输入数据从测试传输到仿真，以便仿真确定输出数据，同时启动测试时间和仿真时间，以便可以获取经过的测试时间和经过的仿真时间，并将确定的输出数据从仿真传输到测试，S2）获取测试时间以及获取仿真时间并比较仿真时间与测试时间，S3a）当比较已经证明模拟时间等于或超过测试时间时：从预定的输入数据集向模拟传输另一个输入数据，以便模拟根据该额外输入数据确定另一个输出数据，或者如果所有输入数据已经传输到模拟，则结束过程，并S3b)当比较已经证明模拟时间小于测试时间时：暂停测试一段时间，在这段时间内停止测试时间的流逝，并在暂停时间后重新执行步骤S2以及随后的步骤S3a或S3b。
这样就提供了一种可以实现精确同步的方法。
",G06F30/20,暂缺
EP4105811B1,EP22163416.5,"一种用于在基于场景的测试和/或通过关键性能指标(KPI)对要测试的至少部分自主驾驶功能的同源性进行评估的计算机实现的方法，其中KPI由KPI插件映射，并且KPI插件被动态地选择并且可重用于模拟和/或测试情况，并且其中至少一个KPI插件由KPI插件机制选择以用于模拟和/或测试定义的目的，并且在由KPI插件机制执行时自动地执行。
",G06F30/15,授权
EP4575875A1,EP23217568.7,"该发明涉及一种计算机实现的方法用于生成针对至少一个模拟运行（12）的更改建议（10），所述模拟运行（12）是包含多个模拟运行（12）的模拟（14），包括以下步骤- 接收一个或多个仿真运行结果(16)的一个或多个已结束的仿真运行(12)的仿真(14)- 预测至少一个尚未完成的模拟运行(12)的模拟运行结果(20)在考虑接收到的模拟运行结果(16)的情况下并且生成变更建议(10)用于尚未结束的模拟运行(12)的模拟(14)并考虑至少一个预测的模拟运行结果(20)。
此外本发明涉及一种数据处理装置包括用于执行上述方法的手段计算机程序产品和相应的计算机可读存储介质
",G06F30/20,实质审查
EP4576022A1,EP23217564.6,"本发明涉及一种计算机实现的方法，用于提供一个生成式深度学习模型（A）以进行对象检测，包括以下步骤：提供（S1）一个基于自然语言数据预训练的生成式深度学习模型（A），特别是基于自然语言数据预训练的生成式Transformer模型，并使用基于车辆环境传感器数据（SD）的单张图像（10）的训练数据集（TD）对生成式深度学习模型（A）进行微调（S2），所述单张图像尤其为时间序列图像和/或点云（12）。此外，本发明还涉及一种使用生成式深度学习模型进行对象检测的计算机实现方法，一种用于提供用于对象检测的生成式深度学习模型的系统以及一种使用生成式深度学习模型进行对象检测的系统。
",G06V20/56,实质审查
EP4575557A1,EP24218716.9,"发送接收装置（SE）用于发送和接收电磁信号（EM），其中所述电磁信号（EM）用于与用于物体检测的传感器（OD）进行交换，其中发送接收装置（SE）具有模拟部分（ANA），该模拟部分被设置为将至少一个中间频率级的第一个信号(S1)转换为传输频率级的第二个信号(S2)，并将第二个信号(S2)作为电磁信号(EM)通过输出端(AUS)输出，接收第三个信号（S3）作为电磁信号（EM）通过一个输入端口（EIN），并且将传输频率层中的第三个信号(S3)转换为至少一个中间频率层中的第四个信号(S4)其中第三个信号（S3）是从第二个信号（S2）导出的，其中发射/接收设备（SE）被设置为生成一个测试信号（TS）并将其作为第一个信号（S1）输入到模拟部分（ANA），并通过将测试信号（TS）与第四个信号（S4）进行比较来检查模拟部分（ANA）。
",G01S7/40,实质审查
US12341512B2,US17739575,"一种用于编程FPGA的方法，其中提供了库，该库包括基本操作和用于库的每个基本操作的特定等待时间表。每个等待时间表指示在FPGA上执行期间，对于FPGA的多个时钟速率和对于特定操作的多个输入位宽度的特定操作的等待时间，这取决于特定操作的输入位宽度和FPGA的时钟速率。定义了一条数据路径，该数据路径指示在FPGA上库的至少两个基本操作的连续执行。检测并相加对于等待时间表中的多个不同时钟速率的数据路径的特定基本操作的特定输入位宽度给出的等待时间，然后选择时钟速率之一。
",G06F30/331,授权
JP2025092461A,JP2024210783,"【课题】提供一种具有简单且高效的半导体开关1以及测量电路2和控制装置3的装置，用于半导体开关的过载保护。【解决手段】在缺陷生成单元中，测量电路2能够检测到过载检测器发现负载路径6出现过载，并且当发生过载时，通过过载信号输出部分13向控制装置3输出过载信号。控制装置连接至半导体开关1的开关信号输入部分7，通过开关信号线路14连接，当接收到过载信号时，向半导体开关提供用于打开半导体开关的开关信号，当过载达到最大值并且达到预先设定的最大过载时，在过载信号消失后，向半导体开关提供用于闭合的开关信号，当过载超过预先设定的最大过载时，在过载信号消失后不再生成用于闭合开关的开关信号。【选择图】图1
",H03K17/08,公开
EP4571331A1,EP24217495.1,"展示和描述了一种计算机实现的方法（1）用于通过硬件在环模拟器（2）对具有励磁绕组的多相电动机进行仿真（3）以测试功率电子控制装置（4）其中包含逆变器（4）。模拟器（2）根据电动机的数学模型（7）计算电动机各绕组中的绕组电流（i_m）以及由电动机反作用产生的无电位绕组连接的电气反作用电势（u_emf_w），其中无电位绕组连接通过电压模拟器（9）被施加到反作用电势上。
该方法通过在每个支路(8)中分别用虚拟开关(11)来补充电动机的数学模型(7)从而减少不准确计算的反向电势对仿真结果的影响其中虚拟开关(11)在打开状态下会减小相应测量的支路电压(u_m)对相应支路(8)中计算出的电流分布(i_m)的影响而虚拟开关(11)则由模拟器(2)的开关逻辑(12)通过评估至少一个支路电压(u_m)和/或一个支路电流(i_m)来在电动机的支路(8)中打开和/或关闭
",G01R31/28,实质审查
US20250191244A1,US19051895,"提供一种检查图形文件的方法。提供具有图形信息的图形文件，该图形信息导致在计算机系统的显示设备上的图形元素的图形表示，该图形表示对应于图形信息。图形文件在每种情况下具有用于至少一些图形元素的预定义注释，并且图形文件具有预定义图形结构信息，该信息至少对于一些图形元素指定在显示设备上的图形表示期间这些图形元素中的哪些必须被显示为彼此连接。接收图形文件的修改版本，其中至少一条图形信息已经被修改，使得它导致显示设备上的不同图形表示。此后检查图形文件的修改版本。
",G06T11/00,暂缺
EP4567585A1,EP23214679.5,"该发明涉及一种计算机实现的方法用于从开发平台的图形控制模型生成目标平台的控制程序该开发平台包括用于存储有关图形控制模型的信息的定义数据库并被设计为支持矩阵运算由此特征在于在定义数据库中可以存储关于图形控制模型中使用的矩阵结构的注释信息，并且在为目标平台生成控制程序时，应考虑存储的注释信息，使得a）在执行至少涉及一个矩阵的编辑任务的目标平台上，计算速度有所提高和/或b）在目标平台保存矩阵时所需存储空间减少。
此外本发明涉及一种用于配置作为控制单元（ES）设计的目标平台的方法。
此外本发明涉及一种数据处理装置(10)包括执行上述方法的手段计算机程序产品和相应的计算机可读存储介质
",G06F8/41,实质审查
JP2025080780A,JP2024198852,"【课题】用于使被测体面临模拟电气错误的测试平台。【解决方案】该测试平台包括：一个用于汇总模拟电气错误中央错误控制单元；以及至少一个作为故障插入单元（FIU）构成的本地节点，该节点配备有用于在测试平台中篡改所选电流的开关装置。错误控制单元被配置为创建指定至少一个连接到电线路的电气错误的抽象错误描述，并将其传输到本地节点。本地节点被配置为从抽象错误描述中导出关于开关装置的驱动控制规则，以便将电气错误连接到电线路，并通过根据驱动控制规则驱动控制开关装置，将电气错误连接到电线路。【选择图】图6
",G01R31/00,暂缺
US20250164869A1,US18950838,"一种用于激励基于红外摄像机的环境检测系统以用于测试目的测试装置。本发明提供一种红外线摄像机可固定为被检测体的保持装置。还提供了红外光源和计算机控制的成像设备。红外光源、成像装置和记录装置以这样的方式布置，即光路从光源通过计算机控制的成像装置到达测试对象。
",G03B43/00,暂缺
EP4557078A1,EP24157503.4,"本发明涉及从块图的一个或多个块生成源代码的方法，该块图包括至少两个块和块之间至少一个信号连接。生成源代码包括将块图转换为临时视图，连续优化临时视图，并将优化临时视图转换为源代码。本发明的优化包括识别至少两个并行的控制流分支，其中一个是读取或写入的，识别访问该变量的操作，检查成本标准，并在满足成本标准时在至少两个并行的控制流分支中复制操作。此外，本发明还涉及配置控制装置、计算机程序产品和计算机系统的方法。
",G06F8/34,实质审查
EP4556917A1,EP24187394.2,"测试台 （2） 用来对抗试样 （4） 的模拟电气故障。试验台包括一个用于编排模拟电气故障的中央故障控制单元（10），以及至少一个作为故障插入单元（FIU）设置的本地节点（8a），其中有一个用于伪造试验台中选定电流的开关安排（16a）。故障控制单元设置为创建一个抽象的故障描述，该描述指定至少一个电气故障，以便在电线（6a）上开启，并将其发送到本地节点。本地节点已设置，从抽象故障描述中得出一个适合将电故障插入电线的开关安排的控制规则，并通过按照控制规则控制开关安排将电故障插入电线。
",G01R31/28,实质审查
EP4553662A1,EP24206600.9,"本发明涉及一种计算机实现的方法，用于基于标准创建一个虚拟测试（VT）的配置（10），以测试车辆的自动驾驶功能，其中包括定义配置参数（12a，12b）对虚拟测试（VT）的目标参数（14）的依赖性（S1），确定 （S2） 测试算法 （A） 和/或参数间隔 （16） 用于各个目标数量 （14）；并设置（S3）配置参数（12a，12b）的应用条件（18），参数间隔（16）和/或测试算法（A）。此外，本发明还涉及基于标准创建用于测试车辆自动驾驶功能的虚拟测试（VT）的配置（10）的系统。
",G06F11/3668,实质审查
US20250147150A1,US18503211,"一种用于测试雷达传感器的测试组件，包括：用于雷达传感器的容器，其中雷达传感器被配置为发送雷达信号；至少一个接收天线，被配置为接收雷达信号；至少两个发射天线，每个发射天线被配置为发射一个反射信号，其中，作为反射信号的叠加信号可由雷达传感器接收；以及计算机，其被配置为基于所接收的雷达信号来确定相应反射信号的至少一个参数。测试组件包括比接收天线更多的发射天线。
",G01S7/40,公开
US20250138784A1,US18495784,"一种用于执行框图的图形建模环境的矩阵计算块。框图的框具有输入端口和/或输出端口，并且可通过相应端口由信号线连接以用于数据传输。矩阵计算块包括：至少一个输入端口，其被配置为接收输入矩阵信号；具有至少一个错误条件检查的计算定义；至少一个输出端口，其被配置用于发射输出信号，所述输出信号是向量信号或矩阵信号；以及被配置用于发射误差信号的误差端口。误差信号的值指示多个预定义误差状态中的误差状态，其包括无误差状态。误差信号的值取决于是否满足至少一个误差条件检查。
",G06F7/57,授权
JP2025067889A,JP2024178882,"【课题】LiDAR我们提出了一种用于测试传感器的设备和方法。【解决方法】LiDAR传感器构成为送出具有第1波长第1光（L1）该装置一种触发检测器（TD），其被配置为在第一光路上接收第一光触发检测器，其被配置为生成与所接收到第一光相关联的触发信号（TS）至少一种发送设备（12），其构成为，与触发信号关联地发送具有第二波长第二光（L2）第二道光路上的第二道光LiDAR能够由传感器接收送出设备在第一光路构成为从第二光路分离第一光学元件（DS）配置在第二光路内且构成为对第二光应用漫反射和/或漫透射第二光学元件（DIFF）中所述修改相应参数的值。图1是示出图1
",G01S7/497,暂缺
JP2025067891A,JP2024178888,"一种用于测试LiDAR传感器的装置和方法。LiDAR传感器（LIDAR）构成为送出第1光（L1）该装置一种触发检测器（TD），其被配置为在第一光路上接收第一光（L1）触发检测器（TD），其被配置为生成与所接收的第一光（L1）相关联的触发信号（TS）至少一个发送设备（12），其被配置为发送与触发信号（TS）相关联的第二光（L2）送出设备（12），其能够由LiDAR传感器（LIDAR）接收第二光路上第二光移动设备（BE），其构成为使至少一个送出设备移动中所述修改相应参数的值。图1是示出图1
",G01S7/497,暂缺
CN113039548B,CN201980075229.2,"本发明示出并说明了一种由计算机实现的、用于借助至少一个计算单元(3)对电路(2)进行仿真的方法(1)，电路(2)具有包括开关元件(T<Sub>i</Sub>)的电路部件(L、R、T<Sub>i</Sub>)，开关元件(T<Sub>i</Sub>)可以占据导通的开关状态或不导通的开关状态，电路(2)通过数学表达MR描述并且在计算单元(3)上通过数值求解描述整体开关状态(SST<Sub>i</Sub>)的数学表达(MR)针对每个整体开关状态(SST<Sub>i</Sub>)计算电路。由此能以尽可能简单的方式在数学表达中表达并且数值计算有开关元件的整体开关状态(SST<Sub>i</Sub>)的多个、最为有利的情况下所有组合并且因此有多个、最为有利的情况下所有整体开关状态(SST<Sub>i</Sub>)的电路(2)，即，在电路中的导通的开关元件(T<Sub>i</Sub>)由开关线圈(7)代表，在电路(2)中的不导通的开关元件(T<Sub>i</Sub>)则由开关电容器(8)代表，开关线圈(7)和开关电容器(8)的电气特性通过结构一致的、时间离散的开关方程i<Sub>S,k</Sub>描述，使得在为开关元件(T<Sub>i</Sub>)使用结构一致的、时间离散的开关方程i<Sub>S,k</Sub>的情况下，产生了针对电路(3)的所有整体开关状态(SST<Sub>i</Sub>)的与开关状态无关的、时间离散的状态空间表达H、Φ、C<Sub>d</Sub>、D<Sub>d</Sub>，并且在计算单元(3)上基于针对电路(2)的所有整体开关状态(SST<Sub>i</Sub>)的与开关状态无关的、时间离散的状态空间表达H、Φ、C<Sub>d</Sub>、D<Sub>d</Sub>执行仿真。
",G06F30/367,授权
US20250123377A1,US18916625,"一种用于测试激光雷达传感器的装置。LIDAR传感器被配置为发射具有第一波长的第一光。该设备包括触发检测器，其被配置为在第一光学路径上接收第一光。触发检测器被配置成根据所接收的第一光生成触发信号。至少一个发射装置被配置为根据触发信号发射具有第二波长的第二光。第二光可由LIDAR传感器接收在第二光路上。光学元件被配置为将第一光路与第二光路分开。
",G01S7/497,授权
EP4538877A1,EP24198573.8,"根据本发明，提供了一种用于在第一处理器类型的真实处理器上执行仿真的方法，其中该仿真具有为第一处理器类型编译的至少一个仿真模型，其过程步骤如下：在第一处理器类型的真实处理器上运行为第一处理器类型编译的仿真模型，作为第一处理器类型处理器的仿真。
这提供了在选择模型以及选择仿真目标平台方面实现极大灵活性的可能性。
",G06F9/455,实质审查
US12278074B2,US18064348,"一种用于具有包括输出电压的输出的模拟电池单元的保护电路，包括：至少一个MOSFET，其连接到模拟电池单元的输出，用于短路模拟电池单元的输出；电容器，连接到至少一个MOSFET的栅电极；过电压检测装置，其被配置为基于超过所述输出电压的过电压限制，利用所述输出电压对所述电容器充电；以及阈值电压检测装置，其被配置为基于未达到电容器处的电压的阈值来释放栅电极。
",H02H9/00,暂缺
US12265771B2,US17697095,"一种用于将图形仿真模型划分为第一子模型和第二子模型的方法，包括：基于采样时间和/或资源分配，将至少一个第一块识别为属于第一子模型，并且将至少一个第二块识别为属于第二子模型；搜索块的循环组，其中，块全部具有相同采样时间的循环组被认为是原子的；识别块的非循环组；将来自所述循环块组和所述非循环块组的单独块分配给所述第一子模型或所述第二子模型，其中原子循环组的所有块被分配给相同子模型；从第一子模型生成用于处理器的程序代码；以及从第二子模型生成用于可编程逻辑模块的配置比特流。
",G06F30/34,授权
EP4506824A8,EP23189912.1,"描述和示出了一种计算机实现的方法（1），用于借助于模拟器（2）上的模拟环境（4）的至少一个计算单元（5）来验证ECU的至少一种ECU功能（f1，f2，f3）的执行，其中，在面向事件的离散仿真中，在连续仿真步骤之间的离散渐进仿真时间（tsim）的零时间假设下，在模拟器（2）上执行ECU功能（f1，f2，f3）。
通过基于模拟器（2）的观测服务（6）比较离散模拟时间（tsim）的进度和模拟器实时（treal）的进度，如果模拟器实时（treal）的进度超过离散模拟时间的进度，可以有效地确定模拟中堵塞情况的原因它（tsim）至少间接地超过预定的极限值（td）多个堆轨道（7），所述堆轨道创建至少一个计算单元（5）（8）其在相应堆轨迹（7）的创建（8）时最频繁地执行了至少一个计算单元（5），并且显示和/或进一步处理所确定的最常见的ECU功能（fmax）。
",G06F11/36,实质审查
EP4521233A1,EP24191642.8,"本发明涉及一种用于从开发平台的图形控制模型（12）生成用于目标平台的控制程序的计算机实现的方法，其中，图形控制模型，第一块（14a）的输出驱动第二块（14b）的输入，以及（i） 第一块（14a）和第二块（14b）被设计为使得第一块（14a）的输入信号（Ii）对应于第二块的输出信号（O2）；或（ii）第一块（14a）和第二块。
此外，本发明涉及数据处理装置（10），其中包括执行上述方法的手段、计算机程序产品和相应的计算机可读数据载体。
",G06F8/34,暂缺
EP4521360A1,EP23196456.0,"本发明涉及一种计算机实现的方法，用于自动注释具有空间重叠测量范围的房间传感器和区域传感器的传感器数据帧。接收到的传感器数据帧最初被独立地注释，从而为传感器数据帧中的每个检测到的对象分配一个边界框。基于时间相关性，对房间传感器的传感器数据帧和区域传感器的传感器信息帧进行分组。三维e对象的边界框被投影到区域传感器的图像平面中。当投影边界框和二维边界框之间的一致性质量度量高于预定义阈值时这些框被分配给同一个对象，不再进一步选中。随后，可以确定对象的属性。
",G06V10/82,暂缺
EP4407466B1,EP22197283.9,"一种用于在模拟器上测试作为虚拟控制装置的至少一个电子控制装置的方法，该模拟器包括至少一个模拟器计算单元。电子控制装置具有硬件配置，该硬件配置具有至少一个计算单元(和用于交换数据的外部接口，软件配置被分配给硬件配置，电子控制装置被映射到虚拟控制装置，其中电子控制装置的软件配置的内部功能被接管作为虚拟控制装置的软件配置的内部功能，电子控制装置的外部接口功能被模拟器计算单元的软件配置的数据传输功能替代，虚拟控制装置的软件配置被转换成用于模拟器计算单元的可执行代码并在模拟器上执行。
",G06F11/26,权利终止
US12250365B2,US18169893,"一种用于测试车辆的立体摄像机的测试装置，包括：计算单元；以及自动立体屏幕。计算单元被配置为生成合成图像数据并且在自动立体屏幕上输出合成图像数据，使得图像数据可被立体相机检测为立体图像。
",H04N17/00,授权
US12250491B2,US18063776,"一种用于生成场景的鸟瞰图像的计算机实现的方法，包括：(a)获取至少一个激光雷达帧，所述激光雷达帧包括具有固有距离信息的点和所述场景的至少一个相机图像；(b)通过使用所述至少一个LIDAR帧生成所述场景的网格表示，所述网格表示具有固有距离信息的所述场景中示出的表面；(c)通过将所述至少一个相机图像的像素分类为表示所述至少一个相机图像的地面像素或非地面像素来产生掩模图像；以及(d)通过增强的逆透视映射生成鸟瞰图像，该逆透视映射利用网格表示的表面固有的距离信息、被分类为地面像素的掩模图像的像素以及至少一个相机图像。
",G06T3/00,授权
US12248420B2,US18129438,"本发明涉及一种计算机系统和方法，其具有至少一个外围设备、用于控制外围设备的控制模块和经由第一数据链路连接至控制模块的连接模块。连接模块具有用于与外围设备交换信息的I/O数据端口，并且外围设备经由第二数据链路连接到I/O数据端口中的一个或多个。第一数据链路中的通道数小于连接模块的I/O数据端口数。控制模块包括第一网关，其接收与连接模块的I/O数据端口有关的多条设定点状态信息。
",G06F13/40,授权
JP2025031511A,JP2024074257,"减少对形成电池模拟器的电构成元件的需求。第一电路，其提供用于模拟第一电池单元的电压的第一单元电压；以及第二电路，其提供用于模拟第二电池单元的电压的第二单元电压。第一单元电压连接到局部电路接地，第二单元电压连接到相同的局部电路接地，并且与第一单元电压串联连接，使得局部电路接地形成第一单元电压和第二单元电压的极。控制电子装置被配置为对第一单元电压和第二单元电压进行闭环控制。通过通过一个控制电子设备驱动控制两个单元电压，可以省略构成元件。图2是示出图2
",G01R31/367,暂缺
US20250080610A1,US18728472,"一种用于自动注释传感器数据的计算机实现的方法，包括：接收多个传感器数据帧；使用至少一个神经网络注释所述多个传感器数据帧，其中所述注释包括向每个传感器数据帧分配至少一个数据点，并且向每个数据点分配至少一个状态属性；基于所述至少一个状态属性对所述数据点进行分组，其中第一组包括所述至少一个状态属性在定义的值范围内的数据点；从第一组中选择一个或多个数据点的第一样本；以及确定第一样本中的一个或多个数据点的质量度量。
",H04L67/12,暂缺
EP4455891A3,EP24159546.1,"本发明涉及一种计算机实现的方法和系统，用于基于标准创建一个包含虚拟车辆环境的场景库（10），用于测试机动车辆的高度自动化驾驶功能，具有：使用预定标准，由传感器数据（12）包含的第一个场景数据集（16），代表感兴趣的驾驶情况（14）；比较（S3）代表感兴趣的驾驶情况（14）的第一个场景数据集（16）与由场景库（10）包含的第二个场景数据集（18）。本发明还涉及一种计算机实现的方法，用于验证模拟模型的模型质量，以执行机动车辆的高度自动化驾驶功能。
",G06F11/36,实质审查
CN112886968B,CN202011310766.1,"本发明涉及一种模块，包括：用于产生数据流的功能单元；数据输出端，其于向解串器单元输出数据流，所述解串器单元设置为用于获取数据流；第二产品系列的串行器单元，其包括用于配置串行器单元的寄存器；和配置数据输入端，其用于获取定义串行器单元的第一寄存器配置的配置数据，设有数据储存器，在其上存储有从第一产品系列的串行器单元的寄存器地址到第二产品系列的串行器单元的寄存器地址上的第一映射；和配置单元，所述配置单元配置为用于从配置数据输入端读取配置数据，借助于所述第二寄存器配置能将第二产品系列的串行器单元配置为，使得根据第二寄存器配置来配置第二产品系列的串行器单元的寄存器。
",H03M9/00,授权
EP4513204A1,EP23192609.8,"电池模拟器，包括提供第一个电池电压以模拟第一个电池电压的第一个电路和提供第二个电池电压以模拟第二个电池电压的第二个电路。第一个电池电压放在本地电路质量上，第二个电池电压放在同一本地电路质量上，并以这种方式连接到第一个电池电压，本地电路块形成第一电池电压和第二电池电压的极点。设置控制电子设备以控制第一电池电压和第二电池电压。通过单个控制电子设备控制两个电池电压，可以节省组件。
",G01R31/28,实质审查
EP4343552B1,EP23191816.0,"一种用于连接到与总线或网络接口连接的被测对象的故障插入单元，其中，所述被测对象可以借助于所述故障插入单元经受大于所述总线或网络接口被设计用于的最大电压的故障电压，所述故障插入单元具有熔丝电路，所述熔丝电路保护连接到所述故障插入单元的总线或网络接口免受大于所述总线或网络接口被设计用于的最大电压的电压的影响。这提供了一种即使在以高带宽总线工作的系统中也能够使用故障插入单元而不存在由于过电压而损坏总线或网络接口的风险的方式。
",G01R31/327,授权
JP2025025408A,JP2023130144,"本发明提供一种避免当乘法余数的数值变大时发生的位数溢出现象（溢出）的加密控制程序和利用该加密控制程序的加密控制系统。包括对指令输入进行加密第1加密部101的输入装置具有秘密计算部201控制装置20在由具备解密部301及对反馈信号进行加密第二加密部302的工厂控制装置30构成的加密控制系统中使用来自输入装置加密的指令输入和来自工厂控制装置的加密的反馈信号（来自控制对象30a的传感器信号）将存储在规定数据类型中的值为了不在乘法时引起位数溢出分割成更小的值进行计算。图5是示出图5
",G09C1/00,公开
EP4507253A1,EP23190448.3,"本发明涉及在网络系统（10）中进行数据传输的计算机实现的方法，该网络系统包括一个外部网络（12）和一个内部网络（14），其中，所述外部网络（12）包括至少一个第一网络元件（18）和一个外部网络通信元件（22）；其中，所述内部网络（14）至少包括第二网络元件（20）和内部网络通信元件（24），其中，所述第二网络元件（20）被设计为仅在所述内部网络（14）内通信，其中该过程包括以下步骤：-对要发送的数据进行编码（S10，34）；将编码数据（S12）传输到外部网络通信元件（22）；通过IP通信通道将数据（S14）传输到内部网络通信元件（24）并对数据进行解码（36）；通过内部网络通信元件（24）将数据（S16）重新编码到应用层；将重新编码的数据（S18）传输到第二个网络元件（20）。
",H04L12/46,实质审查
US12223301B2,US18185413,"一种生成源代码的方法，包括：将框图变换成中间表示，其中将框图变换成中间表示包括变换至少两个块，其中至少一个循环由变换操作块产生；识别中间表示中的至少一个候选循环，其中候选循环的循环主体包括访问数组变量的至少一个指令；从所述至少一个候选循环中识别至少一个可并行化循环；确定所述至少一个可并行化循环的构建选项和所述数组变量；基于所确定的造型选项将造型标记插入中间表示中；以及将中间表示转换成源代码。
",G06F8/41,授权
EP4501730A1,EP23188955.1,"本发明涉及一种计算机实现的方法，用于提供用于在虚拟测试（12）中生成关键交通场景（10）的机器学习算法（a），以验证机动车辆的自动驾驶功能，包括接收（S3）由奖励功能（18）奖励（20）和交通流模拟（14）的当前状态（22）生成的奖励，以响应（16）至少一个道路使用者（15）的执行动作（16）；并提供（S4）训练过的机器学习算法（A），如果一个代表虚拟测试（12）的交通场景（10）的关键性的参数（24）位于预定范围内。本发明涉及提供机器学习算法（A）的系统（1）。
",B60W40/00,实质审查
US12216967B2,US17239068,"一种计算机实现的方法，用于通过模拟器算术单元实时模拟特定电动机的操作，该模拟器算术单元包括在其上实现通用电动机模型的可编程逻辑器件。该方法包括：提供对应于通用电动机模型的通用方程组；接收与待模拟的特定电机对应的用于通用方程组的特定信息，并将该信息输入到通用方程组中；生成包含用于计算特定电机的操作的矩阵操作所需的算术运算中的至少一些的特定库；在通用电机模型中执行对实时仿真特定电机的操作所需的特定库的算术操作的引用；以及通过在模拟器运算单元上运行通用电动机模型来模拟特定电动机的操作。
",G06F30/20,授权
EP4492226A1,EP23185063.7,"用于无故障地运行基于 FPGA 的应用程序的多个计算机控制的构建和/或初始化步骤的计算机系统，该计算机系统配置为在构建步骤中生成 FPGA 程序代码，将其转移到受控制的 FPGA，并在初始化步骤中为运行做好准备，该计算机系统进一步配置为此，将构建和/或初始化步骤的结果集中存储和/或控制在可写内存中，并在构建和/或初始化步骤和/或初始化的 FPGA 程序代码到期之前通过读取可写内存来检查是否已完成至少一个以前的构建和/或初始化步骤。这提供了一个计算机系统，以避免现有技术的缺点。
",G06F8/71,实质审查
US12197336B2,US18213029,"在FPGA中，FPGA的内存将有效增加。这是通过一种用于在FPGA上实现具有模型状态相关存储器前瞻的模型自适应高速缓冲存储器的计算机实现的方法来实现的。
",G06F12/08,授权
EP4485003A1,EP23199431.0,"一种用于实时模拟激光束扫描小物体复合体（如粮田）的设备，包括具有小物体复合体的单片3D模型的虚拟环境，以及用于模拟传感器以通过激光束进行距离测量的传感器模拟。传感器模拟包括模拟激光束的光线投射程序和噪声程序。噪声例程被设计为从包括各种强度分布的数据库中确定激光束在3D模型表面上的入射角，选择与所确定的入射角相关联的强度分布。根据激光束对小物体复合体的穿透深度，可以从每个强度分布中读出激光束反射的强度部分。噪声程序旨在计算噪声距离测量，同时考虑所选的强度分布，以模拟激光束在各种小物体上的各种反射，如粮田的耳朵。
",G01S7/497,实质审查
EP4485095A1,EP24181414.4,"描述和示出了一种用于用模拟器（3）测试电子控制单元（2）的计算机实现的方法（1），其中所述模拟器（3）在计算单元上以数字方式计算数学环境模型（4），其中所述环境模型（3）至少部分地模拟所述控制单元（2）的环境，其中，所述控制装置（2）和所述模拟器）经由对应的I/O接口（5，6）彼此耦合和相互作用，其中，在所述仿真器（3）上的所述环境模式（4）的所述数字计算中，执行矩阵向量乘法，其中，矩阵（M）乘以向量（v）得到结果向量（r），其中，将所述矩阵向量乘法分解为两个被加数的总和（b）的序列，其中每个求和是两个因子的乘积，一个是矩阵（M）的元素（ki），另一个是向量（v）的元素。
矩阵向量乘法的优化规划和执行是通过以下方式实现的：在序列确定步骤（8）中，求和（b）被确定为序列中的下一个求和，其求和取决于来自矩阵（M）的最高占用行的矩阵元素（ki），其中矩阵元素（ki）是从具有最高求和可用性（P）的最高占据行中选择的，因此序列确定步骤，其中，通过将矩阵（M）中的所有矩阵元素（Mij）设置为零，残差矩阵（M’）从矩阵（M”）中出现，这些元素涉及在执行计划序列的求和形成（b）仓的序列（b）中已经计划的求和构成，其中，用于计算求和的乘积构成是在序列中执行的，因为求和是执行求和形成的必要条件，并且在最终求和步骤（9）中，通过连续的对加法计算结果向量（r）每行中的前一个求和（b）得到的和。
",G05B17/00,实质审查
US12181505B2,US18130442,"一种用于测量从馈送天线经由反射器到雷达传感器测试区的路径上的传递函数的系统，包括：消声室；馈送天线，其中馈送天线被配置为发射和接收雷达信号，并且其中馈送天线与反射器一起被设置在消声室内；雷达传感器测试区，其中雷达传感器测试区是消声室内的预定区域；以及设置在雷达传感器测试区中的回射器，其中，回射器被配置为使雷达频率范围中的测量信号的至少一部分经由反射器反射回到馈送天线，其中，测量信号经由反射器从馈送天线接收。
",G01R29/08,授权
CN112166428B,CN201980032587.5,"用于系统的基于事件的模拟的方法，所述模拟在包括第一运算单元和第二运算单元的计算机系统上执行，第一运算单元具有模拟时间；第二运算单元具有系统时间；第二运算单元执行模拟器应用，在其上执行模拟对象；以及第一运算单元管理事件等待队列，对每个模拟步骤列出事件并该事件配置要执行的进程和设定用于执行进程的模拟时刻；第二运算单元具有虚拟时钟发生器，对于每个模拟步骤：通过第一运算单元将开始信号传送给虚拟时钟发生器以用于在第二运算单元中执行新的模拟步骤；以及基于在之前的模拟步骤的模拟时间与新的模拟步骤的模拟时间之间的时间差增加第二运算单元的系统时间，以及在配置给所等候的进程的模拟时刻执行该进程。
",G06F30/20,授权
EP4475011A1,EP24174757.5,"本发明涉及一种用于对交通状况进行分类的计算机实现的方法和系统（1），该交通状况包括机动车辆的环境数据的数据集，该数据集是通过有向图（G）对第一数据集（DS1）的应用（S2）预先确定的，其中，所述有向图（G）的节点（11）分别根据在所述自车辆（10）和/或所述同车（12）相对于车辆环境的运动行为的至少一个分段中以时间间隔（14）满足的第一条件对所述第一数据集（DS1）进行分段，其中，所示有向图；如果所有指定路段都满足给定交通状况的第二条件（16），则对给定交通状况进行分类（S3）；以及一个输出（S4），该类表示给定的交通状况（K）和/或表示给定交通状况的第二数据集的相应开始和结束时间（DS2）。
",G06F18/20,实质审查
EP4471650A1,EP23176385.5,"根据本发明，提供了一种用于为包括多个子模型（11、12、13、21、22）的仿真系统创建残差模型（3）的方法，该方法具有以下过程步骤：选择至少一个子模型（11、12、13、21、22）作为不应包含在剩余模型（3）中的目标模型（21，22），捕获必须连接到目标模型（21、22）的端口的仿真系统（1）的端口，在残差模型（3）中创建具有相同属性的相应端口的检测端口，捕获必须连接到目标模型（21、22）的仿真系统现场总线（1）；以及创建适用于残差模型中捕获的现场总线的现场总线控制器（3）。
",G06F30/20,实质审查
US20240385290A1,US18664932,"一种用于测试环境传感器的测试装置。测试装置包括用于环境传感器的容器，其被配置为发射环境信号。第一物体模拟器被配置为接收环境信号作为第一接收信号。第二物体模拟器被配置为接收环境信号作为第二接收信号。处理器被配置为从第一和第二接收信号中确定信号差，特别是相位差和/或频率差，并且根据信号差来确定环境传感器、第一物体模拟器和第二物体模拟器之间的空间关系，和/或第二物体模拟器。
",G01S7/40,公开
US20240385953A1,US18667582,"一种测试系统，用于应用场景的资源优化参数变化的方法，以测试至少一个被测真实和/或虚拟系统和/或显示测试结果。提供至少一种场景，其中场景由至少一个参数定义，并且其中参数由至少一个数值和/或文本值配置，其中数值和/或文本值可由附加的数值和/或文本值配置。执行至少一个基于场景的测试，其中场景由配置的参数定义并随配置的参数而变化。输出测试结果。
",G06F11/36,暂缺
US20240385289A1,US18578334,"一种用于测试距离传感器的测试装置，包括：接收元件，用于接收电磁自由空间波作为接收信号；发射元件，用于发射电磁输出信号；以及信号处理单元。在模拟运行期间，接收信号或从接收信号导出的接收信号通过信号处理单元以可预先给定的时间延迟被引导，以形成作为模拟反射信号的时间延迟信号。信号处理单元被配置为完全处理接收温度口腔相干和时间受限的传感器信号或在延迟步骤中从接收的传感器信号导出的接收的时间相干和时间受限的传感器信号，其中可预定义的时间延迟作为恒定工作时间延迟以形成时间延迟的传感器信号其中可预定义的时间延迟在延迟步骤开始时预定义。
",G01S7/40,暂缺
US12119812B2,US18065637,"一种用于电流均匀分配的电子电路，包括：第一MOSFET和第二MOSFET，其中第一MOSFET和第二MOSFET并联连接以便分配施加到输入端的电流，该电流流向电子电路的输出端，其中输入端分别连接到第一MOSFET的漏端和第二MOSFET的漏端；以及用于控制电压的端子，其中控制电压被施加到第一MOSFET的栅极端子和第二MOSFET的栅极端子。第一MOSFET包括在第一MOSFET的栅极端子处的第一电阻器，并且第二MOSFET包括在第二MOSFET的栅极端子处的第二电阻器。
",H03K17/14,授权
US20240337695A1,US18619303,"一种用于使用消息来测试控制设备的测试组件，包括：接收器，其被配置为以片段形式接收所述消息，其中每个片段具有第一标识符和第二标识符，所述第一标识符表示所述消息中的相应片段的时间顺序；缓冲存储器，被配置为基于第一和第二标识符存储片段，其中第二标识符使得消息的所有片段被及时记录在缓冲存储器中，以允许在消息的第一片段被发送到控制设备之前操纵消息；机械手，被配置为基于预定数据修改消息；以及发送器，其被配置为将修改后的消息发送到控制设备。
",G01R31/319,暂缺
US20240330674A1,US18190187,"本发明涉及一种用于在虚拟训练环境中训练用于致动技术设备的神经网络的方法。该方法包括在神经网络和技术设备的第一模拟之间建立第一数据链路，然后通过致动第一模拟来训练神经网络。一旦已经实现了第一训练目标，第一数据链路被断开，并且在神经网络和技术设备的第二仿真之间建立第二数据链路，以便通过启动第二仿真来训练神经网络。第二模拟被配置为比第一模拟更逼真，并且由于其更高的真实程度，需要更多的数学运算来进行模拟周期。
",G06N3/08,暂缺
US20240329201A1,US18621356,"一种用于模拟传感器(雷达，LIDAR)的距离的模拟器。模拟器包括接收器，其被设置为从传感器(雷达、LIDAR)接收第一传感器信号并将其转换为工作信号。具有多条延迟线的延迟部分被施加到至少一个衬底。第一电开关装置，其被设置为根据第一选择信号以使得工作信号的信号路径包括第一选择的方式来切换延迟线的第一选择。发射器被设置成在通过信号路径之后将工作信号转换成第二传感器信号并将其发送到传感器(雷达，激光雷达)。还提供了一种用于操作模拟器的方法和用于模拟器的延迟部分。
",G01S7/40,公开
JP7558796B2,JP2020212561,"一种用于测试以超声波工作的距离传感器的测试装置，其中，待测试的距离传感器至少包括用于发射信号的传感器辐射元件和用于接收反射信号的传感器接收元件。为了有效和准确地测试和激励距离传感器，测试装置具有用于接收从待测试的距离传感器发射的超声波的测试接收元件，和用于辐射测试超声波的至少一个测试辐射元件，以及信号处理单元，其中，由测试接收元件接收的超声波作为接收信号被发送到信号处理单元和信号处理单元，作为接收信号和与要模拟的距离有关的模拟距离信息的函数，并且确定用于测试辐射元件的激励信号。
",G01S7/52,授权
EP4432075A1,EP24162626.6,"一种用于从框图的一个或多个块生成源代码的方法，所述框图包括至少两个块，源块发射与阵列变量相对应的矩阵信号，以及操作块接收所述矩阵信号并且基于施加于所述矩阵信号的操作来发射输出信号。该方法包括将框图变换成中间表示，标识中间表示中的至少一个候选循环，其中候选循环的循环主体包括访问数组变量的指令，从至少一个候选循环中标识至少一个可并行化循环，确定所述至少一个可并行化循环的构建选项和所述数组变量，基于所确定的构建选项将构建标记插入所述中间表示中，以及将所述中间表示转换成源代码。
",G06F8/30,实质审查
EP4428821A1,EP24157920.0,"本发明涉及一种计算机实现的方法，用于确定移动系统的自我姿态，并通过因子图（10）表示的优化问题创建基于3D椭球体的环境冲浪图，其中通过将冲浪数据与冲浪图进行比较来识别新的冲浪者（16）和已知的冲浪者，并将冲浪因子（10）添加到先前识别的冲浪者的因子图（14a，14a'）和/或添加到因子图（十）的冲浪节点（12b）和新识别的冲浪器（16）的冲浪因子（14a）。
此外，本发明涉及一种用于数据处理的设备，包括用于执行上述方法的装置和计算机程序产品，该计算机程序产品包括在计算机执行程序时使上述方法执行的命令。
此外，本发明涉及一种存储上述计算机程序产品的计算机可读数据载体。
",G06T7/579,实质审查
US20240296112A1,US18593244,"一种计算机实现的方法，用于经由模拟器上的模拟环境的至少一个计算单元来测试控制单元的至少一个控制单元功能的执行。控制单元功能是在连续地提前两个仿真时间之间的零时间假设下执行的在模拟器上的面向事件的离散模拟中的连续模拟步骤。模拟中死锁情况的原因被识别，因为在模拟器上操作的观察服务将离散模拟时间的提前与模拟器的提前实时地进行比较，并且如果仿真器的超过离散仿真时间的提前的实时提前超过预定极限值，则至少间接地创建至少一个计算单元的堆栈跟踪。
",G06F11/36,公开
US20240289526A1,US18573457,"一种模拟器，其被配置用于模拟具有至少一个开关的电力电子电路，所述开关用于具有至少一个计算机的测试台。该模拟器包括状态识别装置，该状态识别装置被配置为识别至少一个开关的状态，其中该状态基于强制或自然开关操作，每个开关操作具有用于至少一个电开关的至少一个第一电参数的至少两个状态条件。模拟器被配置为基于状态输出功率电子电路的至少一个输出值。
",G06F30/3308,暂缺
US20240289418A1,US18572221,"一种模拟器包括被配置为模拟技术系统的运算单元。模拟器被配置为使用矩阵向量乘法基于输入向量来确定用于输出模拟器的输出信号的输出向量。矩阵代表技术性SY柄。算术单元具有至少一个乘法器和至少一个加法器，用于矩阵向量乘法。所述至少一个乘法器和所述至少一个加法器每个都被配置用于时分复用功能，以执行它们各自的任务。提供一种调度器其被配置为将所述至少一个乘法器的输出值分配给所述至少一个加法器。
",G06F17/16,暂缺
US20240267429A1,US18434055,"一种用于借助于第一程序来变换所记录的通信数据的方法，其中，所述通信数据由来自网络或总线通信的消息给出，其中，所记录的通信基于第一通信矩阵来执行，其中，所记录的通信数据被提供以借助于测试系统被发送到被测ECU，其中，所述测试系统经由第一通信链路被连接到被测ECU，其中，所述被测ECU基于第二通信矩阵来配置。
",H04L67/12,公开
EP4409367A1,EP22786818.9,"本发明涉及一种用于测试至少一个控制器的方法。在模拟器上提供至少两个单独的网络，其中，待测试的控制器经由第一网络和第一控制器接口连接至模拟器，并且待测试的控制器被设计为与至少一个第一附加控制器通信。第一附加控制器的通信至少部分地以记录消息的形式提供，其中用于播放记录消息的再现单元通过至少一个再现接口连接到模拟器上的第二网络或第三网络，再现单元通过再现接口连接到模拟器。
",G05B17/02,期限届满
US20240255566A1,US18427448,"一种用于测试功率电子控制器的测试装置，其中控制器具有电源连接和负载连接。多个功率电子模块中的每一个具有电源连接、至少一个负载连接和用于控制功率电子模块的接口。在测试装置的操作状态中，控制器的供应连接各自连接到供应侧电力电子模块的负载连接，并且控制器的负载连接各自连接到负载侧电力电子模块的负载连接。因此，就了，通过控制装置至少根据所确定的用于前馈控制的负载侧功率电子模块的负载连接的电连接参量来计算所确定的在供电侧功率电子模块的负载连接处的实际电流，可以简单的方式实现快速的供电侧电流调节。
",G01R31/28,公开
EP4096170B1,EP22169648.7,"一种用于在网络系统中传输数据的方法和一种网络系统，该网络系统具有处于混杂模式的第四网络元件的网络控制器的操作，以及在第一网络和第二网络之间产生IP隧道，第三网络元件和第四网络元件是经由接入元件引导的IP隧道的特定端点。
",H04L12/46,授权
EP4402647A1,EP22786900.5,"本发明涉及一种用于自动注释诸如视频帧或音频帧之类的传感器数据帧的计算机实现的方法。基于至少一个条件属性将接收帧分组为多个分组，所述条件属性涉及当FR时存在的环境条件记录AME。使用神经网络注释与条件属性的特定值范围相对应的第一分组。计算机基于第一样本帧确定注释的质量水平。如果至少一个帧的质量等级低于预定阈值基于第一样本的校正注释重新训练神经网络。如果质量高于预定义阈值，则输出注释帧。本发明还涉及非易失性计算机可读介质和计算机系统。
",G06V10/44,期限届满
US12026485B2,US17940036,"一种用于从框图的一个或多个块生成源代码的方法，所述框图包括至少两个非虚拟块和两个非虚拟块之间的至少一个信号链路，所述方法包括：将所述框图变换为中间表示，其中将所述框图变换为所述中间表示包括变换可访问多分量变量的第一块；连续优化所述中间表示；以及将优化的中间表示转换成源代码。变换第一块包括：测试由第一块和相邻块组成的块对是否包括相等分配；以及移除其中在两侧上存在对相同变量的引用的任何分配。
",G06F8/41,授权
EP4390699A1,EP23193448.0,"本发明涉及一种计算机实现的方法和系统，用于确定车辆设备和/或车辆功能的虚拟测试和/或模拟的无错误执行所需的相互兼容的系统元件（10a、10b、10c、10d），通过以下步骤：提供（S1）预先记录的选择或选择执行车辆设备和/或车辆功能的虚拟测试和/或模拟所需的第一系统元件（10a）中的至少一个；确定（S2）支持第二系统元件（10b）的第一系统元件（10）的兼容性要求中的至少一个，以及输出（S3）指定的第二系统元件（10b），其支持正确执行车辆设备和/或功能的虚拟测试和/或模拟所需的第一系统元件（10）的兼容性要求。
",G06F11/263,实质审查
EP4390700A1,EP23195862.0,"本发明涉及一种计算机实现的方法和系统，用于确定执行虚拟测试和/或模拟（a）的算法的特定版本，特别是测试和/或者模拟算法（a）所需的、车辆设备和/或者车辆功能的虚拟测试和／或模拟的无错误执行，兼容测试和/或模拟数据（24a、24b、24c、24d；124a、124b、124c、124d）的配置，包括测试和/或模拟数据（24a、24b、24c、24d；124a、124b、124c、124d）的第一配置（10）和/或第二配置（12）的兼容性要求（14a、14b）之一的确定（S2），以及指定数据集（D1，D2）的输出（S3），其支持测试和/或模拟数据（24a，24b，24c，24d；124a，124b，124c，124d）的第一配置（10）和/或第二配置（12）的兼容性要求（14a，14b）。
",G06F11/263,实质审查
EP4390747A1,EP23211097.3,"本发明涉及一种用于创建具有多个具有通信接口（2）的仿真模块（1）的协同仿真（100）的方法，其中具有仿真代理（4）的仿真模型（1）是可执行的，包括以下步骤：S1）创建仿真组件（6），所述仿真组件是用于一个或多个仿真模块的容器；S2）将模拟模块（1）分类为模拟组件（6），S3）为每个模拟组件（6）提供运行时环境信息模块（8），S4）为每个模拟组件（6）提供包含对所述模拟模块（1）的通信接口（2）的描述的通信单元（10），使得与其他模拟组件（5）的通信成为可能；S5）将模拟组件（6）的通信单元（10）连接到其他模拟组件（六）的一个或多个通信单元（十），S6）创建协同仿真应用程序（18），其中仿真组件级别（12）的仿真组件（6）被分配给具有仿真组件（12）运行时环境信息模块（8）的仿真代理类型（16）。以这种方式，提供了一种用于创建通用协同仿真（100）的时间和存储器高效的方法。
",G06F30/20,实质审查
US20240202396A1,US18540443,"一种用于仿真电动机的相电流以测试功率电子控制单元的测试组件和方法，该功率电子控制单元被设计成控制电动机并且可以连接到测试组件。该测试组件具有电感器仿真器，该电感器仿真器使用功率电子电路来模拟作为控制单元的电负载的电动机，其中电感器仿真器充当电源。此外，测试组件具有测试装置，该测试装置被设计为根据对依赖于控制单元的输出电压的变量的分析来将电感器仿真器切换到另一操作状态。
",G06F30/20,暂缺
US20240202390A1,US18173814,"通过模拟开发和测试多部件系统和/或其相互作用部件。该模拟包括至少两个彼此交换状态数据的实体。一种用于这种开发和测试的方法，包括：请求加入模拟的实体；以及请求实体以这样的方式加入仿真，即，由请求实体进行的状态数据的传输与由先前加入仿真的实体进行的状态数据的传输基本上同时发生。
",G06F30/20,暂缺
US20240201248A1,US18540347,"一种用于仿真电动机的相电流以便测试功率电子器件控制器的测试装置。控制器被设置成驱动电动机并且可连接到测试装置。该测试装置包括电感仿真器，其通过功率电子电路模拟作为控制器的电负载的电动机，并且包括测试装置，该测试装置被设置为根据对控制器的输出处的输出电压的分析来将电感仿真器切换到不同的操作模式。还提供了一种用于借助于用于测试控制器的测试装置来仿真电动机的相电流的方法。
",G01R31/28,暂缺
US20240201247A1,US18535921,"一种用于仿真电动机的相电流以测试电力电子控制单元的测试装置。控制单元驱动电动机并且可以连接到测试装置。电感仿真器通过功率电子电路模拟作为控制单元的电负载的电动机，电感仿真器充当电流源。测试装置根据对控制单元的输出电压的分析来影响电感仿真器的相电流。测试装置执行分析，使得控制单元的输出电压与多个、优选三个电压范围相比较，并且根据控制单元的输出电压所处的电压范围来指定相电流的控制变量的相应范围，该控制变量是电感仿真器的控制电压。
",G01R31/28,公开
US20240201965A1,US18067738,"一种框图，包括至少三个块，包括：发射第一复合信号的源块；接收第一复合信号并发射具有相同尺寸的第二复合信号的通过块；以及接收第二复合信号并丢弃第二复合信号的至少一个分量的滤波器块。一种用于从框图的一个或多个块生成源代码的方法，包括：将所述框图变换成中间表示，包括变换所述至少三个块；将至少一个优化应用于中间表示，包括确定是否满足存储器条件；基于满足的存储条件，用较小维的辅助变量替换经过的块的输出变量；以及将优化的中间表示转换成源代码。
",G06F8/40,暂缺
US20240192265A1,US18534626,"一种用于测试电力电子控制器的测试装置。电中间网络中的中间网络电流被减小，因为在中间网络中流动的中间网络电流由控制器和控制器确定控制器改变至少一个负载侧电力电子模块的至少一个控制值，使得当负载侧电力电子模块的接口被施加修改的控制值时，中间网络电流被减小。
",G01R31/28,公开
US20240192317A1,US18535888,"一种用于环境传感器系统的模拟装置和方法，所述环境传感器系统被配置为基于相应的信号回波来检测至少一个特定对象。接收装置，其被配置为接收从所述环境传感器系统发送的第一信号，并将其转换为第一操作信号。一信号路径，其连接至该接收装置，用以接收该第一操作信号，该信号路径系被配置成经由一第一信号处理，第二操作信号，其是第一操作信号和相应信号回波的函数。相应的信号回波由至少一个信号参数表征。提供第二信号处理以提供具有变化的至少一个信号参数。发射装置被配置为将第二操作信号转换为第二信号，并将第二信号发送到周围环境传感器系统。
",G01S7/40,公开
US20240184538A1,US18074802,"一种用于在基于模型的测试环境中配置系统模型的模型组件的方法，其中所述测试环境包括模型编辑器，其中所述测试环境在具有处理器和显示器的计算机系统上执行，并且其中所述模型组件具有多个端口，所述方法包括以下步骤：S1)将系统模型加载到模型编辑器中，S2)接收对至少包括图形端口标识符的第一端口的用户选择，S3)将所选择的图形端口标识符分配给列表，S4)在测试环境中显示列表，S5)接收对来自列表的第一端口标识符的用户选择，S6)接收对第二端口的用户选择，S7)通过信号连接将第一端口连接到第二端口。
",G06F8/34,授权
CN113796047B,CN202080033771.4,"本发明示出且说明了一种用于重构(U)预定的分布式实时仿真网络(2)的计算机实现的方法(1)，其中，仿真网络(2)具有多个网络节点(4、RK、R、IO)和多个数据连接(DV)，其中，每个网络节点(4、RK、R、IO)具有至少一个数据连接接口以连接数据连接(DV)，其中，网络节点(4、RK、R、IO)通过数据连接(DV)至少部分处于通信连接(KV)中，并且其中，在仿真网络(2)运行中在至少一个网络节点(4、RK、R、IO)上实施仿真应用(5)。用所述方法可以自动找到实时仿真网络(2)的一种结构，在该结构中，以如下方式减少并且尽可能避免处于临界状态的通信连接(KV)，即，检测仿真网络(2)的拓扑，使得存在有关网络节点(4、RK、R、IO)和在网络节点(4、RK、R、IO)之间的数据连接(DV)的拓扑信息，特别是为仿真网络(2)的网络节点(4、RK、R、IO)确定节点数据率的预期值(E‑KDR)和/或节点延时的预期值(E‑KL)。
",H04L41/14,授权
US20240176926A1,US18070968,"一种用于模拟在计算环境中被建模为框图的一个或多个块的程序的计算机实现的方法和系统。所述一个或多个块具有至少一个信号，所述计算环境包括模型编辑器、数据定义工具和代码生成器。该方法包括：在模拟环境中模拟第一时间跨度的框图，显示作为模拟时间的函数的信号值，在特定模拟时间接收暂停命令和新的参数值，在模拟环境中模拟第二时间跨度的框图，以及显示来自第一时间跨度的信号的值和来自第二时间跨度的信号的值两者。来自第二时间跨度的值与来自第一时间跨度的值图形地不同地显示。
",G06F30/20,暂缺
EP4375869A1,EP22209730.5,"本发明涉及一种用于创建和提供具有至少一个FPGA总功能（2）的FPGA模型（1）的FPGA构建结果的方法，该方法具有以下处理步骤：（a） FPGA子系统（5）的识别，从而FPGA模型（1）的FPGA功能可以通过FPGA子系统来配置；b） 识别FPGA模型（1）的用于在处理器上执行的预缩放子系统（6）和缩放后子系统（7）；c） 在缩放前子系统（6）和缩放后子系统（7）中识别内部和外部接口，由此内部接口确保FPGA模型（1）内的数据流，而外部接口确保远离FPGA模型（2）的数据流；d） 生成FPGA整体功能（2），e） 从生成的FPGA总功能生成FPGA构建结果（2），其中FPGA构建结果包括单个总容器文件，f） 将FPGA构建结果提供给另一个应用程序以确定整个FPGA模型（1）和处理器模型（3）的功能。
通过这种方式，提供了一种对FPGA构建结果进行建模的方法，这导致了统一的FPGA模型（1），从而可以使FPGA构建结果的重用变得安全和容易。
",G06F30/323,实质审查
US20240160806A1,US18406790,"一种用于驾驶员辅助系统的虚拟测试环境，其中虚拟道路用户基于游戏理论被模拟。虚拟测试环境被设计为将虚拟测试环境中的至少一个预定交通状况识别为游戏状况，其中涉及第一路径用户和第二路径用户，以将第一路径用户和第二路径用户指定为第一玩家和第二玩家。分配给游戏情形的回报矩阵被存储在虚拟测试环境中。虚拟测试环境被设计成将策略从策略选择分配给游戏情形中的两个玩家中的每一个，这取决于他们各自的点数帐户的余额，并在游戏情形中以他们根据他们各自的分配策略来行动的方式来控制两个玩家中的每一个。
",G06F30/20,公开
US20240157974A1,US18127993,"本发明涉及一种用于将图像数据从车辆的虚拟环境输出到控制单元的模拟装置和方法，其被设计为根据图像数据执行至少一个车辆功能。环境模拟器被配置为模拟车辆的环境，并将关于环境的数据传输到传感器模拟器，其被配置为根据关于环境的数据模拟车辆的至少一个传感器，以检测环境，并且根据其模拟输出图像数据。环境模拟器被设计为确定关于模拟时钟时间处的环境的数据，并且根据模拟时钟时间将这些数据传输到传感器模拟器，并且其中传感器模拟器被配置为在传感器时钟时间处输出图像数据。
",B60W60/00,暂缺
EP4366849A1,EP21743142.8,"一种用于驾驶辅助系统的虚拟测试环境，其中基于游戏理论模拟虚拟道路用户。每个道路用户被分配点数余额。虚拟测试环境被设计成在虚拟测试环境中识别第一和第二道路用户被涉及作为游戏情况的至少一个预定义交通情况，将第一路路用户指定为第一玩家并且将第二玩家指定为第二玩家。分配给游戏情形的回报矩阵被存储在虚拟测试环境中。虚拟测试环境被设计为向两个玩家分配来自选择的一个策略游戏情形中的策略取决于其相应点数的状态而平衡，并控制游戏情形中的两个玩家，使得他们根据其相应分配的策略来行动。虚拟测试环境被设计成读取相应的支付值根据游戏情况的进程，从第一玩家和第二玩家的支付矩阵中，将其相对于各个玩家的点数余额进行补偿。虚拟测试环境包括虚拟测试车辆和用于使用驾驶辅助系统控制虚拟测试车辆的逻辑接口。虚拟测试环境还包括编程接口，用于改变支付矩阵或用于改变点数余额的至少一个偏移值，该偏移值影响策略向第一玩家和第二玩家的分配。该编程接口使得有可能，通过影响策略的分配，影响驾驶辅助系统的虚拟测试环境的难度。
",A63F13/803,撤回
EP4369240A1,EP22206970.0,"提出了一种用于将车辆的虚拟环境（EGO）的图像数据（BD）输出到控制单元（SG）的模拟设备（SV）和方法，其被设计为根据图像数据（BD.）执行至少一个车辆功能。它规定：环境模拟器（US），其被设计成模拟所述车辆（EGO）的环境并将关于所述环境的数据（Da）传输到传感器模拟器（SeSi），所述传感器模拟器被设计成根据关于所述周围的数据（Da）来模拟所述车辆（EGO）的至少一个传感器（SE1、SE2、SE3、SE4），用于检测所述环境并根据其模拟输出所述图像数据（BD），其中，所述环境模拟器（US）被设计成在模拟时间点处确定关于所述环境的所述数据（Da），并根据所述模拟时间点将所述数据传输到所述传感器模拟器（SeSi），并且其中，所说传感器模拟器（Semi）被设计为在传感器时钟时间处输出所述图像数据（BD），其中，关于所述传感器时钟时间的信息被存储在所述环境仿真器（US）中，其中所说环境模拟器（美国）被设计成对于所说传感器时钟时间将所说数据（Da。
",G06F30/20,撤回
EP4060376B1,EP22159467.4,"一种用于LiDAR传感器的测试系统，其包括触发检测器和连接到触发检测器的信号发生器，信号发生单元包括具有预定义数量的像素的显示面板，并且信号发生器被配置成将相同强度的像素聚集到集群中。还提供了一种用于测试LiDAR传感器的方法。
",G01S7/497,授权
CN113009992B,CN202011507562.7,"本发明涉及一种冷却体，其用于面状地安置在装备有电子部件的电路板上，所述冷却体包括第一部分表面和第二部分表面。在第一部分表面与第二部分表面之间延伸有隔热部，并且跨越隔热部的刚性的机械连接部将第一部分表面与第二部分表面相连接。所述冷却体由此能实现将第一部分表面和第二部分表面分配给电路板上的电子部件并且有助于电路板的机械稳定性。
",G06F1/20,授权
US20240126608A1,US18380378,"一种用于执行用于区分验证任务优先级的方法的验证系统，其中，所述验证任务由所述验证系统的执行单元执行，其中，所述执行单元被划分为至少两个组，并且每个组由所述验证系统和/或所述验证系统的用户分配能力，使得相应组的执行单元具有所述组的能力，并且当所述验证任务由所述验证系统和/或所述用户自动执行时，指定和要求所述相应验证任务对所述执行单元的能力的要求和执行优先级，考虑优先级和能力执行单元，确定执行顺序，并由能力执行单元根据执行顺序执行验证任务。
",G06F9/50,公开
EP4351004A1,EP23193434.0,"记录设备，用于记录来自传感器设备的串行图像数据流。记录设备包括用于对图像数据流进行解串的解串器。为了配置第一解串器，其切割由用于配置布置在传感器设备中的串行器的接收设备发送的配置数据流，并且通过分析记录数据记录来导出记录设备的解串器的配置。在优选实施例中，通过将配置串行器抽象为串行器的功能、第一解串器的功能从第一串行器的函数的推导、以及将配置串行化器的功能抽象为串行化器功能来进行推导，以及将所述第一解串器的功能具体化为所述第一去串器的配置。
",H03M9/00,实质审查
US20240103508A1,US18535538,"一种用于测试电子控制单元的装置，具有环路中硬件模拟器以模拟至少一个传感器信号，并且配备有经由其输出传感器信号的双线电缆，并且具有经由双线电缆连接到环路中硬件模拟器的切换装置，使得可从切换装置接收传感器信号。切换装置中的双线电缆被布线到切换装置的两极接口，电子控制单元可以连接到两极接口以接收传感器信号，并且切换装置具有两个电压源，两个电压源中的每一个被分配给双线电缆的导线并且可以经由可控开关连接到相应导线。以这种方式，在诊断期间可以经由两个可切换电压源继续向传感器供应电能，从而避免诊断错误。
",G05B23/02,暂缺
US11940556B2,US17469921,"一种用于测试距离传感器的测试装置包括：接收器，用于接收电磁自由空间波作为接收信号；模数转换器，被配置为在模拟模式中将接收信号转换为采样信号；信号处理单元，被配置为：延迟采样信号或调制采样信号以形成延迟采样信号或调制延迟采样信号；以及在采样信号上或在延迟采样信号上调制作为要被模拟的反射对象的特性运动轮廓的可预定的多普勒签名以形成调制采样信号或调制延迟采样信号；数模转换器，被配置为将调制或调制延迟采样信号转换为模拟反射信号；以及发射器，被配置为辐射模拟反射信号或从模拟反射信号导出的模拟反射信号作为输出信号。
",G01S7/40,授权
US20240094339A1,US17948287,"一种用于有源环境感测系统的雷达传感器的测试系统，包括：第一目标模拟器，其包括第一电子控制单元、连接至所述第一电子控制单元用于接收雷达传感器信号的第一接收天线、以及连接至所述第一电子控制单元用于发射由所述第一电子控制单元生成的第一雷达回波的第一发射天线；以及第二目标模拟器，其包括第二电子控制单元、连接至所述第二电子控制单元用于接收雷达传感器信号的第二接收天线、以及连接至所述第二电子控制单元用于发射由所述第二电子控制单元生成的第二雷达回波的第二发射天线。
",G01S7/40,暂缺
EP4332753A2,EP23188126.9,"在一种用于管理用于处理来自多个传感器的信号的平台中的图形图的组件的方法中，至少一个组件可以处于第一模式，例如B.快速控制原型设计，存在并且处于第二模式，例如B.源代码生成缺失。另外，在第二模式中还可以删除仅由缺失的组件使用的组件。关于这些组件的信息可以被保存在配置简档中。
",G06F8/35,实质审查
EP4325366A1,EP22193650.3,"本发明涉及一种用于执行用于机动车辆的至少部分自主引导的设备（10）的虚拟测试的方法和系统（1），包括使用驾驶状况参数的至少一组参数（P）通过算法（a）执行（S2）虚拟测试执行的虚拟测试模拟至少一组驾驶状况参数（P）；并且如果满足给定和/或算法（a）条件（B），在虚拟测试的运行时间修改（S4）由至少一个车辆传感器（12a）检测到的至少一个第一参数（P1）和/或与车辆致动器（12b）相关的第三参数（P3）。
",G06F11/36,实质审查
CN113412610B,CN202080013626.X,"本发明涉及一种用于通过消息系统向要测试的接收器设备发送受保护的消息的方法和重放单元(D)，其中，重放单元通过消息系统与要测试的设备相连接，其中，重放单元被设置为获取要重放的第一受保护的消息、从所述第一受保护的消息删除第一计数器值(Z)和第一认证码(MAC)并且借助于第二计数器值、加密算法和密钥生成第二认证码，并且其中重放单元被设置为通过将第二计数器值和第二认证码添加到第一受保护的消息中而生成第二受保护的消息，并且其中所述重放单元还被设置为通过消息系统将第二受保护的消息发送到要测试的接收器设备。
",H04L43/50,授权
US20240054258A1,US17886479,"一种用于实时模拟的系统，包括：实时计算设备，其被配置为执行所述实时模拟；以及主机计算设备，其与所述实时计算设备通信。所述主机计算设备被配置为：配置用于执行所述实时模拟的实时模拟应用；以及将所述实时模拟应用发送到所述实时计算设备。所述实时计算设备还被配置为作为执行所述实时模拟的一部分：接收与到所述实时计算设备的缓冲器的数据阵列相对应的消息；将所述缓冲器中的所述消息拆包；确定与所述数据阵列相对应的拆包触发器已被激活；以及响应于确定与所述数据阵列相对应的所述拆包触发器已被激活，将所述缓冲器中的所述消息拆包。
",G06F30/20,暂缺
EP4318245A1,EP22196810.0,"本发明涉及一种用于分析用于机动车辆的至少部分自主引导的设备（10a）和/或功能（10b）的测试工具的方法，其中，针对多个测试用例（T1）中的每一个计算的第一特征值与表示元测试用例（T2）的第二特征值的聚合（S4）；以及使用预定的第二标准（18）并且通过将大多数测试用例（T1）的第一评估结果（14a）与元测试用例（T2）的第二评估结果（18a）逻辑连接来对第二特性（16）进行评估（S5）。此外，本发明涉及一种用于分析用于机动车辆的至少部分自主引导的装置（10a）和/或功能（10b）的测试执行的系统（1）。
",G06F11/36,实质审查
US20240028457A1,US18224778,"在FPGA中，通过以下步骤检测FPGA内的错误：在可配置逻辑块中提供至少一个计算操作，添加奇偶不变的附加结果，通过选取全加法器的XOR操作的XOR位来提供奇偶不变的附加结果，该XOR操作包括至少两个输入信号；借助下式形成输入信号的XOR操作的奇偶性，并提供奇偶性信号：奇偶性(XOR (x1，x2))；借助用于检验奇偶性的设备，使用XOR (奇偶性(x1)，奇偶性(x2))来计算输入信号的携带奇偶性(奇偶性(x1)，奇偶性(x2))的XOR操作；使用下式的真实性的检验来检验奇偶性：XOR (奇偶性(x1)，奇偶性(x2)) = =奇偶性(XOR (x1，x2))；在前述步骤的公式的非正确陈述的存在下检测FPGA内路线/计算中的错误。
",G06F11/14,公开
US20240017747A1,US18251721,"一种用于生成模拟场景的方法包括：接收原始数据，其中，所述原始数据包括多个连续的LIDAR点云、多个连续的相机图像、以及连续的速度和/或加速度数据；将来自确定区域的所述多个LIDAR点云合并到公共坐标系中以产生复合点云；对所述复合点云内的一个或多个静态对象进行定位和分类；基于所述复合点云、一个或多个静态对象和至少一个相机图像来生成道路信息；对所述多个连续的LIDAR点云内的一个或多个动态道路用户进行定位和分类并且生成所述一个或多个动态道路用户的轨迹；基于所述一个或多个静态对象、所述道路信息以及所述一个或多个动态道路用户的所生成的轨迹来创建模拟场景；以及导出所述模拟场景。
",B60W60/00,暂缺
EP4307121A1,EP23176378.0,"本发明涉及一种用于配置用于测试机动车辆的车辆功能的虚拟测试系统的计算机实现的方法，-根据第一条件（B1）对具有最高置信度值（K）的至少一个待测试的附加伪像（16）的输出端口（14）的分配（S3a），根据第二条件（B2）创建（S3b）最高置信度（K）输出端口（14）的列表，或者根据第三条件（B3）不分配（S3c）输出端口n待测试的至少一个附加工件（16）的输出端口（14）。本发明还涉及一种计算机实现的方法，用于提供用于配置用于测试机动车辆的车辆功能的虚拟测试系统的经过训练的机器学习算法（A1）。
",G06F11/36,权利终止
US20240010210A1,US18004071,"一种计算机实现的方法提供了终止驾驶员辅助系统的基于场景的测试过程。测试过程包括循环，并且包括确定关键测试用例及其参数组合。该方法包括：在该循环的每次迭代n之后执行驾驶员辅助系统的至少一个测试，并且确定关键测试情况的集合；在此基础上，建立迭代n中的第一组关键测试用例与来自迭代n-1的第二组关键测试用例之间的距离，或者迭代n中的第一组关键测试用例与预定义的一组关键测试用例之间的距离；以及基于第一组和第二组之间的距离与预设限制之间的差来终止或继续测试过程的循环。
",B60W50/02,授权
EP4300316A1,EP22182519.3,"本发明涉及一种用于基于标准创建具有虚拟车辆环境的场景库（10）的计算机实现的方法和系统（1），该场景库用于测试机动车辆的高度自动化驾驶功能，该方法和系统包括多个场景元素（14）中的一个的比较（S2），具有所述数量的测试场景数据集（12）和用于创建所述场景库（10）的所述需求简档（16）的至少一个集群的进一步的测试场景数据库（18），其中所述比较包括用于将场景元素（14）聚类到由所述场景库（10）和所述另一测试场景记录（18）覆盖的测试场景记录的数量（12）的算法（A）的应用（S3）。
",G06F11/36,实质审查
US11860301B2,US17353905,"一种用于测试使用电磁波操作的距离传感器的测试装置，包括：接收元件，其接收作为接收信号的电磁自由空间波(S <Sub>RX</Sub>)；以及用于辐射电磁输出信号的辐射元件(S <Sub>TX</Sub>)。在测试模式中，测试信号单元生成测试信号(S <Sub>test</Sub>)，并且辐射元件被配置为辐射测试信号(S <Sub>test</Sub>)或从测试信号(S <Sub>test</Sub>)导出的测试信号(S ' <Sub>test</Sub>)作为电磁输出信号(S <Sub>TX</Sub>)。在测试模式中，分析单元被配置为根据接收信号(S <Sub>RX</Sub>)或导出的接收信号(S ' <Sub>RX</Sub>)的相位角(φ)和/或幅度(A)来分析接收信号，并且与测试信号(S <Sub>test</Sub>)或导出的测试信号(S ' <Sub>test</Sub>)的辐射同步地将相位角(φ)和/或幅度(A)的确定的值存储为电磁输出信号(S <Sub>TX</Sub>)。
",G01S7/40,授权
US20230419009A1,US18212368,"一种基于节点形式的行为模型通过实时平台模拟电路的方法，所述行为模型具有阻抗矩阵M或在状态空间中的面向拓扑的行为模型，所述阻抗矩阵M具有矩阵A、B、C和D，并且为了这个目的，描述电路的至少一个阻抗矩阵M或用于描述电路的矩阵A、B、C和D的至少一个集合被存储在实时平台上。因此，结果实现了更有效的操作序列。
",G06F30/3308,暂缺
US20230418324A1,US18209558,"一种对FPGA编程的方法，其中提供具有基本操作的库和针对库的每个基本操作的相应延迟表。定义数据路径。针对彼此不同的多个时钟速率记录延迟，并且针对每个时钟速率添加这些延迟，使得数据路径的总延迟针对该多个不同的时钟速率产生。确定在相应时钟速率下最低总延迟和总延迟之间的比率。识别针对每个时钟速率的FPGA的利用率。确定在相应时钟速率下FPGA的最低利用率和FPGA的利用率之间的比率。在考虑总延迟和FPGA的利用率的同时确定针对每个时钟速率的质量因数。
",G06F1/10,授权
EP4296970A1,EP22180630.0,"本发明涉及一种用于生成用于测试机动车辆的高度自动化驾驶功能的虚拟车辆环境的计算机实现的方法和系统。该方法包括将逐像素分类的相机图像数据（D1）投影（S3）到预先记录的LiDAR点云数据（D2）上，其中分别由具有特别相同的图像坐标的相机图像数据（D1）的分类像素叠加的所述激光雷达点云的点被分配相同的类别（K），并且所述分类的激光雷达点云中的数据（D2）的实例分割（S4）以确定类别（K）中的至少一个包括真实对象（10）。本发明还涉及一种计算机程序和计算机可读数据载体。
",G06V10/82,实质审查
US20230408648A1,US18211836,"一种用于确定空中测试室的部件的布置的计算机实现的方法和系统，包括确定空中测试室中的部件相对于彼此的优化布置的位置数据和/或空中测试室中的优化布置的位置数据的分组，以及第二数据集合的输出，第二数据集合包括DUT的优化布置的位置，特别是雷达传感器、反射器和目标模拟器或目标模拟器的发射/接收设备在空中测试室中的位置，和/或在空中测试室中的优化布置的分组。
",G01S7/40,公开
US20230401145A1,US18208969,"一种使用至少一个测试和/或仿真的存储的规范部件的计算机实现的方法，包括以下步骤：提供待规范的所述至少一个测试和/或用于测试车辆的行驶功能的所述一个仿真，并且通过至少一个参数值和/或设置值来确定所述至少一个测试和/或所述至少一个仿真；以及执行所述至少一个测试和/或所述至少一个仿真的规范，其中，所述规范包括至少一个规范部件，其中，选择所述参数值和/或设置值，并且为所述测试和/或仿真分配由所述规范确定的参数值和/或设置值，其中，人工地或自动地选择所述参数值和/或设置值，并且其中，人工地和/或自动地选择已经存储的规范部件并且将其集成到所述规范中。
",G06F11/36,暂缺
US20230394742A1,US18236037,"一种用于参数化用于图像合成的程序逻辑以使由程序逻辑合成的图像适应于相机模型的方法。三维场景的数字照片由神经网络处理，并且从神经网络的层的选择中提取照片的abicile第一表示。根据输出参数的初始集合参数化程序逻辑，以便合成从场景的三维模型重建照片的图像。由相同的神经网络处理合成图像，从层的相同选择中提取合成图像的abicile第二表示，并且基于考虑第一表示和第二表示的度量来计算合成图像与照片之间的距离。
",G06T15/20,暂缺
EP4283333A1,EP23157567.1,"本发明涉及一种用于传输用于激光雷达传感器（12；112）的测试系统（1；101）的合成生成的光信号（S）的光学单元（10；110），具有承载装置（14；114）以容纳至少一根光纤电缆（16；116），其中，所述载体装置（14；114）具有与所述至少一根光纤（16；116）插入其中的载体装置（14,114）的一个端面（14a；114a）正交地形成的至少一个开口，并且形成（18；118）连接到所述载体装置（14，其中所述载体装置（14；114）和所述至少一个微透镜（18；118）的彼此面对的面（14a；114a，18a；118a）各自是平面的。本发明还涉及一种测试系统（1；101）和用于生产光学单元（10；110）的方法。
",G01S7/497,实质审查
US11822466B2,US17557060,"一种方法提供用于操纵运行时环境的变量以便在测试环境中测试待测试的控制设备程序组件的干预点。根据AUTOSAR标准，控制设备程序组件的软件被分为三层，其中三层包括程序组件层，运行时间环境层，以及设备相关基本程序层。程序组件层具有用于提供输入值的测试场景程序组件和用于接收输出值并显示测试结果的程序组件。该方法包括：为待测试的控制设备程序组件提供分别按照AUTOSAR标准定义的接口；以及创建待测试的控制设备程序组件和测试场景程序组件的可执行程序。
",G06F9/44,授权
EP4276600A1,EP23172265.3,"本发明涉及一种用于从框图的一个或多个块生成源代码的方法，其中存储接收到的对框图的模型元素的改变。创建源代码包括将框图转换为中间视图，依次优化中间视图，并将优化后的中间视图转换为源代码。针对修改的模型元素检查至少一个合理性规则。如果不符合合理性规则，则会发出警告。用户可以批准该改变，以避免重复显示警告。此外，本发明涉及一种用于配置ECU的方法、计算机程序产品和计算机系统。
",G06F8/33,实质审查
US20230359785A1,US18143672,"一种用于借助于环路硬件模拟器的至少一个处理单元来模拟电驱动器的计算机实现的方法。所述电驱动器的模型具有：逆变器，所述逆变器由具有至少一个半桥的直流电压源供电，所述半桥具有至少两个半导体开关；以及电动机，所述电动机具有电绕组电阻和绕组电感。具有所述半桥的中心抽头电压的中心抽头借助于具有供给线路电流的供给线路连接至所述电动机的电动机连接端。通过操控所述半导体开关，所述电动机连接端在所述逆变器的导电状态中能够或者在所述逆变器的打开状态中在所述半导体开关打开的情况下连接至所述直流电压源的电势，或者在所述逆变器的打开状态中在所述半导体开关打开的情况下能够在所述电路上对所述电动机连接端解锁。
",G06F30/20,暂缺
US20230359780A1,US18040269,"一种用于提供对要测试的交通场景的测试过程的计算机实现的方法，具有以下步骤：训练和使用相似性分类模块以用于基于特定场景的参数集的子集对两个场景之间的相似性进行分类的目的；生成包括多个场景的图表表示，其中节点各自表示场景的参数集的子集并且两个节点之间的边用指示相应节点的两个场景之间的相似性的相似性分类值来加权；借助于用户界面和/或自动系统输入接收用户输入，其中输入包括用于覆盖场景的所需值和/或要测试的场景的所需数目，使得确定场景的选择和执行序列。
",G06F30/15,暂缺
EP4273638A1,EP23158920.1,"本申请涉及一种用于配置用于模拟（12）至少一个传感器的模块的方法，其中该模块（12）具有传感器模拟（16）和第一类型的第一数据接口（14），通过第一类型的第一数据接口（14）向用于执行控制和/或控制任务的设备（30）提供合成传感器数据。该程序包括以下步骤：a） 接收真实传感器（22）与所述设备（30）的数据交换的记录（20），其中所述数据交换经由所述第一类型的第二数据接口（24）进行，和/或接收关于所述真实传感器（二十二）和/或所述第一型型型的第二数字接口（二十四）的至少一个特性的信息，b） 读出所述记录（20）和/或关于至少一个特性的信息；c） 确定真实传感器（22）和/或第一类型的第二数据接口（24）的至少一个特性（Ch），d） 使用至少一个特性（Ch）配置传感器模拟（16）和/或第一类型的第一数据接口（14），使得合成传感器数据模拟真实传感器（22）的传感器数据。
本申请还涉及一种用于配置用于模拟的模块的设备。
",G05B17/02,实质审查
US20230353544A1,US18005365,"一种用于经由消息传递系统向待测试接收机设备发送已注册的安全消息的方法，包括：从消息包提供第一安全且仍串行化的消息以用于在再现单元中进行处理；在第一消息中，缩减经串行化的消息的数据量；在每种情况下，通过添加第二计数器值，或者在适用的情况下，通过用第二计数器值替换第一计数器值，对缩减的第一消息进行解串行化，并创建第二安全消息，在每种情况下，通过使用第二计数器值、加密信息和密钥，以及通过使用通信描述或通过应用数据解释算法获得的信息，创建并添加第二认证器；以及对第二安全消息进行串行化，并向待测试接收机设备发送相应的消息包。
",H04L9/40,授权
EP4270176A1,EP22169925.9,"本发明涉及一种用于从框图的至少一个分层块生成源代码的方法，其中分层块包括至少一个通信块，该通信块与分层块之外的块交换数据，其中向至少一个通讯块提供对数据对象的引用。该方法包括读取框图，用引用的数据对象替换引用，基于所选择的数据对象确定至少一个代码生成设置，生成中间表示，对所述中间表示执行至少一个优化，并将所述中间表达翻译成源代码。此外，本发明涉及一种用于配置ECU的方法、计算机程序产品和计算机系统。
",G06F8/34,权利终止
US20230341496A1,US18187701,"一种高电压电子设备具有串联连接在高电压电子设备的端子之间的多个电子模块。一种用于校准高电压电子设备的系统包括：多个可控模块开关，其中，对于多个电子模块中的每个相应的电子模块，相应的可控模块开关被配置为通过相应的电子模块的闭合来分流相应的电子模块。多个可控模块开关被配置为被致动，使得：在多个可控模块开关的第一状态下，没有电子模块连接到端子；在多个可控模块开关的第二状态下，一个电子模块连接到端子；以及在多个可控模块开关的第三状态下，多个电子模块连接到端子。
",G01R31/3842,暂缺
US20230342115A1,US18191905,"一种用于从框图的至少一个分级块生成源代码的方法包括：在框图中读取；用参考数据对象替换参考；基于参考数据对象确定至少一个代码生成设置；生成中间表示；对中间表示执行至少一个优化；以及将中间表示翻译成源代码。
",G06F8/30,暂缺
US20230333892A1,US18134231,"一种用于对在处理器的计算机核上执行的实时系统的计算步骤进行文档记录的方法，其中在计算机核上执行任务，该任务包括一个或多个子任务，并且其中在每一种情况下在计算步骤期间执行任务的子任务。在计算步骤开始时记录第一处理器时间，并且在计算步骤结束时记录第二处理器时间，并且将取决于第一和第二处理器时间的时间信息存储在存储器中。以能够将时间信息分配给子任务和在计算步骤期间执行的任务的方式将该时间信息存储在存储器中。
",G06F9/48,暂缺
EP4248418A2,EP21815163.7,"本发明公开了一种用于注解传感器数据的计算机实施的方法，包括以下步骤：接收原始传感器数据，所述原始传感器数据包括多个连续的LIDAR点云和/或多个连续的摄像机图像；使用一个或多个神经网络识别摄像机数据的每个图像和/或每个点云中的对象；使连续的图像和/或点云内的对象相关；基于可信性标准去除假阳性结果；以及导出驱动场景的注解的传感器数据。
",G06V10/774,期限届满
US20230273303A1,US18143856,"一种用于激励光探测器的试验台，包括光信号装置，该光信号装置包括照明元件的平面布置，这些照明元件能够彼此独立地被激活和去激活，并且该光信号装置被设计成发射由任何照明元件发射的光，该光为至少近似准直光束；会聚透镜，该会聚透镜被设计和定位成将由该光信号装置发射的光束会聚在累积点处；以及用于光探测器的保持装置，该保持装置被布置在该累积点处，借助于该保持装置，光探测器可被以如下方式放置在该累积点处，即由任何第一光源产生的第一光束和由任何第二光源产生的第二光束相对于该试验台的光轴以不同的空间角度撞击该光探测器。
",G01S7/497,暂缺
EP3570063B1,EP18172050.9,"一种用于模拟传感器系统的方法，包括检查第一图像和第二图像的交叉点是否存在，以及评估第二虚拟对象是否被PERSPECTIVALLY遮挡。在存在交叉点的情况下，传感器和第一虚拟对象之间的欧几里得距离小于传感器和第二虚拟对象之间的欧几里得距离，并且交叉点的大小超过预定义的阈值，则第二虚拟对象被评估为PERSPECTIVALLY遮挡。在不存在交叉点的情况下，传感器和第一虚拟对象之间的欧几里得距离不小于传感器和第二虚拟对象之间的欧几里得距离，或者交叉点的大小超过预定义的阈值，则第二虚拟对象被评估为非PERSPECTIVALLY遮挡。
",G06V20/56,授权
EP3196714B2,EP16171914.1,"提供一种用于模拟可以连接到控制装置的外围电路布置的方法。模拟装置电连接到控制装置并且具有第一控制元件，通过该第一控制元件可以影响可以从控制装置的第一负载端子传递到第一控制元件的第一控制元件输出的第一模拟电流。第一控制元件包括第一多级转换器，并且模拟装置还包括第一半导体开关控制器和执行模型代码的计算单元。计算第一开关控制信号并且提供该第一开关控制信号以用于转发到具有至少一个第一比较器的第一半导体开关控制器。生成经脉宽调制的第一栅极-源极电压并且将其施加到第一控制端子，并且第一模拟电流受第一栅极-源极电压影响。
",G05B17/02,授权
US11693998B2,US16113560,"一种提供用于控制单元开发的能够实时地进行的模拟的方法，其中，所述能够实时地进行的模拟对控制单元或者控制单元的环境或者控制单元与控制单元的环境的组合进行模拟，其中，所述能够实时地进行的模拟具有能够实时地进行的子模拟和与所述能够实时地进行的子模拟相互作用的、不能够实时地进行的子模拟的共模拟，其中，所述能够实时地进行的子模拟和所述不能够实时地进行的子模拟被设计用于传递模拟数据，其中，所述能够实时地进行的子模拟具有与实时地相对应的第一模拟时间，并且所述不能够实时地进行的子模拟具有虚拟的、第二模拟时间，所述第二模拟时间与所述第一模拟时间耦合并且在开始所述能够实时地进行的模拟时与所述第一模拟时间相匹配。
",G06F30/20,授权
US20230205937A1,US17996508,"一种用于确定像素聚光灯的总光分布的方法，包括：提供纹理，该纹理包括具有坐标的二维阵列；为多个单独光源中的每个单独光源提供最大通电单独光分布；考虑所有最大通电单独光分布和纹理来确定最大通电数据结构；为每个单独光源提供相对通电值；以及考虑最大通电数据结构和单独光源的相对通电值来确定总光分布。
",G06F30/10,暂缺
EP4202760A1,EP21216228.3,"本发明涉及一种用于创建三维仿真环境的计算机实现方法，包括以下过程步骤：提供具有三维虚拟对象的基本库、检测地理区域的输入、获取特征信息、，表征所述地理区域的不同区域的特征，基于所述特征信息导出所述地理区的不同区域中的附加信息，导出土地利用信息作为附加信息，如果特征信息不包括土地使用信息，则基于特征信息和/或附加信息从基本图书馆识别出现在地理区域中的对象，并将这些对象放置在区域图书馆中，根据土地利用信息将地理区域细分为土地利用平等的部门，为每个部门从区域图书馆中识别与该部门的土地利用相匹配的对象，并用选定的对象填充每个部门，这提供了以简单可靠的方式创建逼真的三维虚拟环境的机会。
",G06K9/62,实质审查
EP4202691A1,EP22207368.6,"本发明涉及一种用于确定系统参数（12、14、24、26）的兼容性的计算机实现的方法和系统，所述系统参数用于对用于机动车辆的至少部分自主引导的设备（8）的虚拟测试的测试执行，包括基于规则的算法（a）对至少一个第一系统参数（12）的应用（S2），以确定具有待测试系统（10a）的组中的至少一个第二系统参数（14）与至少一个所述第一系统参数的兼容性，测试环境（10b），测试系统（10c），测试场景（10d）和/或待测试的车辆类型（10e），其中所述基于规则的算法（A）基于硬件和/或软件组件（16）来确定兼容性，所述硬件和/或者软件组件执行用于所述机动车辆的至少部分自动驾驶的设备（8）的虚拟测试和/或提供测试数据。
",G06F11/36,实质审查
EP4202753A1,EP21216219.2,"本发明涉及一种用于生成测试数据的计算机实现的方法，该测试数据用于测试评估传感器数据流的机动车辆的控制系统，具有以下过程步骤：通过指定虚拟车辆（1）在虚拟仿真环境中的平移运动，在虚拟传感器（2）承载虚拟车辆（2）的情况下模拟驾驶通过虚拟仿真环境的至少一部分，通过捕捉虚拟仿真环境，通过在虚拟传感器（2）的视场中检测虚拟车辆（1）经过的虚拟模拟环境并提供合成传感器数据作为测试数据以测试评估传感器数据流的机动车辆的控制系统，利用虚拟传感器（1）生成合成传感器数据，其中确定通过所述虚拟仿真环境的至少一部分的步骤，其中所述虚拟传感器（2）承载由所述虚拟车辆（1）的平移运动引起的至少一个车辆动态运动，其中车辆动态运动包括虚拟车辆（1）的点头和/或哈欠和/或摆动和/或垂直运动；并且在利用虚拟传感器（2）生成合成传感器数据的步骤中考虑由虚拟车辆（1）的模拟车辆动态运动引起的虚拟传感器（2中）的视场（3）的变化，其中，所述虚拟传感器（1）检测所述虚拟车辆（1）所经过的虚拟模拟环境。这样，提供了在模拟的情况下获得这样的测试数据的可能性，所述测试数据应用于测试ECU，该测试数据与通过实际系统获得的数据的偏差尽可能小。
",G06F30/15,撤回
EP4202779A1,EP21216234.1,"本发明涉及一种用于对虚拟测试（T）的场景进行分类的计算机实现的方法和系统，提供（S1）由多个车载环境传感器（10a、10b、10c）检测到的自我车辆的行程的传感器数据的第一数据集（DS1），通过第一算法（A1）对传感器数据的第一数据集（DS1）的变换（S2），特别地，一种多元数据分析方法，在传感器数据的数据缩减的第二数据集（DS2）中，将机器学习的第二算法（A2）应用于传感器数据的用于对第二数据集中所涵盖的场景进行分类的数据精简的第二数据集（DS2）（S3），表示车辆动作第三记录（DS3）的多个类别（K）。本发明还涉及一种计算机实现的方法，用于提供用于对虚拟测试（T）的场景进行分类的经过训练的第二算法（A2）机器学习。
",G06N3/08,实质审查
EP4202452A1,EP21216287.9,"这是一个装置。提出了一种校准电池模拟器的方法。电池模拟器模拟串联切换的多个电池单元，其中每个模拟的电池单元具有抽头，通过该抽头可以抽头至少一个模拟量，其中该设备具有切换设备，校准标准可以通过该切换设备与不同的抽头可切换地连接。
",G01R31/00,实质审查
EP4202790A1,EP21216241.6,"本发明涉及一种用于对虚拟测试的交通状况（VS）进行分类的计算机实现的方法和系统。该方法包括连接（S3）自我车辆（12）的多个指定数据段（14）的横向和纵向行为，用于识别车辆动作（16）和分类（S4）交通状况n（VS），通过将自身车辆（12）的横向和纵向行为的指定数据段（14）的子集与所识别的车辆动作（16）相链接。本发明还包括一种计算机实现的方法，用于提供用于虚拟测试的交通状况（VS）分类的经过训练的机器学习算法。
",G06N5/04,实质审查
EP4203418A1,EP21216320.8,"本发明涉及一种用于分析网络的节点(1A、1B、1C)的服务(2A、2B、2C)的方法，所述节点通过网络彼此通信，其中通过网络彼此的节点的通信包括通过网络从至少一个节点向至少另一个节点发送消息，所述方法包括以下方法步骤：A)检查消息的至少一部分是否相应的消息包括服务信息(6)并且如果相应的消息不包括服务信息(6)则忽略消息，B)通过从相应的消息的服务信息(6)中识别以下信息中的至少一个来分析包括服务信息(6)的消息的服务信息(6)：发送节点(10)、接收节点(12、14)、服务标识号、在发送节点(10)和接收节点(12、14)之间的服务动作(30)、在发送节点(10)和接收节点(12、14)之间的通信连接(40)的状态以及通信连接的持续时间，C)将之前识别的服务信息(6)保存在多个服务实例历史表(3)中，以及d)通过可视化单元(4)基于多个服务实例历史表(3)来可视化之前识别的服务信息(6) 。以这种方式，提供了一种用于分析网络中的服务的更简单且不易出错的方法。
",H04L43/04,实质审查
EP4202474A1,EP21216214.3,"本申请涉及一种用于对仿真逻辑(EL)进行参数化的方法，所述仿真逻辑(EL)仿真图像传感器(BS)，所述方法具有如下步骤：a)借助所述图像传感器(BS)准备对校准对象(KO)的原始数据记录，b)基于所述原始数据记录来测量所述图像传感器(BS)的至少一个运行参数，c)利用所述至少一个运行参数来对所述仿真逻辑(EL)进行参数化，d)借助所述仿真逻辑(EL)准备对所述校准对象(KO)的数字双筒的合成原始数据记录，e)确定所述原始数据记录与所述合成原始数据记录之间的距离，f)如果所述距离不满足预先给定的终止准则，则：g)改变所述至少一个运行参数，h)以相应改变的至少一个运行参数重复执行步骤c)至h) 。如果所述图像传感器(BS)被设计为雷达传感器、激光雷达传感器或超声波传感器，则所述校准对象(KO)被设计为具有限定的特征的反射器。如果所述图像传感器(BS)被设计为相机，则所述校准对象(KO)被设计为具有限定的几何图案的面板，其中利用所述图案的几何形状来测量所述至少一个运行参数。本申请还涉及一种用于对仿真逻辑(EL)进行参数化的装置(10) 。
",G01S7/40,撤回
US20230194643A1,US18082058,"提出了一种用于校准电池仿真器的装置或方法。电池仿真器仿真多个串联连接的单元，其中每个仿真单元具有抽头，通过这些抽头分接至少一个仿真量，其中该装置包括开关装置，校准标准件可通过该开关装置与不同的抽头可切换地连接。
",G01R35/00,公开
US20230195977A1,US18082756,"一种用于对虚拟测试的场景分类的计算机实现的方法和系统，包括提供由多个车辆侧周围环境检测传感器捕获的本车辆的行驶的传感器数据的第一数据集；通过第一算法将传感器数据的第一数据集变换为传感器数据的数据减少的第二数据集，特别是多变量数据分析方法；将第二机器学习算法应用于传感器数据的数据减少的第二数据集，以用于对由第二数据集包括的场景分类；以及具有表示车辆动作的多个类的第三数据集的输出。还提供了一种用于提供训练的第二机器学习算法以用于对虚拟测试的场景分类的计算机实现的方法。
",G06F30/27,公开
US20230198532A1,US18082345,"一种用于改变FPGA配置的位宽的方法，FPGA配置具有多个至少2×<Sup>n </Sup>位的包含n ε的数据信号≥ 3，并且该方法具有以下步骤：当超过FPGA的电流消耗和/或温度的阈值和/或存在替换信号时，在每种情况下用零替换数据信号的k个最低有效位，其中k为ε3538.在FPGA上执行FPGA配置期间的≥ 2。
",H03L7/107,暂缺
US20230192148A1,US18084633,"一种用于对虚拟测试的交通状况进行分类的计算机实现的方法和系统。该方法包括：连接本车辆的横向和纵向行为的多个所确定的数据段以识别车辆动作；以及通过将本车辆的横向和纵向行为的所确定的数据段的子集与所识别的车辆动作链接来对交通状况进行分类。本发明进一步包括一种用于提供训练的机器学习算法以对虚拟测试的交通状况进行分类的计算机实现的方法。
",B60W60/00,暂缺
US20230199168A1,US18068535,"一种系统，包括：相机单元；控制单元；以及用于测试控制单元的测试装置。测试装置包括处理器和图像输出单元。处理器被配置为操纵图像数据以模拟相机单元的误差，并且在图像输出单元上输出经操纵的图像数据，所述经操纵的图像数据呈经由相机光学器件可检测的图像的形式。相机单元被配置为经由相机光学器件检测所输出的模拟相机单元的误差的图像数据。控制单元被配置为从相机单元接收模拟相机单元的误差的相机图像数据。
",H04N17/00,申请终止
US20230195661A1,US18082288,"提供了一种用于FPGA的至少一个子区域与另一区域之间的数据通信的方法，其中数据通信是资源高效的。这通过以下事实来实现，即，经由FPGA的内部配置接口的命令序列来进行数据通信，即，从一个位置到FPGA的任何块RAM或从任何块RAM到任何块RAM的读和写。
",G06F13/16,授权
US20230195430A1,US18066308,"一种用于在图形开发环境中创建层次块图的计算机实现的方法，包括：自动创建用于相应的另外的子系统块的层次黑盒。所述层次黑盒被配置为当被用户选择时解析对应的层次级别。经由回调函数执行自动创建。所述层次黑盒具有以下特征：在其层次级别中的相应的另外的子系统块的输入/输出的至少一个接口描述和/或图形表示，所述相应的另外的子系统块先前也被占用；对用于相应的另外的子系统块的模型内容的至少一个引用，包括用于从属层次级别中的另外的子系统块的另外的黑盒；相应的另外的子系统块先前也具有的参数；以及生成的相应的另外的子系统块的源代码。
",G06F8/34,授权
EP4198870A1,EP21215590.7,"本发明涉及一种用于通过使用激光雷达信息和相机图像信息来生成场景的鸟瞰视野图像的计算机实现的方法。该方法包括步骤(a)至(d) 。在步骤(a)中，包括具有固有距离信息的点的至少一个激光雷达帧2以及场景的至少一个相机图像3被采集。在步骤(b)中，场景的网格表示4通过使用至少一个激光雷达帧2而被生成，网格表示4表示在具有固有距离信息的场景中示出的表面。在步骤(c)中，通过将至少一个相机图像3的像素分类为表示至少一个相机图像3的地面像素或非地面像素而生成掩模图像6.在步骤(d)中，鸟瞰视野图像1通过增强的逆透视映射并且利用网格表示4的表面、掩模图像6的被分类为地面像素的像素以及至少一个相机图像3所固有的距离信息而被生成。此外，本发明涉及一种被配置为执行该方法的系统100和计算机程序产品。
",G06T3/40,实质审查
EP4198793A1,EP21215421.5,"本发明的目的是一种用于改变FPGA（2）的FPGA配置的比特宽度的方法，该FPGA配置具有多个至少2n比特的数据信号，该数据信号包含n并且≥3，并且该方法具有以下步骤：如果超过FPGA（2）的电流消耗和/或温度的阈值和/或存在替换信号，则在FPGA（2且≥2。
",G06F21/76,实质审查
EP4199553A1,EP21214247.5,"本发明涉及一种计算机实现的方法和测试单元（1），用于最小化用于机动车辆的至少部分自主引导的装置的多个虚拟测试（T1-Tn）的测试执行的计算工作量，包括执行（S3）所述第一虚拟测试（T1）和所述第二虚拟测试（T2），其中以这种方式考虑所述驾驶状况参数（FP）的所述第2虚拟测试（T2）与所述第1虚拟测试（T1）的相同份额（An1）和/或差分份额（An2）和/或者至少一个参数的变化时间点（Z1），测试执行的计算工作量被最小化。
",H04W4/40,实质审查
EP4198750A1,EP21215621.0,"在用于FPGA（1）的至少一个子集与另一范围之间的数据通信的方法中，数据通信应当是资源有效的。这是通过以下事实实现的：数据通信，经由FPGA（1）的内部配置接口（3）的命令序列，从一个位置向FPGA（1。
",G06F13/14,授权
EP4199355A1,EP21214298.8,"在用于电流均匀分布的电子电路装置（1）中，其中多个MOSFET（T11，T21）并联连接，在线性操作中应增加最大功率损耗。这是通过电路装置（1）包括第一晶体管（T12）和第二晶体管（T22）来实现的，在第一MOSFET（T11）和第一晶体管（T12）之间建立热耦合（19），也在第二MOSFET（T21）和第二晶体管（T22）之间。
",H03K17/12,权利终止
EP4198805A1,EP21215702.8,"当在图形开发环境中创建（2）分层框图（1）时，模型操作的执行时间将被缩短，以实现更高性能的、反应式的图形建模。这是通过指定具有至少一个子系统块（S1）的计算机实现的方法来实现的，其中所述子系统块（S1）位于第一层级上，并且另外的子系统块是（S1.1、S1.2、S1.3…），其中所述子系统块（S1）由位于下级层级的子系统块组成，所述子模块（S1）是可参数化的并且具有至少一个回调函数，其中回调函数定义响应于特定建模活动而执行的命令，其中，为了降低子系统块（S1）的层级（S1.1、S1.2、S1.3…），为子系统块创建至少一个层级黑盒（B1.1、B1.2、B1.3…），由此从最低层级向上进行缩减，其中该程序包括以下步骤：自动创建子系统块（S1.1、S1.2、S1.3…）的分层黑盒（B1.1、B1.2、B1.3…），其中，黑盒（B1.1、B1.2、B1.3…）以这样的方式设置，即用户选择相应的层次级别，从而通过回调功能进行自动创建。
",G06F30/30,实质审查
EP4198482A1,EP21215409.0,"本发明涉及一种将来自真实世界的原始数据处理为用于刺激ECU的测试数据的方法，该方法包括以下过程步骤：提供来自真实世界记录的原始数据，从原始数据中检测传感器检测到的真实物体，并创建路线数据集，每个描述具有这些真实对象在连续时间点的图像的场景，提供合成对象库，将合成对象分配给真实对象的捕获图像，用合成物体替换捕获的图像，用补充数据集补充第一路线数据集之前的按时间顺序连续的路线数据集，使得传感器在引入时段的时间零点具有零的绝对速度，并且按时间顺序顺序连续的补充数据集具有合成物体，其示出了准静态时间运动序列，该准静态时间移动序列在记录持续时间的时间零点准瞬时地引导到第一路线数据集的合成对象，并且通过将由补充数据集补充的路线数据集转换成原始数据来生成测试数据，因为它们在真实世界中的引入时段和记录时段期间将由传感器记录，如果传感器将合成物体检测为真实物体。这样，就提供了一种生成此类测试数据的方法，该方法没有不可信之处。
",G01M17/06,授权
EP4198921A1,EP21215399.3,"本发明提供了一种用于检测由传感器11检测到的物体31、32、33、110、201的计算机实现的方法和模拟系统50，其中物体31、31、33、11、201和传感器11在模拟环境100、200中相对于彼此移动。该方法包括识别位于模拟环境100、200中的预选范围30、230、231内的对象110、201的步骤S100，其中预选范围30和230、231包括传感器11的检测范围20，以及确定预选范围30或230内的对象31、32、33、110、201，231被传感器检测范围20检测到。根据本发明，在步骤S100中，通过具有沿着模拟环境100、200的维度的排序坐标的列表210、220来识别在当前时间位于预选范围30、230、231中的对象31、32、33、110、201，该列表识别这样的对象31，32，33、110，201，其先前根据步骤S110在预选范围30230231中。坐标列表210、220在步骤S120中被更新，通过在步骤S124中添加或移除对象标识。本发明还涉及相应的传感器系统50和相应的计算机程序产品60。
",G06V20/58,实质审查
EP4198722A1,EP21215811.7,"根据本发明，提供了一种用于配置ECU在计算机上运行的SIL仿真的方法，其中对于ECU在具有多个任务的ECU的计算机软件模型上的SIL仿真，在各个时钟时间之间具有预定周期的预定时钟中对其进行处理，并且计算机具有多个处理器核，多个虚拟机在其上运行，每个处理器核处理预定任务，具有以下处理步骤：a） 确定软件模型的偶发模型份额和周期模型份额，b） 从软件模型的周期性模型部分创建一组可并行化的任务，c） 确定可并行任务的各个循环时间，直到可并行任务循环时间之间的所有周期的最小公倍数，d） 确定各个任务在各自循环时间的计算时间，以及e） 基于可并行任务的时钟时间之间的所有周期的所确定的最低公倍数和单个任务在它们各自的时钟时间的所确定计算时间，以这样的方式确定任务到各个虚拟机的分配和虚拟机到各个处理器核的分配，SIL模拟的计算时间是最小的。这提供了一种保持SIL测试的计算时间尽可能低的方法。
",G06F9/455,撤回
US20230185989A1,US18061489,"一种用于使用于执行多个虚拟测试的计算工作量最小化的方法，包括：由测试单元提供第一虚拟测试的参数集和第二虚拟测试关于运行状况参数和实现第一虚拟测试和第二虚拟测试的算法的配置数据的参数集；由测试单元确定第二虚拟测试关于运行状况参数和/或第二虚拟测试的至少一个参数与第一虚拟测试相比变化的时间点相对于第一虚拟测试的相同分量和/或差异分量；以及由测试单元在考虑相同分量和/或差异分量的同时执行第一虚拟测试和第二虚拟测试，以便使用于测试执行的计算工作量最小化。
",G06F30/20,暂缺
EP4195435A1,EP21213957.0,"本发明的目的是一种用于模拟电池单元（1）的保护电路，连接到模拟电池单元（1）的用于短路的输出端（6）的电容器（3），与至少一个MOSFET（2）的栅极电极（7）连接的过电压检测装置（4），其被配置为当超过输出电压过电压极限时用输出电压对电容器（3）充电，被布置为在所述电容器（3）处的电压的阈值下释放所述栅电极（7）。
",H02H9/04,实质审查
US20230174083A1,US17680387,"一种用于确定至少部分自主地驱动机动车辆的装置的虚拟测试的计算努力的计算机实现的方法包括：提供执行虚拟测试的第一算法的配置数据和驱动状况参数的至少一个参数集，其中由第一算法执行的虚拟测试模拟驱动状况参数的至少一个参数集，并且其中模拟的结果用于确定在后续迭代中模拟的驱动状况参数的至少一个另外的参数集；将第二算法应用于驱动状况参数的至少一个参数集和第一算法的配置数据；以及输出表示虚拟测试的计算努力的至少一个数值。
",B60W50/06,暂缺
US20230176882A1,US18076977,"一种模拟基于AUTOSAR标准的控制单元的方法，在连续的时间步骤中处理不同的任务，在时间步骤中执行所有任务所需的执行时间被假定为零，该方法包括：测量任务的执行时间，在超过提供AUTOSAR函数调用的AUTOSAR参数时，为特定任务的执行时间定义上阈值；和/或测量任务的激活时间，为特定任务的激活时间定义上阈值，该上阈值是在AUTOSAR参数的帮助下实现的；和/或在至少一个时间步骤中改变AUTOSAR参数，以使得它大于测量的实际激活时间。
",G06F9/455,暂缺
US20230177241A1,US18075791,"一种用于提供用于基于传感器数据的数据集的场景数据来确定类似场景的机器学习算法的计算机实现的方法，其中将优化算法应用于由第一机器学习算法输出的传感器数据的数据集的第一次增加的特征表示，其中优化算法对由第二机器学习算法输出的传感器数据的数据集的第二次增加的特征表示进行近似。本发明还涉及一种用于基于传感器数据的数据集的场景数据来确定类似场景的方法以及一种训练控制器。
",G06F30/27,公开
EP4191265A1,EP21211759.2,"示出并描述了一种用于测试功率电子控制单元(2)的测试装置(1)，控制单元(2)具有用于提供能量的供电端子(3)和用于控制电负载的负载端子(4)，测试装置具有：多个功率电子模块(5)，每个功率电子模块(5)提供用于提供能量的供电端子(6)；至少一个用于提供至少一个电连接尺寸的负载端子(7)；用于提供电控制电压的供电电路；选择电路(10)，其具有用于将控制电压中的一个连接在功率电子模块(5)的负载端子(7)上的功率开关(11)和用于控制功率开关(11)的接口(12)，其中，在测试装置(1)的运行状态中，控制单元(2)的供电端子(3)和/或负载端子(4)分别与功率电子模块(5)的负载端子(7)连接，以便在控制单元(2)的供电端子(3)和/或负载端子(4)上提供期望的电连接尺寸，并且功率电子模块(5)的供电端子(6)经由电中间网络(13)彼此连接。通过如下方式简化了将AC供电部件集成到测试装置中：功率电子模块的供电端子(6)构造为AC供电端子(6 ~)，并且电中间网络(13)构造为AC中间网络(13 ~)，并且供电电路构造为多相电路(8)，以便在多个相导体(9)上提供多个相电压，选择电路(10)利用功率开关(11)构造为用于将相导体(9)连接到功率电子模块(5)的负载端子(7) 。
",G01R31/42,实质审查
EP4191479A1,EP21212420.0,"本发明涉及一种用于生成传感器数据的数据集的复杂度降低的逻辑表示（10）的方法和系统（1），以及第三数据集（DS3）的输出（S4），其表示第二数据集（DS2）的复杂性降低的逻辑场景（L）。本发明还涉及一种用于提供用于生成传感器数据的数据集的复杂度降低的表示的训练算法（a）机器学习的方法。
",G06N3/08,实质审查
EP4191469A1,EP21212482.0,"本发明涉及一种计算机实现的方法，用于提供用于基于传感器数据的数据集（D）的场景数据来确定类似场景的机器学习算法，其中将优化算法（A3）应用于由第一机器学习算法（A1）输出的传感器数据的数据集（D）的第一扩充（14）的特征表示（18），其中所述优化算法（A3）近似由所述第二机器学习算法（A2）输出的传感器数据的数据集（D）的第二扩充（16）的特征表示（20）。本发明还涉及一种用于基于传感器数据的数据集（D）的场景数据来确定类似场景的方法和训练控制器。
",G06K9/62,实质审查
US20230168342A1,US17990274,"一种用于生成模拟雷达回波信号的方法和雷达目标模拟器。从待测试的雷达传感器以已知带宽发送雷达信号。在雷达目标模拟器中接收雷达信号。通过具有已知滤波器曲线的低通滤波器对雷达信号进行滤波。确定低通滤波器的全部带宽上的经滤波的雷达信号的频谱。计算校正频谱和与校正频谱对应的雷达信号的功率。根据经滤波的雷达信号和作为经缩放的雷达信号的反射的雷达回波信号来计算经缩放的雷达信号。从雷达目标模拟器的发射天线向待测试的雷达传感器发送雷达回波信号。
",G01S7/40,授权
US20230162382A1,US17993687,"本发明提供一种用于确定通过3D场景的模拟而产生的像素的距离数据的像素的强度值的计算机实施的方法及系统，其包含：将第一置信值指派给所述像素的所述第一初始值中的每一者及/或将第二置信值指派给所述像素的所述第二强度值中的每一者；及包含使用指派给所述第一强度值及/或第二强度值中的每一者的所述置信值来计算所述像素的第三强度值，尤其是校正强度值。本发明还涉及一种用于提供经训练机器学习算法的计算机实施的方法及一种计算机程序。
",G06T7/521,公开
EP4184213A1,EP21209972.5,"本发明涉及一种用于确定由像素（12）的3D场景（16）的模拟（14）生成的距离数据的像素（12，通过将第一置信值（K1）分配给像素（12）的第一强度值（10）中的每一个和/或将第二置信值（K2）分配给所述像素（12，以及使用分配给第一强度值（10）和/或第二强度值（0）（K1，K2）中的每一个的置信值来计算（S5）像素（12）的第三、特别是校正后的强度值（10%）。本发明还涉及一种用于提供经过训练的机器学习算法（a）的计算机实现的方法和计算机程序。
",G01S17/00,实质审查
EP4174641A1,EP22201338.5,"本发明涉及一种用于产生程序代码(1)的计算机实施的方法，所述方法包括：产生(S1)第一占位符(P1)，所述第一占位符代表在所述程序代码(1)的预定的片段(10)中使用的变量；产生(S2)第二占位符(P2)，所述第二占位符在使用所述程序代码(1)的预定的片段(10)的情况下被置入在所述第一占位符(P1)的开始之前；以及产生(S3)第三占位符(P3)，所述第三占位符在使用所述第一占位符(P1)的情况下在所述程序代码(1)的预定的片段(10)的结束之后。本发明还涉及一种用于配置控制单元的方法。本发明还涉及一种计算机程序、一种计算机可读的数据载体和一种计算机系统。
",G06F8/34,实质审查
US20230131446A1,US17996950,"本发明涉及一种用于产生真实像素前照灯的控制的方法，借助该控制可以基于场景的可被照亮的区域的不同区域的特征来控制场景的可被像素前照灯照亮的区域的照明强度的二维分布。这提供了这样的方法，该方法允许依赖于光功能的物理选择区域的自动捕获以及对物理选择区域影响的像素的光强的自动改变。
",G06F30/27,暂缺
US20230131079A1,US17913454,"一种用于在电子电路的输入和/或输出通道组上以时间同步的方式输入和/或输出具有可选采样速率的信号的方法，包括：在标准采样周期配置组的每个通道；在标准采样周期同步地启动组的所有通道；检测在修改的采样周期T <Sub>Period of </Sub> A组的第一通道的条目；检测当前计数器值T <Sub>Counter</Sub>；在修改的采样周期配置第一通道；根据T <Sub>Waiting</Sub> = T <Sub>Period</Sub> − mod (T <Sub>Counter</Sub>，T <Sub>Period</Sub>)建立T <Sub>Waiting </Sub>时钟的等待时间，其中mod (T <Sub>Counter</Sub>，T <Sub>Period</Sub>)表示从当前计数器值T <Sub>Counter </Sub>和修改的采样周期T <Sub>Period</Sub>的除法余数；以及在等待时间T <Sub>Waiting</Sub>之后启动第一通道。
",G06F1/12,授权
US20230124300A1,US17908250,"本发明涉及一种用于生成可执行模拟程序并且模拟待测试的控制设备(10)与另外的控制设备(20、30、40)之间的控制设备通信(50)的方法，所述待测试的控制设备(10)具有其发送接口和接收接口的描述，并且所述方法包括以下步骤：将发送接口和接收接口的描述传送到数据库；借助于数据库从描述中识别待测试的控制设备(10)的接口；针对待测试的控制设备的每个识别出的发送接口生成作为所述另外的控制设备的接收接口的接收接口元件；针对待测试的控制设备的每个接收接口生成作为所述另外的控制设备的发送接口的发送接口元件；将所生成的接口元件(24、26、28、34、48)存储在数据库中；借助于数据库提供用于模拟控制设备通信(50)的配置，所述配置包括所生成的接口元件(24、26、28、34、48)；使用所述配置生成用于控制设备通信(50)的可执行模拟程序。
",G05B23/02,授权
US20230113864A1,US17954063,"一种用于在使用制动试验台的模拟计算机中模拟可旋转体的运动的方法和装置，该制动试验台具有发动机、表示所模拟的可旋转体的真实的可旋转体、以及制动器。该方法包括以下方法步骤：指定目标速度，将该目标速度施加到发动机，旋转真实的可旋转体，指定制动值，基于所指定的制动值控制制动器，测量真实的可旋转体的实际扭矩和实际速度，确定实际速度是否超过预定极限速度，以及基于所模拟的可旋转体的扭矩来模拟可旋转体的运动。以这种方式，提供了用于模拟可旋转体的运动的可能性，即使对于可旋转体的低速，该可能性也提供至少近似正确的结果。
",G06F30/15,暂缺
EP4155943A1,EP22163418.1,"一种用于自动测试功能，特别是安全功能的计算机实现方法，该方法集成到从车辆中的数据收集到更新车辆中的驾驶功能和/或待测试的至少部分自动驾驶功能的虚拟认证的端到端过程中。
",G06F11/36,实质审查
EP4148613A1,EP22194640.3,"本发明的目的是一种用于将具有可在FPGA上执行的操作的另一电路组件（1）添加到FPGA配置（3）的方法，其中，所述FPGA配置（3）已经具有至少一个本地分布在所述FPGA配置（3）中的现有电路组件（2），所述现有电路组件具有可在FPGA上执行的操作，所述步骤包括：合成所述另一电路组件（1）以获得另一网表，以及考虑FPGA配置（3）中的至少一个现有电路组件（2）的进一步网表的分布式布置。
",G06F30/34,实质审查
US20230076301A1,US17469921,"一种用于测试距离传感器的测试装置，包括：接收器，用于接收电磁自由空间波作为接收信号；模数转换器，被配置为在模拟模式下将所述接收信号转换为采样信号；信号处理单元，被配置为：延迟所述采样信号或调制采样信号以形成延迟采样信号或调制延迟采样信号；以及基于采样信号或基于延迟采样信号调制作为要被模拟的反射对象的特征运动轮廓的可预定多普勒特征，以形成调制采样信号或调制延迟采样信号；数模转换器，被配置为将调制的或调制的延迟采样信号转换为模拟反射信号；以及发射器，其被配置为辐射模拟反射信号或从模拟反射信号导出的模拟反射信号作为输出信号。
",G01S7/40,授权
US20230067783A1,US17462037,"一种用于自动分割包括连续图像的视觉传感器数据的方法，该方法由主机的至少一个处理器执行，该方法包括：A）为每个图像分配场景编号，其中场景包括在单个环境中拍摄的多个图像，其中基于连续图像之间的比较来执行向每个图像分配场景编号；b） 确定每个场景中的图像的累积努力，其中所述累积努力是基于所述场景的图像中的对象的数量来确定的，其中使用一个或多个用于对象检测的神经网络来确定所述对象的数量；以及c）创建图像包，其中具有相同场景编号的图像被分配给相同的包，除非包中的图像的累积努力超过包阈值。
",G06T7/38,授权
US11593076B2,US17356562,"一种用于合并在架构定义工具和行为建模工具之间交换的架构数据的计算机实现方法，包括：在比较工具中打开具有第一架构数据的第一文件和具有第二架构数据的第二文件；将第一架构数据与第二架构数据进行比较以获得第一差异列表；检索至少一个组合规则，其中所述至少一个组合规则包括用于识别差异的识别规则和要应用于所识别的差异的改变；确定满足识别规则并从第一列表中移除第二列表的差异的第二差异列表；以及将在至少一个组合规则中定义的改变应用于第二列表中的每个差异。
",G06F8/41,授权
EP4124915A1,EP21209965.9,"一种计算机实现的方法（1），用于产生适用于数值模拟软件图像（2）的真实ECU（3）的至少一部分，其中真实ECU（4）在操作中将ECU输入（4a）的输入矢量映射到ECU输出（4b）的输出矢量。
软件映像的生成基于：所述软件图像（2）由人工神经网络（5）或支持向量机形成，所述支持向量机具有具有感兴趣的ECU输入（7a）的图像输入（6a）的输入向量和具有感兴趣ECU输出（7b）的输出向量，通过监督学习或通过强化学习（8）对软件图像（2）进行训练，所述软件图像（8）利用感兴趣的ECU输入（7a）的多个训练输入向量（9a）进行训练，其中训练输入向量（9a）和相应的训练输出向量（9b）是从实际ECU（3）的操作、即从ECU输入变量（4a）和ECU输出变量（4b）获得的。
",G05B17/02,实质审查
US20230024981A1,US17847311,"一种由系统延迟循环转发消息的方法包括：由系统的输入侧接收要转发的消息；以及由系统的输出侧发送要转发到接收设备的消息。执行检查以确定当前系统时间与正在考虑的消息的接收时间之间的差是否大于或等于延迟时间。在差值大于或等于延迟时间的情况下，并且继续对消息的检查，直到找到小于消息的指定延迟时间的差异。
",H04J3/06,授权
US20230025895A1,US17857192,"一种用于经由模拟测试控制单元的系统，包括：模拟器；主机；以及用于通信系统的至少一个连接。至少一个通信工具存储在系统上。真实控制单元可经由通信系统连接至系统。在系统上提供至少一个控制器，用于至通信系统的连接。在系统上存储用于至少一个控制器的驱动器软件。至少一个通信工具被配置为生成用于模拟控制单元和/或真实控制单元之间的通信的通信代码，其中，通信代码被配置为与驱动器软件交互，并且将来自真实控制单元和模拟控制单元的信号和/或消息中继至驱动器软件，并且从驱动器软件接收信号和/或消息。提供用于驱动器软件的循环模式。
",G06F11/36,授权
US11558493B2,US17338003,"一种用于监视在至少两个控制单元之间交换的消息分组的方法。消息分组被串接在数据流中，并且每个消息分组具有由预定义字大小的数据项描述的有效载荷的长度规范、有效载荷和标识符。至少两个控制单元通过分发器连接。分发器通过第一分发器端口被连接到至少两个控制单元中的第一控制单元，通过第二分发器端口被连接到至少两个控制单元中的第二控制单元，并且通过第三分发器端口被连接到计算机系统。数据流流经第一和分发器端口以用于第一节点和第二节点之间的通信。计算机系统具有存储器，并且关于消息分组的相应标识符的信息被存储在存储器中。
",G06F15/16,授权
US20220414302A1,US17358028,"用于使用计算机模拟真实氢燃料电池系统的气体流动动力学的系统和方法，其中所述真实氢燃料燃料电池系统包括气体容器体积网络，所述气体容器体积通过气体输送线互连。该方法包括定义体积元素和流道类别，为每个流道实例定义多个体积实例和多个流道实例，为流道实例创建定义源容器体积和目的容器体积的第一互连表示，其中所述第一互连表示模拟真实氢燃料电池系统的气体容器体积网络的一部分，所述热力学状态表示真实氢燃料电池系统的气体容器体积网络的每个容器体积中的热力学参数。
",G06F30/28,授权
US20220404464A1,US17353905,"一种用于测试使用电磁波操作的距离传感器的测试设备包括：接收元件，用于接收电磁自由空间波作为接收信号（SRX）；以及用于辐射电磁输出信号（STX）的辐射元件。在测试模式中，并且所述辐射元件被配置为辐射测试信号（Stest）或从测试信号（Stest）导出的测试信号（S’test）作为电磁输出信号（STX）。在测试模式中，分析单元被配置为根据其相位角（Phi）和/或幅度（A）来分析接收信号（SRX）或导出的接收信号（S’RX），并且与测试信号（Stest）或导出测试信号（S‘test）的辐射同步地存储相位角（Phi）和/或者幅度（A的确定值作为电磁输出信号（STX）。
",G01S7/40,授权
US20220358032A1,US17735219,"一种用于为测试过程自动提供建议的计算机实现的方法，其中，建议由至少一个建议生成器确定，并且用于测试和/或模拟的建议生成器被手动和/或自动选择，其中，建议生成器监视至少两个测试运行，使得在测试运行中检测至少一个事件，并且导出至少一个建议，其中，建议生成器在测试运行期间由建议生成器机构自动执行，并且由建议生成器确定的建议被提供给测试系统和/或用户。
",G06F11/36,暂缺
EP4086773A1,EP22163414.0,"本发明涉及一种用于自动提供用于测试过程的提示的计算机实现的方法，其中，所述提示由至少一个提示提供者确定，并且其中，所述用于测试和/或模拟的提示由手动和/或自动选择，其中，所述提示提供者观察至少两个测试执行，使得在所述测试执行中识别至少一个事件并且推导出至少一个提示，其中，所述提示提供者在所述测试执行期间由提示提供者机制自动执行，并且其中，所述提示由所述提示提供者确定为所述测试系统和/或被提供给用户。
",G06F11/36,实质审查
US20220326386A1,US17436082,"一种方法生成对应于车辆的LiDAR传感器的合成传感器数据，该合成传感器数据包括叠加的距离和强度信息。该方法包括：提供分层变化的自变编码器；利用第二数据集调节第一特征向量和第二特征向量，第二数据集包括距离和强度信息；将经调节的第一特征向量和经调节的第二特征向量组合为得到的第三特征向量；以及对得到的第三特征向量进行解码，以生成合成传感器数据的第三数据集，该第三数据集包括叠加的距离和强度信息。
",G01S17/931,暂缺
EP4060377A1,EP22159475.7,"本发明涉及一种用于隔离用于LiDAR传感器(12)的测试系统(1)的触发信号的装置(10)和方法，其中在设置在光学元件(20a，20b)之前的触发信号(TS)的信号路径(SP)，该光学元件(20a，20b)被设计为允许触发信号(TS)通过，并且被反射尤其是在表面(22)上的光学元件(20a，20b)的背反射(RTS)被通过的触发信号(TS)至少部分地吸收。本发明还涉及一种用于LiDAR传感器(12)的测试系统。
",G01S7/497,实质审查
US11392353B2,US17016499,"框图包括与外部服务交换数据的至少第一块。第一块引用数据定义集合中的发现对象。发现对象定义选择准则。一种用于从框图的一个或多个块生成源代码的方法包括：由计算机系统打开包括模型编辑器中的第一块的框图；由计算机系统从数据定义集合读出所引用的发现对象；由计算机系统至少基于第一块生成应用源代码；以及由计算机系统至少基于所引用的发现对象生成服务发现源代码。
",G06F9/44,授权
US20220222394A1,US17553838,"一种用于提供技术系统的模拟的方法包括：通过不具有实时能力的部分模拟来生成ping消息；通过所述不具有实时能力的部分模拟来向具有实时能力的部分模拟发送ping消息；以及通过所述不具有实时能力的部分模拟来将从所述具有实时能力的部分模拟接收到的乒乓消息存储在日志文件中，或者在没有从所述具有实时能力的部分模拟接收到乒乓消息的情况下，通过所述不具有实时能力的部分模拟来将所述ping消息存储在所述日志文件中。
",G06F30/20,暂缺
EP4027245A1,EP21204716.1,"一种用于通过确定交通场景中的道路用户的运动简档来确定交通场景的相似性值的计算机实现的方法，具有以下步骤：利用运动元素中的至少一个运动元素创建道路用户的至少一个运动简档，横向运动、纵向运动、位置和/或距离参数，其中运动简档中的新分段以至少一个运动元素中的每个变化开始；创建交通场景的本地车辆和/或同地车辆的至少一个运动简档的序列，其中针对至少一个运动元素的分段中的变化使用序列变化；通过比较从运动简档创建的序列，基于来自至少两个交通场景的运动简档来确定相似性度量；提供相似性度量。
",G06F11/36,实质审查
US20220180032A1,US17535743,"一种用于电动机的实时模拟的方法包括：通过计算机系统在包括通过信号连接的至少两个块的图形模型中对电动机进行建模。该图形模型包括至少两个矩阵运算，该至少两个矩阵运算从至少一个多维输入信号产生多维输出信号。计算机系统包括操作者计算机和连接到操作者计算机的实时计算机，其中操作者计算机执行图形建模环境，并且实时计算机具有至少一个可编程逻辑设备。
",G06F30/331,暂缺
EP4002217A1,EP21202565.4,"根据用于使用系统减少训练数据的方法，所述系统包括编码器，所述训练数据的至少一部分形成时间序列并且被组合在第一训练数据集中，并且所述编码器输入数据是原型特征向量集合中的原型特征向量的数据，a)接收来自所述第一训练数据集合的第一输入数据，b)所述第一输入数据由所述编码器传播，其中所述输入数据由所述编码器分配有一个或多个特征向量，并且根据所分配的特征向量，确定特定的原型特征向量集合并且将其分配给所述第一输入数据，c)针对所述第一输入数据创建聚合向量，d)利用来自所述第一训练数据集合的第二输入数据执行步骤a)至c)，并且针对所述第二输入数据创建第二聚合向量，e)将至少所述第一和第二聚合向量进行比较并且确定所述聚合向量的相似性度量，并且f)如果所确定的相似性度量超过阈值，则标记所述第一输入数据或者从所述第一训练数据集合中移除所述第一输入数据，其中所述标记或者移除导致来自所述第一训练数据集合的所述第一输入数据不被用于第一训练。
",G06N3/04,实质审查
CN114503103A,CN202080013884.8,"本发明涉及一种用于容器中的一个或多个应用的基于使用的许可的方法，其中，容器包括许可证模块，应用经由许可证模块查询应用许可证的存在，并且只有在存在应用许可证的情况下才执行所述应用。在许可证模块中存储有一个或多个应用许可证与单义标记的关联，并且容器包括结算模块，所述结算模块在外部许可证源中调用使用单元。结算模块在所获取的使用单元的持续时间上在受保护的数据存储器中提供单义标记，从而能够执行所有与单义标记相关联的应用。本发明还涉及一种计算机系统和一种计算机程序产品。
",G06F21/12,公开
US20220146627A1,US17095813,"一种用于校准用于主动环境检测系统的目标模拟器的方法，包括：通过确定至少一个信号参数的第一值与所述至少一个信号参数的第一参考值的第一偏差来校准包括第一信号路径和第二信号路径的完整信号路径；通过确定所述至少一个信号参数的第二值与所述至少一个信号参数的第二参考值的第二偏差来校准所述第一信号路径和所述第二信号路径中的一个；以及通过将第一偏差偏移第二偏差来校准第一信号路径和第二信号路径中的另一个。
",G01S7/40,授权
US20220120856A1,US17418957,"一种信号延迟装置，包括解多工器，D ∈延迟装置、D附加延迟装置、多路复用器和控制单元。对于每个延迟装置，延迟输入端和多路分解器输出端连接，并且延迟输出端和多路复用器输入端连接。对于每个附加延迟装置，附加延迟输入端连接到延迟信号路径，并且附加延迟输出端和多路复用器输入端连接。多路分解器将输入数据字流分成并行数据字流。每个延迟装置将延迟并行数据字流中的数据字。每个附加延迟装置将延迟并行数据字流中的数据字延迟一个附加延迟时间。控制单元控制多路复用器，使得输出与具有时间延迟的输入数据字流相对应的输出数据字流。
",G01S7/40,暂缺
US20220099797A1,US17425402,"一种用于测试距离传感器的测试装置，包括用于接收作为接收信号的电磁自由空间波的接收元件，以及用于辐射模拟反射信号的辐射元件。接收信号或从其导出的信号经由时间延迟电路被路由，并且因此被时间延迟为时间延迟信号。时间延迟的信号或从其导出的信号作为模拟反射信号被辐射。时间延迟电路具有模拟延迟路径和数字延迟路径。模拟延迟路径实现了比数字延迟路径更短的时间延迟，除了可能的重叠区域。输入开关用于将接收信号或从其导出的信号切换到模拟延迟路径的输入或数字延迟路径的输入，并且该信号在通过连接的延迟路径之后变为时间延迟信号。
",G01S7/40,授权
US20220082658A1,US17419914,"一种测试距离传感器的方法，包括：接收电磁自由空间波作为接收信号；从中产生模拟电磁反射信号；将反射信号的反射频率偏移小于接收信号的信号带宽的多普勒频率；将所述接收信号转换为具有小于所述接收信号的接收频率的第一工作频率的第一工作信号；将第一工作信号转换为具有第二工作频率的第二工作信号，其中第一和第二工作频率之间的差至少与信号带宽加上多普勒频率一样大；将所述第二工作信号转换为具有第三工作频率的第三工作信号，所述第三工作频率对应于被所述多普勒频率偏移的第一工作频率；通过转换频率增加第三工作信号；并发射第三个工作信号。
",G01S7/40,授权
US20220082700A1,US17416895,"描述并显示了一种测试台（1），用于测试使用电磁波工作的距离传感器（2），其中待测试的距离传感器（2）包括至少一个用于发射发射信号（4）的传感器辐射元件（3a）和用于接收反射信号的传感器接收元件（3b），以及用于容纳待测试距离传感器（2）的插座（5），在距离传感器（2）的辐射区域内具有至少部分可移动的连接构件（6、6m、6s），该距离传感器（2）固定在插座（5）中，至少一个测试台接收元件（7）固定在连接构件（6、6m、6s）中，用于接收传感器辐射元件（3a）辐射的传输信号（4），以及至少一个测试台辐射元件（8），其保持在连接构件（6）中，用于辐射测试台发射信号（9）作为模拟反射信号。
通过将至少一个试验台接收元件（7、7a、7b）和一个试验台辐射元件（8、8a、8b）一起布置在连接构件（6）的可移动部分（6m）中，实现了可靠的环境模拟，尤其是用于测试多输入多输出距离传感器（2）。
",G01S17/931,授权
US11255909B2,US16229696,"本发明涉及一种用于使检验装置同步的方法，其中检验装置被构造用于测试至少一个第一电子调节单元。此外，本发明还涉及一种可转移到同步状态的检验装置。此外，本发明还涉及一种复合系统，其包括至少两个检验装置。此外，本发明还涉及一种用于测试至少一个第一调节单元的检验装置以及一种具有至少一个检验装置和另一检验装置的组合系统，其中，所述另一检验装置被设计用于具有与所述第一检验装置相同的作用。
",G06F11/00,授权
US11256032B1,US17121839,"一种可编程光纤延迟线，其模拟环境传感器的空间距离。所述可编程光纤延迟线包括：由多个长度的光纤互连的至少三个光传输开关，其中具有所述多个长度的光纤的所述至少三个光传输开关被配置来提供具有多个不同的可选延迟值的连续延迟线，其中所述不同的延迟值是基于所述至少三个光传输开关的开关位置可选择的。所述至少三个光传输开关中的第一光传输开关的第一端子连接到所述至少三个光传输开关中的第三光传输开关，从而使得能够绕过所述至少三个光传输开关中的第二光传输开关。
",G02B6/28,授权
US20220043411A1,US17394437,"一种用于低等待时间生成传感器数据并将其输入控制单元或控制单元网络的方法包括：由模拟器系统或设备使用虚拟环境场景的环境模型以模拟频率计算环境数据集，其中每个环境数据集与在总体模拟时间中的相应时间点相关联，在该时间点相应的环境数据集可用于进一步处理；以及由所述模拟器系统或设备或由至少一个附加系统或设备执行至少一个传感器模拟模型，其中所述至少一个传感器模拟模型基于所述环境数据集以采样频率生成传感器数据集，以用于由至少一个控制单元进一步处理，其中每个传感器数据集与在所述总体模拟时间中的相应开始时间和相应完成时间相关联。
",G05B17/02,暂缺
US20220019414A1,US17356562,"一种用于合并架构定义工具和行为建模工具之间交换的架构数据的计算机实现方法包括：在比较工具中打开具有第一架构数据的第一文件和具有第二架构数据的第二文件；将所述第一架构数据与所述第二架构数据进行比较以获得差异的第一列表；检索至少一个组合规则，其中所述至少一个组合规则包括用于识别差异的识别规则和要应用于所识别差异的变更；确定满足识别规则的差异的第二列表，并从第一列表中移除第二列表的差异；以及将在所述至少一个组合规则中定义的改变应用于所述第二列表中的每个差异。
",G06F8/41,授权
US20210385307A1,US17338003,"一种用于监视在至少两个控制单元之间交换的消息分组的方法。消息分组在数据流中串联，并且每个消息分组具有标识符、有效载荷和由预定义字长的数据项描述的有效载荷的长度规范。至少两个控制单元由分配器连接。分配器通过第一分配器端口连接到至少两个控制单元中的第一个，通过第二分配器端口连接到至少两个控制单元中的第二个，并通过第三分配器端口连接到计算机系统。数据流流经第一和分发端口，用于第一节点和第二节点之间的通信。计算机系统具有存储器，并且关于消息分组的各个标识符的信息存储在存储器中。
",H04L29/06,授权
CN113448565A,CN202110293727.3,"本发明涉及一种对分布式计算机系统中的第一可编程门装置、例如第一FPGA进行编程的方法，为了进行所述编程而规定：在第一可编程门装置上设立第一配置逻辑。第一配置逻辑被设立为从配置软件接收用于在第一可编程门装置上设立第一用户逻辑的第一用户比特流并且将第一用户比特流存储在第一可编程门装置的只读存储器上，以便紧接着按照第一用户比特流中的规定在第一可编程门装置上设立第一用户逻辑。在本发明的扩展阶段，还规定在第一可编程门装置上设立编程逻辑，用于对第二可编程门装置进行编程，该第二可编程门装置至少与第一可编程门装置电路连接成菊花链。
",G06F8/34,实质审查
EP3876157A1,EP20160370.1,"本发明涉及一种计算机实现的过程以及一种用于利用环境传感器（尤其是激光雷达传感器）、车辆的合成传感器数据（SSD）生成叠加的距离和强度信息（11，12）的系统。本发明还涉及一种计算机实现的程序，该程序提供一种经过训练的机器学习算法，用于利用环境传感器（尤其是激光雷达传感器）的合成传感器数据（SSD）生成叠加的距离和强度信息（I1，I2），车辆。本发明还涉及计算机程序和计算机可读介质。
",G06N3/04,授权
EP3869380A1,EP20157861.4,"本发明涉及一种用于为不同类别的逻辑元件（12）规划分区设计（14，16）的过程，该逻辑元件（12）的预定位置包括可编程网格布置（10）和多个程序例程，包括至少一个初始程序例行程序和至少一个进一步的程序例行程序。规定了以下程序步骤：将可编程网格布置（10）的第一分区（14）分配给第一程序例程，并将可编程网格布置的至少一个分区（16）分配给至少一个程序例程（S1），确定每一类逻辑元件（12）（S2）对第一程序例程的需求，将该需求与对应类（S3）逻辑元件（12）的第一分区（14）的资源进行比较；以及通过改变这些分区（14，16）之间的分区边界（18）的路线，从下一个分区（16）或其他分区中的至少一个分区到第一个分区（14）的至少一个逻辑元素（12）对应类的传输，如果对该类逻辑元件的第一程序例程的需求超过第一分区（14）（S4）的相应资源，其中分区边界（18）在至少一个部分中改变其路线之前和/或之后是圆形的。
",G06F30/337,实质审查
EP3869390A1,EP20157831.7,"本发明涉及一种计算机实现的方法，用于借助于预先记录的视频图像数据(10a)、雷达数据和/或真实车辆环境(U1)的激光雷达点云来产生用于测试机动车(1)的高度自动化驾驶功能的虚拟车辆环境(U2) 。此外，本发明涉及一种用于产生用于测试机动车的高度自动化驾驶功能的虚拟车辆环境(U2)的系统(2) 。此外，本发明涉及一种计算机程序和一种计算机可读数据载体。
",G06K9/00,授权
EP3739479B1,EP19174383.0,"一种用于对计算机系统的编程逻辑进行故障检修的方法。在计算机系统的第一可编程门阵列上对第一逻辑电路和与其通信地隔离的第一监视电路进行编程。在计算机系统的第二可编程门阵列上对第二逻辑电路和与其通信地隔离的第二监视电路进行编程。在计算机系统的编程逻辑中已经检测到错误之后，在第一可编程门阵列中对第一信号线进行编程而不改变第一逻辑电路，所述第一信号线将来自第一逻辑电路的信号施加到第一监视电路的第一信号输入端，并且在第二可编程门阵列中对第二信号线进行编程而不改变第二逻辑电路，所述第二信号线将来自第二逻辑电路的信号施加。
",G06F11/36,授权
EP3244326B1,EP16168899.9,"一种用于创建从FPGA源代码和至少一个影子寄存器生成的FPGA网表的方法。FPGA源代码定义至少一个功能和至少一个信号。影子寄存器被分配给至少一个信号，并且被布置和提供为在运行时存储所分配的信号的值。提供了用于在运行时读出所存储的信号值的选项。在FPGA源代码中定义的功能不被影子寄存器改变。FPGA执行FPGA源代码描述的功能，并且提供影子寄存器与FPGA源代码中描述的功能解耦。经由该去耦合，当FPGA源代码中描述的功能正在执行时，影子寄存器保持在去耦合时存储的信号值。
",G06F30/331,授权
EP3832517A1,EP19212861.9,"本发明涉及一种计算机实现的过程，用于将至少一个信号值集成到具有应用层、运行时环境（RTE）的虚拟控制单元中，基本软件层和微控制器抽象层。虚拟控制器通过个人计算机在模拟平台上操作，并且需要至少一个输入信号，其中至少一个信号值可归因于输入信号。模拟功能应向虚拟控制装置提供至少一个第一模拟信号值。微控制器抽象层具有用于将第一模拟信号值和第二外部信号值集成到虚拟控制设备中的软件组件，其中，第二外部信号值由外部外围设备提供。用户通过用于输入信号的用户接口选择软件组件应使用第一模拟信号值还是第二外部信号值。在另一处理步骤中，当运行虚拟控制器时，根据用户的选择，将第一模拟信号值或第二外部信号值提供给虚拟控制器作为输入信号的信号值。
",G06F30/20,实质审查
US20210132651A1,US16784291,"一种模块化计算机系统，包括：壳体；至少一个容器；数据连接；以及外壳盖。处于降低状态的至少一个集装箱被配置为经由安装在至少一个集装箱的外部上的至少一个数据连接而连接到计算机系统的主板。数据连接用于在之间交换数据当所述至少一个容器处于所述降低状态时，所述计算机系统的所述至少一个容器和所述母板中的可插拔电路板。壳体盖被构造成固定在壳体上。容器保持装置安装在壳体盖上其中，所述容器保持装置构造成在所述壳体盖已经固定在所述壳体上之后在所述至少一个容器上施加力，以将所述至少一个容器以抗振的方式固定在所述壳体中。
",G06F1/16,授权
EP3812966A1,EP19204989.8,"本发明涉及一种计算机实现的过程，用于将数值分配给在图像、视频和/或点云数据（12）（14a、14b）中标识的至少一个对象的注释（10a、10b）。本发明还涉及一种用于为在图像、视频和/或点云数据（12）（14a、14b）中标识的至少一个对象的注释（10a、10b）分配数值的系统。本发明还涉及一种计算机程序。
",G06K9/62,实质审查
EP3812885A1,EP19205062.3,"根据本发明，从模型生成模拟代码和生产代码作为源代码。所述模型包括一个或多个块，所述块规定了程序、尤其是控制程序的期望行为。所述块中的至少一个用模拟代码属性来标记。针对包含模拟代码属性的那些块生成模拟代码。针对所有其他的块生成生产代码。所生成的源代码包括模拟代码部分和生产代码部分两者。模拟代码部分被包含在源代码中，以使得它们可以与生产代码部分分离。
",G06F8/35,授权
US20210096830A1,US17023418,"源代码从框图的一个或多个块生成。该框图是分层的，并且至少包括被配置用于增量代码生成的第一子系统。该框图引用包括第一对象的定义数据库。所述源代码生成方法包括：由计算机系统在模型编辑器中打开包括所述第一子系统的所述框图；由所述计算机系统生成用于所述第一子系统的源代码，其中生成用于所述第一子系统的所述源代码包括确定关于所述第一子系统的信息；由所述计算机系统存储，关于第一对象中的第一子系统的信息；以及由计算机系统生成用于框图的源代码，其中第一对象被读出以影响用于生成用于框图的源代码的至少一个参数。
",G06F8/35,授权
EP3789864A1,EP19195786.9,"本发明涉及一种用于测试控制设备的控制软件的方法，其中所述控制设备提供用于分析的数据流(12)，所述方法具有以下步骤：‑生成用于在列表状的装置(10)的显示区域中显示所述数据流(12)的第一和第二树形结构，所述装置具有多个行(14)和多个列(16)，其中‑所述第一树形结构包括第一多维列表，‑所述第一多维列表包括多个行元素列表，‑所述行元素列表包括多个元素(20)，‑所述行元素列表中的每个元素(20)能够与数据流(12)关联，‑所述元素(20)在所述列(16)中限定所述数据流(12)，所述元素(20)能够与所述数据流关联，其中‑所述第二树形结构包括第二多维列表，‑所述第二多维列表包括多个列元素nt列表，‑所述列元素列表包括多个元素(20)，‑所述元素(20)限定在所述行(14)中显示所述数据流(12)，所述元素(20)能够与所述数据流关联，‑借助于所述第一和第二树形结构显示所述数据流(12) 。
",G06F3/048,实质审查
US20210056432A1,US16999533,"一种计算机实现的方法，用于训练生成的对手网络的人工神经生成器网络，以便近似来自用于机动车辆的至少部分自主引导的设备的虚拟测试的第一测试结果的所识别的子集的第二测试结果。本发明还涉及一种用于训练人工神经鉴别器网络的计算机实现的方法、测试单元、计算机程序和计算机可读数据载体。
",G06N3/08,暂缺
EP3783452A1,EP19192743.3,"本发明涉及一种用于近似设备的虚拟测试的测试结果以至少部分自主引导机动车的计算机实现的方法。本发明还涉及一种用于提供经训练的人造神经网络的计算机实现的方法、一种测试单元(1)、一种计算机程序以及一种计算机可读的数据载体。
",G05D1/02,授权
EP3783446A1,EP19192741.7,"本发明涉及一种计算机实现的程序，用于近似用于车辆的至少部分自主驾驶的设备的虚拟测试的测试结果的子集。本发明还涉及一篇论文单元（1），用于从用于车辆至少部分自主驾驶的装置的虚拟试验得到的试验结果子集的近似值。本发明还涉及计算机程序和计算机可读介质。
",G05B13/02,授权
US20210034337A1,US16983462,"一种用于准备具有一个或多个块的框图以便在计算环境中生成代码的方法，所述计算环境包括模型编辑器、数据定义工具和代码生成器。该方块图系开放於该模型编辑器中，其中第一区块系为一阶层区块，其包含复数个从属区块、至少一输入埠及至少一输出埠，以讯号连接。接收输入和输出端口的最小值和最大值，基于所接收的最小值和最大值确定输入和输出端口的缩放参数。为第一块中的每个从属块确定比例因数，其中至少一个从属块的比例因数是基于至少一个输出端口的比例因数来确定的。此外，提供了一种用于生成程序代码的方法、非暂时性计算机可读介质和计算机系统。
",G06F8/35,授权
EP3764210A1,EP19184906.6,"本发明涉及一种用以下步骤在桌面（1）上显示显示区域（2、3、4）的程序：-接收数据以创建和显示窗口锁定显示区域（2，3），-基于为此目的接收的数据创建窗口保持显示区域（2，3），从预定的窗口句柄组和唯一的ad标识符中分配清晰的窗口句柄，将广告标识符分配保存到窗口保持显示区（2，3），将窗口保持显示区域（2，3）的显示标识符存储在分层列表中的预定位置，在桌面（1）上显示窗口安装的显示区域（2，3），-接收数据以创建和显示无窗口显示区（4），-根据为此目的接收到的数据创建无窗口显示区（4），并分配唯一的广告标识符，将ad标识符分配保存到无窗口显示区将无窗显示区域（4）的显示标识符存储在层次列表中的预定点处，在桌面（1）上显示无窗显示区（4），当层次列表列出无窗显示区（4）的显示标识符在锁定窗口的显示标识符前面的位置时，在锁定窗口的显示区（2，3）前面显示无窗显示区（4）显示区（2，3），反之亦然。
",G06F3/0481,实质审查
US20210006938A1,US16737970,"发布者设备的发送器将数据发送到服务器，并且订阅者设备的接收器接收数据。发布/订购中间件被用于提供具有数据标识符的待发送数据，并用于基于该数据标识符选择待接收数据。
",H04W4/02,申请终止
US10860298B2,US16181781,"一种用于编辑技术计算环境的框图中的一个或多个模型元素的一个或多个属性的计算机实现的方法。模型元素包括块和块中的变量，其中一个或多个属性被分配给每个模型元素。技术计算环境具有模型编辑器、数据定义工具和代码生成器。主机的处理器打开模型编辑器中的框图，显示存在于框图中的模型元素的列表，接收对一个或多个模型元素的选择，突出显示所选择的模型元素，接收编辑命令以设置用于所选择的模型元素的所选择的属性的新值，并且将所选择的属性设置为新值。还提供了一种非暂时性计算机可读介质和计算机系统。
",G06F9/44,授权
EP3736688A1,EP19210226.7,"本发明涉及一种根据自动配置总成标准的虚拟控制器（10），其具有服务层（12），ECU抽象层（14）和微控制器抽象层（16）。根据本发明，虚拟控制器（10）另外配备有硬件层（18），所述硬件层（18）被设置为用于模拟至少一个硬件组件（20）。这提供了一种虚拟控制装置（10），该装置允许容易地使用环境模型进行HiL测试和软件测试以及快速模拟。
",G06F8/10,授权
CN105530236B,CN201510675496.7,"软件模型的保护。按照本发明的加密方法，借助于公共密钥(19)和解密结构(17)加密技术系统的包括软件组件(40)的软件模型(16)，其中，后者的定义由软件模型(16)的组件组(42)所包括。解密结构(17)至少部分集成到加密的软件模型(22)中。相应地按照本发明的解密方法借助于同样包括组件组(42)的定义的私密的密钥(21)仅仅解密如下的组件组(42)，所述组件组的定义包括与加密的软件模型(22)的定义相一致的私密的密钥(21)。按照一个优选实施形式，私密的密钥(21)的定义可以借助于密钥扩展(23)被事后扩展，从而借助于扩展的私密的密钥(37)可以解密另外的组件组(42)。
",H04L29/06,授权
US20200311330A1,US16827796,"一种使用计算机的动态系统的基于计算机的仿真或控制的方法，包括：由可编程逻辑器件循环地接收至少一个输入信号；由可编程逻辑器件计算至少一个矩阵乘法；以及由可编程逻辑器件输出至少一个输出信号。可编程逻辑器件的配置包括：矩阵的至少两个元素的块与向量的至少一个输入信号相关元素的并行乘法，以及用于乘法结果的加法器树。矩阵的连续块被临时存储在管线中并被顺序处理。根据至少一个系统方程的参数的数量和/或值来确定块的目标数量和目标加法级。基于目标块数和达到的目标加法器级，终止当前周期的块处理。
",G06F30/343,授权
US10761814B2,US16190729,"一种用于在模型管理环境中可视化系统模型的方法，包括以下步骤：在模型编辑器中打开系统模型，接收用于重新缩放块的用户输入，确定块中的每个端口的相对水平位置和相对垂直位置，基于块的相对水平位置和相对垂直位置以及为块预设的新的大小来计算块中的每个端口的新的绝对水平位置和绝对垂直位置，以及显示块和块中的每个端口。
",G06F8/35,授权
US10747649B2,US15996850,"一种用于在检验装置中传输计量地采集的和数字化的测量数据的方法和装置。测量数据对应于程序任务，并且测量数据从测试设备的测量数据发送器的传输方向经由数据信道被提供给测试设备的测量数据接收器。测量数据发送器具有信号预处理器、任务监视处理器和数据信道仲裁器。经由该任务监控处理器，在该程式任务的执行开始或在该程式任务的执行结束时产生一任务ID资料封包，将所述任务ID数据包发送给所述数据通道仲裁器。经由数据通道仲裁器，测量数据和任务ID数据包经由数据通道作为数据流被连续转发到测量数据接收器。
",G06F11/36,授权
CN105840331B,CN201610070579.8,"本发明涉及一种用于通过控制单元(3)计算和输出控制脉冲(2)的计算机实现的方法(1)，其中，控制单元(3)具有第一运算单元(4)和第二运算单元(5)，并且控制脉冲(2)由控制单元(3)输出给内燃机(6)。控制脉冲(2)的计算由此优化，使得第一运算单元(4)借助于内燃机(6)的过去的状态数据(Z<Sub>E,old</Sub>)以第一采样率计算具有用于多个未来的控制脉冲(2)的触发信息(s<Sub>r</Sub>,s<Sub>f</Sub>)的控制脉冲图形(S)并且将所计算的控制脉冲图形(S)传输至第二运算单元(5)；第二运算单元(5)以比第一运算单元(4)的第一采样率大的第二采样率借助于内燃机(6)的当前状态数据(Z<Sub>E,new</Sub>)校正当前要输出的控制脉冲(2)的触发信息(s<Sub>r</Sub>,s<Sub>f</Sub>)；并且控制脉冲(2)由控制单元(3)基于所校正的触发信息(s<Sub>r</Sub>',s<Sub>f</Sub>')输出给内燃机(6)。
",F02D41/26,授权
CN106575106B,CN201580042555.5,"本发明涉及一种用于仿真可连接到调节装置(DUT)上的外围电路设备的仿真装置(100)，其中，所述仿真装置(100)与调节装置电气连接或者可电气连接，并且所述仿真装置(100)具有用于影响第一负载电流(IL1)的第一电流调节器(150)以及用于影响第一源电流(IS1)的第二电流调节器(160)，其中，所述仿真装置(100)此外包括计算单元(CU)和能在所述计算单元(CU)上执行的模型代码，并且借助于能由模型代码控制的第一电流调节器(150)能调节第一负载电流(IL1)，并且所述第一负载电流(IL1)被引导到调节装置(DUT)的第一负载连接端(W1)上，并且借助于能由模型代码控制的第二电流调节器(160)能调节第一源电流(IS1)，并且第一源电流(IS1)被引导到调节装置(DUT)的第一电源连接端(C1)上，并且设定和设置所述模型代码，以便通过模型代码对第一电流调节器(150)和第二电流调节器(160)施加影响使得所述第一负载电流(IL1)能至少部分地从第一源电流(IS1)回收，和/或所述第一源电流(IS1)能至少部分地从第一负载电流(IL1)回收。此外，本发明涉及一种用于仿真的方法。
",G05B17/02,授权
US20200201608A1,US16230312,"一种用于基于框图的一个或多个块生成程序代码的计算机实现的方法，至少一个块包括块变量。该方法包括在模型编辑器中打开框图，从数据定义工具检索用于块变量的生成设置，该生成设置包括变量的范围，确定在生成设置中引用了修改规则，以及从数据定义工具检索所引用的修改规则，其中修改规则包括过滤条件和一个或多个代码改变。处理器基于框图和生成设置生成程序代码，并将所引用的修改规则应用于所生成的代码中的块变量，这包括验证对于块变量满足了过滤条件，并将代码改变应用于代码中变量的每次出现。
",G06F8/35,授权
EP3082000B1,EP15163609.9,"一种用于通过测试环境与真实和/或虚拟部分交互来测试真实和/或虚拟汽车系统的计算机实现的方法，所述真实和/或虚拟部分包括具有针对在测试配置中指定的不同执行条件的测试的不同测试例的测试序列。根据对相应测试中的系统的功能的评估，向测试例和测试配置的每个组合分配来自预定义测试状态值的组的测试状态值。为了进一步规划、执行和/或评估所述测试序列，至少一次在得到的测试例配置矩阵中确定所述状态值中的至少一个的相对测试覆盖，和/或确定提高所述状态值中的至少一个关于测试例和/或关于配置的测试覆盖的相对潜在性。
",G06F30/15,授权
US10678537B1,US15817856,"一种用于生成程序的文档的方法，所述程序是从技术计算环境中的框图的一个或多个块生成的，所述程序的所述一个或多个块具有至少一个分层块，所述分层块的功能由所述框图的下级分层层级中的多个块定义。该方法由具有至少一个处理器的计算机系统执行，处理器在技术计算环境的模型编辑器中的顶层层次级别打开框图，并验证对于框图的当前层次级别是否满足文档条件。当满足文档编制条件时，处理器生成当前层次级别中的块的文档编制文本。
",G06F9/44,授权
US20200174860A1,US16697214,"一种通信系统的消息显示方法，包括：经由所述通信系统在待测试的控制单元与测试环境之间交换消息；由所述测试环境的至少一个处理单元创建、接收和处理消息，以用于与待测试的所述控制单元的所述消息交换以及用于由所述至少一个处理单元的查看器进行的命令处理；由所述测试环境的显示设备在第一视图中的第一显示元件中显示所有接收到的消息；由所述查看者根据过滤指令检查每个接收的消息；以及响应于针对相应消息的检查是肯定的，在第二视图中的第二显示元素中附加地显示相应消息。
",G06F9/54,暂缺
US10671783B2,US16207457,"一种基于用作FPGA模型和/或硬件描述的FPGA设计生成FPGA实现的方法，包括从FPGA设计合成网表和从网表生成FPGA实现的步骤。该方法包括寻找相似的FPGA实现，使用相似的FPGA实现从网表生成FPGA实现的步骤，该方法包括基于FPGA设计生成基于图的表示的步骤，搜索类似的FPGA实现的步骤包括将基于图的FPGA设计表示与至少一个类似的FPGA实现的基于图的表示进行比较。还提供了一种基于FPGA设计生成比特流的方法，用作FPGA模型和/或硬件描述。
",G06F17/50,授权
US20200145486A1,US16674620,"具有相互同步的多个时钟的计算机网络，这些时钟分布在计算机网络中的多个参与者之间，并且可以从中读出计算机网络的全局系统时间。计算机网络包括用于第一同步信号的第一同步信号发射器和用于第二同步信号的第二同步信号发射器，并且每个参与方能够基于第一同步信号或第二同步信号将本地存储的变量的值与全局值同步，这样做是为了考虑同步信号的时滞。
",H04L29/08,暂缺
EP3647801A1,EP18203310.0,"第一FPGA程序（1）的错误检测程序，其中，FPGA程序（1）在FPGA（2）上执行，FPGA（2）连接到处理器（3），监视程序（4）在处理器（3）上执行，信号值 （5、6、7、8）从FPGA程序（1）中读出，并馈送到监视程序（4），监视程序（4）的信号值（5、6、7、8）带有 比较其他来源的参考值（9）。
",G01R31/3185,撤回
CN108225782B,CN201711084901.3,"用于控制系统的测试台，所述控制系统设立用于操控宽带型λ传感器，所述测试台设立为，用于在考虑由泵送电压在所述电路中引起的电流的情况下计算表示在宽带型λ传感器的测量间隙中的氧气浓度的实际值或者可导出氧气浓度的指示值。为了可信地模拟宽带型λ传感器的泵电池的电气响应，在电路中第一二极管和第二二极管如此并联连接，使得在泵送电压为第一极性时电流流经第一二极管并且在泵送电压为第二极性时电流流经第二二极管。
",G01M15/10,未缴年费
EP3629151A1,EP19196334.7,"本发明涉及一种用于在包括与信号相连的块的周期性计算模型的框图之间传输变化的过程，其中基本模型包括一个或多个基本块，一个或多个基本块具有一个或多个基本参数，其中，扩展模型包括一个或多个基本块和至少一个具有至少一个附加扩展参数的扩展块。第一框图包括第一版本中的基本模型，第二框图包括从第一版本的基本模型导出的扩展模型，第三个框图包括第二个版本中的基本模型，第一个版本比第二个版本旧。根据本发明，对第一框图应用各种变换规则以获得第一中间模型、第二框图与第一中间模型的比较、从比较中确定至少一个配置规则，将各种转换规则应用于第三框图以获得第二中间模型，并将至少一个配置规则应用于第二中间模型，以获得第四个框图。此外，本发明涉及用于配置控制器、计算机程序产品和计算机系统的过程。
",G06F8/71,撤回
US10585650B1,US16230468,"一种用于在包括模型编辑器和代码生成器的技术计算环境中基于框图的一个或多个块生成程序代码的计算机实现的方法。该方法包括在模型编辑器中打开框图，该框图包括延迟块，该延迟块在由输入端口接收的信号在输出端口被发射之前将该信号延迟多个周期，确定复合信号被连接到输入端口，以及生成变量的定义，该变量包括状态缓冲器、指针和索引。该方法进一步包括生成循环代码，循环代码包括用于将指针设置到状态缓冲器中具有索引偏移的位置的指令、用于从状态缓冲器输出元素的指令、用于将复合信号输入到状态缓冲器的指令、以及用于调整索引的指令。
",G06F8/35,授权
EP2221697B1,EP09002479.5,"方法包括获取以形式语言作为要求的包含时间依赖性的目标功能，其中功能包括动作和预期反应。根据要求生成要求模型。通过执行在考虑功能的时间依赖性的同时在模型中执行的不可逆性分析来生成测试情况。将情况变换成信号模式。通过测试设备经由信号接口将模式应用于装置。利用测试设备检测装置的相关状态变量。还包括对于以下的独立权利要求：(1)具有用于执行利用测试设备测试控制装置的方法的程序代码的计算机程序；(2)用于测试控制装置的测试设备。
",H04L12/26,授权
US10521332B1,US16145208,"一种用于参数化仿真模型的方法，包括：基于基本块和基本块之间的线连接器的布置来构成仿真模型；将包含第一数字标识符的第一标记块添加到所述仿真模型中的第一子系统；将包含第二数字标识符的第二标记块添加到仿真模型中的第二子系统；分析仿真模型；在分层树中列出所述仿真模型的参数，并且在屏幕上显示所述分层树，以便于经由所述分层树来更改所述仿真模型的所述参数；以及基于第一数字标识符和第二数字标识符是否相同，确定是将第一子系统和第二子系统列出在分层树的公共节点中还是列出在分层树的单独节点中。
",G06F9/44,授权
US20190369585A1,US16420203,"一种用于确定具有实时能力的测试设备的物理连接拓扑的方法，所述测试设备被设立用于控制设备开发，所述方法包括：确定多个仿真模型之间的逻辑通信链路；以及通过基于所述多个数据处理单元的物理接口的各自指定量指定所述多个数据处理单元之间的直接物理通信链路来自动确定所述物理连接拓扑。指定直接物理通信链路包括，对于每个逻辑通信链路，对应于逻辑通信链路的直接物理通信链路是否形成物理连接拓扑的一部分。
",G05B19/05,申请终止
EP3575976A1,EP18174804.7,"确定物理连接拓扑的过程涉及设计用于开发控制装置（2）的实时测试装置，在测试装置（2）具有多种数据处理单元（16）的情况下，每个数据处理单元（16）具有用于数据处理单元（16）之间的通信的指定数量的物理接口，其中大量的仿真模型（18）与各种数据处理单元相关联，其中，各种仿真模型（18）包括要控制的技术系统的至少一个模型和/或技术系统的至少一个控制模型和/或至少一个技术环境模型。程序规定了以下步骤：确定仿真模型之间的逻辑通信链路（20），其中，每个逻辑通信链路表示仿真模型的多重性中的两个之间的数据链路，并且其中数据处理单元的多重性中的至少一个的指定物理接口数量小于与该数据处理单元相关联的数量；逻辑通信链路；以及通过在数据处理单元之间建立直接物理通信链路（24）来自动确定物理连接停止学，同时考虑到物理接口的各自数量，其中，对每个逻辑通信链路（20）的直接物理通信链路（24）的确定确定与逻辑通信链路（20）相对应的直接物理通信链路是否是物理连接拓扑（24）的一部分。
",G06F15/173,撤回
US10488835B2,US15220433,"一种用于配置被配备用于测试电子控制单元的测试器的方法，其中技术系统的软件模型在测试器上执行，并且通过测试器的输入/输出接口与连接到测试器的设备电子地通信。配置系统耦合到建模系统，并且在建模系统中存在由彼此连接的功能块表征的软件模型。测试器在配置系统中由互连的配置元件配置，使得输入/输出接口和/或CONN的物理特性通过配置元件定义输入/输出接口与软件模型的连接。配置系统耦合到建模系统，使得软件模型在建模系统的运行时经由耦合接口被提供给配置系统。
",G05B17/02,授权
EP2685382B1,EP13166958.2,"该方法包括创建控制单元程序组件和测试场景程序组件的可执行程序。创建运行时环境。提供一种元件测试服务，其提供一介面至依据自动开放系统架构(AUTOSAR)标准之运行时间环境，作为一介入点，用以操控运行数量IME环境。测试场景程序组件通过用于运行时环境的适当代码生成来允许对该数量的运行时环境的访问。本发明涉及一种开发设备，用于提供干预点，以操纵用于在测试环境中测试控制单元程序组件的运行时间环境的量。
",G06F11/36,授权
EP3543985A1,EP18163089.8,"本发明涉及一种模拟试验车辆（e）不同交通状况的程序，其步骤如下：试验车辆（E）通过模拟预定道路网的模拟驾驶，模拟大多数其他车辆通过路网的随机驾驶，为试验车辆（E）采集至少一个行驶参数，在试验车辆（e）周围的预定试验区域（t）内，为所有其他车辆采集至少一个特定的行驶参数，检查测试车辆（E）和测试区域（T）内的至少一辆其他车辆是否处于预先定义的交通状况；其中预定的交通状况由试验区（t）内的至少一个试验车辆驾驶参数（e）和至少一个其他车辆驾驶参数给出；以及如果试验车辆（E）和试验区（T）内的至少一辆其他车辆处于预先定义的交通状况，则在预先定义的交通状况下，停止至少一辆其他车辆的随机驾驶，并强制另一辆非随机车辆的预定驾驶操作，预定驾驶操作由预先记录的交通状况来定义。这样，当模拟试验车辆（e）的不同交通状况时，可用状况的频率可以是当试验车辆的自主或部分自主反应实际上被激发时，在不显著损害模拟真实性的情况下增加。
",G09B9/04,撤回
US10423571B2,US15730155,"一种用于配置真实或虚拟电子控制单元的方法，其中在控制单元上执行控制单元软件，并且控制单元软件包括基本软件层，基本软件层通过设置参数值由模块配置文件配置，可配置参数的范围在第一模块定义文件中定义，第一模块定义文件包含可配置参数的标识符。第一模块定义文件被第二模块定义文件所取代，并且发生第一模块配置文件到第二模块配置文件的转换。
",G06F15/78,授权
EP3309698B1,EP17192870.8,"在操作至少暂时连接到执行可将可执行二进制代码传输到过程计算机的配置程序的用户计算机的过程中，可以预见许可审查。二进制代码与指示所需许可证的许可证信息相关联，并且配置程序设置为接收过程计算机的唯一标识符。配置程序将牌照和牌照信息添加到授权程序中。授权计划应验证：分配给唯一标识符的可用许可证是否涵盖许可证信息所需的许可证，并且配置程序仅在授权程序已确定可接受性时才将可执行二进制代码传输到过程计算机。本发明还涉及一种操作计算机和计算机程序产品。
",G06F21/12,授权
US20190258460A1,US15901933,"一种用于对于通过在一计算机系统上的一处理器的一电子控制单元产生一软件成分的方法，在计算机系统包括一架构定义工具和一个行为-建模工具上的软件，适于限定软件成分的一个架构的架构定义工具，结构信息包括一个或多个亚成分和一个或多个接口的声明，另外适于出口和进口结构信息的架构定义工具，对于基于包括多个互连的块的一个功能模型的软件成分适于生成源码的行为模型化工具，另外适于进出口结构信息的行为-建模工具，其中行为-建模工具创建或更新用于基于结构信息的功能模型的一航模骨架。
",G06F8/35,申请终止
US10386806B2,US14842152,"一种用于连接技术系统的模型的方法，其在为具有第一技术系统的一第一个模型的连接到第二技术系统的一次第二种模型的控制单元发展装备的一测试装置中。第一个模型和第二种模型包括一控制单元的一模型，一技术系统的一模型被控制，或与控制单元相互作用的一环境的一模型或在技术系统被控制的情况下。第一个模型具有第一数据界面，并且第二种模型具有第二数据界面。方法具有提供一第一个模型层级构造和提供一第二种模型层级构造。方法具有相容的连接的自动配置，以便通过相容的连接的存在于具有存在于测试装置中的第二种模型的测试装置数据交换中的第一个模型。
",G05B17/02,授权
US20190251212A1,US16274951,"一种方法被公开用于创建用于一FPGA 的配置的一网表，其中一第一个程序的代码包括许多子程序。第一个程序的代码可以结合一第二节目的代码以形成第三程序。用于一FPGA 的配置的一网表可以是在第三程序中创建的，其中第一个程序的至少一个第一子程序在第三程序的运行时间不被使用。可以在一自动方式中识别第一子程序，第四程序可以生成于第一个程序的基础。此外，第一子程序可以在第四程序的创建期间被移除，使得第四程序不包含第一子程序。
",G06F17/50,申请终止
EP2942678B1,EP14167501.7,"提供了一种用于将数据处理系统的多个数据结构与人机接口(MMI)的多个元素相链接的方法和数据处理系统。该方法包括步骤：提供具有多个元素的MMI，在列表中排列多个数据结构，由用户选择MMI的元素，将列表中的第一数据结构与MMI的所选元素自动链接，以及将列表的开始设置为在列表中跟随先前链接的数据结构的数据结构。重复执行以下步骤：由用户选择MMI的元素，将列表中的第一数据结构与MMI的所选元素自动链接，以及将列表的开始设置为数据结构。
",G05B19/042,授权
US10353363B1,US15339968,"由一种功能组成的系统接收块，并且一功能为一图解的发送块，用于具有一个框图的技术的和关系式的图形模型化的基于块的模型环境。框图的块具有输入口和/或输出端口，其中块通过用于数据传输的检测信号线可以通过它们的端口连接。实现的一个功能的柔性管理特别达成在于功能接收块具有一功能接收贯穿其中功能接收块的端口可以被赋予一功能可分配的功能的接口仅仅在功能里指定以功能的输出量的输入和/或数的数的形式接收块。功能发送块具有一功能使贯穿其中一功能被发送出至一个联想机能的端口接收块。
",G05B17/02,授权
EP2945082B1,EP14168015.7,"一种用于生成FPGA程序的网表的方法。 FPGA程序的模型由至少两个组件组成，每个组件被分配FPGA上的单独分区。针对每个组件执行独立的构建，并且从组件生成总体分类，其中在触发事件之后自动地开始构建作业，并且触发事件是组件的保存、设计的组件的退出或构建的时间控制的自动化启动。
",G06F17/50,授权
US20190196925A1,US16226730,"用于配置一适合于测试一电子控制单元的测试系统的一组态系统，其中一结构示图具有多个等级制度元素，一等级制度元素也具有一等级制度元素或多种等级制度元素或没有等级制度元素。等级制度元素具有一标识符，其中一等级制度元素具有端口或没有端口，其中至少一个等级制度元素被分配给测试系统的一功能性质被配置，其中在一扩充视图模式中，等级制度元素最少地可被显示在部分地嵌套的和端口，并且在另一个下面显示一个标识符，其中在一至少部分地叠放视图模式中，显示第一组等级制度元素，使得并排地显示标识符，其中端口和标识符保持可见的，并且等级制度元素的等级关系保持所显示的。
",G06F11/26,申请终止
CN109932926A,CN201711370654.3,"本发明涉及一种用于图像处理系统的试验台，其具有：第一计算单元用于运行具有虚拟物体的环境模型；第二计算单元用于基于虚拟物体的位置计算环境模型的图像投影；以及适配器模块用于处理图像数据并且将其馈入图像处理系统。第一计算单元设置用于从图像处理系统读入基于图像数据计算的控制数据并且在考虑控制数据的情况下将新速度向量配置给虚拟物体。试验台设置用于测量从通过第二计算单元计算图像数据开始直到适配器模块对图像数据的处理结束所经过的时间间隔的长度。第一计算单元设置用于借助该长度估计图像数据的延迟时间、借助该延迟时间求取虚拟物体的外推位置并且将其传输给第二计算单元。本发明还涉及一种用于测试图像处理系统的方法。
",G05B17/02,申请终止
US10331833B2,US15585351,"本发明涉及一种用于产生一整体网表（50）的方法包含下列步骤：作为第一网表（26）提供第一PLD 代码（24），其中第一PLD 代码（24）具有至少一个第一功能框（28），提供第二个PLD 代码（30），其中第二PLD 代码（30）具有用于替代用途的至少一个第二功能框（32）而不是一相应的第一功能框（28），提供一个开关PLD 代码（40），其具有至少一个开关（42），其分配给至少一个第一功能框（28），其用于连接分配给开关（42）的第一功能框（28），从至少一个开关（42）作为相应的第一功能框（28）的替代连接至少一个第二功能框（32）到一开关，为至少一个第二功能框（32）实现至少一个开关驱动信号（44），其中至少一个开关驱动信号（44）被分配给用于连接至少一个第二功能框（32）的对应的开关（42），从第一PLD 创建整体网表（50）将（24）编码，第二个PLD 代码（30）和开关PLD 代码（40），从整体网表（50）功能的第一块（28）移除其以对应的开关（42）的开关驱动信号（44）为基础不被利用。
",G06F17/50,授权
US10331804B2,US15097318,"用于经由一工厂模型测试至少第一自动控制装置的一种系统包括：第一子系统；从第一子系统空间上是分开的第二子系统。工厂模型包括一可执行的第一个模型代码和一可执行的第二种模型代码。第一子系统包括第一报时信号处理部件，其配置成从一全球时刻源给第一事件电子地分配一第一次信号（Ts1）。第一个模型代码被配置为提供基于第一事件的第一计算结果。第二子系统包括配置成电子地分配的第二报时信号处理部件从全球时刻源给一第二事件再一次表明（Ts2）。第二种模型代码被配置为提供基于第二事件的第二计算结果。
",G06F17/50,授权
US10310822B1,US15827196,"一种用于作为在一技术的计算环境中的一框图的一个或多个块模拟一程序模型的方法。一框图开放在一型号编辑器。对于框图的一个或多个块使用代码生成程序产生源代码。程序使用一预定义的编辑由源码构造，为了生成一二进制可执行文件，程序被模拟，其包括运行在辅助文件中的至少一个功能，为了确定一对应于在二进制可执行文件中的可变的列举的基本数据类型的至少宽度，为了记录仿真测试结果在确定的字节宽度上分配一个或多个可变基础。
",G06F9/44,授权
US20190165996A1,US15822521,"一种用于操作具有用于计算一个模拟模型的多种网络节点的一真实的时间有能力的模拟网络的方法。网络节点经由一串行数据总线彼此连接，网络节点交换数据通过数据总线信息。模拟模型的至少一个事件驱动任务实施于一第一网络节点，一非定常的触发事件被一第二网络节点检测。第二网络节点传送检测到的触发事件到第一网络节点，并且第一网络节点计算事件驱动任务。一快速响应时间通过一检测信号是从以对模拟网络的多种网络节点或到在串行数据总线上的模拟网络的所有的网络节点的一条播数据总线消息或一条广播数据总线消息的形式的第二网络节点传送的手段实现。
",H04L12/24,申请终止
US20190163452A1,US15827234,"一种用于在一技术的计算环境中的一个框图的一个或多个块上的产生程序编码基数的方法，一标识符被分配给至少一个，优选地各自，框图的一个或多个块。一处理器在型号编辑器中打开框图，使用代码生成程序把框图到一中间表示，如果一替换情况用于在框图中的一当前像块被实现，其中转换包括检查。检查替换情况包括验证一预定义的操作码单元被分配给当前像块的标识符，在这种情况下改变块到占位符包含输入/输出-定义但是没有功能。处理器接着把中间表示到程序代码，转换包括从数据定义工具至对应于占位符块的定义码添加一预定义的操作码单元。
",G06F8/35,授权
US20190152486A1,US15818787,"用于一图象处理装置的一张试验台包括：设置在试验台中的第一计算部件，其中第一计算部件对于一环境模型经配置以执行仿真软件，仿真软件，其经配置以计算一第一位置x （t）和一初速向量v （t）并分配第一位置x （t）和初速向量v （t）到在环境模型中的第一虚物；第二计算部件设置在试验台中的，其中第二计算部件经配置以在环境模型中的第一虚物的一位置循环地读取并计算，基于至少记录位置，表示一个二维的第一图像数据，环境模型的第一图形投射；设置在试验台中的过渡舱。
",B60W40/04,申请终止
US20190155256A1,US16192822,"一种用于装置控制设备的半自动化的开发资料管理的方法包括在包括多个互相相关的配置数据单元的一中央数据存储中节省一个开发资料模型，其中配置数据单元每一库容管理命令和/或架构参数。所述方法还包括：提供一规则集并标识一初始配置数据单元，其中以下情况是可能的，使用规则集，以具有初始配置数据单元的一种其关系为基础自动地标识另外的配置数据单元。在增加中，方法包括为了在开发资料模型之内标识配置数据单元的一件子集将提供的规则集到开发资料模型并节省识别的子集。
",G05B19/4155,授权
US20190138310A1,US16182637,"在运行时间的来自一现场可编程门阵列（FPGA）的一种用于读取变数的方法包括：计算，在FPGA 中，首变量操作，其中首变量操作与第一阴影寄存器和第二阴影寄存器有关，其中首变量操作与第一测量栅有关和具有第二测量栅；同步地储存，在第一时间点，所有的变量与在阴影寄存器与各自的变量相关联的中的第一测量栅相关联的；同步地储存，在第二时间点，所有的变量与在阴影寄存器与各自的变量相关联的中的第二测量栅相关联的；互相之间独立读出阴影寄存器。
",G06F9/30,申请终止
EP3479237A1,EP17733359.8,"本发明涉及一种用于使检验设备(10)同步的方法，其中，所述检验设备(10)被构造用于检验至少一个第一控制设备，并且所述检验设备(10)至少具有：一个第一计算单元(Cn1)用于执行模型代码，其中，借助所述模型代码能够提供模拟的受控系统信号用于激励所述控制设备，并且能够处理所述控制设备的执行器信号，其中，第一时间差(T1D)反映全局开始时间(TGO)与第一局部开始时间(TLO)的时间距离，并且随后提供所述第一时间差(T1D)用于在所述检验设备(10)中的附加应用和/或用于在附加的检验设备中的应用。本发明还涉及一种相应的检验设备(10)和一种复合系统，其中，所述复合系统具有至少一个检验设备(10)和与检验设备(10)同样工作的附加检验设备。
",G06F9/52,授权
US10275542B2,US13957463,"一配置工具包括一有形的，非暂时性计算机可读介质，其具有计算器可执行指令，其用于配置一技术系统的一模型并展示对连接到一台计算机的显示器的模型。模型至少包括二模型组成部分。每一模型组成部分具有至少一个端口。每一模型组成部分在显示器上的一展开的分量表示中是可显示。每一模型组成部分的至少一个端口用端口的结合线可连通到另一模型组成部分的至少一个端口。每一模型组成部分在随着每一模型组成部分的至少一个端口和端口的结合线的显示器上的一展开的划线表示中是可显示。至少为一选择模型组件连接到选择模型组件的端口的端口的结合线可被选出显示在一减少的划线表示中。
",G06F9/455,授权
CN104981807B,CN201480008273.9,"本发明涉及一种用于在运行中改变FPGA(5)的信号值的方法，所述方法包括如下步骤：将具有至少一个信号值的FPGA硬件配置(24)加载到FPGA(5)上；在FPGA(5)上实施FPGA硬件配置(24)；设置用于传输至FPGA(5)的信号值；由所述信号值确定回写数据；将所述回写数据作为状态数据写入所述FPGA(5)的配置存储器(8)；以及将状态数据从所述配置存储器(8)传输到所述FPGA(5)的功能层(6)中。此外，本发明涉及一种用于基于FPGA模型(20)以硬件描述语言执行FPGA构建的方法，所述方法包括如下步骤：创建具有多个信号值的FPGA硬件配置(24)；在FPGA硬件配置(24)的相邻的区域中设置信号值；基于FPGA硬件配置(24)针对多个信号值的状态数据确定出配置存储器(8)的存储器位置(9)，创建具有在运行中能访问的和/或能改变的信号值和与之相对应的存储器位置(9)的列表。
",G06F17/50,授权
EP3465363A1,EP17725521.3,"一种检查设备可测试至少一个第一闭环控制单元。所述检查设备可包括第一定时传输单元，其可从第一时间信号生成第一周期定时信号，并且其可将所述第一周期定时信号输出至第一PLL.所述检查装置可进一步包括第一振荡器，其可生成第二周期定时信号，并且其可将所述第二周期定时信号输出至第二PLL.所述检查装置可另外包括第一时钟，并且可将第一时钟信号转发至第一输入/输出单元和/或第一计算单元。第一切换信号可用于控制第一多路复用器，使得根据所述第一切换信号的状态，所述第一多路复用器可将第一频率稳定的定时信号或第二频率稳定的定时信号转发至所述第一时钟。
",G05B19/042,授权
US20190102149A1,US16146018,"一种用于提供基于多个模拟模型的一个一体化控制装置开发过程的方法，具有至少一个第一模拟模型和第二模拟模型，其中综合过程模拟一控制单元的一控制单元或一环境并在一模拟设备上是可执行的。方法包括以下步骤：外部地隔离第一模拟模型的可见的第一通信参数和第二个模拟模型的孤立的外部地可见的第二通信参数；比较第一通信参数和第二通信参数并标识同名的通信参数；对于第一模拟模型和第二模拟模型之一至少修改同名的通信参数，使得综合过程在一单核心处理器上是可执行的。
",G06F8/35,申请终止
EP3454234A1,EP17189524.6,"为控制装置的开发提供实时模拟（6）的程序，其中，实时模拟（6）模拟控件或控件的环境或控件与控件环境的组合；其中，实时仿真（6）是实时局部仿真（10）的联合仿真，以及与实时局部仿真交互的联合仿真，非实时子仿真（12），其中实时子仿真（10）和非实时子仿真（12）用于仿真数据通信（SE0、SN0、SE1、SN1、SE2、SN2、SE3、SN3、SE4、SN4、SE5、SN5）经过培训，其中实时部分仿真（10）具有与实时（TE）对应的第一仿真时间，而非实时部分仿真（12）具有与第一仿真时间G对应的第一仿真时间。耦合虚拟二次仿真时间（TV）对应于实时仿真开始时的第一次仿真时间；如果程序有以下步骤：（a）在实时局部模拟的基础上进行验证（10），非实时子仿真（12）是否为即将到来的宏仿真步骤提供了计算的仿真数据；（b）非实时子仿真（12）已经为即将到来的宏模拟步骤完成了计算的模拟数据（sn2），并提供了实时局部模拟（10），在实时子仿真（10）的下一个宏仿真步骤中使用计算的仿真数据（sn2）；（c）如果非实时子仿真（12）没有e已经完成了即将进行的宏观模拟步骤的计算模拟数据，并提供了实时局部模拟（10）。为下一个宏模拟步骤创建估计的模拟数据（SG3）；（d）完成读数后，将第二个模拟时间调整为第一个模拟时间。未及时提供仿真数据的计算（sn3）。
",G06F17/50,撤回
EP3451202A1,EP17188999.1,"在技术系统（6）的测试装置（2）上产生可执行模型的过程，其中，测试设备和可执行模型被训练为实时测试连接到测试设备（8）的控件，并且可执行模型是由通信可执行子模型（10，12）构造而成，其中每个可执行子模型具有单独的地址空间和/或连接到测试设备的控制设备在单独的处理器上测试；或r分离处理器核心。程序应规定以下步骤：（a）对于大多数可执行子模型，以及识别相应可执行子模型的通信属性，（b）访问通信定义（16），以便在可执行子模型之间进行通信；（c）配置通信接口（110、112、114、128、130，132）基于可执行子模型的通信属性和可执行子模型之间通信的通信定义的大多数可执行子模型n个子模型，以及（d）基于可执行子模型的通信特性在可执行子模型和可执行子模型之间通信的通信定义。
",G06F17/50,授权
US20190065644A1,US16119780,"一试验设备可测试性设计的一组态系统一电子控制单元。试验设备是一台硬件回路模拟器或一台快速的样机控制系统模拟器。一技术系统的一软件模型在试验设备上被执行，经由具有连接到试验设备的设备的试验设备的一输入输出接口，软件模型连通。数据电子地是由通信传输，其中组态系统具有多个配置条目。使用技术的功能性质，配置条目在所连接设备和软件模型之间被赋予试验设备和试验设备和/或通信的技术的功能性质被配置。配置条目被赋予一功能类别并在组态系统中的功能性板材中被建造。
",G06F17/50,申请终止
US20190065356A1,US16119718,"用于一试验设备可测试性设计的一组态系统一电子控制单元。试验设备是一台硬件回路模拟器或一台快速的样机控制系统模拟器，其中一技术系统的一软件模型在试验设备上被执行，并且软件模型电子地经由具有一种系统的试验设备的一输入输出接口传送被测试那连接到试验设备。仿真数据电子地是由通信传输，组态系统被连接到一建模系统并在建模系统中是其特征在于横向地和纵向地连接的处理功能块的软件模型。组态系统通过互连的配置条目配置试验设备，使得配置条目确定输入输出接口的物理特性和/或与软件模型的输入输出接口的连接。
",G06F11/36,未缴年费
EP3438817A1,EP17183987.1,"本发明涉及一种方法生成的源代码从一个或多个块，一个拉丁方这是一个动态的系统模型至少包括一个连接块之间，其中该框图可以执行以动态系统仿真框图，并在至少一个地区的定义是在一个或多个块设置。该系统包括一个第一块和第二块，其中第一块第二块的第一和第二blockvariable blockvariable）每个标识符是一个blockvariable的拉丁方。blockvariable标识符标识符是与第一和第二blockvariable比较，这是检查是否第一个块和第二块是在同一地区。第一和第二blockvariable blockvariable作为唯一的变量在源代码中被实现，如果标识符匹配的块和在一个地区的谎言。如果标识符是不同的块和\/或不在同一地区，第一和第二的blockvariable blockvariable为两个独立的变量在源代码中的实现。本发明还涉及一个方法来配置一个控制装置和一个计算机系统，一个computerprogrammprodukt。
",G06F9/45,撤回
US10180917B2,US15151767,"在一计算机系统和一外部环境的一第一处理器之间的用于数据交换的一接口装置。接口装置具有用于从外部环境和一个首次访问管理单元接收输入数据的一些输入数据通道。存取管理单元被配置为接收对提供输入数据的要求，存储在输入数据通道的数中，从存储在接口装置中的第一界面处理器和从存储在接口装置中的和提供或不提供输入数据第二界面处理器，存储在输入数据通道的数中，对第一界面处理器和第二界面处理器。一当务之急和一第二优先级可贮存在第一调整孔管理单元。
",G06F13/10,授权
EP3418924A1,EP18178008.1,"描述和示出了一种用于借助于至少一个运算单元(3)模拟总电路(2)的计算机实现的方法(1)，其中，总电路(2)包括电路组件并且由总状态空间表示(4)在数学上描述，总电路(2)在分离步骤(100)中通过将电路分支分离成至少两个子电路(5a,5b)而被分解，其中，每个子电路(5a,5b)经由耦合变量(i <Sub>I, II, k</Sub>，V <Sub>II, I, k</Sub>，U <Sub>I, MS, k</Sub>，U <Sub>II, MS, k</Sub>，Y <Sub>I, MS, k</Sub>)由相应的部分状态空间表示(6a,6b)在数学上描述， Y <Sub>II, MS , k</Sub>)耦合在一起，并且通过对至少一个运算单元(3)上的耦合部分状态空间表示(6a,6b)进行数值求解来计算每个子电路(5a,5b)
通过将耦合方程系统(11)中的部分状态空间表示(6a,6b)耦合到子电路(5a,5b)之间的计算的耦合变量(i <Sub>I, II, k</Sub>，v <Sub>II, I, k</Sub>)的替换，实现了子电路中的整个电路的稳定分离的系统发现，以便自动实现整个电路的功能的稳定和优化的仿真，映射所述系统发现，在评估步骤(110)中，基于耦合方程系统(11)来映射至少一个稳定性参数S，其中在选择步骤(120)中，根据计算的稳定性参数S来决定整个电路(2)到子电路(5a,5b)中的当前分解是否基于仿真，并且在成功选择之后，通过在WE上计算部分状态空间表示(6a,6b)来进行整个电路(2)的仿真，至少一个算术单元(3)发生。
",G06F17/50,实质审查
EP3413204A1,EP17175193.6,"一个计算机系统的接口单元的至少一个第一和一个第二schnittstellenprozessor schnittstellenprozessor包括一个至少有两个softwareroutinen综合数量softwareroutinen转发输入的数据从一个处理器的计算机系统的外围设备，在一个应用程序，程序的编制。第一子集的数据传输是一个softwareroutinen tasksynchrone规定的第一类，第二子集和一对softwareroutinen将连续数据传输规定的第二类。第一schnittstellenprozessor将与第一子集和第二子集的第二schnittstellenprozessor编程。执行启动的软件，在软件执行的第一子集的第一与第一zyklusrate schnittstellenprozessor循环。由第二和第二子集使用第二zyklusrate schnittstellenprozessor循环结束。
",G06F13/12,授权
EP3401849A1,EP17170127.9,"根据本发明的方法是通过确定一个测试produktreife声称其中一个测试的测试环境，执行一个通过使用一个testfalls测试对象包括至少一个测试，并没有结果，和程序制定规则的步骤，计算一个概率，一个测试是不成功或不成功的结果，将其中规则作为输入，利用目前或预期的结果作为输出变量的概率和检测概率的计算和返回到一个T结果EST，什么没有，会成功的通过至少一个预定的规则和显示部分在前面的步骤中produktreife依赖包括计算概率。
",G06Q10/00,公开
EP3399425A1,EP17169690.9,"检测方法之间的一个verdrahtungstopologie FPGA。第一FPGA包括一个多元化的接口引脚，特别是一个接口引脚。一个包括多个接口的FPGA引脚接口，特别是第二针。一个管道连接的一部分第一FPGA的接口引脚与引脚的第二部分接口的FPGA实现。第一个驱动程序是在第一个接口引脚的存在。第一empfangsregister销将第二接口的实现。第一个驱动程序是在第一senderegister实现。从senderegister将发行从第一驱动信号的定义。第一个驱动程序是在第一aktivierungssignal激活。第一个驱动程序发送一个第一信号。通过将读出的第一empfangsregisters是否第一信号接收的第二接口引脚。第二个接口引脚第一组接口引脚，当信号的第一个驱动程序接收的第二接口引脚。
",G06F13/40,授权
EP3399375A1,EP17169687.5,"方法用于配置至少一个第一功能实体和\/或模拟控制单元（ECU，ecu2）在第一个功能，第一个功能类（F1，F2），在至少一个第一功能的第一功能类（F1，F2）依赖的功能和至少一个功能类（F1的，从第一个参数F2）依赖（的P1，P2，P3，P4，P5，P6）包括第一个功能的至少一个控制单元（ECU，ecu2）被分配给的第一个功能类（F1，F2）作为一个其他类（其他类）和至少一个第一默认类别（K1，K2，K3的，K4），和第一个功能类的功能（F1，F2），每一个类别（K1，K2，K3，K4，其他属在第一类的默认（K1，K2，K3，K4）为至少一个第一参数（P1，P2，P3，P4，P5，P6）一第一预定值（V1，V2，V3的，V4）沉积在第一和第一个映射功能默认类别（K1，K2，K3，K4）为至少一个第一参数（P1，P2，P3，P4，P5P6）第一预定值（V1，V2，V3，V4），因此在一个变化的第一预定值（V1，V2，V3，V4）的参数（P1，P2，P3，P4，P5，P6）第一默认类别（K1，K2，K3的，K4）的修改已经默认所有第一类（K1，K2，K3，K4）被分配的功能，虽然这一功能分配控制器（ECU1，ecu2）和功能参数（P1，P2，P3，P4，P5，P6）第一个功能配置。
",G05B19/042,公开
EP3396549A1,EP17168710.6,"一个适配器用于连接一个嵌入式系统，包括一个bedienrechner标准接口的网络接口，特别是一，第一和第二teilschaltung teilschaltung，其中第一teilschaltung为此建立的标准接口，如以太网，特别是在一个标准的或iocnet，最好是通过一个standardprotokolls，bedienrechner XCP与沟通。这是第一teilschaltung建立在一个标准的标准接口的要求，从大量的protokollfunktionalität protokollfunktionalitäten呼吁支持在一个或多个元素arfunktionen总量由初等函数定义的翻译。一个内部接口的第一和第二teilschaltung teilschaltung相连，其中一个可编程的第二teilschaltung rechenbaustein）它被配置为从至少一个初等函数elementarfunktion总量分配什么样的内部通过一个调用接口调用。第一teilschaltung是建立一个或多个内部的一个接口接收到的值的standardprotokolls协议格式翻译其中，第二teilschaltung individualschnittstelle嵌入式系统与一个可连接其中第一和第二teilschaltung teilschaltung实施和定制设计。本发明还涉及一个计算机系统和方法定制一个适配器。
",G06F9/54,公开
US10102325B2,US15291113,"一种用于确定一高性能逻辑器件的功耗的方法，其中根据一预定义的配置确定至少一个配置参数，并且根据高性能逻辑器件确定至少一个器件参数。预定义的配置通过至少一个接口管脚用一计算部件设计成使得高性能逻辑器件数据交换并从至少一个信号源接收数据和/或通过至少一个接口管脚将其传送到至少一个信号接收机。确定在接收自至少一个信号源的和/或发送到至少一个信号接收机的数据的计算部件和高性能逻辑器件以及至少一个信号特征之间的数据交换的至少一个数据的特点。
",G06F17/00,授权
US10095194B2,US15133502,"一种用于配置一试验设备的方法自称是通过一组态系统测试一电子控制单元，因此一技术系统的一软件模型在试验设备中被执行，经由具有连接到试验设备的设备的试验设备的一输入输出接口，软件模型连通，因此组态系统具有一种第二元件类型的一种第一元件类型和一个二级配置元素的第一配置元件，因此配置元件被赋予试验设备的属性，用其在所连接设备和软件模型之间的通信被配置，其中有点在组态系统中出现属性，排序在分选式，即，一并集种，交叉点种，以及冷凝种之间是可切换的。
",G05B9/02,授权
CN103944794B,CN201410016033.5,"本发明涉及一种用于将总线用户连接到至少一个总线上的电路装置，其具有用于将总线用户与电路装置连接的接口、第一总线输入端和第一总线输出端。电路装置包括第二总线输入端和第二总线输出端以用于环形拓扑地将总线连接到电路装置上，使第一总线输出端至少间接地经由该总线而与第二总线输入端连接并且第二总线输出端至少间接地经由总线而与第一总线输入端连接，其中总线在电路装置中能拆散以用于获得线性拓扑，并且电路装置在其中一个总线输入端或总线输出端处能配置成使总线终止。此外本发明包括用于在仿真环境中对总线上的总线用户进行功能检查的系统，该系统包括仿真器单元和多个所述电路装置。
",H04L12/40,授权
US10078500B2,US15273992,"提供一种用于从在一主机上的一框图产生生产代码的方法。在框图中的一块具有用于接收信号的一些输入口和用于发送信号的一些输出端口。处理器在框图中标识第一块。输入信号追溯到第二块上游的第一块。服从一最优条件被检查，最优条件，其被实现，当一组临近块具有当留下不变的复合变量的至少一个元素时，其影响输入信号的一个或多个元素的赋值运算时。当最优条件被实现时，对临近块小组来说产生一合作生产代码，以便合作生产代码包括用于受赋值运算影响的复合变量的那些元素的写入指令。
",G06F9/44,授权
EP2977894B1,EP14177800.1,"本发明涉及一种方法来创建一个FPGA的FPGA代码模型（1）的步骤，全面的模型设计的FPGA的FPGA（1）作为程序的至少两个部件（3）和至少一个signalpfad（5）之间的至少两个部件（3），设置一个马rkierung（7）中的至少一个signalpfad（5）FPGA的程序生成的模型作为FPGA的FPGA（1）的至少一部分的代码的基础上的模型，其中的步骤的模型制造，使用FPGA的FPGA（1）作为代码自动插入一个beeinflussungsstruktur（11）上的标记（7）（5）和杆状signalpfad FPGA产生的M（1）odells与beeinflussungsstruktur（11）包括。的beeinflussungsstruktur（11）允许在任何一个时间，执行的过程模型，基于FPGA的signalwert signalpfad（5）完全取代或改变。本发明还涉及一datenverarbeitungseinrichtung computerprogrammprodukt数字存储设备，以及一个电子信号线auslesbaren执行的程序。
",G06F8/41,授权
EP3352028A1,EP17152600.7,"一个模拟器（5）和一个测试程序（1）在一个函数（f））的一个控制面板（2）的车辆（3A）。该车辆包括umgebungssensoren，例如雷达（6A，6B），照相机（6C条）和接收器（6D），作为输入的steuergerätefunktion（f））。服务控制单元（2），其中一款型号为（12A条），和一个umgebungsmodell sensormodelle（13）（14）在一个多recheneinheiten（4A，4B条，4C）和存储器（5）和分布式模拟器进行模拟。仿真steuergerätefunktion（ECU）和模拟作为其输入（第12A），辅助车辆umgebungsmodell sensormodelle（13）和（14）被同步到recheneinheiten（4a，4b，4c）的启动，其中对内存进行数据交换。通过对多recheneinheiten（4A，4B，4C）及数据交换的分布式仿真是一种高simulationsgeschwindigkeit内存了。控制单元（2）不一定需要模拟真实的存在。通过参与，例如数据从其他车辆（3B）（8）建筑物，道路，交通标志（9），（10）或（11）交通标志，可以模拟。
",G05B17/02,撤回
EP2487499B1,EP11185161.4,"本发明涉及提供多个相互连接的单电池、用于仿真目的电池、所述电池由基于计算单元的整体模型描述以及对参考电池(EMK)建模的第一模型，利用所述第一模型计算参考电池的端电压。提供第二模型，利用所述第二模型基于参考端电压和每个电池的端电压的推导值计算每个电池的端电压与参考电池的端电压的偏差。
",H01M10/42,授权
EP2881857B1,EP13005730.0,"本发明涉及一种用于改变电子控制装置的存储器中的软件的方法，其中，可以将来自覆盖存储器的每个存储器地址通过分配信息分配给只读存储器的存储器地址。在控制装置的运行时间期间，将至少部分地替换原始程序例程的旁路例程的至少功能性部分存储在覆盖存储器的地址范围中，或者将转移指令作为旁路例程的第一部分存储在覆盖存储器中，该第一部分引用存储在处理器可访问的地址范围中的旁路例程的第二部分。为了激活覆盖功能性，将覆盖存储器的地址和/或地址范围分配给要替换的程序例程的地址或地址范围。
",G06F9/445,授权
EP3336730A1,EP17204564.3,"提出了一种创建与技术系统的仿真装置（2）兼容的模型的过程，仿真装置（2）是为开发控制装置而设计的仿真装置，并且仿真装置（2）上的兼容模型（8）是可执行的。该程序应包括以下步骤：（a）提供与技术系统的模拟装置（80）不兼容的模型；（b）提供虚拟执行环境（82），其中与技术系统的仿真设备（80）不兼容的模型可在虚拟执行环境（82）中执行；以及（c）将与技术系统的仿真设备（80）和虚拟执行环境（82）不兼容的模型封装到兼容的容器单元中，它形成技术系统的兼容模型（8），技术系统的不兼容模型（80）可通过兼容容器单元和仿真设备（2）上的虚拟执行环境（82）访问。
",G06F17/50,授权
CN108139723A,CN201680062135.8,"说明并示出了一种用于运行控制器(1)的方法，其中，在所述控制器(1)上存放有具有内部控制器函数(a())的程序代码(4)，所述程序代码(4)配备有至少一个服务函数(ecu_s())，通过外部旁路能够实现有针对性地调用内部控制器函数(a())，其方式为在控制器(1)上提供用于服务函数(ecu_s())的服务配置(6)，在控制器(1)中检测所述服务配置(6)并且在调用服务函数(ecu_s())时根据所述服务配置(6)来实施服务功能，其中，所述服务配置(6)描述至少一个内部控制器函数(a())，所述至少一个内部控制器函数作为对应的服务函数(ecu_s())的服务功能实施。所述服务函数(ecu_s())设置用于借助服务配置(6)在控制器(1)中提供用于内部控制器函数(a())的至少一个自变量的至少一个值(x')和/或接受所述内部控制器函数(a())的至少一个返回值。
",G05B19/042,申请终止
EP3316477A1,EP16195987.9,"提出并描述了用负载模拟器（1）再现三相电动机的过程。其中负载模拟器（1）通过其负载终端（2）分三个阶段连接到发动机控制装置（4）的馈送终端（3），负载模拟器（1）具有模拟器电力电子装置（5）和模拟器控制装置（6）以控制模拟器电力电子装置（5），其中仿真器控制装置（6）确定由发动机控制装置（4）控制的电源端子（3）和非电源端子（3），以及由仿真器控制装置（6）控制的仿真器电力电子装置（5），由发动机控制装置（4）计算的相电流从仿真器控制装置（6）根据发动机模型（8）和不受发动机控制装置（3）控制的馈电线连接（4）流入馈电线终端（3）仿真器控制装置根据发动机模型（8）计算的相电流放弃相电压仿真。
通过负载模拟器（1）包括开关（15），可以实现考虑三相电动机超出正常电动机或发电机运行的特殊运行情况的可能性，其中，仿真器电力电子器件（5）可以大容量地从馈电电路（3）断开，仿真器电力电子器件（5）可以低电平地接通馈电电路（3），并且馈电电路（3）通过高电平电阻网络（16）连接，为了模拟电动机的选定运行模式，带有开关（15）的仿真器电力电子器件（5）应与高电平（3）的馈电电路分开，或者仿真器电力电子器件已被开关（15）从低电平到电源（3）。
",H02P27/06,授权
US20180101501A1,US15730155,"配置方法真实或虚拟电子数控制单元，其中控制单元软件运行在控制单元，和控制单元软件包括基础软件层，对基本软件层配置，通过模块配置文件通过设定值的参数，范围的可配置参数，限定在第一模块定义文件包含这些标识符的可配置参数。第一模块定义文件被第二模块定义文件，和转换第一模块到第二模块的配置文件，配置文件中发生。
",G06F15/78,授权
US09940297B2,US13900008,"用于操纵总线通信的电子控制装置，其总线包括总线独立于硬件的第一连通层，以及总线与硬件有关第二通信层。第一通信层编码至少一个信息在第一数据单元和将它发送到第二通信层和/或第一通信层接收第一协议数据单元从第二通信层和解码第一信息从第一协议数据单元。第二通信层生成总线与硬件有关总线信息原来协议数据单元或从附加协议数据单元从第一协议数据单元经由总线和/或第二通信层产生第一协议数据单元或额外的协议数据单元，从第一协议数据单元可被导出。
",G06F13/42,授权
CN105426280B,CN201510680947.6,"本发明涉及一种用于部分释放第一可编程的硬件构件的调试接口的设备，其中，在配置存储器上可存储用于所述可编程的硬件构件的第一逻辑，并且配置装置构造用于，借助所述可编程的硬件构件的配置接口按照第一逻辑对所述可编程的硬件构件进行编程.所述配置装置另外构造用于，记录借助调试接口按照第二逻辑进行的对所述可编程的硬件构件的编程过程并且在借助调试接口进行的编程过程结束后按照第一逻辑重编程所述可编程的硬件构件。
",G06F11/26,授权
US09929734B2,US15258059,"在一种用于改变配置逻辑模块，初始配置的可编程逻辑模块的读取，结果是一个逻辑说明，特别是映射网表，初始配置至少部分可用的。一个或多个逻辑元件和/或连接元件与逻辑描述的初始结构的可编程逻辑模块的更换或重新配置，和逻辑说明目标配置具有一个或一个以上额外的元件被创建时，没有，或者多个元件中缺失的初始配置是目标配置。分配信息被用来确定转发点，也就是说逻辑元件存在于目标配置和初始配置，特别是寄存器、和/或连接元件，向这些改变应用于目标配置逻辑功能，至少逻辑元件的可编程逻辑模块哪个已经未使用的初始配置是标注为空闲，和附加的元件放置在逻辑元件目标配置标记为空闲且与传输点经由未使用的连接元件。改变的位流从逻辑创建目标配置的描述，位元流写入可编程逻辑模块。
",H03K19/177,授权
JP2018041450A,JP2017158131,"“课题”提供能够模拟技术系统的操作软件和其硬件依赖性的软件部分的简单且低成本的手段。“解决手段”是用于打开循环控制或控制设备的控制单元的操作软件20，以硬件依赖性的软件来代替硬件依赖性的软件部分。用等价函数23模拟亚部分的功能行为。为了改变所需的变更，自动与硬件依存性的软件部分进行同定，自动生成等价函数。改变的操作软件在适当的模拟环境200上执行时，不依赖技术系统的实际软件模拟技术系统。【选择图】图3
",G06F11/34,授权
EP3232327B1,EP16165503.0,"用于监视差错的方法当测试在一模拟环境（40）中的一控制设备的一控制程序（10）时，被在一台计算机上的一仿真程序执行的控制程序（10），仿真程序，其分配项目（50 ，60 ，70）的一扩充域到控制程序（10）的程序变量，一变量值（52 ，62 ，72）分配给储存在项目（50 ，60 ，70）的扩充域的一程序变量的，作为错误的或非错误的，对一第一纲（K1）和错误的程序变量到一第二类（K2），在具有一数据区（64）的第二类（K2）中的每一程序变量的项目（60）的扩充域，或以存储在项目（70）的扩充域中的一误差场（76），被分配给被分配给一错误的程序变量的误差场（76），每一程序变量的项目（70）的扩充域的一非错误的程序变量和一误差值的误差场（76）的一有效性值为基础进行的标记以非错误的程序变量的一个分配为基础进行的标记的仿真程序标注程序变量，其具有一数据区（74）。
",G06F11/26,授权
EP3290898A1,EP17177327.8,"本发明是一种方法，用于在用于测试一fahrassistenzsystems 在一个fahrsimulator 的两辆车辆之间的一次kollisionssituation 的模拟或一辆车辆，在其中，--循环scenario.in simulationsrechner 模拟同伴的车辆，通过在simulationsrechner 的车辆和一名研究员之间的碰撞的一轨道根据kollisionsort 一计划控制自我vehicle.the 同伴的车辆将继续在kollisionsort 上的一所需的eintreffgeschwindigkeit）.the fahrassistenzsystem 以实时的方式用模拟环境成立数据交换和一kollisionssituation 自我车辆的加工性能对对kollisionsort 的不可预见的受控的自我车辆的influence.the 距离和车辆的电流速度对于同伴的车辆sollabstand 到kollisionsort 测定在first.for 每循环识别同伴的车辆应当haveunder ，其与与在kollisionsort arrive.a 控制电路上的自我车辆重合的指定的eintreffgeschwindigkeit 的系统的假设研究员--车辆调整速度，减少的在sollabstand 和实距之间的差异。
",G01M17/007,撤回
EP2363809B1,EP11153945.8,"本发明涉及用于执行机构的控制程序的优化方法。在该方法中，将分配给参数(P1)的值(W1)存储在分配给一个模型单元的存储区域中。通过代码发生器(CG)利用分配给函数(F1)的程序线路来产生一个优化的控制程序(OCC) 。通过代码发生器的优化单元(OE)从所分配的存储区域中读取分配给参数的值。根据由优化单元进行的比较来确定一个结果。
",G06F11/36,授权
US20180060457A1,US15249737,"一种计算机实施的方法比较框图，框图描述一种时空演化研究和/或内部状态的动态系统技术计算环境主机，其中方块框图可包括输入端口和输出端口接收发送信号。该方法包括打开第一框图，将第一框图向中间形式，该转化包括使用过滤器过滤脚本，打开第二框图，转换成第二图至中间形式，该转化包括使用过滤器过滤脚本，确定现有的之间的中间形式的第一框图，和中间形态第二块的框图，并输出该确定的差异。同样，非暂时性计算机可读介质和提供了计算机系统。
",G06F17/50,申请终止
EP3285165A1,EP16184654.8,"根据本发明，修改了技术系统的操作软件10，特别是用于控制或控制至少一个技术设备的控制器，通过不执行在技术系统10上直接可执行的硬件相关软件部件，而是通过替换功能23再现硬件相关软件部件的功能行为。自动识别硬件相关的软件组件（步骤S1），以及替换功能23自动检测或生成。当在适当的模拟环境200上运行时，经修改的操作软件20独立于其实际硬件100来模拟技术系统（步骤S3）。
",G06F9/455,撤回
CN104142678B,CN201410180526.2,"本发明涉及一种用于在模拟器中利用模拟环境(3)测试虚拟控制仪(2)的至少一部分的测试装置(1)，其具有虚拟控制仪和模拟环境(3)，其中，所述虚拟控制仪包括至少一个带有至少一个外部数据接口(7)的软件组件(4、5、6)，其中，所述模拟环境包括至少一个数据接口(8)以用于与所述虚拟控制仪(2)至少间接地进行数据交换；特别是利用虚拟控制仪能简单地进行电气错误模拟，减少的相关性由如下方式达到：在虚拟控制仪和模拟环境之间设有虚拟控制仪插脚单元(9)和虚拟影响单元(13)，它们通过虚拟控制仪插脚单元的虚拟控制仪插脚(12)传输至少一个虚拟的物理控制仪信号，所述虚拟影响单元输出受影响的虚拟的物理控制仪信号。
",G05B23/02,授权
US20180039566A1,US15229231,"计算机实施的方法，用于控制程序，即模型化为一个或更多个块的计算环境的框图。第一用户接口提供用于选择模拟模式操作的框图，和第二用户提供用于选择编译器意欲产生代码编译。当确认在环仿真模式已经被选择的第一用户界面，这块的框图转换成生产代码和编译成可执行的使用编译器选择在第二用户界面。通过运行该电脑主机可执行而记录一个或多个数据点基于输入/输出信号和/或评估遵守一个或多个数据指向一个或多个标准，控制程序对应于所述一个或多个块的测试。
",G06F11/36,授权
EP2587225B1,EP12184008.6,"本方法涉及从传感器信号(151)获得标记部分，并确定排除标记部分的排除标准。确定存储的标记部分的总数和排除的标记部分的数目的差异是否等于一。从非排除的标记部分确定发动机的旋转角度。通过传感器(141)在标记载体(110)即齿轮的旋转方向上扫描标记部分，并通过计算单元(30)计数，其中传感器设有标记(133-137)或齿。
",G01D5/249,授权
US09870440B2,US14711116,"一种用于产生一FPGA 程序的一网表的方法。FPGA 程序的模型由至少两个组件，被赋予在FPGA 上的一个单独的分区的各组成分组成。对于各组成分和一综合分析技术进行一座独立的构造从组件生成，其中构造工作被自动地开始在一触发事件和触发事件是一节省一组件，设计的一组件的出口，或一座构造的定时控制的，自动化的引发。
",G06F17/50,授权
CN107560839A,CN201710518721.5,"一种用于调节体积流量的方法和一种用于实施所述方法的具有液体回路的试验台。泵和节流阀在液体回路中串联连接，并且根据液体的体积流量的理论值调节节流阀的开口宽度，以便借助开口宽度确定泵的关于压差记录体积流量的特性曲线。在确定特性曲线之后这样调节泵的压差，使得所述体积流量相当于体积流量的理论值。
",G01M13/00,申请终止
US09841954B1,US15233347,"用于发电生产代码从方框图在技术计算环境主机。第一接收第一输入信号具有多个元件。一种尺寸的第一所需信号外部函数的尺寸被确定并且与第一输入信号。当该大小第一所需信号的大小对应的元素在第一输入信号产生代码产生封闭调用外部函数，通过环路连续地寻址多个元件中的每个的第一输入信号。当该大小第一所需信号的大小对应的第一输入讯号产生代码打电话外部函数的产生没有封闭的环路元件上的第一输入信号。
",G06F9/44,授权
EP3244325A1,EP16168898.1,"方法输出时间同步信号同步的时间和\/或捕获的信号处理的一个或多个电子电路综合ausgabekanälen以下步骤：摘要一个频道数量，特别是一种teilanzahl所有通道的逻辑电路组kanallatenz B.查询每一组从一个数据源相关的渠道，C.确定从所有的请求的最大kanallatenz kanallatenzen和至少暂时存储作为最大的kanallatenz gruppenlatenz，每个通道D.属于集团的执行时间是确定的请求的kanallatenz gruppenlatenz之间和各自的渠道和储存模量差比kanalzugehörigen latenzoffset在存储，特别是不可约的一个存储器电路一个signallaufes E.影响至少在各自的渠道依赖的latenzoffset有限的存储。
",G06F17/50,授权
EP3244327A1,EP16168901.3,"方法产生一个参考表的交叉引用表，其中基于FPGA的源代码编译的源代码，其中至少有一个第一信号，第一信号，利用第一个至少有一个是分配给第一个登记册，其中在zuordnungsliste第一信号和第一个寄存器相互配合，将列出其中第二个信号的第二点在FPGA使用的源代码，自动识别，是价值的第二信号根据第一信号的功能从第一值可确定，其中第二个信号zuordnungstabelle，第一个寄存器的功能是相匹配的第一项。
",G06F17/50,撤回
EP3242232A1,EP16168055.8,"本发明涉及一种方法生产的一个步骤gesamtnetzliste（50）提供第一PLD的代码（24）作为第一个表（26），其中第一PLD的代码（24）的至少一个功能块（28）的第一和第二PLD提供的代码（30）其中第二PLD编码（30）的至少一个功能块（32）和第二可供使用而不是一个相应的第一功能块（28）一个开关可提供的代码（40）至少有一个至少有一个第一功能块（28）相关的开关（42）连接的第一个开关（42）相关的功能块（28），连接的至少一个第二个功能块（32）至少有一个开关（42）相应的第一功能块（28）schalteransteuerungssignals（44）执行至少一个功能块的至少一个第二对（32），其中至少有一个schalteransteuerungssignal（44）相应的开关（42）连接的至少一个第二个功能块（32）是分配给创建gesamtnetzliste（50）包括第一PLD的代码（24）第二PLD编码开关（30）和PLD的代码（40），和删除功能的第一块（28）的基础上，schalteransteuerungssignal（44）相应的开关（42）不使用，从gesamtnetzliste（50）。
",G06F17/50,授权
US09811361B2,US14055497,"用于生成软件的硬件部件的测量、控制或调节系统具有处理器，FPGA，多个输入输出通道。该I/O通道连接到FPGA的FPGA连接到处理器通过通信接口。该方法包括步骤：选择第一子集的输入输出通道操作，通过该FPGA，产生第一应用执行的FPGA，选择第二子组的输入输出通道的操作由处理器，和生成第二执行申请的处理器。产生步骤包括连接第一代码的第二子集的输入输出通道到通信接口。本发明另外还涉及一种用于操作硬件组件。
",G06F9/44,授权
CN103106145B,CN201210281662.1,"本发明涉及一种用于在数据通信装置中处理控制装置的数据的方法，其中数据通信装置具有第一存储区和第二存储区，并且经由接口与控制装置连接，并且数据从控制装置经由接口传输到数据通信装置。数据中的每一个数据具有一个地址和一个数值。数值相同地存储在第一存储区和第二存储区中。由数据通信装置检测是否存在第一触发。当存在第一触发时中断在第一存储区中的存储，或者在第一触发时检测触发类型并且仅当存在预定的触发类型时中断在第一存储区中的存储。随后，从第一存储区读出数据的数值，其中由数据通信装置将在时间上晚于第一触发到达的数值存储在第二存储区中。
",G06F12/02,授权
CN103033364B,CN201210322295.5,"本发明涉及用于借助模拟器实时测试内燃机控制设备的方法，其中模拟器包括第一模拟器计算单元和第一模拟器I/O接口，控制设备包括控制设备计算单元和控制设备I/O接口且控制设备和模拟器通过其I/O接口借助第一数据通道相互连接并且控制设备通过第一数据通道传送内燃机控制数据至模拟器，模拟器借助内燃机控制数据及内燃机整体模型在模拟器的第一模拟器计算单元上以第一采样步长计算内燃机状态参数且以第一传输步长传输至少一部分内燃机状态参数至控制设备。模拟器利用内燃机局部模型以不同于第一采样步长的第二采样步长计算至少一个特定的内燃机状态参数，这样所选出的内燃机状态参数可以以另一个尤其是比通过第一采样步长所能实现的更高的频率被提供。
",G01M15/00,授权
CN107037803A,CN201710027205.2,"本发明描述和阐述了一种用于仿真残余总线控制仪组合的计算机实现的方法，其中，所述残余总线控制仪组合包括至少两个经由总线系统相连接的残余总线控制仪，并且所述残余总线控制仪组合与至少一个另外的控制仪经由总线系统相连接，其中，描述残余总线控制仪的通信关系，基于所述通信关系生成用于仿真残余总线控制仪的程序代码，并且借助于程序代码的可执行的文本在仿真计算机上对残余总线控制仪组合进行仿真。通过如下方式能实现对残余总线控制仪组合的简化和灵活的仿真，即，为残余总线控制仪生成唯一的共同的残余总线控制仪模型作为用于仿真残余总线控制仪的程序代码。此外，本发明涉及一种用于仿真残余总线控制仪组合的设备。
",G05B23/02,申请终止
EP3203376A1,EP16154002.6,"描述并描述了用于模拟剩余总线控制单元连接（2）的计算机实现的过程（1）。如果剩余总线控制单元组合（2）包括至少两个连接到总线系统（3）（2a，2b）的剩余总线控制单元，并且剩余总线控制单元组合（2）经由总线系统（3）连接到至少一个附加控制单元（4），则剩余总线控制装置（2a，在通信关系的基础上，创建程序代码（5，5a，5b）以模拟剩余总线控制器（2a，2b），并且剩余总线控制器化合物（2）通过程序代码（5）的可执行版本在模拟计算机（6，6a，6b）上模拟。
通过为剩余总线控制装置（2a，2b）生成单个公共剩余总线控制装置模型作为程序代码（5）来模拟剩余总线控制装置（2a，2b），可以简化和灵活地模拟剩余总线控制单元组合（2）。
",G06F11/26,驳回
EP3200428A1,EP16152681.9,"（1）computerimplementiertes程序实现应用程序在目标硬件v2x（2）与（3），其中funkadapter v2x应用通过一个图形化的modellierungsumgebung（4）形成一个模拟电路（5），其中，在一个列表（6）接收的LDM从一个telematikdaten umgebungsobjekten覆盖其中一个传感器（7）从列表中确定的telematikdaten umgebungsobjekten覆盖，其中一个telematikdatenvergleichsblock（8）通过比较与LDM的列表（6）传感器差分umgebungsobjekte列表（7）确定的传感器，只有在列表中包括的其中一个至少有一个差分umgebungsobjekt表示v2x消息（9），与此差分umgebungsobjektes telematikdaten创建框图（5），其中一个在目标硬件v2x（2）可执行程序（10）和v2x翻译程序（10）的Z（2）在ielhardware转移被执行其中表示v2x消息从目标硬件（9）（2）（3）对funkadapter被发送。
",H04L29/08,授权
CN106997198A,CN201710013965.8,"本发明涉及一种用于自动配置为测试控制器而设置的测试设备的方法，在测试设备中执行技术系统的第一模型和第二模型，测试设备包括用于执行第一或第二模型的FPGA和用于执行第一或第二模型的CPU，要测试的控制器在执行模型时连接到测试设备上并且实现在所述控制器和/或第一模型和/或第二模型之间的数据交换。所述方法具有下列步骤：为第一模型分配个性化的第一采样率并且为第二模型分配个性化的第二采样率；配设第一模型以用于在CPU或者FPGA上执行并且配设第二模型以用于在CPU或FPGA上执行；以及自动配置测试设备，以用于在FPGA或CPU上以第一采样率执行第一模型并且在FPGA或CPU上以第二采样率执行第二模型。本发明还涉及一种为测试控制器而设置的测试设备。
",G05B23/02,申请终止
US20170206097A1,US14996281,"输入/输出接口的测试设备的构造，其中输入/输出接口是开发用于硬件单元至行为模型存在于试验装置。该方法包括步骤：显示图形表示之间的信号路径的输入/输出接口作为硬件端口连接的硬件和至少一个模型端口，用于将行为模型经由可选输入/输出功能；接收到第一信号通路；接收一测试值可预定义的，即在硬件端口或模型端口的信号路径，但是，例如，还通过可预定义的图形表示硬件端口或端口；模型传送测试信号与测试值沿信号路径，根据第一配置信号路径，和显示传播对测试信号的图形表示模型端口或硬件端口。
",G06F9/445,授权
EP3193221A1,EP16151449.2,"根据本发明的步骤，配置测试设备的输入/输出接口，输入/输出接口被标记为将硬件连接到测试设备中存在的行为模型。此过程显示以下步骤：将输入/输出接口的图形表示显示为硬盘驱动器之间的信号路径连接硬件的软件端口和至少一个通过可选输入/输出功能连接行为模型的模型端口。接收信号路径的第一配置。在硬件端口或信号路径模型端口接收预定的测试值特别是，还可以使用硬件端口或模型端口的图形表示。根据信号路径的第一配置，沿着信号路径传播与测试值相关联的测试信号，在模型端口或硬件端口的图形表示中分别显示传播的测试信号。
",G05B19/042,撤回
US20170168920A1,US15367216,"传送有效载荷数据从缓冲区到目的数据存储器被提供，使得这些数据可以在那里进行处理，由计算机辅助开发环境。为此，数据管理环境提供了缓冲和目的地存储。具有有效载荷数据的数据记录和语义数据彼此关联，带有有效载荷数据提供缓冲器，和一个数据对象与处理特定的对象语义的数据提供存储。对象实例化与有效载荷数据通过语义数据那个有效载荷数据放置在数据对象作为该对象的功能语义数据对象以开发环境可以基于对象语义的有效载荷数据。
",G06F11/36,申请终止
EP2645200B1,EP12162111.4,"方法涉及提供用于系统时间的CPU计数器(100)和CPU (110)中的同步计数器，其中同步计数器由CPU的时钟信号驱动。 CPU计数器被读取(120)以由实时应用提供系统时间。在应用中查询(130)同步计数器。当同步计数器输出对应于从CPU计数器与系统时间的上一次同步起的超过预定时间段的值时，CPU计数器与应用中的系统时间同步(150) 。对于以下内容还包括独立权利要求：(1)数据处理系统(2)包括用于执行用于在实时系统中提供时间戳的方法的指令集的计算机程序产品(3)包括用于执行用于在实时系统中提供时间戳的方法的指令集的数字存储介质。
",G06F9/48,授权
CN103475552B,CN201310122307.4,"本发明阐述并描写了一种用于干扰控制装置的总线通信的方法，其中，总线通信包括至少一个不依赖于总线硬件的第一通信层以及至少一个依赖于总线硬件的第二通信层，第一通信层将至少一条信息编码为第一协议数据单元并将其传输到第二通信层，和/或第一通信层从第二通信层得到第一协议数据单元并从第一协议数据单元解码第一条信息，并且，第二通信层由第一协议数据单元或者由一个从第一协议数据单元推导出的另一个协议数据单元产生一条依赖于总线硬件的总线信息用于通过总线传输，和/或第二通信层至少由一条依赖于总线硬件的总线信息产生第一协议数据单元或者产生能够推导出第一协议数据单元的另一个协议数据单元。
",H04L12/40,授权
CN102132528B,CN200880130860.X,"介绍展示了通信系统，具有至少两个外围装置(2a，2b，2c，2d)，每个外围装置(2a，2b，2c，2d)有至少一个I/O接口(3a，3b，3c，3d)，外围装置(2a，2b，2c，2d)经至少一个数据总线(4)相互连接且通过通信关系(KV)经数据总线(4)交换数据，本发明的任务是提供一种通信系统，利用其能至少部分解决现有技术中的问题。该任务根据本发明通过以下方式实现：设置具有外围装置接口(6a，6b，6c)和数据总线接口(7a，7b，7c)的至少一个接口装置(5a，5b，5c)，接口装置(5a，5b，5c)经其外围装置接口(6a，6b，6c)与外围装置(2a，2b，2c)之一经该外围装置的I/O接口(3a，3b，3c)相连且接口装置(5a，5b，5c)经其数据总线接口(7a，7b，7c)与数据总线(4)相连且通信关系(KV)能预给定给接口装置(5a，5b，5c)。
",H04L12/40,未缴年费
US20170126232A1,US15334659,"一种用于可编程逻辑器件的访问信号具有功能层，和一个配置电平在运行时当可编程逻辑器件执行预定配置。接入至少一个信号用于请求比特的数目。单独配置每个都位于地址单元具有一个地址偏移每件，以使得一个或多个信号值的比特位于一个地址单元。逐位访问请求的信号值的发生，其存取的单独位排序的函数可应用的咬入地址单元包含这样一种方式，即可访问所有比特依次位于地址单元进行起的偏移量，独立于信号包含可用比特。
",H03K19/177,授权
EP3142032A1,EP15184169.9,"在一个方法，一个可编程的配置更改的配置，可编程的logikbausteins logikbausteins读取，使logikbeschreibung放置的网表，特别是一种配置，至少部分的存在。从logikbeschreibung的配置一个或多个可编程的logikbausteins logikelemente和\/或紧固件和一个取代或改造一个zielkonfiguration logikbeschreibung会产生哪些不是一个或多个额外的logikelemente）而不是一个或多个元素的配置，在zielkonfiguration缺乏。采用常规的滴点测定zuordnungsinformationen，因此在目标和现有的logikelemente寄存器配置，特别是，和\/或在zielkonfiguration verschaltungselemente，各种改进建议，其中，至少在未使用的配置，可编程logikbausteins logikelemente作为自由标记，其中附加的zielkonfiguration logikelemente作为自由标记放置在logikelementen和闲置verbindungselemente与交货点的连接。从对一个由logikbeschreibung zielkonfiguration bitstrom创建和修改后的bitstrom将在可编程logikbaustein写的。
",G06F17/50,授权
CN103116488B,CN201210281762.4,"本发明涉及用于在影响装置中处理数据的方法，影响装置与汽车控制装置和与数据处理装置连接。如果影响装置收到来自汽车控制装置、数据处理装置或影响装置的第一触发，则检测第一触发与函数的有效对应。当存在有效对应时开始所对应的函数。函数具有第一地址。借助第一地址从影响装置和/或汽车控制装置的存储器读出数值。函数还具有第一子函数和/或第二子函数。检测第一地址和/或该数值与第一子函数或第二子函数的有效对应。当存在有效对应时调用所对应的子函数。根据所调用的子函数检查和/或操作该数值并且根据检测的结果将经检测的数值和/或经操作的数值由影响装置发送到汽车控制装置和/或数据处理装置和/或存储到影响装置的存储器。
",G06F9/44,授权
CN102467091B,CN201110043394.5,"一种电池模拟设备(5)，具有控制单元(7)和用于根据由控制单元(7)经电隔离的接口(14)预给定的额定值在电池控制装置(3)的端子(10d)上模拟电池单元电压的至少一个模拟通道(8)，其中模拟通道(8)具有电压源(15)、放大器单元(13)、用于连接模拟通道(8)的连接线路(11a，11b，11c)、及测量线路(9a，9b)和用于对故障状态、尤其是对电缆断裂进行仿真的装置(12)。
",G05B17/02,授权
US20170045418A1,US15234403,"一种计算期望轨迹提供一种车辆。车辆位于道路上的位置为边界的两条道路边缘，其中该边缘至少在已知道路周围的区的位置。弹簧质量模型引入，其中弹簧质量模型用于计算期望的轨迹，其中该位置点的质量计算弹簧质量模型的停止状态，并且所计算的位置点质量块用作数据点计算曲线连接尖轨的质量，从而该曲线代表轨迹。
",G01M17/06,授权
EP3130970A1,EP15180733.6,"提出了一种用于使用控制装置的已经存在的基本测试模型(100)将测试装置(2)的输入/输出接口(4)连接到测试器中的技术系统的模型(8)的方法。在这种情况下，接口被设计为连接控制装置的装置特定实现(200)或者连接待控制的技术系统，并且模型是技术系统的测试模型(8)或者控制装置的测试模型。该方法包括以下步骤：访问基本测试模型，其中基本测试模型被配置用于纯计算机基的基本测试；基于通信要求从基本测试模型中提取通信要求；限定适于通信请求的物理信号传输的特性；以及配置测试器的接口与模型之间的连接(6)，使得能够根据特性通过接口进行物理信号传输。
",G05B19/042,撤回
JP2017021026A,JP2016135715,"[目的]至障碍物的距离和障碍物的速度求出雷达装置的试验台的提供。解决手段是，光线后至仿真装置具有至少一个雷达天线、周围模型具有计算机单元。周围模型雷达装置相对的相对位置和相对速度至少一个障碍物的数据( x，v )、射线后至仿真装置，雷达设备中接收到的周围变为脉调制雷达信号模型、基于预先设定的相对位置和相对速度来适当的反射雷达信号的雷达设备的方向送出雷达装置是预先设定的相对位置和相对速度，具有检测障碍物。射线后至仿真装置雷达装置的前方的规定的角度范围中扩展的情况该角度范围内具有相对位置和相对速度，所述障碍物，在相互不同的角度来含有。图1中
",G01S7/40,撤回
US20170010346A1,US15204305,"一种用于测试距离雷达仪器以确定障碍物的距离和速度的测试台，包括雷达模拟设备，所述雷达模拟设备包括至少一个雷达天线和具有周围环境模型的计算机单元，其中所述周围环境模型包括来自所述距离雷达仪器的具有相对位置和相对速度的至少一个障碍物的数据(x，v)，其中在接收到来自所述距离雷达仪器的扫描雷达信号之后，所述雷达模拟设备至少部分地在所述距离雷达仪器的方向上基于由所述周围环境模型预先确定的所述相对位置和所述相对速度来发射合适的反射雷达信号，使得所述距离雷达仪器以预先确定的相对位置和相对速度来检测障碍物，其中所述雷达模拟设备在所述距离雷达仪器前方的角度范围上延伸，使得可以在所述角度范围中以相互可区分的角度来模拟具有相对位置和相对速度的所述障碍物。
",G01S7/40,申请终止
US20170010887A1,US14794331,"一种计算机实现的方法，用于编辑数据对象变量被描述和至少一个软件工具，由此数据对象变体至少具有一个共同的软件/硬件属性和各构型的属性。可以针对改变构型的硬件属性不同的数据对象变形以改变配组，而且因此在编辑数据对象变量，因为至少一个属性匹配构造属性在不同的数据对象变体被捕获和那个属性信息配组的数据对象被存储在变体具有匹配构造属性。
",G06F9/44,未缴年费
EP3115804A1,EP16178215.6,"一种用于测试距离雷达装置以确定障碍物的距离和速度的测试台，包括：雷达模拟装置，具有至少一个雷达天线；以及计算机单元，具有环境模型，环境模型包括具有距离雷达装置的相对位置和速度的至少一个障碍物的数据(x，v) 。在从距离雷达装置接收到A味雷达信号之后，至少部分地在距离雷达装置的方向上基于由环境模型相对位置和速度所预定的来发射适当的反射雷达信号，使得距离雷达检测具有预定的相对位置和速度的障碍物，其特征在于，雷达模拟装置在距离雷达装置前方的角度范围上延伸，使得可以在该角度范围中以可区别的角度来模拟具有相对位置和GescheWi的障碍物，并且雷达模拟装置包括多个固定的雷达天线，其分布在角度范围上。
",G01S7/40,撤回
CN102955875B,CN201210281759.2,"根据本发明的主题提出一种用于在影响装置中处理数据的方法，其中影响装置与汽车控制装置并与数据处理装置连接。汽车控制装置和影响装置设置在汽车中或例如被设置在实验室的测试台中。第一程序在汽车控制装置上运行。根据XCP协议或CCP协议在数据处理装置与影响装置之间交换数据。根据本发明，影响装置具有第二执行单元，其中第二执行单元比第一执行单元更快地执行预定的数据步骤。按照根据本发明的方法，按照预定的标准检验借助于XCP协议或CCP协议交换的数据，并且根据检验结果或者在第一执行单元中或者在第二执行单元中执行数据处理。
",G06F17/50,授权
EP3104278A1,EP15171801.2,"本发明涉及一种用于确定实时系统中的控制单元的控制程序的功能所需的运行时间的过程和设备，所述实时系统是在循环处理器（PIL）仿真中的目标处理器上执行的，在创建控制单元的图形开发模型或将其加载到开发环境中的过程中，图形开发模型通过图形开发模型中的功能块来显示控制单元的功能；在图形开发模型中至少选择一个功能块，特别是通过图形用户界面选择功能块，根据图形开发模型自动生成控制程序在目标处理器上运行，控制程序包含该功能，它表示所选功能块的功能。此外，第一运行点与控制程序中的函数的开始相关联，第二运行点与函数的结束相关联，在控制程序中，在函数的第一运行点之前，所使用的目标处理器的高速缓存处于预定状态，并且在目标处理器上的控制程序的第一和第二运行点处测量运行值，从中确定到期日。
",G06F11/30,撤回
EP3070553A1,EP15159845.5,"描述并描绘了用于计算机辅助生成用于用电子计算单元（5）控制控制系统（4）的可执行控制程序（3）的计算机实现的过程（1）。如果控制程序（3）的功能至少部分地在图形模型（6）中描述，并且图形模型（6，R）包括至少一个子模型（7，V）和至少一个子功能，则图形模型（6，R）首先被翻译成更高级编程语言中的模型代码（8）；模型代码（8）随后被编译成可在控制系统（3）上执行的控制程序（4）。
通过在高级编程语言中将子模型（7，V）转换为子模型代码函数（V），在高级编程语言中将模型（6，R）转换为综合模型代码（8），改进了图形模型中子模型函数的处理，以及子模型代码函数（v）通过指针从横向模型代码（8）调用到子模型代码函数（v）。
",G05B19/042,授权
JP2016142270A,JP2016019028,"要解决的问题：提供计算并输出方法[1]控制脉冲[2]控制装置[3]通过计算机程序实现，并提供该控制装置包括第一核算单位[4]及第二核算单位[5]使控制脉冲以被输出，来自控制装置的内燃机。
SOLUTION：计算控制脉冲的方式执行一第一计算单元控制脉冲[S] [sr图案包括触发信息，sf]相对于多个未来控制脉冲以第一采样率的关于前一状态数据[ZE，old]内燃机，并将其中的第二单元，该第二核算单位校正触发信息将目前控制脉冲输出的第二采样速率的数据根据现时状态[ZE，new]的内燃机，并控制从控制装置到内燃机根据修正的触发信息[sr '，平方英尺']，然后优化措施。
所选附图中：图2
COPYRIGHT：( C ) 2016、JPO&INPIT
",F02D45/00,授权
US20160210380A1,US14695431,"一种计算机实现的方法，用于自动生成至少一个块表示驱动器用于基于块的建模环境，其中,所述驱动器功能起控制硬件元件的目标硬件装置的方法，包括制备描述驾驶员功能的形式语言，读入形式语言描述的评价驾驶员的功能，并产生代表该块的驱动器，该驱动器用于建模功能的建模环境的框图。
",G06F17/50,申请终止
US20160203244A1,US14593709,"基于计算机的系统和方法用于指定至少一个信号的至少一个符号的程序将I/O功能的目标硬件装置提供。建模工具具有一个符号的程序与信号是房号未定的。信号被分配的符号的程序与至少一个I/O功能性目标硬件单元的规定配置工具。使用该建模工具，I/O功能性目标硬件装置分配了该符号的程序到信号是房号未定的。一种信号指定信息项产生建模工具由此分配。该信号被从分配信息条款建模工具对配置工具，与配置工具接管分配给I/O功能性目标硬件装置的信号被指配的符号的程序根据信号指定信息项。
",G06F17/50,申请终止
EP2942851B1,EP14167629.6,"一种用于监测具有电容负载的电力消耗装置的功耗的方法，所述可控电路元件和所述装置串联。检测流经所述装置的电流的幅值、所述装置上的电压降、以及所述装置上的电压降的随时间变化。根据所述装置上的电压降和预定功率来计算允许的工作电流幅值。根据所述装置上的电压降的随时间变化来计算所述电容负载的充电电流幅值。计算允许的瞬时电流幅值。将所述允许的瞬时电流幅值与流经所述装置的电流的幅值进行比较，并且如果流经所述装置的电流的幅值大于所述允许的瞬时电流幅值，则增大所述电路元件的电阻。
",H02H9/02,授权
US09360853B2,US13621773,"提供了一种用于创建电子控制单元软件的计算机项目管理系统和方法。该系统具有配置成设计用于机动车辆的电子控制单元的图形模型的软件架构工具。行为模型工具将图形模型转换成计算机可读生产代码。软件容器具有基于电子控制单元生成的文件。容器管理器在向软件架构工具或行为模型工具输入或输出软件容器期间将软件容器与之前的软件容器进行比较，并且生成指示是否对电子控制单元进行了接口修改的比较列表。然后在显示屏幕上向用户显示比较列表。
",G06F9/44,授权
US09342672B2,US14167200,"一种计算机实现的方法，用于管理至少一个数据元素控制单元发展，该方法允许统一数据元件通过整个开发过程，通过提供管理单元具有用户界面，相关的数据元素与管理单元，并将访问配置与管理单元。该访问构造限定数据元件可由用户经由用户界面。
",G06F21/62,授权
EP3015995A1,EP15193577.2,"一个计算机系统的方法来配置一个接口单元包括第一处理器和第二处理器上沉积的一个接口单元，其中一个连接之间的第一个处理器和第二处理器和接口设置一个单位和至少一个eingangsdatenkanal ausgangsdatenkanal）。一个计算机系统的外围设备设置为输入数据，输入数据eingangsdatenkanal存放在从第二ausgangsdatenkanal处理器读取和设置为输入数据从数据读取和在从eingangsdatenkanalgangsdatenkanal存款。一个序列的第二个prozessorbefehlen处理器编写的，编写了大量的subsequenzen，最初的一个例行的subsequenz读取和处理至少在第一和第二eingangsdatenkanal沉积数据的分析和处理subsequenz一例行的至少第二个数据或eingangsdatenkanal沉积一个常规的处理通过第一个处理器生成的数据和存放。处理后的数据在第一ausgangsdatenkanal R是通过拼接序列和序列的subsequenzen创建在第二个处理器加载。
",G06F15/78,授权
CN105556402A,CN201480051803.8,"本发明描述一种用于通过第二函数影响电子控制设备的控制程序的第一函数的方法。所述控制程序通过处理器的第一计算核心执行，而所述第二函数在控制程序的执行期间通过第二计算核心执行。所述第一函数给变量配属第一值并且将所述第一值在第一时刻写入到所述变量的存储器地址中。所述第二函数给变量配属第二值，所述第二值在第二时刻写入到所述变量的存储器地址中，其中，覆写由所述第一函数写入的第二值。在第三时刻，所述控制程序从所述变量的存储器地址读取所述第二值。管理实例这样在时间上彼此协调对所述变量的存储器地址的访问，使得所述第一时刻位于所述第二时刻之前并且所述第二时刻位于所述第三时刻之前。
",G05B19/042,申请终止
EP2869145B1,EP13190584.6,"一种用于影响控制程序的方法，该控制程序具有多个第一功能并且第一功能中的至少一个被配置用于控制执行器，并且提供存储器并且该存储器具有由分配给第一功能的子程序所占据的存储器区域，由此当调用第一功能中的一个时，在控制程序的程序代码中存在分支地址，该分支地址指向与功能调用相关联的子程序的存储器地址。针对功能调用的出现分析控制程序，并且确定与功能调用相关联的分支地址和返回命令的地址。选择要删除的第一功能中的一个。用第二功能代替第一功能，其中所选择的第一功能的程序代码被第二功能的程序代码覆写。
",G05B19/05,授权
EP2881858B1,EP13196209.4,"本发明涉及一种用于改变电子控制单元的存储器中的软件的方法。在电子控制单元的工作存储器中存储旁路例程，并且在表中存储旁路函数的地址。服务函数从表中读取地址并且调用旁路例程。在电子控制单元的运行时间，通过擦除表条目可以替换旁路例程。服务函数的调用通过覆盖存储器、存储器管理单元或借助于手表集成到电子控制单元的程序代码中。
",G06F9/445,授权
EP3001318A1,EP14186124.5,"本发明涉及一种自动测定方法modellsignalen FPGA的FPGA方案的生成（4）和一个FPGA回读（8）从提取的步骤，产生一个综合的FPGA模型（2），并生成一个代码（3）FPGA的FPGA模型（2）但过程结束之前的步骤，生成的代码（3）FPGA的FPGA模型（2）额外的步骤，自动检测的analysierens从FPGA（8）包括一个auslesbaren读回信号的步骤和程序，ausgebens金的FPGA（8）包括一个auslesbaren读回信号。本发明还涉及一种datenverarbeitungseinrichtung（1）执行上述程序。
",G06F11/36,驳回
EP3001313A1,EP14185873.8,"方法执行一个应用程序的第一个开口的第一个计算机上使用的应用程序，其中第一个功能来控制执行器和传感器的功能和\/或处理和\/或提供的致动器和\/或数据传感器执行其中第一控制装置控制设备的硬件的一个具有至少一个第一类型的第一rechenkern rechenkern和计算机硬件，计算机（1）和至少一个第二和一个第二rechenkern rechenkern类型，其中第一类型和rechenkern至少在第二rechenkern型使用的指令集识别一个操作系统的一个源代码控制设备和控制设备的操作系统，其中第一个通过一个接口控制单元之间的硬件和应用程序的第一个开口的第一个训练，应用程序的源代码是可用的，源代码和源代码控制设备操作系统的可执行应用程序的第一，第二rechenkern型编译的编译通过，其中一第一操作系统的虚拟设备（6）和（8第一虚拟应用程序计算机生成的），并显示一个仿真环境（7）同时通过仿真环境simulationsumgebungsschnittstelle（9）的一个移交的日期和\/或一个虚拟应用程序事件的第一（8）和\/或操作系统的虚拟设备（6）提供的，和一个计算机操作系统（5））通过这一之间的第二界面的计算机操作系统的计算机硬件和软件仿真环境（1）（7）培训和管理程序（2），其中一个第一个虚拟机管理程序的计算机提供的（4）通过第一个虚拟机（4）计算机硬件（1）部分或完全的形式提供虚拟硬件的虚拟硬件，包括至少一个第一虚拟rechenkern虚拟操作系统，和控制设备（6）在第一个虚拟机执行，（4）仿真环境（7）一个虚拟应用程序执行的第一个控制设备（8）内的操作系统（6）在第一个虚拟机（4）的simulationsumgebungsschnittstelle发起和控制（9）其中税款移交数据和\/或事件在虚拟应用程序（8）和\/或虚拟设备的操作系统（6）包括第一虚拟应用程序，通过控制设备（8）（6）虚拟操作系统直接访问虚拟的硬件提供的第一个虚拟机（4）提供。
",G06F9/455,撤回
EP2925025B1,EP14162150.8,"一种用于在具有无线电适配器的目标硬件上实现V2X应用程序的计算机实施的方法，其中借助于图形建模环境以框图的形式对V2X应用程序建模，并且将框图编译成能够在目标硬件上执行的V2X程序，并且将V2X程序传输到目标硬件上并且在那里执行。用于实现V2X应用程序的方法以尤其简单和有利的方式实现，其方式是使用具有至少一个无线电适配器接口的V2X通信块来创建框图，借助于所述无线电适配器接口在无线电适配器和V2X通信块之间交换数据。
",H04W4/00,授权
EP2990892A1,EP14183024.0,"一个程序，一个连接的输入\/输出接口（4），一个控制面板提供用于测试一个testgeräts（2）与一个在测试设备（2）提供的现有技术系统的模型，的输入\/输出接口连接器的测试或控制面板（20）连接到一个控制系统和技术训练与输入\/输出接口（4）连接到一个模型的模型（8）从屏幕或一个技术系统这个模型的控制面板，其中测试装置（2）仍然是与模型相关的输入\/输出功能（6））。这提供了一个接口的方法是提供一个hierarchiestruktur hierarchiestruktur以及功能。一个自动配置程序继续指出兼容接口之间的联系和功能上的hierarchiestruktur hierarchiestruktur，这样在测试装置（2）对现有的模型，至少部分兼容与碲的化合物控制单元（20）和stenden该系统可以从屏幕的技术交流。
",G05B17/02,授权
EP2990941A1,EP15172489.5,"描述并描绘了用于生成控制装置程序代码（2）的计算机实现的过程（1）。其中，在使用至少一个第一软件工具（4）从至少一个第一数据对象（3）产生控制装置程序代码（2）的过程中生成控制装置程序代码（2）或中间表示，其中，第一软件工具（4）产生用于产生控制装置程序代码（2）或中间表示的至少一条消息（M1、Mn、Mv），并且计算机实现的消息管理环境（5）记录由软件工具（M1、Mn、Mv）发出的消息。
通过从消息管理环境（5）到记录的消息（M1、Mn、Mv）的限定（q1、qn），对软件工具在生产过程中发出的消息的评估更加有效，qv）被确认为至少“开放”或“签署”，并且资格要求（p1、pn、pv）也被记录为来自报告管理环境（5）的“签署”消息（M1、Mn、Mv）。
",G06F9/44,授权
EP2977905A1,EP15177297.7,"本发明的目的是防止第一可编程硬件组件（1）的配置的布置，使用第一可编程硬件组件（1），第二可编程硬件组件（2）和开关元件（3），其中，第一可编程硬件组件（1）是设计用于配置第一可编程硬件组件（1）的逻辑（4）的配置接口（5），数据接口（7），设计用于将逻辑（4）与第二可编程硬件组件（2）通信，调试接口（8），设计用于调试和配置逻辑（4）和配置监控接口（9），设计用于向逻辑（4）的配置过程发送信号，其中开关元件（3）连接到调试接口（8），并且设计为在配置过程中可中断对调试接口（8）的访问。
",G06F11/36,撤回
EP2978168A1,EP14177808.4,"一种用于模拟通过时间的方法，其中第一网络订户经由第一网络线路连接到网络网关，并且第二网络订户经由第二网络线路连接到网络网关，第一网络订户具有经由第一网络线路发送的寻址到第二网络订户的第一消息，其中网络网关经由第一网络线路接收第一消息，并且经由第二网络线路发送到第二网络订户，第二网络订户经由第二网络线路接收第一消息，借助于网络网关延迟第一消息，使得第一消息从第一网络订户到第二网络订户的第一通过时间基本上对应于第一消息通过分段网络的第二通过时间，其中分段网络至少包括第一分段耦合元件和第二分段耦合元件以及连接到第一分段耦合元件和第二分段耦合元件的第三网络线路。
",H04L12/40,撤回
EP2963541A1,EP14174904.4,"本发明涉及一种方法创建一个FPGA代码（7）基于FPGA的一个模型（10）至少有一个signalwert（16）为常数，建立全面的步骤，插入一个常数（16）与一个预定的signalwert在FPGA（10）模型schaltvariablen（22）设置在FPGA（10）模式之间切换正常模式和一个kalibriermodus为FPGA代码（7）生成的代码（7）对FPGA的FPGA（10）全面实施模型常数（16）在FPGA代码（7）在实施schaltvariable常数（16）（22）在一个正常模式的实现为常数（16）为固定值，在FPGA（7）包括编码的实现，和常数（16）的槽（22）在schaltvariable为实现这kalibriermodus更多的工作（16）常数比（24）signalwert FPGA代码（7）包括。本发明还涉及一个校准模型的方法在FPGA（10）。
",G06F9/44,授权
EP2960731A1,EP14174029.0,"方法执行一个中断的方案（1）一个电子控制装置（1），其中一个方案是第一个应用程序（11）（12），第一个变量在第一个程序（11）一个变量的第一个值在第一个内存（12）存储一个内存模块的第一或第一个变量的第一个值第一内存区（12）从内存读取，和第二个程序（21）与第二个变量在第二个程序（21），第一个变量的第一个值从第一个内存（12）存储或内存的第一个变量的第一个值第一内存区（12）从内存读取，以之间的第一和第二个verknü程序程序生产操作结果，其中一个图形（201）是明显的，modellierungsumgebungmodellierungsumgebung（201）和图形显示在图形modellelemente，modellierungsumgebung（201）（11）通过的第一个程序的第一modellelement（202）的代表，和第二个程序（21）通过一个第二modellelement（203）的代表，之间的联系，并在第一程序和第二程序由第三modellelement（205）是其特征在于通过一个haltebedingung modellierungsumgebung（201）指定（2），其中通过一个选择（2）haltebedingung整个方案的执行：（1）领导（3）是固定的，programmzustandsänderung（1）整个计划被执行在执行一个programmzustandsänderung整体方案（1）（3）检测和programmzustandsänderung（3）被认为是作为一个执行的开始和\/或结束的第一和\/或第二方案（21）和\/或一个读和\/或写访问的第一值的第一个内存区和\/或修改的第一个值的第一个内存区，整个计划的执行被中断，（1）如果在指定的programmzustandsänderung haltebedingung（2）（3）发生。
",G05B19/042,撤回
EP2950175A1,EP14169948.8,"本发明涉及一种方法和一种装置，用于测试一个开口（8），其中在一个真正的或模拟的控制单元（8）通过网络连接（7）的传感器的数据传输，通过一个数据处理设备（1）是要通过模拟计算恩，其中，模拟传感器的数据至少部分至少有一个图形处理器，至少有一个grafikprozessoreinheit（2）数据处理设备（1）进行的模拟传感器的数据和图像数据编码中的一个visualisierungsschnittstelle（9）快递在一个egeben datenwandlereinheit（6），特别是一个在visualisierungsschnittstelle（9）连接，通过模拟visualisierungseinheit datenwandlereinheit（6）接收的图像数据转换成paketdaten paketbasiert含有的传感器，特别是根据TCP \/ IP或UDP \/ IP协议通过网络连接到控制单元（7）（8）转让。本发明还涉及一种datenwandlereinheit（6）全面empfangsschnittstelle（6A）连接到一个指定的visualisierungsschnittstelle grafikprozessoreinheit（2）（9）和一个数据处理设备（1）sendeschnittstelle（6B），以输出一个数据包在基于分组的网络协议。（6）建立，datenwandlereinheit在图像数据编码的用户数据，empfangsschnittstelle（6a）的接收，转换数据的网络数据包中含有的versendbar sendeschnittstelle（6B）。
",G05B23/02,授权
EP2940857A1,**********.0,"本发明公开了一种用于用一负载仿真程序（1）模拟一三相无刷直流电动机的方法，其中对一个电动机控制器（4）的馈供终端（3）的通过其加载口（2）的负载仿真程序（1）三相连接和用于控制仿真程序动力电子设备（5）的负载仿真程序一仿真程序动力电子设备（5）和一台仿真程序控制器（6），其中仿真程序控制器（6）由发动机调节器（4）馈供终端（3）驱动的和无驱动供给口（3）确定和仿真程序动力电子设备（5）（通过仿真程序控制器6）被驱动那样在（通过电动机控制器4）馈供终端（3）（仿真程序控制器6）基于一电动机模型（8）驱动计算的相电流i 以一电动机模型（8）为基础仿真仿真器控制的流动和（通过发动机操纵4）无驱动馈送端（3）计算的相电压v 仿真被废弃。
可靠的交换被保证那检测对来自在从动状态S PH （j ）=0 中的非驱动状态S PH （j ）=1 的发动机操纵（4）的一馈送端j 的过渡如果微分电压v 困难（j）的量值在之间到馈供终端j 到电动机控制器（4）测得的输出电压V inv （j）和计算的相电压v 对于一预定的第一次周期t 1 仿真（j）大于一预定阈值电压值V th ，那在发动机控制的馈送端j 的状态转换的检测之后，其来自在从动状态S PH （j ）=0 中的非驱动状态S PH （j ）=1 ，仿真程序控制器（6），仿真程序动力电子设备（5）在这种通过现在通过仿真程序控制器（6）的发动机操纵（4）被驱动的馈送端j 计算的相电流i 仿真（j）流动
",H02P7/06,授权
JP2015191671A,JP2015065615,"要解决的问题：提供交互方法对控制程序的控制装置，能够扩展目前
SOLUTION：程序代码的控制程序( STP )检查，转移地址与函数调用包括程序代码的控制程序和变量与各第一功能( F1A，F1B、F1C、体量，确定相应的存储器地址F1N )基于各个变量。确定第一函数和变量分别对应于第一功能存储在第一分配表( ZUORD 1 )与相关联的存储器地址它们。基于比较在第一分配表并设定了第二分配表( ZUORD 2 )，至少一些函数名分配给多个第一功能，第一值的至少一个变量被第二值。
",G06F11/00,未缴年费
EP2933695A1,EP14165123.2,"描述并示出了一种用于利用模拟器(2)对控制单元(1)进行实时测试的计算机实现的方法，其中，模拟器(2)包括模拟器I/O接口(3)，其中，控制单元(1)是控制单元I和O接口(4)，并且其中，控制单元(1)和模拟器(2)经由它们的I/O接口(3、4)借助于至少一个数据通道(5)而互连，并且控制单元(1)经由数据通道(5)使转换器控制数据(6)并且借助于电负载模型(7)而在不考虑由功率转换器(8)引起的电流间隙(11)的情况下发送到模拟器(2)的转换器控制数据(6)来计算负载电流(ix)和负载电压(ux)，并且将负载状态变量的至少一部分发送到控制单元(1) 。通过在模拟器(2)上附加地执行控制技术的观察器(9)来以更高的精度进行实时测试，其中，观察器(9)在考虑转换器控制数据(6)的情况下并且利用观察器负载模型(10)至少检测来自所计算的负载电流(ix)的负载电流(ix)，观察器(9)检测负载电流(ix)的过零和由此引起的电流间隙(11)，并且在检测到电流间隙(11)时观察器(9)检测电补偿变量(11) 。以如下方式计算u comp)：使得当负载模型(7)中的电负载附加地负载有补偿变量(u comp)时，利用负载模型(7)以对于现有电流间隙(11)减小的误差来计算负载电流(ix) 。
",G05B17/02,授权
EP2924522A1,EP14162207.6,"影响我们的遗产的一个开口的方法，其中一个控制程序，第一个多功能的第一功能和子程序）和至少一个相关的第一个功能控制系统中的执行器进行训练，这控制单元的第一内存来记录我们的遗产）作为一个二进制代码和控制程序，可用在代码中调用我们的遗产的一个功能是一个第一和一个sprungadresse sprungadresse内存地址与函数调用相关的子程序和显示子程序的程序代码作为一个序列的binärem vorliEGT和结束的代码序列到一个D rücksprungbefehl次级方案rücksprungbefehl rücksprungadresse EM被误读、次级方案的代码序列的第一部分包括一个内存地址和每个variablenzugriff variablenzugriffen是分配，和其中至少一个变量的值赋给一个将但在我们的遗产代码的发生函数调用与函数调用进行了研究和sprungadressen和连接各自的第一和rücksprungadressen与功能相关的变量名称与变量和与客户的在内存地址的变量和函数，来确定第一个D在各自的第一功能相关的变量相关的内存地址存储在第一zuordnungstabelle，一个比较第一和第二预先确定的zuordnungstabelle zuordnungstabelle具有至少一个第一部分和至少一个函数的函数名被分配到一个变量的第一个值的第二值取代。
",G05B19/042,授权
JP2015170367A,JP2015044745,"要解决的问题：提供一种生成可执行的控制程序从图形控制模型，使资源的控制系统可以被使用。
SOLUTION：图形控制模型转换为程序代码，其至少有一个FXP操作至少一个FLP操作。所生成的程序代码然后传递至可执行的控制程序。在执行所述控制程序，对控制系统的一部分，执行该控制程序的FXP单元和另一部分执行的控制程序的FLP单元。
",G05B19/042,授权
EP2916183A1,EP14158000.1,"computerimplementiertes程序（1）产生一个在一个控制系统（2）（3）包括一个可执行steuerungsprogramms图形控制（4）控制系统（2），其中一个prozessschnittstelle）和控制系统（2）是这样设置的，在prozessschnittstelle（5）的至少一个prozessgröße物理过程（6）控制系统（2）包括一个输出和\/或影响的物理过程（6）控制系统（2）可花，其中至少有一个电子控制系统（2）rechnereinheit（7）具有至少一个固定点的执行单元（FXP单元，8）和至少一个浮点执行单元（FLP）单元，9），其中一个图形控制（4）数量（1 modelloperationen0，11）的问题。
更好的利用控制系统（2），这表明，该图形模式（4）在翻译的代码（13）将生成的代码（13）至少有一个（12）和FXP手术至少一个FLP问题和操作（14）生成在可执行程序代码（13）（3）控制程序翻译成这样的steuerungsprogramms（3）在执行上的控制系统的一部分（2）（3）在steuerungsprogramms FXP单元（8）执行与另一部分（3）在steuerungsprogramms FLP单元（9）执行。
",G05B19/042,授权
CN102472775B,CN201080027582.2,"一种测量电气变量的设备，包括外部测量端子能与要测量的电子装置连接的外部测量端子(1)，所述设备包括转换器(3)，转换器(3)被设置为将要测量的不同变量类型的经由测量端子(1)测得或能测量的多个测量变量转换为单个预先指定或能预先指定的变量类型的相应电气测量变量，设置有用于控制转换器(3)的控制装置(2)，借助于控制能选择要利用转换器测量的变量类型中至少一个，转换器(3)对于至少两个要测量的变量类型分别具有至少一个独立的输入级(3a，3b)，要测量的变量类型的测量变量借助于输入级能被采集并且能被转换为预先指定或能预先指定的变量类型，预先指定或能预先指定的变量类型的转换后的测量变量出现在输入级(3a，3b)的信号输出端(5a，5b)上，其中针对至少两个不同的标准通过以下方式对在测量端子(1)上提供的电信号并行地进行检查：至少两个用于不同的要测量的变量类型的输入级(3a，3b)在时间上并行地工作，且同时分别在信号输出端(5a，5b)提供相同的预先指定或能预先指定的变量类型的各自转换后的测量变量。本发明还涉及该设备的运行方法。
",G01R15/12,授权
EP2899652A1,EP14152109.6,"本发明涉及用于优化用于电子车辆控制单元的可编程逻辑模块、具有软CPU和/或未使用剩余空间的可编程逻辑模块的使用的过程和系统。通过创建各种模型变量来说明控制单元的功能，并创建具有不同配置范围的各种软CPU配置，这些软CPU配置占据与可编程逻辑模块的配置范围相对应的区域，从而解决了该任务，在根据可编程逻辑模块上的软CPU配置修复软CPU之后，对各种型号变体和/或软CPU配置执行处理器在环仿真，并利用PIL仿真过程中产生的软CPU的轮廓数据对输入信号进行处理，以优化可编程逻辑模块的使用。
",G06F17/50,权利终止
EP2891527A1,EP14150184.1,"本发明涉及一种弯曲semiflex biegevorrichtung一印刷电路板（1）的至少一个flexbereich（4）和两个侧，在每一个starrbereich（23）加入有两个翅膀（25）和一对翅膀（25）安装在第一个固定的第一fixiervorrichtung（20）的第一starrbereichs（2）印刷电路板（1）和第二的航班第二fixiervorrichtung L（25）安装（21），第二个固定starrbereichs（3）印刷电路板（1）其中至少有两个翅膀（25），以获得一个轴的位置是verschwenkbar和至少一个fixiervorrichtungen（20，21）在各自的翅膀特别是从轴的距离的函数的至少一个schwenkwinkels verschwenkbaren翼（25）是可变的。本发明涉及一种弯曲的印刷电路板的方法继续使用这种装置。
",B21D5/04,授权
CN204405746U,CN201390000553.6,"本实用新型涉及一种用于测试电气部件的设备(1)，所述设备具有一个用于生成模拟信号的模拟装置(2)、多个测试装置(4)以及至少一个电气连接装置(5)，其中，所述模拟装置(2)和多个测试装置(4)通过所述至少一个连接装置(5)导电地连接或能连接，并且所述至少一个连接装置(5)具有至少一个电气开关装置(9)，所述开关装置这样设置，以便断开或接通在多个测试装置(4)之间的电气连接。
",G01R31/00,期限届满
EP2881814A1,EP13196219.3,"影响我们的遗产的一个开口的方法，在多个第一功能和驱动功能，其中至少有一个第一致动器控制系统的训练和大量的变量，并提供每个。一个变量的内存地址是分配给的R，其中的变量之间的第一个预定的配置和功能训练，一个处理器和控制单元具有多个算术问题，在第一rechenkern这与第一个控制程序执行的功能，其特征我们的遗产是在执行的第二rechenkern执行第二功能的第二功能，其中一个控制程序，至少部分在不同的programmkode）利用二次函数和一个指定的第一个变量的值功能改变及随后的修正值是从控制程序访问。
",G05B19/042,撤回
EP2876512A1,EP13194228.6,"方法用于自动连接的组件模型的一个技术系统的计算机上显示一个模式，其中第一和第二modellkomponente modellkomponente）。第一和第二modellkomponente modellkomponente hierarchieelement分别显示在至少一个，其中一个或一个hierarchieelement hierarchieelement hierarchieelement hierarchieelemente包含一个或多个端口或没有。hierarchieelement指出一个或多个端口E的其中一个端口，一个端口标识符和一个hierarchieelement特性。两个端口之间的连接提供了一个技术系统的映射。通过一个图形用户交互的数量和\/或hierarchieelementen第一端口和第二端口的数量和\/或hierarchieelementen选择一个端口，其中第一和第二港口第一数量的第二数量的分配，我们考虑一个潜在的D.一个可能的映射方法，给出了在第一端口和第二端口，以及相对应的等级上升的第一端口和第二端口的标识符hierarchieelemente的第一端口和第二端口的标准，每一个从端口到一个相同的层次水平vorgebbaren数量根据预定的规则是一致的或一致的评价。在有可能成为第一个端口分配第一数量自动与第二第二连接端口的数量。
",G05B17/02,驳回
EP2874060A1,EP14153375.2,"本发明涉及一种用于配置模型的发展的一个技术系统来显示波形，特别是在一个计算机系统有一个显示模式，其中至少有两个波形的显示和在一个系统和技术ngsdarstellung所有输入信号。和所有的处理单元的输出信号的形式在一个blockelementen映射图，它的特点是发展的训练后，选择一个任意的或无效的ausgangssignals模型表示在signalver运行选定的信号，以减少只有相关的处理单元的显示或强调。
",G06F9/44,撤回
JP2015088188A,JP2014218868,"要解决的问题：に允许控制程序，而不需要修改的源代码或描述控制程序，即使对于信息缺失的控制程序。
SOLUTION：响应于函数调用，程序代码控制程序检查来检测跳转目的地址涉及函数调用和地址的返回指令，存储器容量所占用的面积相应的程序代码，并检测标识符包括尺寸和地址的存储区分配给各个第一功能显示在显示单元上。气密娇小者第一函数中选择将被删除，且尺寸和地址的选择的第一功能存储在信息结构，并调用选择的第一功能是停用和/或选择的第一功能被第二个功能，与程序代码的选择的第一功能被重写该程序代码的第二功能。
",G06F9/44,授权
EP2866111A1,EP13190448.4,"本发明的目的是通过测试环境（2）测试控制装置（1）的装置，基于计算机的综合测试管理工具（5），其中，测试管理工具（5）被设计用于基于模型的开发和/或管理至少一个被设计为用于测试控制装置（1）的数据结构（3）的测试计划，并且测试计划（3）具有用于触发测试计划（3）的执行的至少一个测试（6）和一个启动条件（8），基于计算机的测试执行控制工具（4），其中，测试执行控制工具（4）设计用于在满足启动条件（8）和基于计算机的数据库（9）时，在测试环境（2）上启动测试计划（3）的执行，其中，数据库（9）被设计为将测试计划存储为数据结构（3），并通过测试管理工具（5）和测试执行控制工具（4）共享对测试计划（3）的同时访问。
",G05B23/02,授权
EP2851815A1,EP13184920.0,"显示和描述用于实时测试具有控制装置代码（EC）的虚拟控制装置（3）的至少一部分的测试装置（1）；其中，测试设备（1）具有至少一个具有第一组指令（IS1）的第一类型计算器（4）和至少一个用于实时模拟虚拟控制设备环境（3）的模拟环境（5），并且其中，模拟环境（5）和控制设备代码（EC）具有至少一个计算器（4类型已计算。
使用控制装置代码（EC）对虚拟控制装置（3）进行试验，它能够在具有第二指令集（IS2）的计算器（6）第二类型上运行，由此，计算器核心（6）第二类型的第二指令集（IS2）与计算器核心（4）第一类型的第一指令集（IS1）不同，第一类计算器（4）执行仿真器（7）以模拟第二类计算器核心（6），仿真器（7）执行控制设备代码（EC），仿真器（7）具有模拟环境接口（8），以与模拟环境（5）交换数据和/或事件。
",G06F17/50,驳回
JP2014220813A,JP2014095807,"要解决的问题：提供一种能够在FPGA控制系统可变数据传输发送器和接收器之间的侧具有期望的数据速率和等待时间与提供高数据速率和低，实现其成本低。
SOLUTION：最大寄存器9传输的配置每个FPGA应用8 。共享，固定寄存器宽度被配置用于所有寄存器9 。使能信号EN设置在发送方3的寄存器9传输超出最大数量的寄存器9发送，使能信号EN被从发送方到接收方侧3 4，并且寄存器9在使能信号EN已被设置了被从发送方到接收方的3 4 。
COPYRIGHT：( C ) 2015、JPO&INPIT
",H03K19/173,授权
EP2801915A1,EP13167208.1,"本发明涉及一种方法执行一个自适应接口（7）之间的至少一个（2）至少有一个FPGA的FPGA（8）和至少一个应用程序的I \/ O模块（5）连接到FPGA（2），（3）分别为相应的变速器（4）接受培训，其中至少有一个FPGA之间（2）和至少一个I \/ O模块（5），一个串行接口（6）综合训练的步骤，配置的最大数量的传输寄存器（9）每个FPGA的应用（8）一个共同的固定配置，registerbreite所有寄存器（9）设置一个使能信号（s）在变速器（3）和寄存器（9）的最大数量的传输寄存器（9），使传输的信号（s）从发送端到接收端（3）（4）和转移登记册（9），这是enable信号（S）是设置，从发送端到接收端（3）（4）。本发明还涉及一种基于FPGA的控制系统（1）至少有一个FPGA的FPGA（2）至少有一个应用程序（8）和至少一个I／O模块（5）连接到FPGA（2），（3）分别为相应的变速器（4）接受培训，其中至少有一个FPGA之间（2）和至少一个I \/ O模块（5），一个串行接口（6）进行训练，在FPGA的控制系统（1），以实现一个自适应接口（7）之间（2）和至少一个现场可编程门阵列（FPGA）至少有一个I／O模块（5）按照上面的方法进行训练。
",G06F13/42,授权
EP2801872A1,EP13166604.2,"描述的测试是一个测试的一部分，至少一个虚拟仿真环境的控制面板（2）（3）定义在一个模拟器，虚拟仿真环境的控制装置（2）和（3），其中至少有一个虚拟控制装置（2）的软件组件（4,5,6）与至少一个外部主机接口（7）包括在仿真环境（3）至少有一个主机接口（8）至少间接的数据交换与虚拟控制装置（2）包括。一个链接的anpassungsaufwand减少从而实现了一个虚拟设备针单元（9）的至少一个虚拟steuergeräteschnittstelle（10）是利用虚拟steuergeräteschnittstelle（10）至少有datenschnittste外（7）所有构件（4）的虚拟控制面板（2）连接。
一个测试之间的依赖关系，在虚拟仿真环境，控制和减少，从而实现了一个虚拟设备针单元（9）的至少一个虚拟steuergeräteschnittstelle（10）是利用虚拟steuergeräteschnittstelle（10）至少与外部主机接口（7）软件组件（4）连接的虚拟控制面板（2）IST控制单元的虚拟针单元（9）至少有一个simulationsumgebungsschnittstelle（11）通过simulationsumgebungsschnittstelle）和主机接口（8）与环境（3）连接到控制单元和虚拟单元（9）至少销一个虚拟设备的引脚（12），和物理接口有一个引脚对应的模拟真实的控制，同时通过虚拟设备的引脚（12）是一个虚拟的物理steuergerätesignal转让。
",G05B17/02,授权
EP2770434A1,EP13000867.5,"在由控制设备测试系统通过模型模拟的环境中，利用控制设备测试系统测试控制设备(1) 。针对部分硬件部件或全部因模拟而异的硬件部件，读出唯一地和数字地识别硬件部件(1，4，8)的部件信息。存储所读出的识别部件信息。通过测试系统的计算机(5.1，5.2)或与测试系统连接的控制计算机来进行比较。
",G06F11/22,授权
EP2765528A1,EP13154741.6,"将具有信号值的FPGA硬件配置(24)加载到FPGA (5)上。执行FPGA上的FPGA硬件配置。设置用于向FPGA传输的信号值。根据信号值确定回写数据，并且将其作为状态数据写入到FPGA的配置存储器(8) 。将状态数据从配置存储器传输到FPGA的功能级(6) 。包括独立的权利要求用于执行FPGA构建的方法。
",G06F17/50,授权
US20140215270A1,US14167154,"一种操纵存储器操作程序存储器的控制单元上的虚拟或真实电子控制单元( ECU )，例如用于车辆中，例如。操纵该存储操作的完成是通过存储器组件处理程序，通过这种一组操纵功能，提供从所述至少一个操纵功能被选择，以使得该功能，通过激活存储器组件处理程序，改变了存储器访问由控制单元根据所选择的处理函数程序执行期间的控制单元的程序。
",G06F11/26,授权
US20140214783A1,US14167019,"计算机实现的方法及控制单元的产品管理变体开发提供依据。一致的数据管理是由最初指定产品特征模型的变型，规范组件的至少一个域，并定义特征/组件依赖关系通过关联组件具有至少一产品特征，随后说明书所关心的至少一个产品变型通过选择产品特征，规范至少一个域所关注的，组件的自动识别，属于所关心的产品变体通过自动评估特征/组件相关性，以及自动化输出标识的组件。
",G06F17/30,申请终止
EP2759939A1,EP13152993.5,"方法涉及提供一组操纵功能，以及选择操纵功能。激活存储器操纵程序组件(100)，以便基于所选择的功能改变由控制单元程序发起的存储器访问，其中该组件形成离散的程序组件。该组件在定义的接口上与存储器操纵接口程序组件(30)、存储器操纵程序组件(7)和/或存储器操纵程序组件连接。通过代码生成来控制存储器操纵程序组件。
",G06F11/36,授权
EP2759964A1,EP13152999.2,"本发明涉及在变体模型中确定产品特征(1)并且在域(5)中确定组件(4) 。通过将所述组件与所述产品特征相关联来定义特征/组件依赖性(6) 。通过选择产品特征来确定感兴趣的产品变体(V)，并且确定感兴趣的域(PD) 。通过自动化地评估所述特征/组件依赖性来自动地识别属于所述感兴趣的产品变体的组件，并且自动地输出所识别的组件。
",G06Q10/06,驳回
JP2014123381A,JP2013272735,"要解决的问题：提供一种调节控制装置。
SOLUTION：在用于调整由调整单元控制装置具有第一存储器单元，另一存储器单元，和调试界面，调试界面有监控功能监控处理的程序代码由控制装置。信息写入由控制装置的另一存储器单元在第一时间点而且书写了由调试界面以第一时间点，报告调节装置当程序代码被处理。传输的信息从调试接口调节装置在第一时间点，触发点在时间上与处理例程在设置单元。第二值覆写第一存储器单元，通过调试界面，在第二时间点，从调整装置，通过该处理例程。读取第一存储器单元由控制装置在第三时间点。
COPYRIGHT：( C ) 2014、JPO&INPIT
",G05B19/042,授权
CN102099755B,CN200980127353.5,"这里通过以下方式可以对具有高功率转换的大负载进行仿真，即在桥对角线支路(12)中设置可控电压源(13)和该桥对角线支路中的有效电感(14)，实际电流(i<Sub>ist</Sub>)借助于作用于可控电压源(13)的电流控制单元(15)能调节为预给定的额定电流(i<Sub>soll</Sub>)的值。
",G05B17/02,未缴年费
CN203596509U,CN201320437740.2,"本实用新型涉及一种开关柜，具有底部元件和顶部元件以及正面和背面以及左侧面元件和右侧面元件，并且在所述正面上沿着各前壳体边缘分别构成有至少一个垂直的板条，并且板条沿着纵向延伸方向具有多个孔状的空隙；以及侧面元件沿垂直方向构成在底部元件和顶部元件之间并且与它们连接，所述开关柜具有内腔区域，其设置为用于接纳能够从正面插入的组件；以及所述组件在正面上分别具有至少一个侧面的凸缘，并且所述垂直的板条由所插入的组件的所述侧面的凸缘至少部分地遮盖，并且组件借助穿过所述凸缘的固定器件与所述垂直的板条连接；所述固定器件实施为螺钉，并且在螺钉的头部与所述凸缘之间构成有适配件，并且螺钉的头部偏心地定位在适配件中。
",H02B1/26,期限届满
EP2711794A1,EP12185821.1,"本方法包括在开发环境中读取设计模型的所有模型对象。如果第一数据可用，则从存储位置读取具有全局唯一键的第一数据。将该键分配给模型对象，使得全局唯一键在设计模型的编辑期间可用。如果第一数据在编辑期间可用，则生成新的全局唯一键，并且如果第一数据在编辑期间不可用，则保留该新的全局唯一键。将第一数据与全局唯一键一起保存，并且进一步保存设计模型的所有模型对象(15) 。
",G05B19/042,授权
EP2557506B1,EP11176910.5,"方法在一次触发器的存在期间涉及在一个存储区的中断记忆，测试具有触发器的一触发器类。在存储区的存储器在触发器类的存在期间被中断。在存储区的数据储存的（12）的值是读取自存储区。其他值横向到达，在来自一数据通信机的触发器在另一存储区被储存（13）之后。测试通过数据通信机执行（14）以检查是否出现另一触发器，其中数据通信机包括二数据检测单元。
",G06F13/42,授权
CN103633614A,CN201310365299.6,"本发明涉及电子保护装置、用于驱动电子保护装置的方法及其应用。电子保护装置用于保护至少一个电负载（LS），电负载能连接到保护装置上，该电子保护装置具有输入端子（IN）和输出端子，该保护装置具有热自恢复的保险丝元件，该保险丝元件设计和构建为，根据保险丝元件温度（T<Sub>PF</Sub>）传导或限制第一电流（I<Sub>1</Sub>），为了限制第一电流，设置限制装置（T1、WA），该限制装置包括：与保险丝元件串联的第一晶体管（T1），和影响第一晶体管的监控电路（WA），其设计和构建为，a）在第一电流（I<Sub>1</Sub>）达到或超过预定义的最大电流值（I<Sub>wh</Sub>）时使第一晶体管截止，以及b）在第一电流达到或低于预定义的接通电流值（I<Sub>wh</Sub>）时接通第一晶体管。
",H02H3/08,授权
EP2706421A1,EP12183797.5,"本方法涉及在图形模型(S-0)中描述控制程序(2)的功能。在子模型(S-1-S-3，S-11，S-12，S-111)中，图形模型在分级级别中被结构化。针对不同分级级别的两个嵌套子模型，规定图形模型转换选项的值。在计算机辅助地生成每个子模型的程序代码中，执行共享选项的值。不提供较低级别上的子模型的选项的值，而提供较高分级级别上的子模型的选项的值。
",G05B19/042,授权
EP2672660A1,EP12170769.9,"本方法包括操纵从协议数据单元中导出的协议数据单元(5)或附加协议数据单元(6) 。被操纵的协议数据单元和附加协议数据单元被提供给通信层用于处理，其中接收操纵层(9)操纵附加协议数据单元或从附加协议数据单元中导出的协议数据单元，并将获得的被操纵的协议数据单元或被操纵的附加协议数据单元提供给通信层用于处理。
",H04L12/26,授权
US20130326098A1,US13900008,"提供一种用于操纵一电子控制器装置的总线通信方法的方法，其中总线通信方法包括一条总线独立于硬件的第一层次通信层和一总线与硬件有关第二通信层。第一层次通信层在第一协议数据单位中编码至少一个信息并将其传输至第二通信层和/或第一层次通信层从第二通信层接收第一协议数据单位并从第一协议数据单位译码第一信息。第二通信层从第一协议数据单位生成总线与硬件有关公共汽车信息或从用于经由总线和/或第二通信层的传输的来源于第一协议数据单位的附加议定书数据单元生成第一协议数据单位或一附加议定书数据单元，从哪个第一协议数据单位可以被导出。
",G06F13/42,授权
US20130268919A1,US13911311,"检测内存泄漏产生的本发明涉及的一种方法用于由运行于计算机上的程序，其中在程序运行期间，每个分配存储区域有关的至少一个信息项所进行过的分配进入分配和检查列表是否在分配列表包括所存储的参考图案的信息的一个或更多个内存泄漏是典型的，并在找到所存储的参考图案运行的暂停，或存储器泄漏，因此检测与区域比赛中没有赢的希望的程序谁的处理导致中最近的条目分配列表。本发明还涉及一种计算机程序产品，当所述计算机程序进行了执行该方法的计算机程序。
",G06F11/36,公开
EP2642359A1,EP12160249.4,"系统(101)具有生成控制单元程序的电子计算机单元，以及显示单元(102)，例如计算机监视器，包括矩形显示区域(104)，以流程图(106)的形式图形显示程序的构建过程(105) 。显示单元的另一个矩形显示区域(140)图形显示程序的模型元素(141、143、146)，例如软件组件，其中模型元素被分配到构建过程的构建过程步骤(111-114)，其可以由开发系统执行。针对用于创建控制单元程序的方法，还包括独立权利要求。
",G05B19/042,撤回
EP2592504A1,EP12191528.4,"本方法涉及提供具有资源模型(50)的估计单元(30)，资源模型(50)具有多个描述硬件的参数(R1-Rn) 。基于描述硬件和/或程序代码表示的参数确定控制设备程序代码的存储空间要求的估计值和控制设备程序代码的运行时估计值。将存储空间要求的估计值和/或运行时估计值存储在存储区域中和/或在可执行模型(10，10 ')中在显示设备(60)上显示。将估计单元设计为代码生成器的部分程序。存储区域为RAM或ROM.
",G05B19/042,权利终止
CN102016805B,CN200980116525.9,"本发明涉及校正至少一个信源(1)传送给至少一个信宿(2)的至少一个数字信息的方法，信源借助数据传输媒介(4)与信宿、也与校正设备(3)连接，信源为信宿提供的信息包括第一变量名，具有以下步骤：a)在校正设备(3)的第二存储区(32)中提供列出的变量名，b)信源发送包含第一变量名的信息，c)从传输的信息中提取第一变量名并存储在第一存储区(31)中，d)比较第一变量名与列出的一个变量名并基于该比较确定决定标准(EK)，e)基于决定标准(EK)决定第一变量名是保持不变还是在步骤c)中用列出的这个变量名替换，或者是否利用列出的另一变量名并借助确定另一决定标准而重复步骤d)和e)。本发明还涉及校正设备。
",G06F11/00,授权
EP2579115A1,EP11184086.4,"本发明涉及将具有计算单元(3)和模拟器-输入/输出(I/O)接口(4)的模拟器(2)与具有控制器-I/O接口(5)的控制单元(1)连接。在控制器和模拟器之间形成数据通道(6)，模拟器通过该数据通道接收电动机控制数据。基于电动机控制数据的接收，通过模拟器计算单元的全电动机模型(7)计算发动机状态参数和发动机状态变量。在模拟器电动机模型(8)中扫描计算的电动机结果。为模拟器包括独立权利要求。
",G05B17/02,授权
US20130073063A1,US13621773,"一台计算机项目管理系统和方法创建电子控制单元软件被提供。该系统具有一软件架构工具被配置成设计图形模型的电子控制单元的机动车辆。行为模型工具转换图形模型生成到计算机可读取代码。软件容器具有一文件产生基于电子控制单元。容器管理器比较软件容器与现有软件容器在进口或出口的容器的软件或工具或工具软件体系结构行为模型，比较列表产生表明是否界面修改用于电子控制单元是制造。比较列表然后被显示给用户的显示屏上。
",G05B19/042,授权
EP2557462A1,EP11176934.5,"本发明涉及基于确定的地址从影响装置(10)的存储器和从机动车控制装置(11)的存储器(13)中获得值。检查地址或所选值与子函数的关联。基于有效关联的存在，调用所述确定的子函数。基于所调用的子函数，检查或操纵所选值。基于所检查的结果，将所述值传输给机动车控制装置或数据处理装置(12) 。
",G05B19/042,授权
EP2557463A1,EP11176936.0,"方法涉及根据一份扩展校准协议（XCP）或一控制器区域网络校准协议（CCP）在一数据处理设备（14）和一影响的装置（11）之间交换数据，其中影响的装置与一机动车操控装置（12）连接。数据基于一预设标准被检查。在以一考试结果为基础的第一及第二执行单元（17 ，18）之一中，执行数据处理，其中控制和影响的装置被设置在一辆机动车或一测试台上。第一执行部件是一处理器单元，第二执行部件是一现场可编程门阵列单元。
",G05B19/042,驳回
EP2530584A1,EP11004526.7,"装置具有用于在库字段中显示图形化的库功能元件(5)的显示装置。通过放置功能元件的实例在配置字段中产生测试序列。功能元件设有功能占位符(8) 。在实例中为功能占位符提供实例功能。实例的参考保留在功能元件上。在配置字段中产生的测试序列或部分测试序列被定义为功能元件。
",G06F9/44,驳回
EP2302516B1,EP10015461.6,"一种方法包括将单个任务的计算时段的信息存储在数据处理单元中。以根据所存储的信息的计算时段开始任务。以任务的开始开始来开始计时器，其中任务管理器根据循环持续时间和计时器的时间值的差来终止所开始的任务。
",G06F9/48,授权
CN101441473B,CN200810175669.9,"本发明涉及一种测试至少一个电子控制系统(1)的测试设备(2)和用于操作测试设备的相应方法，其中具有一个可编址的物理存储器，测试设备(2)适合且被设置为通过数据通道与待测试的控制系统(1)建立连接。测试设备还适合且被设置为计算至少一个环境模型(21)和执行至少一个测试模型(22)。其中环境模型(21)可以通过环境模型变量来描述，环境模型变量按固定的物理地址存储在存储器的存储位置处。测试设备(2)还包括一个对应单元(23)，在该单元中以可读取的方式存储了所有或部分环境模型变量与存储器的相应物理地址之间的对应关系。
",G05B23/02,未缴年费
CN101278242B,CN200680036597.9,"本发明涉及一种用于对控制器、尤其是汽车控制器的端子上至少一个电气/电子负载、尤其是电感负载的效应进行仿真的方法，其中通过以下方式实际地模拟理论上流经至少一个端子(3，4，5)上被仿真负载的电流，即借助于与该至少一个端子(3，4，5)连接的可控的电流单元(11)对控制器(1)抽取或馈入电流。本发明还涉及一种用于对控制器、尤其是汽车控制器上至少一个电气/电子负载、尤其是电感负载的效应进行仿真的装置，该装置可连接到控制器(1)，并且至少通过以下部件模拟流经控制器(1)的至少一个端子(3，4，5)上的被仿真负载的电流：计算单元，用于尤其根据在被仿真负载上降低的总电压或其大小计算和/或提供表示负载中电流的控制量；和电流单元(11)，具有至少一个用于构成电流源和/或电流宿的辅助电压源(15A，15B)，其中控制量可从计算单元传输到电流单元，并且电流单元从控制器(1)中抽取或向控制器(1)馈入取决于所传输的控制量的实际电流。
",G05B19/042,授权
CN102495794A,CN201110376528.5,"本公开涉及一种用于在计算机支持下以图形方框的形式设置测试过程的设备，所述设备包括用于配置所述图形方框的装置和用于将所述图形方框相互嵌套的装置，所述设备包括用于解释所述图形方框的装置。本公开的一个实施例解决的一个问题是将图形的测试过程转换为机器可读的代码。根据本公开的一个实施例的一个用途是图形地开发自动测试。
",G06F11/36,申请终止
CN202183045U,CN201120043267.0,"本公开涉及一种用来测试至少一个控制单元的装置，具有用来执行至少一个环境模型的至少一个计算单元、用来为电子控制单元提供至少一个信号的至少一个信号生成卡、及用来在计算单元与信号生成卡之间双向传输数据的至少一个串行总线，且不仅计算单元而且信号生成卡均具有用来接收和/或发送时间同步消息及角度同步消息的至少一个与串行总线的接口。本公开一实施例解决的一个问题是克服用于测试至少一个电子控制单元的传统装置和传统的HIL仿真器在执行相对复杂的模型、尤其是复杂环境模型时存在性能瓶颈，或者提高在硬件扩展或者硬件空间分布方面的灵活性。根据本公开的一个实施例的一个用途是对汽车中的控制单元进行测试。
",G05B23/02,期限届满
CN202177519U,CN201120326315.7,"本公开涉及转向试验台，包括第一转向装置、测量第一转向装置处转向力矩第一值及实际转向角的测量装置、与转向助力器相连的将借助第一计算单元计算的力或扭矩施加到转向助力器上并根据额定转向角设置转向助力器处第一转向角的传动装置、与传动装置连接的控制单元和与控制单元连接的接收测量装置测得的实际转向角并将为传动装置模拟的额定值传输到控制单元的第一计算单元、和测试驾驶者单元，测试驾驶者单元具有根据第一值设置转向力矩第二值的第二转向装置，测试驾驶者单元和控制单元之间设置有将在力反馈转向盘处设置的作为额定转向角的第二转向角和/或在力反馈转向盘处出现的转向角速度传输到控制单元的第一连接，在测试驾驶者单元和测量装置之间设置有将在第一转向装置处测得的转向力矩的第一值传输到测试驾驶者单元的第二连接，从而降低受伤风险等。
",G01M17/06,期限届满
CN202171101U,CN201120136719.X,"本实用新型涉及一种侧支架(1)，用于将一个壳体保持在一个置物架中，包括一个具有一个竖直的固定板(2a)的型材件(2)，该型材件(2)以固定板可固定在一个置物架中，其中，型材件(2)包括一个与固定板(2a)连接的水平的滑板(2b)并且包括至少一个保持元件(3)，一个壳体可安装在所述滑板上并且可滑动地移动，一个沿水平方向作用的力(F)利用该保持元件从型材件(2)起可施加到壳体的一个侧壁上。
",F16M1/00,期限届满
CN202077316U,CN201120189368.9,"本公开涉及一种印制电路板支架，所述印制电路板支架具有相互间隔开的两个支撑元件(1)，至少三个相互平行并且间隔开的棒条(2)在所述两个支撑元件之间延伸，其中所述棒条的端部可拆卸地固定在这两个支撑元件(1)至少一个上，并且所述印制电路板支架还包括具有至少一个预定轴向长度的能推到所述棒条(2)上的多个套管(3)。本公开的一个实施例解决的一个问题是以简单的方式方法实现一个、或者优选甚至多个要上下堆叠的印制电路板的可靠的位置稳定性。根据本公开的一个实施例的一个用途是印制电路板的固定。
",H05K7/14,期限届满
CN102262695A,CN201110180472.6,"本公开涉及一种能够在计算机辅助的情况下实现和/或配置实时应用程序的至少一个输入/输出模型(111)的工具(110)，其中该输入/输出模型(111)与行为模型(121)分开建模。本公开的一个实施例解决的一个问题是提供一种用于实现和配置实时应用程序的工具，通过该工具能够灵活地建立实时应用程序，特别是输入/输出函数，特别是能够灵活地和简单地调换和改变实时应用程序的单个部分。根据本公开的一个实施例的一个用途是建立和配置输入/输出函数、与仿真硬件匹配以及建立实时应用程序。
",G06F17/50,申请终止
CN202041846U,CN201120010554.1,"本实用新型涉及一种用于仿真系统经由不同总线与电子控制单元通信的设备，包括至少一个控制器、多个收发器和用于总线线路的接头，其特征在于，具有用于经由所述接头的通信的多个收发器，并且所述多个收发器能够分别与所述总线线路连接。本实用新型的一个实施例解决的一个问题是减少不必要的硬件和空间消耗。根据本实用新型的一个实施例的一个用途是用同一仿真系统对经由不同总线系统与其他控制装置通信的不同控制装置进行测试。
",G05B23/02,期限届满
EP1901146B1,EP07016974.3,"设备具有显示设备(2)，其中模型配置字段(4)中的输入/输出(I/O)接入点(3)和/或功能配置字段(6)中的硬件功能(5)可由显示设备呈现。接口配置字段(8)中的I/O接入点、硬件功能和硬件接口(7)通过具有输入和/或输出端口(10a、10b)的块来呈现。 I/O接入点、硬件功能和硬件接口通过图形指派单元(例如信号线(11))来彼此指派。针对以下内容还包括了独立权利要求：(1)包括指令的计算机程序，以执行用于生成用于控制控制系统的控制程序的方法；(2)用于生成用于控制控制系统的控制程序的方法。
",G06F9/44,授权
EP2343611A1,EP10000076.9,"方法涉及指派控制系统的电子计算机单元和输入/输出设备。外部设备的外部输入/输出接口(7)的连接部分(8)的特性在数据库中被部分指定。在选择接口的连接部分之后，输入/输出设备的硬件功能被指派和指示，其中功能独立于存储的规范和接口的连接部分的特性在功能上兼容。独立权利要求也被包括在配置设备中，配置设备用于配置控制系统以用于计算机辅助地生成可执行控制程序的部分来控制控制系统。
",G05B19/042,撤回
CN201887174U,CN201020601980.8,"本实用新型涉及一种用于印刷电路板的接触导通装置及包括接触导通装置的接触导通布置系统，该接触导通装置包括第一和第二插塞连接器，第一插塞连接器具有多个第一触点元件，第二插塞连接器具有多个第二触点元件，第一插塞连接器设置在印刷电路板第一侧上，第二插塞连接器设置在与印刷电路板第一侧对置的印刷电路板第二侧上，第一触点元件构成为用于建立至少与第二触点元件的可拆式导电连接的连接器件和/或第二触点元件构成为用于建立至少与第一触点元件的可拆式导电连接的连接器件，第一和第二触点元件穿过印刷电路板的开口连接。该接触导通装置能实现在印刷电路板上特别简单的安装及第一和第二插塞连接器在印刷电路板上特别节省空间的布置。
",H01R12/00,期限届满
EP2330469A1,EP09015018.6,"该方法涉及部分地列出在不同于功能模型的数据元素集合中的功能模型组件中使用的数据元素(4a，4b) 。分配给组件的数据元素被分组为数据元素组(8)中的公共数据元素，并且数据元素的一部分被编译为数据代码(9) 。公共数据元素的数据代码是独立于功能模型从数据集合生成的，并且功能代码(3a，3b)和数据代码被完全合并并且利用参考信息被编译为可执行总控制程序(1) 。独立权利要求也被包括在下面：(1)一种计算机程序，其具有用于执行用于生成用于控制控制系统的可执行总控制程序的方法的程序代码；以及一种开发环境，用于生成用于控制控制系统的可执行总控制程序，包括电子处理器单元。
",G05B19/042,授权
CN201837890U,CN201020552641.5,"本公开涉及一种用于测试电气元件的设备和仿真器，该设备包括：就至少一种电气特性而言相互不同的至少两个连接装置(3，4，5)，用于选择连接装置(3，4，5)的选择装置，和能借助于选择装置与至少一个连接装置(3，4，5)导电地连接的用于生成仿真信号的仿真装置(1)和用于连接电气元件的测试装置(2)。本公开的一个实施例解决的一个问题是构建一种能提供更好测试结果的电气元件测试设备，依据该项公开所实现的电气元件测试设备能够以简单的方式将由于连接装置(3，4，5)的寄生特性而形成的信号失真情况最小化，从而提高测试的精确性。根据本公开的一个实施例的一个用途是对诸如汽车或者自动化装置的控制系统进行检测。
",G05B23/02,期限届满
JP2010174891A,JP2010015711,"要解决的问题：提供一种用于有利地调节控制装置。
SOLUTION：该方法使用一个调整单元，用于调整控制装置。这里，调试接口检测第一时间点，当控制装置写入到第一存储单元的第一值，第一内存，调节装置使用传送的信息，从上述调试接口与调整装置，在第一时间点获取触发时间点处理例程，与调整装置，使用处理单元用于写入第二值通过调试到第一存储单元的第一内存在一第二时间点之前控制设备中读出第一存储单元的第一内存在第三时间点。
COPYRIGHT：( C ) 2010、JPO&INPIT
",F02D45/00,授权
JP2010173633A,JP2010015277,"要解决的问题：提供一种能够有利地调节控制装置。
SOLUTION：第一预定时间t1由调试接口TRDE当处理程序代码。触发时间与处理例程RU1由操作单元IN通过信息被发送到操作单元IN在第一时刻t1从调试接口TRDE 。第二值V1Y写入第一存储单元的第一存储器SP1在第二时间t2，通过调试界面TRDE IN从操作单元由处理例程RU1 。然后，第一存储单元的第一存储器SP1读出在第三时间由ECU t3控制。
COPYRIGHT：( C ) 2010、JPO&INPIT
",B60R16/02,授权
EP2169485A1,EP09010968.7,"设备具有计算单元和显示设备，其中该设备执行包括应用任务(4A，4b)和FTcom任务(5A，5b)以及在总线节点(2A，2b)上的传输任务(6A，6b)的节点任务。节点任务程序和传输任务程序被用于在节点上实现节点任务和传输任务。节点任务以时间顺序在节点任务字段中表示。传输任务在传输任务字段中表示。节点任务和传输任务通过图形分配单元彼此分配。
",G05B19/042,权利终止
EP2128726A1,EP08009808.0,"本方法包括提供计算单元(5)，以及在利用另一计算单元(2)计算过程模型时，利用隐式积分过程执行过程模型。利用计算单元(5)和计算隐式过程的过程模型状态变量保持计算单元(2)，用于校正由计算单元(2)执行的过程模型计算。确定显式积分过程的状态变量用作工艺模型的状态变量。
",G05B19/042,权利终止
EP2083339A1,EP08100972.2,"本发明涉及一种方法，所述方法包括使用两个双向交换系统(1a，1b)来设计测试模型(2a)和/或特定测试部分(3a，3b)，其中所述系统之一是设置用于设计环境模型的仿真处理器。电子控制系统(5)使用数据信道(6)与所述仿真处理器连接。所述测试模型被设计在所述系统(1a)中。部分测试模型被设计在所述系统(1b)中和/或通过与所述系统(1b)的通信而被传输到所述系统(1b) 。对于测试装置还包括独立的权利要求，所述测试装置包括用于测试电子控制系统的两个双向交换系统。
",G05B23/02,撤回
EP1997005A1,EP07722970.6,"描述了一种用于创建优化的流程图以借助于时间控制的分布式计算机系统执行功能的方法，其中分布式计算机系统和功能具有至少一个元素类的(尤其是结构和功能的)元素的集合，并且元素至少部分地依赖于该依赖性。在此，求解任务的根据本发明的方法最初地并且基本上以如下事实为特征，即识别元素之间的依赖性，分类并且将元素分配给相应的依赖性类，并且通过协调至少一个依赖性类的元素来进行对流程图的优化。
",G06F9/50,驳回
JP2008287709A,JP2008102563,"要解决的问题：に构成装置，能进行影响或传输存储内容的存储器属于控制装置在不同工作状态控制装置，并提供相应的方法。
SOLUTION：适配单元( 6 )用于执行选择性构型之间的数据连接的控制装置( 2 )和至少一个影响装置( 4 )来影响控制装置( 2 )包括存储器( 32 ) 。存储器( 32 )存储至少一部分装置之间传送的数据的影响( 4 )与控制装置( 2 )，即数据读取和/或写入由微控制器的接通的控制装置( 2 ) 。
COPYRIGHT：( C ) 2009、JPO&INPIT
",G06F11/28,驳回
JP2008171391A,JP2007277493,"要解决的问题：提供以下方法建立嵌入式系统的需求描述方法，即使任何用户独特地认识到什么是正在处理的混凝土要求按照需求描述和用于创建需求描述适于自动地创建唯一的测试嵌入式系统从所测试的要求。
SOLUTION：在数据处理系统中，词汇是由可选择的自然语言文本段中，存储了至少一个自然语言语句包括英语和由结合自然语言文本段创建机器可读需求描述。气密娇小者自然语言的语句不只限于英语。
",G06F9/44,撤回
EP1947568A1,EP07024784.6,"该方法涉及提供具有跟踪功能的调试接口以监视跟踪地址域中的跟踪地址范围内的监视地址(8A-8c) 。在控制器中提供监视服务以供通过微控制器执行。地址域中的跟踪地址范围被分配有预设地址(11) 。地址域中的监视地址被分布或传送到服务。监视地址的地址内容被顺序地复制在跟踪地址范围的预设地址中。
",G06F11/36,驳回
EP1936452A1,EP07024786.1,"本方法包括通过测试模型处理属于系统中的外围模型的类型的对象。测试结构由两个双向交换系统和用于测试的系统组成。从该类型的对象生成相同的模型或另一类型的不同模型对象。通过管理设备为系统之一提供两种类型的对象。为管理设备还包括独立权利要求。
",G05B19/042,撤回
EP1898282A1,EP06018945.3,"本方法包括通过测试装置(3)执行用于影响环境模型和/或用于计算环境模型和/或电子控制系统(1)的测试模型。独立于环境模型以及在与环境模型同步的测试操作上功能性地执行测试模型。配置装置(6)通过数据通道(7)与测试装置连接，并且电子控制系统通过另一数据通道(2)与测试装置连接。独立权利要求还包括于下列项：(1)用于通过测试装置(2)执行测试模型的调度方法；以及(2)用于测试电子控制系统的测试装置。
",G05B19/05,授权
EP1522910B1,EP04024001.2,"程序生成方法具有用于控制系统(2)的功能控制程序(9)，该控制系统(2)借助于通过图形建模环境(5)提供的框图(6)而生成，该功能控制程序(9)具有用于功能块(7)之间的信号传输的信号线(8) 。至少一个信号线代表用于控制系统的输入/输出设备(4)的输入/输出接入点(10)，其中配置环境(12)生成与功能控制程序相组合的输入/输出控制程序(13)，以提供总控制程序(1) 。还包括对以下独立权利要求：(A)在配置环境中使用的计算机程序，用于生成用于控制系统的总控制程序；(B)配置设备，用于生成用于控制系统的输入/输出控制程序；(C)具有由数据处理器执行的配置设备程序代码的计算机程序；(D)具有存储在机器可读的数据载体上的配置设备程序代码的计算机程序。
",G05B19/042,全部撤销
EP1880480A1,EP06721508.7,"本发明涉及一种通信方法、系统和信号，并且尤其涉及一种用于在数字信号中传送信息的方法和系统。它尤其适用于基于卫星或地面分组的多用户无线电通信系统。公开了一种用于在包括数据符号的数字信号中传送信息的方法，其中信息被编码在分布于数据符号之间的导频符号的序列特性中，使得接收机能够确定序列特性并且能够检索所传送的信息。本发明允许信息被编码到导频符号的序列特性中，而不是依赖于将这样的信息调制到导频符号自身上。这允许比迄今可能的更大数量的信息被传送，并且该技术比现有技术更耐大的频率误差。
",H04B1/76,权利终止
EP1516432B1,EP03727028.7,"本发明涉及电信信号处理领域，并且涉及用于处理多用户信号的方法和接收机。在本发明的方法中，由接收机使用迭代接收机过程来处理多用户信号，所述迭代接收机过程包括以下步骤：(a)在TDMA信道上接收包括多个用户信号的信号传输，(b)检测一个或多个用户信号，并且确定每个所述用户信号的传输信道估计，(c)通过从所述第一用户的检测到的用户信号减去其他用户信号的加权表示(如果可用的话)，来导出所述第一用户的软信号，(d)计算包括所述软信号的每个符号的后验概率，(e)在迭代解码算法中细化利用所述第一用户的传输信道估计的所述概率，其中，根据解码器收敛准则的应用，概率被部分或完全解码，以及(f)返回到步骤(a)、(b)或(c)，其中，所细化的概率形成要从其他用户的检测到的用户信号减去的加权表示的一部分。该方法通过控制在迭代接收机过程内操作的迭代解码算法的行为来允许多个用户信号的有效检测和解码。
",H04B1/10,权利终止
JP2006351023A,JP2006169063,"要解决的问题：提供模拟生成方法，通过计算机支持的框图消除了传统工艺的缺点。
SOLUTION：计算机辅助方法基于块的模型生成方法，包括如下步骤：用于产生第一框图分配存储块在第一模型层面；连接块之间彼此由多个块的方式水平地输送数据，以便以水平地交换之间数据的多个块；提供另一模型层面与第一模型层面以产生另一框图；设置总电流框图，用另一框图从其它模型层面产生不同于第一模型层面的第一框图；和分配相应的高程基准运输工具垂直交换数据至少在两个模型层面。
COPYRIGHT：( C ) 2007、JPO&INPIT
",G06F17/50,授权
JP2006197595A,JP2006004020,"要解决的问题：提供一种方法和电路能够电隔离自由电位发射一个信号，通过变压器，以擅长简单的结构，具有低功率损失成本，更有利地在特别小的所需空间。
SOLUTION：一次侧变压器L1、L2被与脉冲宽度调制电压SIG1其中异款匙比率指示出差信号状态，所获得的电压二次侧被转换了由电子电路为信号电压取决于主要比率和谁的量值代表信号的状态。
COPYRIGHT：( C ) 2006、JPO&NCIPI
",H04L25/02,授权
EP1529350A1,EP03810340.4,"本发明涉及电信中的信号处理，特别但不排他地用于无线TDMA系统中。特别地，本发明涉及用于利用导频符号的通信系统中的方法。本发明提供一种将导频符号放置在电信系统的数据流中的方法，其中，使用符号之间的不同间隔的范围将导频符号在时间上隔开。导频符号之间的间隔本质上是基本上分形的，导频符号的分布涉及数据流中的不规则的导频符号分组的重复。优选地，不规则的导频符号分组在数据流中不规则地隔开。本发明还提供一种用于通过使用如上定义的、在数据分组内分布的导频符号来获取该数据分组的时间和频率偏移的方法和装置。
",H04B1/76,权利终止