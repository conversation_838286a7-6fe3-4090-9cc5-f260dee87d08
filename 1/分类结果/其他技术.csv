﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,当前法律状态,技术领域,分类得分,分类理由,技术手段,应用场景
US20250260405A1,US19051914,"一种可编程门阵列，其被设置为执行功能，并且当执行该功能时，由该功能的至少一个功能部分使用至少一个信号。门阵列具有至少一个检测元件，该检测元件被设置为使用对信号的改变来确定当执行该功能时该至少一个功能部分是否在门阵列上运行，并且根据该确定来提供至少一个检测值。还提供了具有这种门阵列的计算机装置、这种计算机装置的使用、具有这种计算机装置的测试设备、以及用于生成用于对这种门阵列编程的配置数据的方法。
",H03K19/173,,其他技术,0.0,未匹配到特定技术领域,"提供, 设备, 检测, 方法, 装置, 具有, 配置, 设置, 测试, 生成, 计算","检测, 测试"
EP4576022A1,EP23217564.6,"本发明涉及一种计算机实现的方法，用于提供一个生成式深度学习模型（A）以进行对象检测，包括以下步骤：提供（S1）一个基于自然语言数据预训练的生成式深度学习模型（A），特别是基于自然语言数据预训练的生成式Transformer模型，并使用基于车辆环境传感器数据（SD）的单张图像（10）的训练数据集（TD）对生成式深度学习模型（A）进行微调（S2），所述单张图像尤其为时间序列图像和/或点云（12）。此外，本发明还涉及一种使用生成式深度学习模型进行对象检测的计算机实现方法，一种用于提供用于对象检测的生成式深度学习模型的系统以及一种使用生成式深度学习模型进行对象检测的系统。
",G06V20/56,实质审查,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 检测, 方法, 实现, 生成, 模型, 系统, 计算","检测, 传感器, 车辆"
EP4575557A1,EP24218716.9,"发送接收装置（SE）用于发送和接收电磁信号（EM），其中所述电磁信号（EM）用于与用于物体检测的传感器（OD）进行交换，其中发送接收装置（SE）具有模拟部分（ANA），该模拟部分被设置为将至少一个中间频率级的第一个信号(S1)转换为传输频率级的第二个信号(S2)，并将第二个信号(S2)作为电磁信号(EM)通过输出端(AUS)输出，接收第三个信号（S3）作为电磁信号（EM）通过一个输入端口（EIN），并且将传输频率层中的第三个信号(S3)转换为至少一个中间频率层中的第四个信号(S4)其中第三个信号（S3）是从第二个信号（S2）导出的，其中发射/接收设备（SE）被设置为生成一个测试信号（TS）并将其作为第一个信号（S1）输入到模拟部分（ANA），并通过将测试信号（TS）与第四个信号（S4）进行比较来检查模拟部分（ANA）。
",G01S7/40,实质审查,其他技术,0.0,未匹配到特定技术领域,"设备, 检测, 装置, 具有, 设置, 测试, 生成, 模拟","检测, 传感器, 测试"
US20250191244A1,US19051895,"提供一种检查图形文件的方法。提供具有图形信息的图形文件，该图形信息导致在计算机系统的显示设备上的图形元素的图形表示，该图形表示对应于图形信息。图形文件在每种情况下具有用于至少一些图形元素的预定义注释，并且图形文件具有预定义图形结构信息，该信息至少对于一些图形元素指定在显示设备上的图形表示期间这些图形元素中的哪些必须被显示为彼此连接。接收图形文件的修改版本，其中至少一条图形信息已经被修改，使得它导致显示设备上的不同图形表示。此后检查图形文件的修改版本。
",G06T11/00,暂缺,其他技术,0.0,未匹配到特定技术领域,"提供, 设备, 方法, 具有, 系统, 计算",通用
US20250164869A1,US18950838,"一种用于激励基于红外摄像机的环境检测系统以用于测试目的测试装置。本发明提供一种红外线摄像机可固定为被检测体的保持装置。还提供了红外光源和计算机控制的成像设备。红外光源、成像装置和记录装置以这样的方式布置，即光路从光源通过计算机控制的成像装置到达测试对象。
",G03B43/00,暂缺,其他技术,0.0,未匹配到特定技术领域,"提供, 设备, 检测, 控制, 装置, 测试, 系统, 计算","检测, 摄像, 控制, 测试"
US20250147150A1,US18503211,"一种用于测试雷达传感器的测试组件，包括：用于雷达传感器的容器，其中雷达传感器被配置为发送雷达信号；至少一个接收天线，被配置为接收雷达信号；至少两个发射天线，每个发射天线被配置为发射一个反射信号，其中，作为反射信号的叠加信号可由雷达传感器接收；以及计算机，其被配置为基于所接收的雷达信号来确定相应反射信号的至少一个参数。测试组件包括比接收天线更多的发射天线。
",G01S7/40,公开,其他技术,0.0,未匹配到特定技术领域,"计算, 配置, 测试, 包括","雷达, 传感器, 测试"
JP2025067889A,JP2024178882,"【课题】LiDAR我们提出了一种用于测试传感器的设备和方法。【解决方法】LiDAR传感器构成为送出具有第1波长第1光（L1）该装置一种触发检测器（TD），其被配置为在第一光路上接收第一光触发检测器，其被配置为生成与所接收到第一光相关联的触发信号（TS）至少一种发送设备（12），其构成为，与触发信号关联地发送具有第二波长第二光（L2）第二道光路上的第二道光LiDAR能够由传感器接收送出设备在第一光路构成为从第二光路分离第一光学元件（DS）配置在第二光路内且构成为对第二光应用漫反射和/或漫透射第二光学元件（DIFF）中所述修改相应参数的值。图1是示出图1
",G01S7/497,暂缺,其他技术,0.0,未匹配到特定技术领域,"设备, 检测, 方法, 装置, 具有, 配置, 测试, 生成","检测, 传感器, 测试"
JP2025067891A,JP2024178888,"一种用于测试LiDAR传感器的装置和方法。LiDAR传感器（LIDAR）构成为送出第1光（L1）该装置一种触发检测器（TD），其被配置为在第一光路上接收第一光（L1）触发检测器（TD），其被配置为生成与所接收的第一光（L1）相关联的触发信号（TS）至少一个发送设备（12），其被配置为发送与触发信号（TS）相关联的第二光（L2）送出设备（12），其能够由LiDAR传感器（LIDAR）接收第二光路上第二光移动设备（BE），其构成为使至少一个送出设备移动中所述修改相应参数的值。图1是示出图1
",G01S7/497,暂缺,其他技术,0.0,未匹配到特定技术领域,"设备, 检测, 方法, 装置, 配置, 测试, 生成","检测, 传感器, 测试"
US20250123377A1,US18916625,"一种用于测试激光雷达传感器的装置。LIDAR传感器被配置为发射具有第一波长的第一光。该设备包括触发检测器，其被配置为在第一光学路径上接收第一光。触发检测器被配置成根据所接收的第一光生成触发信号。至少一个发射装置被配置为根据触发信号发射具有第二波长的第二光。第二光可由LIDAR传感器接收在第二光路上。光学元件被配置为将第一光路与第二光路分开。
",G01S7/497,授权,其他技术,0.0,未匹配到特定技术领域,"设备, 包括, 检测, 装置, 具有, 配置, 测试, 生成","检测, 激光, 传感器, 测试, 雷达"
EP4521360A1,EP23196456.0,"本发明涉及一种计算机实现的方法，用于自动注释具有空间重叠测量范围的房间传感器和区域传感器的传感器数据帧。接收到的传感器数据帧最初被独立地注释，从而为传感器数据帧中的每个检测到的对象分配一个边界框。基于时间相关性，对房间传感器的传感器数据帧和区域传感器的传感器信息帧进行分组。三维e对象的边界框被投影到区域传感器的图像平面中。当投影边界框和二维边界框之间的一致性质量度量高于预定义阈值时这些框被分配给同一个对象，不再进一步选中。随后，可以确定对象的属性。
",G06V10/82,暂缺,其他技术,0.0,未匹配到特定技术领域,"检测, 方法, 实现, 具有, 计算","检测, 传感器"
US12250491B2,US18063776,"一种用于生成场景的鸟瞰图像的计算机实现的方法，包括：(a)获取至少一个激光雷达帧，所述激光雷达帧包括具有固有距离信息的点和所述场景的至少一个相机图像；(b)通过使用所述至少一个LIDAR帧生成所述场景的网格表示，所述网格表示具有固有距离信息的所述场景中示出的表面；(c)通过将所述至少一个相机图像的像素分类为表示所述至少一个相机图像的地面像素或非地面像素来产生掩模图像；以及(d)通过增强的逆透视映射生成鸟瞰图像，该逆透视映射利用网格表示的表面固有的距离信息、被分类为地面像素的掩模图像的像素以及至少一个相机图像。
",G06T3/00,授权,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 实现, 具有, 生成, 计算","激光, 雷达"
CN112886968B,CN202011310766.1,"本发明涉及一种模块，包括：用于产生数据流的功能单元；数据输出端，其于向解串器单元输出数据流，所述解串器单元设置为用于获取数据流；第二产品系列的串行器单元，其包括用于配置串行器单元的寄存器；和配置数据输入端，其用于获取定义串行器单元的第一寄存器配置的配置数据，设有数据储存器，在其上存储有从第一产品系列的串行器单元的寄存器地址到第二产品系列的串行器单元的寄存器地址上的第一映射；和配置单元，所述配置单元配置为用于从配置数据输入端读取配置数据，借助于所述第二寄存器配置能将第二产品系列的串行器单元配置为，使得根据第二寄存器配置来配置第二产品系列的串行器单元的寄存器。
",H03M9/00,授权,其他技术,0.0,未匹配到特定技术领域,"配置, 设置, 包括",通用
US12197336B2,US18213029,"在FPGA中，FPGA的内存将有效增加。这是通过一种用于在FPGA上实现具有模型状态相关存储器前瞻的模型自适应高速缓冲存储器的计算机实现的方法来实现的。
",G06F12/08,授权,其他技术,0.0,未匹配到特定技术领域,"方法, 实现, 具有, 模型, 计算",通用
US12181505B2,US18130442,"一种用于测量从馈送天线经由反射器到雷达传感器测试区的路径上的传递函数的系统，包括：消声室；馈送天线，其中馈送天线被配置为发射和接收雷达信号，并且其中馈送天线与反射器一起被设置在消声室内；雷达传感器测试区，其中雷达传感器测试区是消声室内的预定区域；以及设置在雷达传感器测试区中的回射器，其中，回射器被配置为使雷达频率范围中的测量信号的至少一部分经由反射器反射回到馈送天线，其中，测量信号经由反射器从馈送天线接收。
",G01R29/08,授权,其他技术,0.0,未匹配到特定技术领域,"包括, 配置, 设置, 测试, 系统","雷达, 传感器, 测试"
EP4475011A1,EP24174757.5,"本发明涉及一种用于对交通状况进行分类的计算机实现的方法和系统（1），该交通状况包括机动车辆的环境数据的数据集，该数据集是通过有向图（G）对第一数据集（DS1）的应用（S2）预先确定的，其中，所述有向图（G）的节点（11）分别根据在所述自车辆（10）和/或所述同车（12）相对于车辆环境的运动行为的至少一个分段中以时间间隔（14）满足的第一条件对所述第一数据集（DS1）进行分段，其中，所示有向图；如果所有指定路段都满足给定交通状况的第二条件（16），则对给定交通状况进行分类（S3）；以及一个输出（S4），该类表示给定的交通状况（K）和/或表示给定交通状况的第二数据集的相应开始和结束时间（DS2）。
",G06F18/20,实质审查,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 实现, 系统, 计算","机动车, 车辆"
US20240385289A1,US18578334,"一种用于测试距离传感器的测试装置，包括：接收元件，用于接收电磁自由空间波作为接收信号；发射元件，用于发射电磁输出信号；以及信号处理单元。在模拟运行期间，接收信号或从接收信号导出的接收信号通过信号处理单元以可预先给定的时间延迟被引导，以形成作为模拟反射信号的时间延迟信号。信号处理单元被配置为完全处理接收温度口腔相干和时间受限的传感器信号或在延迟步骤中从接收的传感器信号导出的接收的时间相干和时间受限的传感器信号，其中可预定义的时间延迟作为恒定工作时间延迟以形成时间延迟的传感器信号其中可预定义的时间延迟在延迟步骤开始时预定义。
",G01S7/40,暂缺,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 处理, 配置, 测试, 模拟","传感器, 测试"
JP7558796B2,JP2020212561,"一种用于测试以超声波工作的距离传感器的测试装置，其中，待测试的距离传感器至少包括用于发射信号的传感器辐射元件和用于接收反射信号的传感器接收元件。为了有效和准确地测试和激励距离传感器，测试装置具有用于接收从待测试的距离传感器发射的超声波的测试接收元件，和用于辐射测试超声波的至少一个测试辐射元件，以及信号处理单元，其中，由测试接收元件接收的超声波作为接收信号被发送到信号处理单元和信号处理单元，作为接收信号和与要模拟的距离有关的模拟距离信息的函数，并且确定用于测试辐射元件的激励信号。
",G01S7/52,授权,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 处理, 具有, 测试, 模拟","传感器, 测试"
EP4402647A1,EP22786900.5,"本发明涉及一种用于自动注释诸如视频帧或音频帧之类的传感器数据帧的计算机实现的方法。基于至少一个条件属性将接收帧分组为多个分组，所述条件属性涉及当FR时存在的环境条件记录AME。使用神经网络注释与条件属性的特定值范围相对应的第一分组。计算机基于第一样本帧确定注释的质量水平。如果至少一个帧的质量等级低于预定阈值基于第一样本的校正注释重新训练神经网络。如果质量高于预定义阈值，则输出注释帧。本发明还涉及非易失性计算机可读介质和计算机系统。
",G06V10/44,期限届满,其他技术,0.0,未匹配到特定技术领域,"方法, 实现, 系统, 计算","传感器, 网络"
EP4351004A1,EP23193434.0,"记录设备，用于记录来自传感器设备的串行图像数据流。记录设备包括用于对图像数据流进行解串的解串器。为了配置第一解串器，其切割由用于配置布置在传感器设备中的串行器的接收设备发送的配置数据流，并且通过分析记录数据记录来导出记录设备的解串器的配置。在优选实施例中，通过将配置串行器抽象为串行器的功能、第一解串器的功能从第一串行器的函数的推导、以及将配置串行化器的功能抽象为串行化器功能来进行推导，以及将所述第一解串器的功能具体化为所述第一去串器的配置。
",H03M9/00,实质审查,其他技术,0.0,未匹配到特定技术领域,"设备, 配置, 包括",传感器
CN113412610B,CN202080013626.X,"本发明涉及一种用于通过消息系统向要测试的接收器设备发送受保护的消息的方法和重放单元(D)，其中，重放单元通过消息系统与要测试的设备相连接，其中，重放单元被设置为获取要重放的第一受保护的消息、从所述第一受保护的消息删除第一计数器值(Z)和第一认证码(MAC)并且借助于第二计数器值、加密算法和密钥生成第二认证码，并且其中重放单元被设置为通过将第二计数器值和第二认证码添加到第一受保护的消息中而生成第二受保护的消息，并且其中所述重放单元还被设置为通过消息系统将第二受保护的消息发送到要测试的接收器设备。
",H04L43/50,授权,其他技术,0.0,未匹配到特定技术领域,"设备, 方法, 设置, 测试, 生成, 算法, 系统",测试
US20240017747A1,US18251721,"一种用于生成模拟场景的方法包括：接收原始数据，其中，所述原始数据包括多个连续的LIDAR点云、多个连续的相机图像、以及连续的速度和/或加速度数据；将来自确定区域的所述多个LIDAR点云合并到公共坐标系中以产生复合点云；对所述复合点云内的一个或多个静态对象进行定位和分类；基于所述复合点云、一个或多个静态对象和至少一个相机图像来生成道路信息；对所述多个连续的LIDAR点云内的一个或多个动态道路用户进行定位和分类并且生成所述一个或多个动态道路用户的轨迹；基于所述一个或多个静态对象、所述道路信息以及所述一个或多个动态道路用户的所生成的轨迹来创建模拟场景；以及导出所述模拟场景。
",B60W60/00,暂缺,其他技术,0.0,未匹配到特定技术领域,"生成, 方法, 模拟, 包括",通用
US20240010210A1,US18004071,"一种计算机实现的方法提供了终止驾驶员辅助系统的基于场景的测试过程。测试过程包括循环，并且包括确定关键测试用例及其参数组合。该方法包括：在该循环的每次迭代n之后执行驾驶员辅助系统的至少一个测试，并且确定关键测试情况的集合；在此基础上，建立迭代n中的第一组关键测试用例与来自迭代n-1的第二组关键测试用例之间的距离，或者迭代n中的第一组关键测试用例与预定义的一组关键测试用例之间的距离；以及基于第一组和第二组之间的距离与预设限制之间的差来终止或继续测试过程的循环。
",B60W50/02,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 方法, 实现, 测试, 系统, 计算","驾驶, 测试"
US11860301B2,US17353905,"一种用于测试使用电磁波操作的距离传感器的测试装置，包括：接收元件，其接收作为接收信号的电磁自由空间波(S <Sub>RX</Sub>)；以及用于辐射电磁输出信号的辐射元件(S <Sub>TX</Sub>)。在测试模式中，测试信号单元生成测试信号(S <Sub>test</Sub>)，并且辐射元件被配置为辐射测试信号(S <Sub>test</Sub>)或从测试信号(S <Sub>test</Sub>)导出的测试信号(S ' <Sub>test</Sub>)作为电磁输出信号(S <Sub>TX</Sub>)。在测试模式中，分析单元被配置为根据接收信号(S <Sub>RX</Sub>)或导出的接收信号(S ' <Sub>RX</Sub>)的相位角(φ)和/或幅度(A)来分析接收信号，并且与测试信号(S <Sub>test</Sub>)或导出的测试信号(S ' <Sub>test</Sub>)的辐射同步地将相位角(φ)和/或幅度(A)的确定的值存储为电磁输出信号(S <Sub>TX</Sub>)。
",G01S7/40,授权,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 配置, 测试, 生成","传感器, 测试"
EP4296970A1,EP22180630.0,"本发明涉及一种用于生成用于测试机动车辆的高度自动化驾驶功能的虚拟车辆环境的计算机实现的方法和系统。该方法包括将逐像素分类的相机图像数据（D1）投影（S3）到预先记录的LiDAR点云数据（D2）上，其中分别由具有特别相同的图像坐标的相机图像数据（D1）的分类像素叠加的所述激光雷达点云的点被分配相同的类别（K），并且所述分类的激光雷达点云中的数据（D2）的实例分割（S4）以确定类别（K）中的至少一个包括真实对象（10）。本发明还涉及一种计算机程序和计算机可读数据载体。
",G06V10/82,实质审查,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 实现, 具有, 测试, 生成, 系统, 计算","驾驶, 激光, 测试, 机动车, 雷达, 车辆"
US20230353544A1,US18005365,"一种用于经由消息传递系统向待测试接收机设备发送已注册的安全消息的方法，包括：从消息包提供第一安全且仍串行化的消息以用于在再现单元中进行处理；在第一消息中，缩减经串行化的消息的数据量；在每种情况下，通过添加第二计数器值，或者在适用的情况下，通过用第二计数器值替换第一计数器值，对缩减的第一消息进行解串行化，并创建第二安全消息，在每种情况下，通过使用第二计数器值、加密信息和密钥，以及通过使用通信描述或通过应用数据解释算法获得的信息，创建并添加第二认证器；以及对第二安全消息进行串行化，并向待测试接收机设备发送相应的消息包。
",H04L9/40,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 设备, 包括, 方法, 处理, 测试, 算法, 系统","通信, 测试"
EP4248418A2,EP21815163.7,"本发明公开了一种用于注解传感器数据的计算机实施的方法，包括以下步骤：接收原始传感器数据，所述原始传感器数据包括多个连续的LIDAR点云和/或多个连续的摄像机图像；使用一个或多个神经网络识别摄像机数据的每个图像和/或每个点云中的对象；使连续的图像和/或点云内的对象相关；基于可信性标准去除假阳性结果；以及导出驱动场景的注解的传感器数据。
",G06V10/774,期限届满,其他技术,0.0,未匹配到特定技术领域,"方法, 计算, 包括","网络, 传感器, 摄像"
US20230273303A1,US18143856,"一种用于激励光探测器的试验台，包括光信号装置，该光信号装置包括照明元件的平面布置，这些照明元件能够彼此独立地被激活和去激活，并且该光信号装置被设计成发射由任何照明元件发射的光，该光为至少近似准直光束；会聚透镜，该会聚透镜被设计和定位成将由该光信号装置发射的光束会聚在累积点处；以及用于光探测器的保持装置，该保持装置被布置在该累积点处，借助于该保持装置，光探测器可被以如下方式放置在该累积点处，即由任何第一光源产生的第一光束和由任何第二光源产生的第二光束相对于该试验台的光轴以不同的空间角度撞击该光探测器。
",G01S7/497,暂缺,其他技术,0.0,未匹配到特定技术领域,"装置, 包括",通用
EP3570063B1,EP18172050.9,"一种用于模拟传感器系统的方法，包括检查第一图像和第二图像的交叉点是否存在，以及评估第二虚拟对象是否被PERSPECTIVALLY遮挡。在存在交叉点的情况下，传感器和第一虚拟对象之间的欧几里得距离小于传感器和第二虚拟对象之间的欧几里得距离，并且交叉点的大小超过预定义的阈值，则第二虚拟对象被评估为PERSPECTIVALLY遮挡。在不存在交叉点的情况下，传感器和第一虚拟对象之间的欧几里得距离不小于传感器和第二虚拟对象之间的欧几里得距离，或者交叉点的大小超过预定义的阈值，则第二虚拟对象被评估为非PERSPECTIVALLY遮挡。
",G06V20/56,授权,其他技术,0.0,未匹配到特定技术领域,"方法, 模拟, 系统, 包括",传感器
EP4203418A1,EP21216320.8,"本发明涉及一种用于分析网络的节点(1A、1B、1C)的服务(2A、2B、2C)的方法，所述节点通过网络彼此通信，其中通过网络彼此的节点的通信包括通过网络从至少一个节点向至少另一个节点发送消息，所述方法包括以下方法步骤：A)检查消息的至少一部分是否相应的消息包括服务信息(6)并且如果相应的消息不包括服务信息(6)则忽略消息，B)通过从相应的消息的服务信息(6)中识别以下信息中的至少一个来分析包括服务信息(6)的消息的服务信息(6)：发送节点(10)、接收节点(12、14)、服务标识号、在发送节点(10)和接收节点(12、14)之间的服务动作(30)、在发送节点(10)和接收节点(12、14)之间的通信连接(40)的状态以及通信连接的持续时间，C)将之前识别的服务信息(6)保存在多个服务实例历史表(3)中，以及d)通过可视化单元(4)基于多个服务实例历史表(3)来可视化之前识别的服务信息(6) 。以这种方式，提供了一种用于分析网络中的服务的更简单且不易出错的方法。
",H04L43/04,实质审查,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 包括","通信, 网络"
EP4202474A1,EP21216214.3,"本申请涉及一种用于对仿真逻辑(EL)进行参数化的方法，所述仿真逻辑(EL)仿真图像传感器(BS)，所述方法具有如下步骤：a)借助所述图像传感器(BS)准备对校准对象(KO)的原始数据记录，b)基于所述原始数据记录来测量所述图像传感器(BS)的至少一个运行参数，c)利用所述至少一个运行参数来对所述仿真逻辑(EL)进行参数化，d)借助所述仿真逻辑(EL)准备对所述校准对象(KO)的数字双筒的合成原始数据记录，e)确定所述原始数据记录与所述合成原始数据记录之间的距离，f)如果所述距离不满足预先给定的终止准则，则：g)改变所述至少一个运行参数，h)以相应改变的至少一个运行参数重复执行步骤c)至h) 。如果所述图像传感器(BS)被设计为雷达传感器、激光雷达传感器或超声波传感器，则所述校准对象(KO)被设计为具有限定的特征的反射器。如果所述图像传感器(BS)被设计为相机，则所述校准对象(KO)被设计为具有限定的几何图案的面板，其中利用所述图案的几何形状来测量所述至少一个运行参数。本申请还涉及一种用于对仿真逻辑(EL)进行参数化的装置(10) 。
",G01S7/40,撤回,其他技术,0.0,未匹配到特定技术领域,"装置, 方法, 具有, 仿真","激光, 雷达, 传感器"
EP4198870A1,EP21215590.7,"本发明涉及一种用于通过使用激光雷达信息和相机图像信息来生成场景的鸟瞰视野图像的计算机实现的方法。该方法包括步骤(a)至(d) 。在步骤(a)中，包括具有固有距离信息的点的至少一个激光雷达帧2以及场景的至少一个相机图像3被采集。在步骤(b)中，场景的网格表示4通过使用至少一个激光雷达帧2而被生成，网格表示4表示在具有固有距离信息的场景中示出的表面。在步骤(c)中，通过将至少一个相机图像3的像素分类为表示至少一个相机图像3的地面像素或非地面像素而生成掩模图像6.在步骤(d)中，鸟瞰视野图像1通过增强的逆透视映射并且利用网格表示4的表面、掩模图像6的被分类为地面像素的像素以及至少一个相机图像3所固有的距离信息而被生成。此外，本发明涉及一种被配置为执行该方法的系统100和计算机程序产品。
",G06T3/40,实质审查,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 实现, 具有, 配置, 生成, 系统, 计算","激光, 雷达"
EP4198793A1,EP21215421.5,"本发明的目的是一种用于改变FPGA（2）的FPGA配置的比特宽度的方法，该FPGA配置具有多个至少2n比特的数据信号，该数据信号包含n并且≥3，并且该方法具有以下步骤：如果超过FPGA（2）的电流消耗和/或温度的阈值和/或存在替换信号，则在FPGA（2且≥2。
",G06F21/76,实质审查,其他技术,0.0,未匹配到特定技术领域,"方法, 具有, 配置",通用
EP4198921A1,EP21215399.3,"本发明提供了一种用于检测由传感器11检测到的物体31、32、33、110、201的计算机实现的方法和模拟系统50，其中物体31、31、33、11、201和传感器11在模拟环境100、200中相对于彼此移动。该方法包括识别位于模拟环境100、200中的预选范围30、230、231内的对象110、201的步骤S100，其中预选范围30和230、231包括传感器11的检测范围20，以及确定预选范围30或230内的对象31、32、33、110、201，231被传感器检测范围20检测到。根据本发明，在步骤S100中，通过具有沿着模拟环境100、200的维度的排序坐标的列表210、220来识别在当前时间位于预选范围30、230、231中的对象31、32、33、110、201，该列表识别这样的对象31，32，33、110，201，其先前根据步骤S110在预选范围30230231中。坐标列表210、220在步骤S120中被更新，通过在步骤S124中添加或移除对象标识。本发明还涉及相应的传感器系统50和相应的计算机程序产品60。
",G06V20/58,实质审查,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 检测, 方法, 模拟, 实现, 具有, 系统, 计算","检测, 传感器"
EP4191469A1,EP21212482.0,"本发明涉及一种计算机实现的方法，用于提供用于基于传感器数据的数据集（D）的场景数据来确定类似场景的机器学习算法，其中将优化算法（A3）应用于由第一机器学习算法（A1）输出的传感器数据的数据集（D）的第一扩充（14）的特征表示（18），其中所述优化算法（A3）近似由所述第二机器学习算法（A2）输出的传感器数据的数据集（D）的第二扩充（16）的特征表示（20）。本发明还涉及一种用于基于传感器数据的数据集（D）的场景数据来确定类似场景的方法和训练控制器。
",G06K9/62,实质审查,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 实现, 算法, 控制, 计算","传感器, 控制"
US20230162382A1,US17993687,"本发明提供一种用于确定通过3D场景的模拟而产生的像素的距离数据的像素的强度值的计算机实施的方法及系统，其包含：将第一置信值指派给所述像素的所述第一初始值中的每一者及/或将第二置信值指派给所述像素的所述第二强度值中的每一者；及包含使用指派给所述第一强度值及/或第二强度值中的每一者的所述置信值来计算所述像素的第三强度值，尤其是校正强度值。本发明还涉及一种用于提供经训练机器学习算法的计算机实施的方法及一种计算机程序。
",G06T7/521,公开,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 模拟, 算法, 系统, 计算",通用
EP4184213A1,EP21209972.5,"本发明涉及一种用于确定由像素（12）的3D场景（16）的模拟（14）生成的距离数据的像素（12，通过将第一置信值（K1）分配给像素（12）的第一强度值（10）中的每一个和/或将第二置信值（K2）分配给所述像素（12，以及使用分配给第一强度值（10）和/或第二强度值（0）（K1，K2）中的每一个的置信值来计算（S5）像素（12）的第三、特别是校正后的强度值（10%）。本发明还涉及一种用于提供经过训练的机器学习算法（a）的计算机实现的方法和计算机程序。
",G01S17/00,实质审查,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 实现, 生成, 算法, 模拟, 计算",通用
US20230067783A1,US17462037,"一种用于自动分割包括连续图像的视觉传感器数据的方法，该方法由主机的至少一个处理器执行，该方法包括：A）为每个图像分配场景编号，其中场景包括在单个环境中拍摄的多个图像，其中基于连续图像之间的比较来执行向每个图像分配场景编号；b） 确定每个场景中的图像的累积努力，其中所述累积努力是基于所述场景的图像中的对象的数量来确定的，其中使用一个或多个用于对象检测的神经网络来确定所述对象的数量；以及c）创建图像包，其中具有相同场景编号的图像被分配给相同的包，除非包中的图像的累积努力超过包阈值。
",G06T7/38,授权,其他技术,0.0,未匹配到特定技术领域,"包括, 检测, 方法, 处理, 具有","检测, 传感器, 网络"
US20230024981A1,US17847311,"一种由系统延迟循环转发消息的方法包括：由系统的输入侧接收要转发的消息；以及由系统的输出侧发送要转发到接收设备的消息。执行检查以确定当前系统时间与正在考虑的消息的接收时间之间的差是否大于或等于延迟时间。在差值大于或等于延迟时间的情况下，并且继续对消息的检查，直到找到小于消息的指定延迟时间的差异。
",H04J3/06,授权,其他技术,0.0,未匹配到特定技术领域,"方法, 设备, 系统, 包括",通用
US20220404464A1,US17353905,"一种用于测试使用电磁波操作的距离传感器的测试设备包括：接收元件，用于接收电磁自由空间波作为接收信号（SRX）；以及用于辐射电磁输出信号（STX）的辐射元件。在测试模式中，并且所述辐射元件被配置为辐射测试信号（Stest）或从测试信号（Stest）导出的测试信号（S’test）作为电磁输出信号（STX）。在测试模式中，分析单元被配置为根据其相位角（Phi）和/或幅度（A）来分析接收信号（SRX）或导出的接收信号（S’RX），并且与测试信号（Stest）或导出测试信号（S‘test）的辐射同步地存储相位角（Phi）和/或者幅度（A的确定值作为电磁输出信号（STX）。
",G01S7/40,授权,其他技术,0.0,未匹配到特定技术领域,"设备, 配置, 测试, 包括","传感器, 测试"
US20220326386A1,US17436082,"一种方法生成对应于车辆的LiDAR传感器的合成传感器数据，该合成传感器数据包括叠加的距离和强度信息。该方法包括：提供分层变化的自变编码器；利用第二数据集调节第一特征向量和第二特征向量，第二数据集包括距离和强度信息；将经调节的第一特征向量和经调节的第二特征向量组合为得到的第三特征向量；以及对得到的第三特征向量进行解码，以生成合成传感器数据的第三数据集，该第三数据集包括叠加的距离和强度信息。
",G01S17/931,暂缺,其他技术,0.0,未匹配到特定技术领域,"生成, 提供, 方法, 包括","传感器, 车辆"
CN114503103A,CN202080013884.8,"本发明涉及一种用于容器中的一个或多个应用的基于使用的许可的方法，其中，容器包括许可证模块，应用经由许可证模块查询应用许可证的存在，并且只有在存在应用许可证的情况下才执行所述应用。在许可证模块中存储有一个或多个应用许可证与单义标记的关联，并且容器包括结算模块，所述结算模块在外部许可证源中调用使用单元。结算模块在所获取的使用单元的持续时间上在受保护的数据存储器中提供单义标记，从而能够执行所有与单义标记相关联的应用。本发明还涉及一种计算机系统和一种计算机程序产品。
",G06F21/12,公开,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 方法, 系统, 计算",通用
US20220120856A1,US17418957,"一种信号延迟装置，包括解多工器，D ∈延迟装置、D附加延迟装置、多路复用器和控制单元。对于每个延迟装置，延迟输入端和多路分解器输出端连接，并且延迟输出端和多路复用器输入端连接。对于每个附加延迟装置，附加延迟输入端连接到延迟信号路径，并且附加延迟输出端和多路复用器输入端连接。多路分解器将输入数据字流分成并行数据字流。每个延迟装置将延迟并行数据字流中的数据字。每个附加延迟装置将延迟并行数据字流中的数据字延迟一个附加延迟时间。控制单元控制多路复用器，使得输出与具有时间延迟的输入数据字流相对应的输出数据字流。
",G01S7/40,暂缺,其他技术,0.0,未匹配到特定技术领域,"装置, 具有, 控制, 包括",控制
US20220099797A1,US17425402,"一种用于测试距离传感器的测试装置，包括用于接收作为接收信号的电磁自由空间波的接收元件，以及用于辐射模拟反射信号的辐射元件。接收信号或从其导出的信号经由时间延迟电路被路由，并且因此被时间延迟为时间延迟信号。时间延迟的信号或从其导出的信号作为模拟反射信号被辐射。时间延迟电路具有模拟延迟路径和数字延迟路径。模拟延迟路径实现了比数字延迟路径更短的时间延迟，除了可能的重叠区域。输入开关用于将接收信号或从其导出的信号切换到模拟延迟路径的输入或数字延迟路径的输入，并且该信号在通过连接的延迟路径之后变为时间延迟信号。
",G01S7/40,授权,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 实现, 具有, 电路, 测试, 模拟","传感器, 测试"
US20220082700A1,US17416895,"描述并显示了一种测试台（1），用于测试使用电磁波工作的距离传感器（2），其中待测试的距离传感器（2）包括至少一个用于发射发射信号（4）的传感器辐射元件（3a）和用于接收反射信号的传感器接收元件（3b），以及用于容纳待测试距离传感器（2）的插座（5），在距离传感器（2）的辐射区域内具有至少部分可移动的连接构件（6、6m、6s），该距离传感器（2）固定在插座（5）中，至少一个测试台接收元件（7）固定在连接构件（6、6m、6s）中，用于接收传感器辐射元件（3a）辐射的传输信号（4），以及至少一个测试台辐射元件（8），其保持在连接构件（6）中，用于辐射测试台发射信号（9）作为模拟反射信号。
通过将至少一个试验台接收元件（7、7a、7b）和一个试验台辐射元件（8、8a、8b）一起布置在连接构件（6）的可移动部分（6m）中，实现了可靠的环境模拟，尤其是用于测试多输入多输出距离传感器（2）。
",G01S17/931,授权,其他技术,0.0,未匹配到特定技术领域,"包括, 实现, 具有, 测试, 模拟","传感器, 测试"
US11256032B1,US17121839,"一种可编程光纤延迟线，其模拟环境传感器的空间距离。所述可编程光纤延迟线包括：由多个长度的光纤互连的至少三个光传输开关，其中具有所述多个长度的光纤的所述至少三个光传输开关被配置来提供具有多个不同的可选延迟值的连续延迟线，其中所述不同的延迟值是基于所述至少三个光传输开关的开关位置可选择的。所述至少三个光传输开关中的第一光传输开关的第一端子连接到所述至少三个光传输开关中的第三光传输开关，从而使得能够绕过所述至少三个光传输开关中的第二光传输开关。
",G02B6/28,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 具有, 配置, 模拟",传感器
US20210385307A1,US17338003,"一种用于监视在至少两个控制单元之间交换的消息分组的方法。消息分组在数据流中串联，并且每个消息分组具有标识符、有效载荷和由预定义字长的数据项描述的有效载荷的长度规范。至少两个控制单元由分配器连接。分配器通过第一分配器端口连接到至少两个控制单元中的第一个，通过第二分配器端口连接到至少两个控制单元中的第二个，并通过第三分配器端口连接到计算机系统。数据流流经第一和分发端口，用于第一节点和第二节点之间的通信。计算机系统具有存储器，并且关于消息分组的各个标识符的信息存储在存储器中。
",H04L29/06,授权,其他技术,0.0,未匹配到特定技术领域,"方法, 具有, 系统, 控制, 计算","通信, 控制"
EP3869390A1,EP20157831.7,"本发明涉及一种计算机实现的方法，用于借助于预先记录的视频图像数据(10a)、雷达数据和/或真实车辆环境(U1)的激光雷达点云来产生用于测试机动车(1)的高度自动化驾驶功能的虚拟车辆环境(U2) 。此外，本发明涉及一种用于产生用于测试机动车的高度自动化驾驶功能的虚拟车辆环境(U2)的系统(2) 。此外，本发明涉及一种计算机程序和一种计算机可读数据载体。
",G06K9/00,授权,其他技术,0.0,未匹配到特定技术领域,"方法, 实现, 测试, 系统, 计算","驾驶, 激光, 测试, 机动车, 雷达, 车辆"
EP3812966A1,EP19204989.8,"本发明涉及一种计算机实现的过程，用于将数值分配给在图像、视频和/或点云数据（12）（14a、14b）中标识的至少一个对象的注释（10a、10b）。本发明还涉及一种用于为在图像、视频和/或点云数据（12）（14a、14b）中标识的至少一个对象的注释（10a、10b）分配数值的系统。本发明还涉及一种计算机程序。
",G06K9/62,实质审查,其他技术,0.0,未匹配到特定技术领域,"实现, 系统, 计算",通用
EP3789864A1,EP19195786.9,"本发明涉及一种用于测试控制设备的控制软件的方法，其中所述控制设备提供用于分析的数据流(12)，所述方法具有以下步骤：‑生成用于在列表状的装置(10)的显示区域中显示所述数据流(12)的第一和第二树形结构，所述装置具有多个行(14)和多个列(16)，其中‑所述第一树形结构包括第一多维列表，‑所述第一多维列表包括多个行元素列表，‑所述行元素列表包括多个元素(20)，‑所述行元素列表中的每个元素(20)能够与数据流(12)关联，‑所述元素(20)在所述列(16)中限定所述数据流(12)，所述元素(20)能够与所述数据流关联，其中‑所述第二树形结构包括第二多维列表，‑所述第二多维列表包括多个列元素nt列表，‑所述列元素列表包括多个元素(20)，‑所述元素(20)限定在所述行(14)中显示所述数据流(12)，所述元素(20)能够与所述数据流关联，‑借助于所述第一和第二树形结构显示所述数据流(12) 。
",G06F3/048,实质审查,其他技术,0.0,未匹配到特定技术领域,"提供, 设备, 包括, 方法, 装置, 具有, 测试, 生成, 控制","控制, 测试"
EP3764210A1,EP19184906.6,"本发明涉及一种用以下步骤在桌面（1）上显示显示区域（2、3、4）的程序：-接收数据以创建和显示窗口锁定显示区域（2，3），-基于为此目的接收的数据创建窗口保持显示区域（2，3），从预定的窗口句柄组和唯一的ad标识符中分配清晰的窗口句柄，将广告标识符分配保存到窗口保持显示区（2，3），将窗口保持显示区域（2，3）的显示标识符存储在分层列表中的预定位置，在桌面（1）上显示窗口安装的显示区域（2，3），-接收数据以创建和显示无窗口显示区（4），-根据为此目的接收到的数据创建无窗口显示区（4），并分配唯一的广告标识符，将ad标识符分配保存到无窗口显示区将无窗显示区域（4）的显示标识符存储在层次列表中的预定点处，在桌面（1）上显示无窗显示区（4），当层次列表列出无窗显示区（4）的显示标识符在锁定窗口的显示标识符前面的位置时，在锁定窗口的显示区（2，3）前面显示无窗显示区（4）显示区（2，3），反之亦然。
",G06F3/0481,实质审查,其他技术,0.0,未匹配到特定技术领域,未明确,通用
US20210006938A1,US16737970,"发布者设备的发送器将数据发送到服务器，并且订阅者设备的接收器接收数据。发布/订购中间件被用于提供具有数据标识符的待发送数据，并用于基于该数据标识符选择待接收数据。
",H04W4/02,申请终止,其他技术,0.0,未匹配到特定技术领域,"提供, 设备, 具有",通用
CN105530236B,CN201510675496.7,"软件模型的保护。按照本发明的加密方法，借助于公共密钥(19)和解密结构(17)加密技术系统的包括软件组件(40)的软件模型(16)，其中，后者的定义由软件模型(16)的组件组(42)所包括。解密结构(17)至少部分集成到加密的软件模型(22)中。相应地按照本发明的解密方法借助于同样包括组件组(42)的定义的私密的密钥(21)仅仅解密如下的组件组(42)，所述组件组的定义包括与加密的软件模型(22)的定义相一致的私密的密钥(21)。按照一个优选实施形式，私密的密钥(21)的定义可以借助于密钥扩展(23)被事后扩展，从而借助于扩展的私密的密钥(37)可以解密另外的组件组(42)。
",H04L29/06,授权,其他技术,0.0,未匹配到特定技术领域,"模型, 方法, 系统, 包括",通用
US10671783B2,US16207457,"一种基于用作FPGA模型和/或硬件描述的FPGA设计生成FPGA实现的方法，包括从FPGA设计合成网表和从网表生成FPGA实现的步骤。该方法包括寻找相似的FPGA实现，使用相似的FPGA实现从网表生成FPGA实现的步骤，该方法包括基于FPGA设计生成基于图的表示的步骤，搜索类似的FPGA实现的步骤包括将基于图的FPGA设计表示与至少一个类似的FPGA实现的基于图的表示进行比较。还提供了一种基于FPGA设计生成比特流的方法，用作FPGA模型和/或硬件描述。
",G06F17/50,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 方法, 实现, 生成, 模型",通用
US20200145486A1,US16674620,"具有相互同步的多个时钟的计算机网络，这些时钟分布在计算机网络中的多个参与者之间，并且可以从中读出计算机网络的全局系统时间。计算机网络包括用于第一同步信号的第一同步信号发射器和用于第二同步信号的第二同步信号发射器，并且每个参与方能够基于第一同步信号或第二同步信号将本地存储的变量的值与全局值同步，这样做是为了考虑同步信号的时滞。
",H04L29/08,暂缺,其他技术,0.0,未匹配到特定技术领域,"计算, 具有, 系统, 包括",网络
CN108225782B,CN201711084901.3,"用于控制系统的测试台，所述控制系统设立用于操控宽带型λ传感器，所述测试台设立为，用于在考虑由泵送电压在所述电路中引起的电流的情况下计算表示在宽带型λ传感器的测量间隙中的氧气浓度的实际值或者可导出氧气浓度的指示值。为了可信地模拟宽带型λ传感器的泵电池的电气响应，在电路中第一二极管和第二二极管如此并联连接，使得在泵送电压为第一极性时电流流经第一二极管并且在泵送电压为第二极性时电流流经第二二极管。
",G01M15/10,未缴年费,其他技术,0.0,未匹配到特定技术领域,"模拟, 系统, 电路, 测试, 控制, 计算","传感器, 控制, 测试"
EP3543985A1,EP18163089.8,"本发明涉及一种模拟试验车辆（e）不同交通状况的程序，其步骤如下：试验车辆（E）通过模拟预定道路网的模拟驾驶，模拟大多数其他车辆通过路网的随机驾驶，为试验车辆（E）采集至少一个行驶参数，在试验车辆（e）周围的预定试验区域（t）内，为所有其他车辆采集至少一个特定的行驶参数，检查测试车辆（E）和测试区域（T）内的至少一辆其他车辆是否处于预先定义的交通状况；其中预定的交通状况由试验区（t）内的至少一个试验车辆驾驶参数（e）和至少一个其他车辆驾驶参数给出；以及如果试验车辆（E）和试验区（T）内的至少一辆其他车辆处于预先定义的交通状况，则在预先定义的交通状况下，停止至少一辆其他车辆的随机驾驶，并强制另一辆非随机车辆的预定驾驶操作，预定驾驶操作由预先记录的交通状况来定义。这样，当模拟试验车辆（e）的不同交通状况时，可用状况的频率可以是当试验车辆的自主或部分自主反应实际上被激发时，在不显著损害模拟真实性的情况下增加。
",G09B9/04,撤回,其他技术,0.0,未匹配到特定技术领域,"模拟, 测试","驾驶, 车辆, 测试"
EP3309698B1,EP17192870.8,"在操作至少暂时连接到执行可将可执行二进制代码传输到过程计算机的配置程序的用户计算机的过程中，可以预见许可审查。二进制代码与指示所需许可证的许可证信息相关联，并且配置程序设置为接收过程计算机的唯一标识符。配置程序将牌照和牌照信息添加到授权程序中。授权计划应验证：分配给唯一标识符的可用许可证是否涵盖许可证信息所需的许可证，并且配置程序仅在授权程序已确定可接受性时才将可执行二进制代码传输到过程计算机。本发明还涉及一种操作计算机和计算机程序产品。
",G06F21/12,授权,其他技术,0.0,未匹配到特定技术领域,"配置, 设置, 计算",验证
US20190251212A1,US16274951,"一种方法被公开用于创建用于一FPGA 的配置的一网表，其中一第一个程序的代码包括许多子程序。第一个程序的代码可以结合一第二节目的代码以形成第三程序。用于一FPGA 的配置的一网表可以是在第三程序中创建的，其中第一个程序的至少一个第一子程序在第三程序的运行时间不被使用。可以在一自动方式中识别第一子程序，第四程序可以生成于第一个程序的基础。此外，第一子程序可以在第四程序的创建期间被移除，使得第四程序不包含第一子程序。
",G06F17/50,申请终止,其他技术,0.0,未匹配到特定技术领域,"生成, 方法, 配置, 包括",通用
EP2945082B1,EP14168015.7,"一种用于生成FPGA程序的网表的方法。 FPGA程序的模型由至少两个组件组成，每个组件被分配FPGA上的单独分区。针对每个组件执行独立的构建，并且从组件生成总体分类，其中在触发事件之后自动地开始构建作业，并且触发事件是组件的保存、设计的组件的退出或构建的时间控制的自动化启动。
",G06F17/50,授权,其他技术,0.0,未匹配到特定技术领域,"生成, 模型, 方法, 控制",控制
US10331833B2,US15585351,"本发明涉及一种用于产生一整体网表（50）的方法包含下列步骤：作为第一网表（26）提供第一PLD 代码（24），其中第一PLD 代码（24）具有至少一个第一功能框（28），提供第二个PLD 代码（30），其中第二PLD 代码（30）具有用于替代用途的至少一个第二功能框（32）而不是一相应的第一功能框（28），提供一个开关PLD 代码（40），其具有至少一个开关（42），其分配给至少一个第一功能框（28），其用于连接分配给开关（42）的第一功能框（28），从至少一个开关（42）作为相应的第一功能框（28）的替代连接至少一个第二功能框（32）到一开关，为至少一个第二功能框（32）实现至少一个开关驱动信号（44），其中至少一个开关驱动信号（44）被分配给用于连接至少一个第二功能框（32）的对应的开关（42），从第一PLD 创建整体网表（50）将（24）编码，第二个PLD 代码（30）和开关PLD 代码（40），从整体网表（50）功能的第一块（28）移除其以对应的开关（42）的开关驱动信号（44）为基础不被利用。
",G06F17/50,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 具有, 实现",通用
US10331804B2,US15097318,"用于经由一工厂模型测试至少第一自动控制装置的一种系统包括：第一子系统；从第一子系统空间上是分开的第二子系统。工厂模型包括一可执行的第一个模型代码和一可执行的第二种模型代码。第一子系统包括第一报时信号处理部件，其配置成从一全球时刻源给第一事件电子地分配一第一次信号（Ts1）。第一个模型代码被配置为提供基于第一事件的第一计算结果。第二子系统包括配置成电子地分配的第二报时信号处理部件从全球时刻源给一第二事件再一次表明（Ts2）。第二种模型代码被配置为提供基于第二事件的第二计算结果。
",G06F17/50,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 控制, 装置, 处理, 配置, 测试, 模型, 系统, 计算","控制, 测试"
US20190155256A1,US16192822,"一种用于装置控制设备的半自动化的开发资料管理的方法包括在包括多个互相相关的配置数据单元的一中央数据存储中节省一个开发资料模型，其中配置数据单元每一库容管理命令和/或架构参数。所述方法还包括：提供一规则集并标识一初始配置数据单元，其中以下情况是可能的，使用规则集，以具有初始配置数据单元的一种其关系为基础自动地标识另外的配置数据单元。在增加中，方法包括为了在开发资料模型之内标识配置数据单元的一件子集将提供的规则集到开发资料模型并节省识别的子集。
",G05B19/4155,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 设备, 包括, 方法, 装置, 具有, 配置, 模型, 控制",控制
CN104981807B,CN201480008273.9,"本发明涉及一种用于在运行中改变FPGA(5)的信号值的方法，所述方法包括如下步骤：将具有至少一个信号值的FPGA硬件配置(24)加载到FPGA(5)上；在FPGA(5)上实施FPGA硬件配置(24)；设置用于传输至FPGA(5)的信号值；由所述信号值确定回写数据；将所述回写数据作为状态数据写入所述FPGA(5)的配置存储器(8)；以及将状态数据从所述配置存储器(8)传输到所述FPGA(5)的功能层(6)中。此外，本发明涉及一种用于基于FPGA模型(20)以硬件描述语言执行FPGA构建的方法，所述方法包括如下步骤：创建具有多个信号值的FPGA硬件配置(24)；在FPGA硬件配置(24)的相邻的区域中设置信号值；基于FPGA硬件配置(24)针对多个信号值的状态数据确定出配置存储器(8)的存储器位置(9)，创建具有在运行中能访问的和/或能改变的信号值和与之相对应的存储器位置(9)的列表。
",G06F17/50,授权,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 具有, 配置, 设置, 模型",通用
EP3465363A1,EP17725521.3,"一种检查设备可测试至少一个第一闭环控制单元。所述检查设备可包括第一定时传输单元，其可从第一时间信号生成第一周期定时信号，并且其可将所述第一周期定时信号输出至第一PLL.所述检查装置可进一步包括第一振荡器，其可生成第二周期定时信号，并且其可将所述第二周期定时信号输出至第二PLL.所述检查装置可另外包括第一时钟，并且可将第一时钟信号转发至第一输入/输出单元和/或第一计算单元。第一切换信号可用于控制第一多路复用器，使得根据所述第一切换信号的状态，所述第一多路复用器可将第一频率稳定的定时信号或第二频率稳定的定时信号转发至所述第一时钟。
",G05B19/042,授权,其他技术,0.0,未匹配到特定技术领域,"设备, 包括, 装置, 测试, 生成, 控制, 计算","控制, 测试"
EP3451202A1,EP17188999.1,"在技术系统（6）的测试装置（2）上产生可执行模型的过程，其中，测试设备和可执行模型被训练为实时测试连接到测试设备（8）的控件，并且可执行模型是由通信可执行子模型（10，12）构造而成，其中每个可执行子模型具有单独的地址空间和/或连接到测试设备的控制设备在单独的处理器上测试；或r分离处理器核心。程序应规定以下步骤：（a）对于大多数可执行子模型，以及识别相应可执行子模型的通信属性，（b）访问通信定义（16），以便在可执行子模型之间进行通信；（c）配置通信接口（110、112、114、128、130，132）基于可执行子模型的通信属性和可执行子模型之间通信的通信定义的大多数可执行子模型n个子模型，以及（d）基于可执行子模型的通信特性在可执行子模型和可执行子模型之间通信的通信定义。
",G06F17/50,授权,其他技术,0.0,未匹配到特定技术领域,"设备, 控制, 装置, 处理, 具有, 配置, 测试, 模型, 系统","通信, 控制, 测试"
EP3418924A1,EP18178008.1,"描述和示出了一种用于借助于至少一个运算单元(3)模拟总电路(2)的计算机实现的方法(1)，其中，总电路(2)包括电路组件并且由总状态空间表示(4)在数学上描述，总电路(2)在分离步骤(100)中通过将电路分支分离成至少两个子电路(5a,5b)而被分解，其中，每个子电路(5a,5b)经由耦合变量(i <Sub>I, II, k</Sub>，V <Sub>II, I, k</Sub>，U <Sub>I, MS, k</Sub>，U <Sub>II, MS, k</Sub>，Y <Sub>I, MS, k</Sub>)由相应的部分状态空间表示(6a,6b)在数学上描述， Y <Sub>II, MS , k</Sub>)耦合在一起，并且通过对至少一个运算单元(3)上的耦合部分状态空间表示(6a,6b)进行数值求解来计算每个子电路(5a,5b)
通过将耦合方程系统(11)中的部分状态空间表示(6a,6b)耦合到子电路(5a,5b)之间的计算的耦合变量(i <Sub>I, II, k</Sub>，v <Sub>II, I, k</Sub>)的替换，实现了子电路中的整个电路的稳定分离的系统发现，以便自动实现整个电路的功能的稳定和优化的仿真，映射所述系统发现，在评估步骤(110)中，基于耦合方程系统(11)来映射至少一个稳定性参数S，其中在选择步骤(120)中，根据计算的稳定性参数S来决定整个电路(2)到子电路(5a,5b)中的当前分解是否基于仿真，并且在成功选择之后，通过在WE上计算部分状态空间表示(6a,6b)来进行整个电路(2)的仿真，至少一个算术单元(3)发生。
",G06F17/50,实质审查,其他技术,0.0,未匹配到特定技术领域,"仿真, 包括, 方法, 模拟, 实现, 电路, 系统, 计算",通用
EP3401849A1,EP17170127.9,"根据本发明的方法是通过确定一个测试produktreife声称其中一个测试的测试环境，执行一个通过使用一个testfalls测试对象包括至少一个测试，并没有结果，和程序制定规则的步骤，计算一个概率，一个测试是不成功或不成功的结果，将其中规则作为输入，利用目前或预期的结果作为输出变量的概率和检测概率的计算和返回到一个T结果EST，什么没有，会成功的通过至少一个预定的规则和显示部分在前面的步骤中produktreife依赖包括计算概率。
",G06Q10/00,公开,其他技术,0.0,未匹配到特定技术领域,"包括, 检测, 方法, 测试, 计算","检测, 测试"
EP3399375A1,EP17169687.5,"方法用于配置至少一个第一功能实体和\/或模拟控制单元（ECU，ecu2）在第一个功能，第一个功能类（F1，F2），在至少一个第一功能的第一功能类（F1，F2）依赖的功能和至少一个功能类（F1的，从第一个参数F2）依赖（的P1，P2，P3，P4，P5，P6）包括第一个功能的至少一个控制单元（ECU，ecu2）被分配给的第一个功能类（F1，F2）作为一个其他类（其他类）和至少一个第一默认类别（K1，K2，K3的，K4），和第一个功能类的功能（F1，F2），每一个类别（K1，K2，K3，K4，其他属在第一类的默认（K1，K2，K3，K4）为至少一个第一参数（P1，P2，P3，P4，P5，P6）一第一预定值（V1，V2，V3的，V4）沉积在第一和第一个映射功能默认类别（K1，K2，K3，K4）为至少一个第一参数（P1，P2，P3，P4，P5P6）第一预定值（V1，V2，V3，V4），因此在一个变化的第一预定值（V1，V2，V3，V4）的参数（P1，P2，P3，P4，P5，P6）第一默认类别（K1，K2，K3的，K4）的修改已经默认所有第一类（K1，K2，K3，K4）被分配的功能，虽然这一功能分配控制器（ECU1，ecu2）和功能参数（P1，P2，P3，P4，P5，P6）第一个功能配置。
",G05B19/042,公开,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 模拟, 配置, 控制",控制
US10102325B2,US15291113,"一种用于确定一高性能逻辑器件的功耗的方法，其中根据一预定义的配置确定至少一个配置参数，并且根据高性能逻辑器件确定至少一个器件参数。预定义的配置通过至少一个接口管脚用一计算部件设计成使得高性能逻辑器件数据交换并从至少一个信号源接收数据和/或通过至少一个接口管脚将其传送到至少一个信号接收机。确定在接收自至少一个信号源的和/或发送到至少一个信号接收机的数据的计算部件和高性能逻辑器件以及至少一个信号特征之间的数据交换的至少一个数据的特点。
",G06F17/00,授权,其他技术,0.0,未匹配到特定技术领域,"方法, 配置, 计算",通用
US10095194B2,US15133502,"一种用于配置一试验设备的方法自称是通过一组态系统测试一电子控制单元，因此一技术系统的一软件模型在试验设备中被执行，经由具有连接到试验设备的设备的试验设备的一输入输出接口，软件模型连通，因此组态系统具有一种第二元件类型的一种第一元件类型和一个二级配置元素的第一配置元件，因此配置元件被赋予试验设备的属性，用其在所连接设备和软件模型之间的通信被配置，其中有点在组态系统中出现属性，排序在分选式，即，一并集种，交叉点种，以及冷凝种之间是可切换的。
",G05B9/02,授权,其他技术,0.0,未匹配到特定技术领域,"设备, 方法, 控制, 具有, 配置, 测试, 模型, 系统","通信, 控制, 测试"
EP2487499B1,EP11185161.4,"本发明涉及提供多个相互连接的单电池、用于仿真目的电池、所述电池由基于计算单元的整体模型描述以及对参考电池(EMK)建模的第一模型，利用所述第一模型计算参考电池的端电压。提供第二模型，利用所述第二模型基于参考端电压和每个电池的端电压的推导值计算每个电池的端电压与参考电池的端电压的偏差。
",H01M10/42,授权,其他技术,0.0,未匹配到特定技术领域,"模型, 仿真, 提供, 计算",通用
EP3336730A1,EP17204564.3,"提出了一种创建与技术系统的仿真装置（2）兼容的模型的过程，仿真装置（2）是为开发控制装置而设计的仿真装置，并且仿真装置（2）上的兼容模型（8）是可执行的。该程序应包括以下步骤：（a）提供与技术系统的模拟装置（80）不兼容的模型；（b）提供虚拟执行环境（82），其中与技术系统的仿真设备（80）不兼容的模型可在虚拟执行环境（82）中执行；以及（c）将与技术系统的仿真设备（80）和虚拟执行环境（82）不兼容的模型封装到兼容的容器单元中，它形成技术系统的兼容模型（8），技术系统的不兼容模型（80）可通过兼容容器单元和仿真设备（2）上的虚拟执行环境（82）访问。
",G06F17/50,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 设备, 仿真, 包括, 控制, 模拟, 装置, 模型, 系统",控制
CN108139723A,CN201680062135.8,"说明并示出了一种用于运行控制器(1)的方法，其中，在所述控制器(1)上存放有具有内部控制器函数(a())的程序代码(4)，所述程序代码(4)配备有至少一个服务函数(ecu_s())，通过外部旁路能够实现有针对性地调用内部控制器函数(a())，其方式为在控制器(1)上提供用于服务函数(ecu_s())的服务配置(6)，在控制器(1)中检测所述服务配置(6)并且在调用服务函数(ecu_s())时根据所述服务配置(6)来实施服务功能，其中，所述服务配置(6)描述至少一个内部控制器函数(a())，所述至少一个内部控制器函数作为对应的服务函数(ecu_s())的服务功能实施。所述服务函数(ecu_s())设置用于借助服务配置(6)在控制器(1)中提供用于内部控制器函数(a())的至少一个自变量的至少一个值(x')和/或接受所述内部控制器函数(a())的至少一个返回值。
",G05B19/042,申请终止,其他技术,0.0,未匹配到特定技术领域,"提供, 检测, 方法, 实现, 具有, 配置, 设置, 控制","检测, 控制"
US09929734B2,US15258059,"在一种用于改变配置逻辑模块，初始配置的可编程逻辑模块的读取，结果是一个逻辑说明，特别是映射网表，初始配置至少部分可用的。一个或多个逻辑元件和/或连接元件与逻辑描述的初始结构的可编程逻辑模块的更换或重新配置，和逻辑说明目标配置具有一个或一个以上额外的元件被创建时，没有，或者多个元件中缺失的初始配置是目标配置。分配信息被用来确定转发点，也就是说逻辑元件存在于目标配置和初始配置，特别是寄存器、和/或连接元件，向这些改变应用于目标配置逻辑功能，至少逻辑元件的可编程逻辑模块哪个已经未使用的初始配置是标注为空闲，和附加的元件放置在逻辑元件目标配置标记为空闲且与传输点经由未使用的连接元件。改变的位流从逻辑创建目标配置的描述，位元流写入可编程逻辑模块。
",H03K19/177,授权,其他技术,0.0,未匹配到特定技术领域,"具有, 配置",通用
EP3290898A1,EP17177327.8,"本发明是一种方法，用于在用于测试一fahrassistenzsystems 在一个fahrsimulator 的两辆车辆之间的一次kollisionssituation 的模拟或一辆车辆，在其中，--循环scenario.in simulationsrechner 模拟同伴的车辆，通过在simulationsrechner 的车辆和一名研究员之间的碰撞的一轨道根据kollisionsort 一计划控制自我vehicle.the 同伴的车辆将继续在kollisionsort 上的一所需的eintreffgeschwindigkeit）.the fahrassistenzsystem 以实时的方式用模拟环境成立数据交换和一kollisionssituation 自我车辆的加工性能对对kollisionsort 的不可预见的受控的自我车辆的influence.the 距离和车辆的电流速度对于同伴的车辆sollabstand 到kollisionsort 测定在first.for 每循环识别同伴的车辆应当haveunder ，其与与在kollisionsort arrive.a 控制电路上的自我车辆重合的指定的eintreffgeschwindigkeit 的系统的假设研究员--车辆调整速度，减少的在sollabstand 和实距之间的差异。
",G01M17/007,撤回,其他技术,0.0,未匹配到特定技术领域,"方法, 控制, 系统, 电路, 测试, 模拟","车辆, 控制, 测试"
US20180060457A1,US15249737,"一种计算机实施的方法比较框图，框图描述一种时空演化研究和/或内部状态的动态系统技术计算环境主机，其中方块框图可包括输入端口和输出端口接收发送信号。该方法包括打开第一框图，将第一框图向中间形式，该转化包括使用过滤器过滤脚本，打开第二框图，转换成第二图至中间形式，该转化包括使用过滤器过滤脚本，确定现有的之间的中间形式的第一框图，和中间形态第二块的框图，并输出该确定的差异。同样，非暂时性计算机可读介质和提供了计算机系统。
",G06F17/50,申请终止,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 方法, 系统, 计算",通用
EP2587225B1,EP12184008.6,"本方法涉及从传感器信号(151)获得标记部分，并确定排除标记部分的排除标准。确定存储的标记部分的总数和排除的标记部分的数目的差异是否等于一。从非排除的标记部分确定发动机的旋转角度。通过传感器(141)在标记载体(110)即齿轮的旋转方向上扫描标记部分，并通过计算单元(30)计数，其中传感器设有标记(133-137)或齿。
",G01D5/249,授权,其他技术,0.0,未匹配到特定技术领域,"方法, 计算",传感器
US09870440B2,US14711116,"一种用于产生一FPGA 程序的一网表的方法。FPGA 程序的模型由至少两个组件，被赋予在FPGA 上的一个单独的分区的各组成分组成。对于各组成分和一综合分析技术进行一座独立的构造从组件生成，其中构造工作被自动地开始在一触发事件和触发事件是一节省一组件，设计的一组件的出口，或一座构造的定时控制的，自动化的引发。
",G06F17/50,授权,其他技术,0.0,未匹配到特定技术领域,"生成, 模型, 方法, 控制",控制
CN107560839A,CN201710518721.5,"一种用于调节体积流量的方法和一种用于实施所述方法的具有液体回路的试验台。泵和节流阀在液体回路中串联连接，并且根据液体的体积流量的理论值调节节流阀的开口宽度，以便借助开口宽度确定泵的关于压差记录体积流量的特性曲线。在确定特性曲线之后这样调节泵的压差，使得所述体积流量相当于体积流量的理论值。
",G01M13/00,申请终止,其他技术,0.0,未匹配到特定技术领域,"方法, 具有",通用
EP3244325A1,EP16168898.1,"方法输出时间同步信号同步的时间和\/或捕获的信号处理的一个或多个电子电路综合ausgabekanälen以下步骤：摘要一个频道数量，特别是一种teilanzahl所有通道的逻辑电路组kanallatenz B.查询每一组从一个数据源相关的渠道，C.确定从所有的请求的最大kanallatenz kanallatenzen和至少暂时存储作为最大的kanallatenz gruppenlatenz，每个通道D.属于集团的执行时间是确定的请求的kanallatenz gruppenlatenz之间和各自的渠道和储存模量差比kanalzugehörigen latenzoffset在存储，特别是不可约的一个存储器电路一个signallaufes E.影响至少在各自的渠道依赖的latenzoffset有限的存储。
",G06F17/50,授权,其他技术,0.0,未匹配到特定技术领域,"处理, 方法, 电路",通用
EP3244327A1,EP16168901.3,"方法产生一个参考表的交叉引用表，其中基于FPGA的源代码编译的源代码，其中至少有一个第一信号，第一信号，利用第一个至少有一个是分配给第一个登记册，其中在zuordnungsliste第一信号和第一个寄存器相互配合，将列出其中第二个信号的第二点在FPGA使用的源代码，自动识别，是价值的第二信号根据第一信号的功能从第一值可确定，其中第二个信号zuordnungstabelle，第一个寄存器的功能是相匹配的第一项。
",G06F17/50,撤回,其他技术,0.0,未匹配到特定技术领域,方法,通用
EP3242232A1,EP16168055.8,"本发明涉及一种方法生产的一个步骤gesamtnetzliste（50）提供第一PLD的代码（24）作为第一个表（26），其中第一PLD的代码（24）的至少一个功能块（28）的第一和第二PLD提供的代码（30）其中第二PLD编码（30）的至少一个功能块（32）和第二可供使用而不是一个相应的第一功能块（28）一个开关可提供的代码（40）至少有一个至少有一个第一功能块（28）相关的开关（42）连接的第一个开关（42）相关的功能块（28），连接的至少一个第二个功能块（32）至少有一个开关（42）相应的第一功能块（28）schalteransteuerungssignals（44）执行至少一个功能块的至少一个第二对（32），其中至少有一个schalteransteuerungssignal（44）相应的开关（42）连接的至少一个第二个功能块（32）是分配给创建gesamtnetzliste（50）包括第一PLD的代码（24）第二PLD编码开关（30）和PLD的代码（40），和删除功能的第一块（28）的基础上，schalteransteuerungssignal（44）相应的开关（42）不使用，从gesamtnetzliste（50）。
",G06F17/50,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 包括",通用
CN103106145B,CN201210281662.1,"本发明涉及一种用于在数据通信装置中处理控制装置的数据的方法，其中数据通信装置具有第一存储区和第二存储区，并且经由接口与控制装置连接，并且数据从控制装置经由接口传输到数据通信装置。数据中的每一个数据具有一个地址和一个数值。数值相同地存储在第一存储区和第二存储区中。由数据通信装置检测是否存在第一触发。当存在第一触发时中断在第一存储区中的存储，或者在第一触发时检测触发类型并且仅当存在预定的触发类型时中断在第一存储区中的存储。随后，从第一存储区读出数据的数值，其中由数据通信装置将在时间上晚于第一触发到达的数值存储在第二存储区中。
",G06F12/02,授权,其他技术,0.0,未匹配到特定技术领域,"检测, 方法, 装置, 处理, 具有, 控制","检测, 通信, 控制"
EP3200428A1,EP16152681.9,"（1）computerimplementiertes程序实现应用程序在目标硬件v2x（2）与（3），其中funkadapter v2x应用通过一个图形化的modellierungsumgebung（4）形成一个模拟电路（5），其中，在一个列表（6）接收的LDM从一个telematikdaten umgebungsobjekten覆盖其中一个传感器（7）从列表中确定的telematikdaten umgebungsobjekten覆盖，其中一个telematikdatenvergleichsblock（8）通过比较与LDM的列表（6）传感器差分umgebungsobjekte列表（7）确定的传感器，只有在列表中包括的其中一个至少有一个差分umgebungsobjekt表示v2x消息（9），与此差分umgebungsobjektes telematikdaten创建框图（5），其中一个在目标硬件v2x（2）可执行程序（10）和v2x翻译程序（10）的Z（2）在ielhardware转移被执行其中表示v2x消息从目标硬件（9）（2）（3）对funkadapter被发送。
",H04L29/08,授权,其他技术,0.0,未匹配到特定技术领域,"实现, 电路, 模拟, 包括",传感器
EP3193221A1,EP16151449.2,"根据本发明的步骤，配置测试设备的输入/输出接口，输入/输出接口被标记为将硬件连接到测试设备中存在的行为模型。此过程显示以下步骤：将输入/输出接口的图形表示显示为硬盘驱动器之间的信号路径连接硬件的软件端口和至少一个通过可选输入/输出功能连接行为模型的模型端口。接收信号路径的第一配置。在硬件端口或信号路径模型端口接收预定的测试值特别是，还可以使用硬件端口或模型端口的图形表示。根据信号路径的第一配置，沿着信号路径传播与测试值相关联的测试信号，在模型端口或硬件端口的图形表示中分别显示传播的测试信号。
",G05B19/042,撤回,其他技术,0.0,未匹配到特定技术领域,"模型, 设备, 配置, 测试",测试
US20170126232A1,US15334659,"一种用于可编程逻辑器件的访问信号具有功能层，和一个配置电平在运行时当可编程逻辑器件执行预定配置。接入至少一个信号用于请求比特的数目。单独配置每个都位于地址单元具有一个地址偏移每件，以使得一个或多个信号值的比特位于一个地址单元。逐位访问请求的信号值的发生，其存取的单独位排序的函数可应用的咬入地址单元包含这样一种方式，即可访问所有比特依次位于地址单元进行起的偏移量，独立于信号包含可用比特。
",H03K19/177,授权,其他技术,0.0,未匹配到特定技术领域,"具有, 配置",通用
EP3142032A1,EP15184169.9,"在一个方法，一个可编程的配置更改的配置，可编程的logikbausteins logikbausteins读取，使logikbeschreibung放置的网表，特别是一种配置，至少部分的存在。从logikbeschreibung的配置一个或多个可编程的logikbausteins logikelemente和\/或紧固件和一个取代或改造一个zielkonfiguration logikbeschreibung会产生哪些不是一个或多个额外的logikelemente）而不是一个或多个元素的配置，在zielkonfiguration缺乏。采用常规的滴点测定zuordnungsinformationen，因此在目标和现有的logikelemente寄存器配置，特别是，和\/或在zielkonfiguration verschaltungselemente，各种改进建议，其中，至少在未使用的配置，可编程logikbausteins logikelemente作为自由标记，其中附加的zielkonfiguration logikelemente作为自由标记放置在logikelementen和闲置verbindungselemente与交货点的连接。从对一个由logikbeschreibung zielkonfiguration bitstrom创建和修改后的bitstrom将在可编程logikbaustein写的。
",G06F17/50,授权,其他技术,0.0,未匹配到特定技术领域,"方法, 配置",通用
US20170045418A1,US15234403,"一种计算期望轨迹提供一种车辆。车辆位于道路上的位置为边界的两条道路边缘，其中该边缘至少在已知道路周围的区的位置。弹簧质量模型引入，其中弹簧质量模型用于计算期望的轨迹，其中该位置点的质量计算弹簧质量模型的停止状态，并且所计算的位置点质量块用作数据点计算曲线连接尖轨的质量，从而该曲线代表轨迹。
",G01M17/06,授权,其他技术,0.0,未匹配到特定技术领域,"模型, 提供, 计算",车辆
EP3130970A1,EP15180733.6,"提出了一种用于使用控制装置的已经存在的基本测试模型(100)将测试装置(2)的输入/输出接口(4)连接到测试器中的技术系统的模型(8)的方法。在这种情况下，接口被设计为连接控制装置的装置特定实现(200)或者连接待控制的技术系统，并且模型是技术系统的测试模型(8)或者控制装置的测试模型。该方法包括以下步骤：访问基本测试模型，其中基本测试模型被配置用于纯计算机基的基本测试；基于通信要求从基本测试模型中提取通信要求；限定适于通信请求的物理信号传输的特性；以及配置测试器的接口与模型之间的连接(6)，使得能够根据特性通过接口进行物理信号传输。
",G05B19/042,撤回,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 控制, 装置, 实现, 配置, 测试, 模型, 系统, 计算","通信, 控制, 测试"
JP2017021026A,JP2016135715,"[目的]至障碍物的距离和障碍物的速度求出雷达装置的试验台的提供。解决手段是，光线后至仿真装置具有至少一个雷达天线、周围模型具有计算机单元。周围模型雷达装置相对的相对位置和相对速度至少一个障碍物的数据( x，v )、射线后至仿真装置，雷达设备中接收到的周围变为脉调制雷达信号模型、基于预先设定的相对位置和相对速度来适当的反射雷达信号的雷达设备的方向送出雷达装置是预先设定的相对位置和相对速度，具有检测障碍物。射线后至仿真装置雷达装置的前方的规定的角度范围中扩展的情况该角度范围内具有相对位置和相对速度，所述障碍物，在相互不同的角度来含有。图1中
",G01S7/40,撤回,其他技术,0.0,未匹配到特定技术领域,"提供, 设备, 仿真, 检测, 装置, 具有, 模型, 计算","检测, 雷达"
US20170010346A1,US15204305,"一种用于测试距离雷达仪器以确定障碍物的距离和速度的测试台，包括雷达模拟设备，所述雷达模拟设备包括至少一个雷达天线和具有周围环境模型的计算机单元，其中所述周围环境模型包括来自所述距离雷达仪器的具有相对位置和相对速度的至少一个障碍物的数据(x，v)，其中在接收到来自所述距离雷达仪器的扫描雷达信号之后，所述雷达模拟设备至少部分地在所述距离雷达仪器的方向上基于由所述周围环境模型预先确定的所述相对位置和所述相对速度来发射合适的反射雷达信号，使得所述距离雷达仪器以预先确定的相对位置和相对速度来检测障碍物，其中所述雷达模拟设备在所述距离雷达仪器前方的角度范围上延伸，使得可以在所述角度范围中以相互可区分的角度来模拟具有相对位置和相对速度的所述障碍物。
",G01S7/40,申请终止,其他技术,0.0,未匹配到特定技术领域,"设备, 包括, 检测, 具有, 测试, 模型, 模拟, 计算","检测, 雷达, 测试"
EP3115804A1,EP16178215.6,"一种用于测试距离雷达装置以确定障碍物的距离和速度的测试台，包括：雷达模拟装置，具有至少一个雷达天线；以及计算机单元，具有环境模型，环境模型包括具有距离雷达装置的相对位置和速度的至少一个障碍物的数据(x，v) 。在从距离雷达装置接收到A味雷达信号之后，至少部分地在距离雷达装置的方向上基于由环境模型相对位置和速度所预定的来发射适当的反射雷达信号，使得距离雷达检测具有预定的相对位置和速度的障碍物，其特征在于，雷达模拟装置在距离雷达装置前方的角度范围上延伸，使得可以在该角度范围中以可区别的角度来模拟具有相对位置和GescheWi的障碍物，并且雷达模拟装置包括多个固定的雷达天线，其分布在角度范围上。
",G01S7/40,撤回,其他技术,0.0,未匹配到特定技术领域,"包括, 检测, 装置, 具有, 测试, 模型, 模拟, 计算","检测, 雷达, 测试"
EP3070553A1,EP15159845.5,"描述并描绘了用于计算机辅助生成用于用电子计算单元（5）控制控制系统（4）的可执行控制程序（3）的计算机实现的过程（1）。如果控制程序（3）的功能至少部分地在图形模型（6）中描述，并且图形模型（6，R）包括至少一个子模型（7，V）和至少一个子功能，则图形模型（6，R）首先被翻译成更高级编程语言中的模型代码（8）；模型代码（8）随后被编译成可在控制系统（3）上执行的控制程序（4）。
通过在高级编程语言中将子模型（7，V）转换为子模型代码函数（V），在高级编程语言中将模型（6，R）转换为综合模型代码（8），改进了图形模型中子模型函数的处理，以及子模型代码函数（v）通过指针从横向模型代码（8）调用到子模型代码函数（v）。
",G05B19/042,授权,其他技术,0.0,未匹配到特定技术领域,"包括, 控制, 处理, 实现, 生成, 模型, 系统, 计算",控制
US20160210380A1,US14695431,"一种计算机实现的方法，用于自动生成至少一个块表示驱动器用于基于块的建模环境，其中,所述驱动器功能起控制硬件元件的目标硬件装置的方法，包括制备描述驾驶员功能的形式语言，读入形式语言描述的评价驾驶员的功能，并产生代表该块的驱动器，该驱动器用于建模功能的建模环境的框图。
",G06F17/50,申请终止,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 装置, 实现, 生成, 控制, 计算","控制, 驾驶"
US20160203244A1,US14593709,"基于计算机的系统和方法用于指定至少一个信号的至少一个符号的程序将I/O功能的目标硬件装置提供。建模工具具有一个符号的程序与信号是房号未定的。信号被分配的符号的程序与至少一个I/O功能性目标硬件单元的规定配置工具。使用该建模工具，I/O功能性目标硬件装置分配了该符号的程序到信号是房号未定的。一种信号指定信息项产生建模工具由此分配。该信号被从分配信息条款建模工具对配置工具，与配置工具接管分配给I/O功能性目标硬件装置的信号被指配的符号的程序根据信号指定信息项。
",G06F17/50,申请终止,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 装置, 具有, 配置, 工具, 系统, 计算",通用
US09342672B2,US14167200,"一种计算机实现的方法，用于管理至少一个数据元素控制单元发展，该方法允许统一数据元件通过整个开发过程，通过提供管理单元具有用户界面，相关的数据元素与管理单元，并将访问配置与管理单元。该访问构造限定数据元件可由用户经由用户界面。
",G06F21/62,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 实现, 具有, 配置, 控制, 计算",控制
CN105556402A,CN201480051803.8,"本发明描述一种用于通过第二函数影响电子控制设备的控制程序的第一函数的方法。所述控制程序通过处理器的第一计算核心执行，而所述第二函数在控制程序的执行期间通过第二计算核心执行。所述第一函数给变量配属第一值并且将所述第一值在第一时刻写入到所述变量的存储器地址中。所述第二函数给变量配属第二值，所述第二值在第二时刻写入到所述变量的存储器地址中，其中，覆写由所述第一函数写入的第二值。在第三时刻，所述控制程序从所述变量的存储器地址读取所述第二值。管理实例这样在时间上彼此协调对所述变量的存储器地址的访问，使得所述第一时刻位于所述第二时刻之前并且所述第二时刻位于所述第三时刻之前。
",G05B19/042,申请终止,其他技术,0.0,未匹配到特定技术领域,"设备, 方法, 处理, 控制, 计算",控制
EP2869145B1,EP13190584.6,"一种用于影响控制程序的方法，该控制程序具有多个第一功能并且第一功能中的至少一个被配置用于控制执行器，并且提供存储器并且该存储器具有由分配给第一功能的子程序所占据的存储器区域，由此当调用第一功能中的一个时，在控制程序的程序代码中存在分支地址，该分支地址指向与功能调用相关联的子程序的存储器地址。针对功能调用的出现分析控制程序，并且确定与功能调用相关联的分支地址和返回命令的地址。选择要删除的第一功能中的一个。用第二功能代替第一功能，其中所选择的第一功能的程序代码被第二功能的程序代码覆写。
",G05B19/05,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 具有, 配置, 控制",控制
EP2925025B1,EP14162150.8,"一种用于在具有无线电适配器的目标硬件上实现V2X应用程序的计算机实施的方法，其中借助于图形建模环境以框图的形式对V2X应用程序建模，并且将框图编译成能够在目标硬件上执行的V2X程序，并且将V2X程序传输到目标硬件上并且在那里执行。用于实现V2X应用程序的方法以尤其简单和有利的方式实现，其方式是使用具有至少一个无线电适配器接口的V2X通信块来创建框图，借助于所述无线电适配器接口在无线电适配器和V2X通信块之间交换数据。
",H04W4/00,授权,其他技术,0.0,未匹配到特定技术领域,"方法, 具有, 实现, 计算",通信
EP2960731A1,EP14174029.0,"方法执行一个中断的方案（1）一个电子控制装置（1），其中一个方案是第一个应用程序（11）（12），第一个变量在第一个程序（11）一个变量的第一个值在第一个内存（12）存储一个内存模块的第一或第一个变量的第一个值第一内存区（12）从内存读取，和第二个程序（21）与第二个变量在第二个程序（21），第一个变量的第一个值从第一个内存（12）存储或内存的第一个变量的第一个值第一内存区（12）从内存读取，以之间的第一和第二个verknü程序程序生产操作结果，其中一个图形（201）是明显的，modellierungsumgebungmodellierungsumgebung（201）和图形显示在图形modellelemente，modellierungsumgebung（201）（11）通过的第一个程序的第一modellelement（202）的代表，和第二个程序（21）通过一个第二modellelement（203）的代表，之间的联系，并在第一程序和第二程序由第三modellelement（205）是其特征在于通过一个haltebedingung modellierungsumgebung（201）指定（2），其中通过一个选择（2）haltebedingung整个方案的执行：（1）领导（3）是固定的，programmzustandsänderung（1）整个计划被执行在执行一个programmzustandsänderung整体方案（1）（3）检测和programmzustandsänderung（3）被认为是作为一个执行的开始和\/或结束的第一和\/或第二方案（21）和\/或一个读和\/或写访问的第一值的第一个内存区和\/或修改的第一个值的第一个内存区，整个计划的执行被中断，（1）如果在指定的programmzustandsänderung haltebedingung（2）（3）发生。
",G05B19/042,撤回,其他技术,0.0,未匹配到特定技术领域,"装置, 检测, 方法, 控制","检测, 控制"
EP2924522A1,EP14162207.6,"影响我们的遗产的一个开口的方法，其中一个控制程序，第一个多功能的第一功能和子程序）和至少一个相关的第一个功能控制系统中的执行器进行训练，这控制单元的第一内存来记录我们的遗产）作为一个二进制代码和控制程序，可用在代码中调用我们的遗产的一个功能是一个第一和一个sprungadresse sprungadresse内存地址与函数调用相关的子程序和显示子程序的程序代码作为一个序列的binärem vorliEGT和结束的代码序列到一个D rücksprungbefehl次级方案rücksprungbefehl rücksprungadresse EM被误读、次级方案的代码序列的第一部分包括一个内存地址和每个variablenzugriff variablenzugriffen是分配，和其中至少一个变量的值赋给一个将但在我们的遗产代码的发生函数调用与函数调用进行了研究和sprungadressen和连接各自的第一和rücksprungadressen与功能相关的变量名称与变量和与客户的在内存地址的变量和函数，来确定第一个D在各自的第一功能相关的变量相关的内存地址存储在第一zuordnungstabelle，一个比较第一和第二预先确定的zuordnungstabelle zuordnungstabelle具有至少一个第一部分和至少一个函数的函数名被分配到一个变量的第一个值的第二值取代。
",G05B19/042,授权,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 具有, 系统, 控制",控制
JP2015170367A,JP2015044745,"要解决的问题：提供一种生成可执行的控制程序从图形控制模型，使资源的控制系统可以被使用。
SOLUTION：图形控制模型转换为程序代码，其至少有一个FXP操作至少一个FLP操作。所生成的程序代码然后传递至可执行的控制程序。在执行所述控制程序，对控制系统的一部分，执行该控制程序的FXP单元和另一部分执行的控制程序的FLP单元。
",G05B19/042,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 系统, 生成, 模型, 控制",控制
EP2916183A1,EP14158000.1,"computerimplementiertes程序（1）产生一个在一个控制系统（2）（3）包括一个可执行steuerungsprogramms图形控制（4）控制系统（2），其中一个prozessschnittstelle）和控制系统（2）是这样设置的，在prozessschnittstelle（5）的至少一个prozessgröße物理过程（6）控制系统（2）包括一个输出和\/或影响的物理过程（6）控制系统（2）可花，其中至少有一个电子控制系统（2）rechnereinheit（7）具有至少一个固定点的执行单元（FXP单元，8）和至少一个浮点执行单元（FLP）单元，9），其中一个图形控制（4）数量（1 modelloperationen0，11）的问题。
更好的利用控制系统（2），这表明，该图形模式（4）在翻译的代码（13）将生成的代码（13）至少有一个（12）和FXP手术至少一个FLP问题和操作（14）生成在可执行程序代码（13）（3）控制程序翻译成这样的steuerungsprogramms（3）在执行上的控制系统的一部分（2）（3）在steuerungsprogramms FXP单元（8）执行与另一部分（3）在steuerungsprogramms FLP单元（9）执行。
",G05B19/042,授权,其他技术,0.0,未匹配到特定技术领域,"包括, 具有, 系统, 设置, 生成, 控制",控制
EP2899652A1,EP14152109.6,"本发明涉及用于优化用于电子车辆控制单元的可编程逻辑模块、具有软CPU和/或未使用剩余空间的可编程逻辑模块的使用的过程和系统。通过创建各种模型变量来说明控制单元的功能，并创建具有不同配置范围的各种软CPU配置，这些软CPU配置占据与可编程逻辑模块的配置范围相对应的区域，从而解决了该任务，在根据可编程逻辑模块上的软CPU配置修复软CPU之后，对各种型号变体和/或软CPU配置执行处理器在环仿真，并利用PIL仿真过程中产生的软CPU的轮廓数据对输入信号进行处理，以优化可编程逻辑模块的使用。
",G06F17/50,权利终止,其他技术,0.0,未匹配到特定技术领域,"仿真, 处理, 具有, 配置, 系统, 模型, 控制","车辆, 控制"
EP2891527A1,EP14150184.1,"本发明涉及一种弯曲semiflex biegevorrichtung一印刷电路板（1）的至少一个flexbereich（4）和两个侧，在每一个starrbereich（23）加入有两个翅膀（25）和一对翅膀（25）安装在第一个固定的第一fixiervorrichtung（20）的第一starrbereichs（2）印刷电路板（1）和第二的航班第二fixiervorrichtung L（25）安装（21），第二个固定starrbereichs（3）印刷电路板（1）其中至少有两个翅膀（25），以获得一个轴的位置是verschwenkbar和至少一个fixiervorrichtungen（20，21）在各自的翅膀特别是从轴的距离的函数的至少一个schwenkwinkels verschwenkbaren翼（25）是可变的。本发明涉及一种弯曲的印刷电路板的方法继续使用这种装置。
",B21D5/04,授权,其他技术,0.0,未匹配到特定技术领域,"装置, 方法, 电路",通用
EP2881814A1,EP13196219.3,"影响我们的遗产的一个开口的方法，在多个第一功能和驱动功能，其中至少有一个第一致动器控制系统的训练和大量的变量，并提供每个。一个变量的内存地址是分配给的R，其中的变量之间的第一个预定的配置和功能训练，一个处理器和控制单元具有多个算术问题，在第一rechenkern这与第一个控制程序执行的功能，其特征我们的遗产是在执行的第二rechenkern执行第二功能的第二功能，其中一个控制程序，至少部分在不同的programmkode）利用二次函数和一个指定的第一个变量的值功能改变及随后的修正值是从控制程序访问。
",G05B19/042,撤回,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 控制, 处理, 具有, 配置, 系统",控制
EP2765528A1,EP13154741.6,"将具有信号值的FPGA硬件配置(24)加载到FPGA (5)上。执行FPGA上的FPGA硬件配置。设置用于向FPGA传输的信号值。根据信号值确定回写数据，并且将其作为状态数据写入到FPGA的配置存储器(8) 。将状态数据从配置存储器传输到FPGA的功能级(6) 。包括独立的权利要求用于执行FPGA构建的方法。
",G06F17/50,授权,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 具有, 配置, 设置",通用
US20140214783A1,US14167019,"计算机实现的方法及控制单元的产品管理变体开发提供依据。一致的数据管理是由最初指定产品特征模型的变型，规范组件的至少一个域，并定义特征/组件依赖关系通过关联组件具有至少一产品特征，随后说明书所关心的至少一个产品变型通过选择产品特征，规范至少一个域所关注的，组件的自动识别，属于所关心的产品变体通过自动评估特征/组件相关性，以及自动化输出标识的组件。
",G06F17/30,申请终止,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 实现, 具有, 模型, 控制, 计算",控制
EP2759964A1,EP13152999.2,"本发明涉及在变体模型中确定产品特征(1)并且在域(5)中确定组件(4) 。通过将所述组件与所述产品特征相关联来定义特征/组件依赖性(6) 。通过选择产品特征来确定感兴趣的产品变体(V)，并且确定感兴趣的域(PD) 。通过自动化地评估所述特征/组件依赖性来自动地识别属于所述感兴趣的产品变体的组件，并且自动地输出所识别的组件。
",G06Q10/06,驳回,其他技术,0.0,未匹配到特定技术领域,模型,通用
JP2014123381A,JP2013272735,"要解决的问题：提供一种调节控制装置。
SOLUTION：在用于调整由调整单元控制装置具有第一存储器单元，另一存储器单元，和调试界面，调试界面有监控功能监控处理的程序代码由控制装置。信息写入由控制装置的另一存储器单元在第一时间点而且书写了由调试界面以第一时间点，报告调节装置当程序代码被处理。传输的信息从调试接口调节装置在第一时间点，触发点在时间上与处理例程在设置单元。第二值覆写第一存储器单元，通过调试界面，在第二时间点，从调整装置，通过该处理例程。读取第一存储器单元由控制装置在第三时间点。
COPYRIGHT：( C ) 2014、JPO&INPIT
",G05B19/042,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 装置, 处理, 具有, 设置, 控制",控制
CN203596509U,CN201320437740.2,"本实用新型涉及一种开关柜，具有底部元件和顶部元件以及正面和背面以及左侧面元件和右侧面元件，并且在所述正面上沿着各前壳体边缘分别构成有至少一个垂直的板条，并且板条沿着纵向延伸方向具有多个孔状的空隙；以及侧面元件沿垂直方向构成在底部元件和顶部元件之间并且与它们连接，所述开关柜具有内腔区域，其设置为用于接纳能够从正面插入的组件；以及所述组件在正面上分别具有至少一个侧面的凸缘，并且所述垂直的板条由所插入的组件的所述侧面的凸缘至少部分地遮盖，并且组件借助穿过所述凸缘的固定器件与所述垂直的板条连接；所述固定器件实施为螺钉，并且在螺钉的头部与所述凸缘之间构成有适配件，并且螺钉的头部偏心地定位在适配件中。
",H02B1/26,期限届满,其他技术,0.0,未匹配到特定技术领域,"具有, 设置",通用
EP2711794A1,EP12185821.1,"本方法包括在开发环境中读取设计模型的所有模型对象。如果第一数据可用，则从存储位置读取具有全局唯一键的第一数据。将该键分配给模型对象，使得全局唯一键在设计模型的编辑期间可用。如果第一数据在编辑期间可用，则生成新的全局唯一键，并且如果第一数据在编辑期间不可用，则保留该新的全局唯一键。将第一数据与全局唯一键一起保存，并且进一步保存设计模型的所有模型对象(15) 。
",G05B19/042,授权,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 具有, 生成, 模型",通用
CN103633614A,CN201310365299.6,"本发明涉及电子保护装置、用于驱动电子保护装置的方法及其应用。电子保护装置用于保护至少一个电负载（LS），电负载能连接到保护装置上，该电子保护装置具有输入端子（IN）和输出端子，该保护装置具有热自恢复的保险丝元件，该保险丝元件设计和构建为，根据保险丝元件温度（T<Sub>PF</Sub>）传导或限制第一电流（I<Sub>1</Sub>），为了限制第一电流，设置限制装置（T1、WA），该限制装置包括：与保险丝元件串联的第一晶体管（T1），和影响第一晶体管的监控电路（WA），其设计和构建为，a）在第一电流（I<Sub>1</Sub>）达到或超过预定义的最大电流值（I<Sub>wh</Sub>）时使第一晶体管截止，以及b）在第一电流达到或低于预定义的接通电流值（I<Sub>wh</Sub>）时接通第一晶体管。
",H02H3/08,授权,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 装置, 具有, 电路, 设置",通用
EP2706421A1,EP12183797.5,"本方法涉及在图形模型(S-0)中描述控制程序(2)的功能。在子模型(S-1-S-3，S-11，S-12，S-111)中，图形模型在分级级别中被结构化。针对不同分级级别的两个嵌套子模型，规定图形模型转换选项的值。在计算机辅助地生成每个子模型的程序代码中，执行共享选项的值。不提供较低级别上的子模型的选项的值，而提供较高分级级别上的子模型的选项的值。
",G05B19/042,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 生成, 模型, 控制, 计算",控制
EP2642359A1,EP12160249.4,"系统(101)具有生成控制单元程序的电子计算机单元，以及显示单元(102)，例如计算机监视器，包括矩形显示区域(104)，以流程图(106)的形式图形显示程序的构建过程(105) 。显示单元的另一个矩形显示区域(140)图形显示程序的模型元素(141、143、146)，例如软件组件，其中模型元素被分配到构建过程的构建过程步骤(111-114)，其可以由开发系统执行。针对用于创建控制单元程序的方法，还包括独立权利要求。
",G05B19/042,撤回,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 控制, 具有, 生成, 模型, 系统, 计算",控制
EP2592504A1,EP12191528.4,"本方法涉及提供具有资源模型(50)的估计单元(30)，资源模型(50)具有多个描述硬件的参数(R1-Rn) 。基于描述硬件和/或程序代码表示的参数确定控制设备程序代码的存储空间要求的估计值和控制设备程序代码的运行时估计值。将存储空间要求的估计值和/或运行时估计值存储在存储区域中和/或在可执行模型(10，10 ')中在显示设备(60)上显示。将估计单元设计为代码生成器的部分程序。存储区域为RAM或ROM.
",G05B19/042,权利终止,其他技术,0.0,未匹配到特定技术领域,"提供, 设备, 方法, 具有, 生成, 模型, 控制",控制
US20130073063A1,US13621773,"一台计算机项目管理系统和方法创建电子控制单元软件被提供。该系统具有一软件架构工具被配置成设计图形模型的电子控制单元的机动车辆。行为模型工具转换图形模型生成到计算机可读取代码。软件容器具有一文件产生基于电子控制单元。容器管理器比较软件容器与现有软件容器在进口或出口的容器的软件或工具或工具软件体系结构行为模型，比较列表产生表明是否界面修改用于电子控制单元是制造。比较列表然后被显示给用户的显示屏上。
",G05B19/042,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 控制, 具有, 配置, 生成, 模型, 工具, 系统, 计算","机动车, 控制"
CN101278242B,CN200680036597.9,"本发明涉及一种用于对控制器、尤其是汽车控制器的端子上至少一个电气/电子负载、尤其是电感负载的效应进行仿真的方法，其中通过以下方式实际地模拟理论上流经至少一个端子(3，4，5)上被仿真负载的电流，即借助于与该至少一个端子(3，4，5)连接的可控的电流单元(11)对控制器(1)抽取或馈入电流。本发明还涉及一种用于对控制器、尤其是汽车控制器上至少一个电气/电子负载、尤其是电感负载的效应进行仿真的装置，该装置可连接到控制器(1)，并且至少通过以下部件模拟流经控制器(1)的至少一个端子(3，4，5)上的被仿真负载的电流：计算单元，用于尤其根据在被仿真负载上降低的总电压或其大小计算和/或提供表示负载中电流的控制量；和电流单元(11)，具有至少一个用于构成电流源和/或电流宿的辅助电压源(15A，15B)，其中控制量可从计算单元传输到电流单元，并且电流单元从控制器(1)中抽取或向控制器(1)馈入取决于所传输的控制量的实际电流。
",G05B19/042,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 仿真, 方法, 模拟, 装置, 具有, 控制, 计算","汽车, 控制"
CN202177519U,CN201120326315.7,"本公开涉及转向试验台，包括第一转向装置、测量第一转向装置处转向力矩第一值及实际转向角的测量装置、与转向助力器相连的将借助第一计算单元计算的力或扭矩施加到转向助力器上并根据额定转向角设置转向助力器处第一转向角的传动装置、与传动装置连接的控制单元和与控制单元连接的接收测量装置测得的实际转向角并将为传动装置模拟的额定值传输到控制单元的第一计算单元、和测试驾驶者单元，测试驾驶者单元具有根据第一值设置转向力矩第二值的第二转向装置，测试驾驶者单元和控制单元之间设置有将在力反馈转向盘处设置的作为额定转向角的第二转向角和/或在力反馈转向盘处出现的转向角速度传输到控制单元的第一连接，在测试驾驶者单元和测量装置之间设置有将在第一转向装置处测得的转向力矩的第一值传输到测试驾驶者单元的第二连接，从而降低受伤风险等。
",G01M17/06,期限届满,其他技术,0.0,未匹配到特定技术领域,"包括, 模拟, 装置, 具有, 设置, 测试, 控制, 计算","驾驶, 控制, 测试"
CN202171101U,CN201120136719.X,"本实用新型涉及一种侧支架(1)，用于将一个壳体保持在一个置物架中，包括一个具有一个竖直的固定板(2a)的型材件(2)，该型材件(2)以固定板可固定在一个置物架中，其中，型材件(2)包括一个与固定板(2a)连接的水平的滑板(2b)并且包括至少一个保持元件(3)，一个壳体可安装在所述滑板上并且可滑动地移动，一个沿水平方向作用的力(F)利用该保持元件从型材件(2)起可施加到壳体的一个侧壁上。
",F16M1/00,期限届满,其他技术,0.0,未匹配到特定技术领域,"具有, 包括",通用
CN202077316U,CN201120189368.9,"本公开涉及一种印制电路板支架，所述印制电路板支架具有相互间隔开的两个支撑元件(1)，至少三个相互平行并且间隔开的棒条(2)在所述两个支撑元件之间延伸，其中所述棒条的端部可拆卸地固定在这两个支撑元件(1)至少一个上，并且所述印制电路板支架还包括具有至少一个预定轴向长度的能推到所述棒条(2)上的多个套管(3)。本公开的一个实施例解决的一个问题是以简单的方式方法实现一个、或者优选甚至多个要上下堆叠的印制电路板的可靠的位置稳定性。根据本公开的一个实施例的一个用途是印制电路板的固定。
",H05K7/14,期限届满,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 实现, 具有, 电路",通用
CN102262695A,CN201110180472.6,"本公开涉及一种能够在计算机辅助的情况下实现和/或配置实时应用程序的至少一个输入/输出模型(111)的工具(110)，其中该输入/输出模型(111)与行为模型(121)分开建模。本公开的一个实施例解决的一个问题是提供一种用于实现和配置实时应用程序的工具，通过该工具能够灵活地建立实时应用程序，特别是输入/输出函数，特别是能够灵活地和简单地调换和改变实时应用程序的单个部分。根据本公开的一个实施例的一个用途是建立和配置输入/输出函数、与仿真硬件匹配以及建立实时应用程序。
",G06F17/50,申请终止,其他技术,0.0,未匹配到特定技术领域,"提供, 仿真, 实现, 配置, 模型, 工具, 计算",通用
EP2343611A1,EP10000076.9,"方法涉及指派控制系统的电子计算机单元和输入/输出设备。外部设备的外部输入/输出接口(7)的连接部分(8)的特性在数据库中被部分指定。在选择接口的连接部分之后，输入/输出设备的硬件功能被指派和指示，其中功能独立于存储的规范和接口的连接部分的特性在功能上兼容。独立权利要求也被包括在配置设备中，配置设备用于配置控制系统以用于计算机辅助地生成可执行控制程序的部分来控制控制系统。
",G05B19/042,撤回,其他技术,0.0,未匹配到特定技术领域,"设备, 包括, 方法, 控制, 配置, 生成, 系统, 计算",控制
CN201887174U,CN201020601980.8,"本实用新型涉及一种用于印刷电路板的接触导通装置及包括接触导通装置的接触导通布置系统，该接触导通装置包括第一和第二插塞连接器，第一插塞连接器具有多个第一触点元件，第二插塞连接器具有多个第二触点元件，第一插塞连接器设置在印刷电路板第一侧上，第二插塞连接器设置在与印刷电路板第一侧对置的印刷电路板第二侧上，第一触点元件构成为用于建立至少与第二触点元件的可拆式导电连接的连接器件和/或第二触点元件构成为用于建立至少与第一触点元件的可拆式导电连接的连接器件，第一和第二触点元件穿过印刷电路板的开口连接。该接触导通装置能实现在印刷电路板上特别简单的安装及第一和第二插塞连接器在印刷电路板上特别节省空间的布置。
",H01R12/00,期限届满,其他技术,0.0,未匹配到特定技术领域,"包括, 装置, 实现, 具有, 电路, 设置, 系统",通用
EP2330469A1,EP09015018.6,"该方法涉及部分地列出在不同于功能模型的数据元素集合中的功能模型组件中使用的数据元素(4a，4b) 。分配给组件的数据元素被分组为数据元素组(8)中的公共数据元素，并且数据元素的一部分被编译为数据代码(9) 。公共数据元素的数据代码是独立于功能模型从数据集合生成的，并且功能代码(3a，3b)和数据代码被完全合并并且利用参考信息被编译为可执行总控制程序(1) 。独立权利要求也被包括在下面：(1)一种计算机程序，其具有用于执行用于生成用于控制控制系统的可执行总控制程序的方法的程序代码；以及一种开发环境，用于生成用于控制控制系统的可执行总控制程序，包括电子处理器单元。
",G05B19/042,授权,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 控制, 处理, 具有, 生成, 模型, 系统, 计算",控制
JP2010174891A,JP2010015711,"要解决的问题：提供一种用于有利地调节控制装置。
SOLUTION：该方法使用一个调整单元，用于调整控制装置。这里，调试接口检测第一时间点，当控制装置写入到第一存储单元的第一值，第一内存，调节装置使用传送的信息，从上述调试接口与调整装置，在第一时间点获取触发时间点处理例程，与调整装置，使用处理单元用于写入第二值通过调试到第一存储单元的第一内存在一第二时间点之前控制设备中读出第一存储单元的第一内存在第三时间点。
COPYRIGHT：( C ) 2010、JPO&INPIT
",F02D45/00,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 设备, 检测, 方法, 装置, 处理, 控制","检测, 控制"
EP2169485A1,EP09010968.7,"设备具有计算单元和显示设备，其中该设备执行包括应用任务(4A，4b)和FTcom任务(5A，5b)以及在总线节点(2A，2b)上的传输任务(6A，6b)的节点任务。节点任务程序和传输任务程序被用于在节点上实现节点任务和传输任务。节点任务以时间顺序在节点任务字段中表示。传输任务在传输任务字段中表示。节点任务和传输任务通过图形分配单元彼此分配。
",G05B19/042,权利终止,其他技术,0.0,未匹配到特定技术领域,"设备, 包括, 实现, 具有, 计算",通用
EP2128726A1,EP08009808.0,"本方法包括提供计算单元(5)，以及在利用另一计算单元(2)计算过程模型时，利用隐式积分过程执行过程模型。利用计算单元(5)和计算隐式过程的过程模型状态变量保持计算单元(2)，用于校正由计算单元(2)执行的过程模型计算。确定显式积分过程的状态变量用作工艺模型的状态变量。
",G05B19/042,权利终止,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 方法, 模型, 计算",通用
EP1936452A1,EP07024786.1,"本方法包括通过测试模型处理属于系统中的外围模型的类型的对象。测试结构由两个双向交换系统和用于测试的系统组成。从该类型的对象生成相同的模型或另一类型的不同模型对象。通过管理设备为系统之一提供两种类型的对象。为管理设备还包括独立权利要求。
",G05B19/042,撤回,其他技术,0.0,未匹配到特定技术领域,"提供, 设备, 包括, 方法, 处理, 测试, 生成, 模型, 系统",测试
EP1898282A1,EP06018945.3,"本方法包括通过测试装置(3)执行用于影响环境模型和/或用于计算环境模型和/或电子控制系统(1)的测试模型。独立于环境模型以及在与环境模型同步的测试操作上功能性地执行测试模型。配置装置(6)通过数据通道(7)与测试装置连接，并且电子控制系统通过另一数据通道(2)与测试装置连接。独立权利要求还包括于下列项：(1)用于通过测试装置(2)执行测试模型的调度方法；以及(2)用于测试电子控制系统的测试装置。
",G05B19/05,授权,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 控制, 装置, 配置, 测试, 模型, 系统, 计算","控制, 测试"
EP1516432B1,EP03727028.7,"本发明涉及电信信号处理领域，并且涉及用于处理多用户信号的方法和接收机。在本发明的方法中，由接收机使用迭代接收机过程来处理多用户信号，所述迭代接收机过程包括以下步骤：(a)在TDMA信道上接收包括多个用户信号的信号传输，(b)检测一个或多个用户信号，并且确定每个所述用户信号的传输信道估计，(c)通过从所述第一用户的检测到的用户信号减去其他用户信号的加权表示(如果可用的话)，来导出所述第一用户的软信号，(d)计算包括所述软信号的每个符号的后验概率，(e)在迭代解码算法中细化利用所述第一用户的传输信道估计的所述概率，其中，根据解码器收敛准则的应用，概率被部分或完全解码，以及(f)返回到步骤(a)、(b)或(c)，其中，所细化的概率形成要从其他用户的检测到的用户信号减去的加权表示的一部分。该方法通过控制在迭代接收机过程内操作的迭代解码算法的行为来允许多个用户信号的有效检测和解码。
",H04B1/10,权利终止,其他技术,0.0,未匹配到特定技术领域,"包括, 检测, 方法, 处理, 算法, 控制, 计算","检测, 控制"
JP2006351023A,JP2006169063,"要解决的问题：提供模拟生成方法，通过计算机支持的框图消除了传统工艺的缺点。
SOLUTION：计算机辅助方法基于块的模型生成方法，包括如下步骤：用于产生第一框图分配存储块在第一模型层面；连接块之间彼此由多个块的方式水平地输送数据，以便以水平地交换之间数据的多个块；提供另一模型层面与第一模型层面以产生另一框图；设置总电流框图，用另一框图从其它模型层面产生不同于第一模型层面的第一框图；和分配相应的高程基准运输工具垂直交换数据至少在两个模型层面。
COPYRIGHT：( C ) 2007、JPO&INPIT
",G06F17/50,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 方法, 设置, 生成, 模型, 工具, 模拟, 计算",通用
JP2006197595A,JP2006004020,"要解决的问题：提供一种方法和电路能够电隔离自由电位发射一个信号，通过变压器，以擅长简单的结构，具有低功率损失成本，更有利地在特别小的所需空间。
SOLUTION：一次侧变压器L1、L2被与脉冲宽度调制电压SIG1其中异款匙比率指示出差信号状态，所获得的电压二次侧被转换了由电子电路为信号电压取决于主要比率和谁的量值代表信号的状态。
COPYRIGHT：( C ) 2006、JPO&NCIPI
",H04L25/02,授权,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 具有, 电路",通用
EP1529350A1,EP03810340.4,"本发明涉及电信中的信号处理，特别但不排他地用于无线TDMA系统中。特别地，本发明涉及用于利用导频符号的通信系统中的方法。本发明提供一种将导频符号放置在电信系统的数据流中的方法，其中，使用符号之间的不同间隔的范围将导频符号在时间上隔开。导频符号之间的间隔本质上是基本上分形的，导频符号的分布涉及数据流中的不规则的导频符号分组的重复。优选地，不规则的导频符号分组在数据流中不规则地隔开。本发明还提供一种用于通过使用如上定义的、在数据分组内分布的导频符号来获取该数据分组的时间和频率偏移的方法和装置。
",H04B1/76,权利终止,其他技术,0.0,未匹配到特定技术领域,"提供, 方法, 装置, 处理, 系统",通信
