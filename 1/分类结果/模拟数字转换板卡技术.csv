﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,当前法律状态,技术领域,分类得分,分类理由,技术手段,应用场景
JP2025092461A,JP2024210783,"【课题】提供一种具有简单且高效的半导体开关1以及测量电路2和控制装置3的装置，用于半导体开关的过载保护。【解决手段】在缺陷生成单元中，测量电路2能够检测到过载检测器发现负载路径6出现过载，并且当发生过载时，通过过载信号输出部分13向控制装置3输出过载信号。控制装置连接至半导体开关1的开关信号输入部分7，通过开关信号线路14连接，当接收到过载信号时，向半导体开关提供用于打开半导体开关的开关信号，当过载达到最大值并且达到预先设定的最大过载时，在过载信号消失后，向半导体开关提供用于闭合的开关信号，当过载超过预先设定的最大过载时，在过载信号消失后不再生成用于闭合开关的开关信号。【选择图】图1
",H03K17/08,公开,模拟数字转换板卡技术,2.0,IPC分类号匹配: H03K17/08,"提供, 检测, 装置, 具有, 电路, 生成, 控制","检测, 控制"
EP4513204A1,EP23192609.8,"电池模拟器，包括提供第一个电池电压以模拟第一个电池电压的第一个电路和提供第二个电池电压以模拟第二个电池电压的第二个电路。第一个电池电压放在本地电路质量上，第二个电池电压放在同一本地电路质量上，并以这种方式连接到第一个电池电压，本地电路块形成第一电池电压和第二电池电压的极点。设置控制电子设备以控制第一电池电压和第二电池电压。通过单个控制电子设备控制两个电池电压，可以节省组件。
",G01R31/28,实质审查,模拟数字转换板卡技术,2.0,IPC分类号匹配: G01R31/28,"提供, 设备, 包括, 控制, 电路, 设置, 模拟",控制
US12119812B2,US18065637,"一种用于电流均匀分配的电子电路，包括：第一MOSFET和第二MOSFET，其中第一MOSFET和第二MOSFET并联连接以便分配施加到输入端的电流，该电流流向电子电路的输出端，其中输入端分别连接到第一MOSFET的漏端和第二MOSFET的漏端；以及用于控制电压的端子，其中控制电压被施加到第一MOSFET的栅极端子和第二MOSFET的栅极端子。第一MOSFET包括在第一MOSFET的栅极端子处的第一电阻器，并且第二MOSFET包括在第二MOSFET的栅极端子处的第二电阻器。
",H03K17/14,授权,模拟数字转换板卡技术,2.0,IPC分类号匹配: H03K17/14,"电路, 控制, 包括",控制
US20240255566A1,US18427448,"一种用于测试功率电子控制器的测试装置，其中控制器具有电源连接和负载连接。多个功率电子模块中的每一个具有电源连接、至少一个负载连接和用于控制功率电子模块的接口。在测试装置的操作状态中，控制器的供应连接各自连接到供应侧电力电子模块的负载连接，并且控制器的负载连接各自连接到负载侧电力电子模块的负载连接。因此，就了，通过控制装置至少根据所确定的用于前馈控制的负载侧功率电子模块的负载连接的电连接参量来计算所确定的在供电侧功率电子模块的负载连接处的实际电流，可以简单的方式实现快速的供电侧电流调节。
",G01R31/28,公开,模拟数字转换板卡技术,2.0,IPC分类号匹配: G01R31/28,"装置, 实现, 具有, 测试, 控制, 计算","控制, 测试"
US20240201248A1,US18540347,"一种用于仿真电动机的相电流以便测试功率电子器件控制器的测试装置。控制器被设置成驱动电动机并且可连接到测试装置。该测试装置包括电感仿真器，其通过功率电子电路模拟作为控制器的电负载的电动机，并且包括测试装置，该测试装置被设置为根据对控制器的输出处的输出电压的分析来将电感仿真器切换到不同的操作模式。还提供了一种用于借助于用于测试控制器的测试装置来仿真电动机的相电流的方法。
",G01R31/28,暂缺,模拟数字转换板卡技术,2.0,IPC分类号匹配: G01R31/28,"提供, 仿真, 包括, 方法, 模拟, 装置, 电路, 设置, 测试, 控制","控制, 测试"
US20240201247A1,US18535921,"一种用于仿真电动机的相电流以测试电力电子控制单元的测试装置。控制单元驱动电动机并且可以连接到测试装置。电感仿真器通过功率电子电路模拟作为控制单元的电负载的电动机，电感仿真器充当电流源。测试装置根据对控制单元的输出电压的分析来影响电感仿真器的相电流。测试装置执行分析，使得控制单元的输出电压与多个、优选三个电压范围相比较，并且根据控制单元的输出电压所处的电压范围来指定相电流的控制变量的相应范围，该控制变量是电感仿真器的控制电压。
",G01R31/28,公开,模拟数字转换板卡技术,2.0,IPC分类号匹配: G01R31/28,"仿真, 模拟, 装置, 电路, 测试, 控制","控制, 测试"
US20240192265A1,US18534626,"一种用于测试电力电子控制器的测试装置。电中间网络中的中间网络电流被减小，因为在中间网络中流动的中间网络电流由控制器和控制器确定控制器改变至少一个负载侧电力电子模块的至少一个控制值，使得当负载侧电力电子模块的接口被施加修改的控制值时，中间网络电流被减小。
",G01R31/28,公开,模拟数字转换板卡技术,2.0,IPC分类号匹配: G01R31/28,"装置, 控制, 测试","网络, 控制, 测试"
US20240192317A1,US18535888,"一种用于环境传感器系统的模拟装置和方法，所述环境传感器系统被配置为基于相应的信号回波来检测至少一个特定对象。接收装置，其被配置为接收从所述环境传感器系统发送的第一信号，并将其转换为第一操作信号。一信号路径，其连接至该接收装置，用以接收该第一操作信号，该信号路径系被配置成经由一第一信号处理，第二操作信号，其是第一操作信号和相应信号回波的函数。相应的信号回波由至少一个信号参数表征。提供第二信号处理以提供具有变化的至少一个信号参数。发射装置被配置为将第二操作信号转换为第二信号，并将第二信号发送到周围环境传感器系统。
",G01S7/40,公开,模拟数字转换板卡技术,1.0,关键词匹配: 信号转换,"提供, 检测, 方法, 模拟, 装置, 处理, 具有, 配置, 系统","检测, 传感器"
US11940556B2,US17469921,"一种用于测试距离传感器的测试装置包括：接收器，用于接收电磁自由空间波作为接收信号；模数转换器，被配置为在模拟模式中将接收信号转换为采样信号；信号处理单元，被配置为：延迟采样信号或调制采样信号以形成延迟采样信号或调制延迟采样信号；以及在采样信号上或在延迟采样信号上调制作为要被模拟的反射对象的特性运动轮廓的可预定的多普勒签名以形成调制采样信号或调制延迟采样信号；数模转换器，被配置为将调制或调制延迟采样信号转换为模拟反射信号；以及发射器，被配置为辐射模拟反射信号或从模拟反射信号导出的模拟反射信号作为输出信号。
",G01S7/40,授权,模拟数字转换板卡技术,5.0,"关键词匹配: 模数转换, 数模转换, 转换器, 采样, 信号转换","包括, 装置, 处理, 配置, 测试, 模拟","传感器, 测试"
EP4199355A1,EP21214298.8,"在用于电流均匀分布的电子电路装置（1）中，其中多个MOSFET（T11，T21）并联连接，在线性操作中应增加最大功率损耗。这是通过电路装置（1）包括第一晶体管（T12）和第二晶体管（T22）来实现的，在第一MOSFET（T11）和第一晶体管（T12）之间建立热耦合（19），也在第二MOSFET（T21）和第二晶体管（T22）之间。
",H03K17/12,权利终止,模拟数字转换板卡技术,2.0,IPC分类号匹配: H03K17/12,"装置, 实现, 电路, 包括",通用
US20230076301A1,US17469921,"一种用于测试距离传感器的测试装置，包括：接收器，用于接收电磁自由空间波作为接收信号；模数转换器，被配置为在模拟模式下将所述接收信号转换为采样信号；信号处理单元，被配置为：延迟所述采样信号或调制采样信号以形成延迟采样信号或调制延迟采样信号；以及基于采样信号或基于延迟采样信号调制作为要被模拟的反射对象的特征运动轮廓的可预定多普勒特征，以形成调制采样信号或调制延迟采样信号；数模转换器，被配置为将调制的或调制的延迟采样信号转换为模拟反射信号；以及发射器，其被配置为辐射模拟反射信号或从模拟反射信号导出的模拟反射信号作为输出信号。
",G01S7/40,授权,模拟数字转换板卡技术,5.0,"关键词匹配: 模数转换, 数模转换, 转换器, 采样, 信号转换","包括, 装置, 处理, 配置, 测试, 模拟","传感器, 测试"
US20220082658A1,US17419914,"一种测试距离传感器的方法，包括：接收电磁自由空间波作为接收信号；从中产生模拟电磁反射信号；将反射信号的反射频率偏移小于接收信号的信号带宽的多普勒频率；将所述接收信号转换为具有小于所述接收信号的接收频率的第一工作频率的第一工作信号；将第一工作信号转换为具有第二工作频率的第二工作信号，其中第一和第二工作频率之间的差至少与信号带宽加上多普勒频率一样大；将所述第二工作信号转换为具有第三工作频率的第三工作信号，所述第三工作频率对应于被所述多普勒频率偏移的第一工作频率；通过转换频率增加第三工作信号；并发射第三个工作信号。
",G01S7/40,授权,模拟数字转换板卡技术,1.0,关键词匹配: 信号转换,"包括, 方法, 具有, 测试, 模拟","传感器, 测试"
CN105840331B,CN201610070579.8,"本发明涉及一种用于通过控制单元(3)计算和输出控制脉冲(2)的计算机实现的方法(1)，其中，控制单元(3)具有第一运算单元(4)和第二运算单元(5)，并且控制脉冲(2)由控制单元(3)输出给内燃机(6)。控制脉冲(2)的计算由此优化，使得第一运算单元(4)借助于内燃机(6)的过去的状态数据(Z<Sub>E,old</Sub>)以第一采样率计算具有用于多个未来的控制脉冲(2)的触发信息(s<Sub>r</Sub>,s<Sub>f</Sub>)的控制脉冲图形(S)并且将所计算的控制脉冲图形(S)传输至第二运算单元(5)；第二运算单元(5)以比第一运算单元(4)的第一采样率大的第二采样率借助于内燃机(6)的当前状态数据(Z<Sub>E,new</Sub>)校正当前要输出的控制脉冲(2)的触发信息(s<Sub>r</Sub>,s<Sub>f</Sub>)；并且控制脉冲(2)由控制单元(3)基于所校正的触发信息(s<Sub>r</Sub>',s<Sub>f</Sub>')输出给内燃机(6)。
",F02D41/26,授权,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"方法, 实现, 具有, 控制, 计算",控制
JP2016142270A,JP2016019028,"要解决的问题：提供计算并输出方法[1]控制脉冲[2]控制装置[3]通过计算机程序实现，并提供该控制装置包括第一核算单位[4]及第二核算单位[5]使控制脉冲以被输出，来自控制装置的内燃机。
SOLUTION：计算控制脉冲的方式执行一第一计算单元控制脉冲[S] [sr图案包括触发信息，sf]相对于多个未来控制脉冲以第一采样率的关于前一状态数据[ZE，old]内燃机，并将其中的第二单元，该第二核算单位校正触发信息将目前控制脉冲输出的第二采样速率的数据根据现时状态[ZE，new]的内燃机，并控制从控制装置到内燃机根据修正的触发信息[sr '，平方英尺']，然后优化措施。
所选附图中：图2
COPYRIGHT：( C ) 2016、JPO&INPIT
",F02D45/00,授权,模拟数字转换板卡技术,1.0,关键词匹配: 采样,"提供, 包括, 方法, 装置, 实现, 控制, 计算",控制
CN102472775B,CN201080027582.2,"一种测量电气变量的设备，包括外部测量端子能与要测量的电子装置连接的外部测量端子(1)，所述设备包括转换器(3)，转换器(3)被设置为将要测量的不同变量类型的经由测量端子(1)测得或能测量的多个测量变量转换为单个预先指定或能预先指定的变量类型的相应电气测量变量，设置有用于控制转换器(3)的控制装置(2)，借助于控制能选择要利用转换器测量的变量类型中至少一个，转换器(3)对于至少两个要测量的变量类型分别具有至少一个独立的输入级(3a，3b)，要测量的变量类型的测量变量借助于输入级能被采集并且能被转换为预先指定或能预先指定的变量类型，预先指定或能预先指定的变量类型的转换后的测量变量出现在输入级(3a，3b)的信号输出端(5a，5b)上，其中针对至少两个不同的标准通过以下方式对在测量端子(1)上提供的电信号并行地进行检查：至少两个用于不同的要测量的变量类型的输入级(3a，3b)在时间上并行地工作，且同时分别在信号输出端(5a，5b)提供相同的预先指定或能预先指定的变量类型的各自转换后的测量变量。本发明还涉及该设备的运行方法。
",G01R15/12,授权,模拟数字转换板卡技术,1.0,关键词匹配: 转换器,"提供, 设备, 包括, 方法, 装置, 具有, 设置, 控制",控制
EP1880480A1,EP06721508.7,"本发明涉及一种通信方法、系统和信号，并且尤其涉及一种用于在数字信号中传送信息的方法和系统。它尤其适用于基于卫星或地面分组的多用户无线电通信系统。公开了一种用于在包括数据符号的数字信号中传送信息的方法，其中信息被编码在分布于数据符号之间的导频符号的序列特性中，使得接收机能够确定序列特性并且能够检索所传送的信息。本发明允许信息被编码到导频符号的序列特性中，而不是依赖于将这样的信息调制到导频符号自身上。这允许比迄今可能的更大数量的信息被传送，并且该技术比现有技术更耐大的频率误差。
",H04B1/76,权利终止,模拟数字转换板卡技术,1.0,关键词匹配: 数字信号,"方法, 系统, 包括",通信
