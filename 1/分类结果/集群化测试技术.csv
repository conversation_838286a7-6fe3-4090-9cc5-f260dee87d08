﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,当前法律状态,技术领域,分类得分,分类理由,技术手段,应用场景
US12341512B2,US17739575,"一种用于编程FPGA的方法，其中提供了库，该库包括基本操作和用于库的每个基本操作的特定等待时间表。每个等待时间表指示在FPGA上执行期间，对于FPGA的多个时钟速率和对于特定操作的多个输入位宽度的特定操作的等待时间，这取决于特定操作的输入位宽度和FPGA的时钟速率。定义了一条数据路径，该数据路径指示在FPGA上库的至少两个基本操作的连续执行。检测并相加对于等待时间表中的多个不同时钟速率的数据路径的特定基本操作的特定输入位宽度给出的等待时间，然后选择时钟速率之一。
",G06F30/331,授权,集群化测试技术,2.0,IPC分类号匹配: G06F30/331,"检测, 提供, 方法, 包括",检测
CN113039548B,CN201980075229.2,"本发明示出并说明了一种由计算机实现的、用于借助至少一个计算单元(3)对电路(2)进行仿真的方法(1)，电路(2)具有包括开关元件(T<Sub>i</Sub>)的电路部件(L、R、T<Sub>i</Sub>)，开关元件(T<Sub>i</Sub>)可以占据导通的开关状态或不导通的开关状态，电路(2)通过数学表达MR描述并且在计算单元(3)上通过数值求解描述整体开关状态(SST<Sub>i</Sub>)的数学表达(MR)针对每个整体开关状态(SST<Sub>i</Sub>)计算电路。由此能以尽可能简单的方式在数学表达中表达并且数值计算有开关元件的整体开关状态(SST<Sub>i</Sub>)的多个、最为有利的情况下所有组合并且因此有多个、最为有利的情况下所有整体开关状态(SST<Sub>i</Sub>)的电路(2)，即，在电路中的导通的开关元件(T<Sub>i</Sub>)由开关线圈(7)代表，在电路(2)中的不导通的开关元件(T<Sub>i</Sub>)则由开关电容器(8)代表，开关线圈(7)和开关电容器(8)的电气特性通过结构一致的、时间离散的开关方程i<Sub>S,k</Sub>描述，使得在为开关元件(T<Sub>i</Sub>)使用结构一致的、时间离散的开关方程i<Sub>S,k</Sub>的情况下，产生了针对电路(3)的所有整体开关状态(SST<Sub>i</Sub>)的与开关状态无关的、时间离散的状态空间表达H、Φ、C<Sub>d</Sub>、D<Sub>d</Sub>，并且在计算单元(3)上基于针对电路(2)的所有整体开关状态(SST<Sub>i</Sub>)的与开关状态无关的、时间离散的状态空间表达H、Φ、C<Sub>d</Sub>、D<Sub>d</Sub>执行仿真。
",G06F30/367,授权,集群化测试技术,2.0,IPC分类号匹配: G06F30/367,"仿真, 包括, 方法, 实现, 具有, 电路, 计算",通用
US20240385953A1,US18667582,"一种测试系统，用于应用场景的资源优化参数变化的方法，以测试至少一个被测真实和/或虚拟系统和/或显示测试结果。提供至少一种场景，其中场景由至少一个参数定义，并且其中参数由至少一个数值和/或文本值配置，其中数值和/或文本值可由附加的数值和/或文本值配置。执行至少一个基于场景的测试，其中场景由配置的参数定义并随配置的参数而变化。输出测试结果。
",G06F11/36,暂缺,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G06F11/36,"提供, 方法, 配置, 测试, 系统",测试
US20240289526A1,US18573457,"一种模拟器，其被配置用于模拟具有至少一个开关的电力电子电路，所述开关用于具有至少一个计算机的测试台。该模拟器包括状态识别装置，该状态识别装置被配置为识别至少一个开关的状态，其中该状态基于强制或自然开关操作，每个开关操作具有用于至少一个电开关的至少一个第一电参数的至少两个状态条件。模拟器被配置为基于状态输出功率电子电路的至少一个输出值。
",G06F30/3308,暂缺,集群化测试技术,2.0,IPC分类号匹配: G06F30/3308,"包括, 装置, 具有, 配置, 电路, 测试, 模拟, 计算",测试
EP4375869A1,EP22209730.5,"本发明涉及一种用于创建和提供具有至少一个FPGA总功能（2）的FPGA模型（1）的FPGA构建结果的方法，该方法具有以下处理步骤：（a） FPGA子系统（5）的识别，从而FPGA模型（1）的FPGA功能可以通过FPGA子系统来配置；b） 识别FPGA模型（1）的用于在处理器上执行的预缩放子系统（6）和缩放后子系统（7）；c） 在缩放前子系统（6）和缩放后子系统（7）中识别内部和外部接口，由此内部接口确保FPGA模型（1）内的数据流，而外部接口确保远离FPGA模型（2）的数据流；d） 生成FPGA整体功能（2），e） 从生成的FPGA总功能生成FPGA构建结果（2），其中FPGA构建结果包括单个总容器文件，f） 将FPGA构建结果提供给另一个应用程序以确定整个FPGA模型（1）和处理器模型（3）的功能。
通过这种方式，提供了一种对FPGA构建结果进行建模的方法，这导致了统一的FPGA模型（1），从而可以使FPGA构建结果的重用变得安全和容易。
",G06F30/323,实质审查,集群化测试技术,2.0,IPC分类号匹配: G06F30/323,"提供, 包括, 方法, 处理, 具有, 配置, 生成, 模型, 系统",通用
EP4307121A1,EP23176378.0,"本发明涉及一种用于配置用于测试机动车辆的车辆功能的虚拟测试系统的计算机实现的方法，-根据第一条件（B1）对具有最高置信度值（K）的至少一个待测试的附加伪像（16）的输出端口（14）的分配（S3a），根据第二条件（B2）创建（S3b）最高置信度（K）输出端口（14）的列表，或者根据第三条件（B3）不分配（S3c）输出端口n待测试的至少一个附加工件（16）的输出端口（14）。本发明还涉及一种计算机实现的方法，用于提供用于配置用于测试机动车辆的车辆功能的虚拟测试系统的经过训练的机器学习算法（A1）。
",G06F11/36,权利终止,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G06F11/36,"提供, 方法, 实现, 具有, 配置, 测试, 算法, 系统, 计算","机动车, 车辆, 测试"
US20230419009A1,US18212368,"一种基于节点形式的行为模型通过实时平台模拟电路的方法，所述行为模型具有阻抗矩阵M或在状态空间中的面向拓扑的行为模型，所述阻抗矩阵M具有矩阵A、B、C和D，并且为了这个目的，描述电路的至少一个阻抗矩阵M或用于描述电路的矩阵A、B、C和D的至少一个集合被存储在实时平台上。因此，结果实现了更有效的操作序列。
",G06F30/3308,暂缺,集群化测试技术,2.0,IPC分类号匹配: G06F30/3308,"方法, 平台, 实现, 具有, 电路, 模型, 模拟",通用
EP4283333A1,EP23157567.1,"本发明涉及一种用于传输用于激光雷达传感器（12；112）的测试系统（1；101）的合成生成的光信号（S）的光学单元（10；110），具有承载装置（14；114）以容纳至少一根光纤电缆（16；116），其中，所述载体装置（14；114）具有与所述至少一根光纤（16；116）插入其中的载体装置（14,114）的一个端面（14a；114a）正交地形成的至少一个开口，并且形成（18；118）连接到所述载体装置（14，其中所述载体装置（14；114）和所述至少一个微透镜（18；118）的彼此面对的面（14a；114a，18a；118a）各自是平面的。本发明还涉及一种测试系统（1；101）和用于生产光学单元（10；110）的方法。
",G01S7/497,实质审查,集群化测试技术,1.0,关键词匹配: 测试系统,"方法, 装置, 具有, 测试, 生成, 系统","激光, 雷达, 传感器, 测试"
US20230205937A1,US17996508,"一种用于确定像素聚光灯的总光分布的方法，包括：提供纹理，该纹理包括具有坐标的二维阵列；为多个单独光源中的每个单独光源提供最大通电单独光分布；考虑所有最大通电单独光分布和纹理来确定最大通电数据结构；为每个单独光源提供相对通电值；以及考虑最大通电数据结构和单独光源的相对通电值来确定总光分布。
",G06F30/10,暂缺,集群化测试技术,2.0,IPC分类号匹配: G06F30/10,"提供, 方法, 具有, 包括",通用
EP4202691A1,EP22207368.6,"本发明涉及一种用于确定系统参数（12、14、24、26）的兼容性的计算机实现的方法和系统，所述系统参数用于对用于机动车辆的至少部分自主引导的设备（8）的虚拟测试的测试执行，包括基于规则的算法（a）对至少一个第一系统参数（12）的应用（S2），以确定具有待测试系统（10a）的组中的至少一个第二系统参数（14）与至少一个所述第一系统参数的兼容性，测试环境（10b），测试系统（10c），测试场景（10d）和/或待测试的车辆类型（10e），其中所述基于规则的算法（A）基于硬件和/或软件组件（16）来确定兼容性，所述硬件和/或者软件组件执行用于所述机动车辆的至少部分自动驾驶的设备（8）的虚拟测试和/或提供测试数据。
",G06F11/36,实质审查,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G06F11/36,"提供, 设备, 包括, 方法, 实现, 具有, 测试, 算法, 系统, 计算","机动车, 驾驶, 车辆, 测试"
EP4198805A1,EP21215702.8,"当在图形开发环境中创建（2）分层框图（1）时，模型操作的执行时间将被缩短，以实现更高性能的、反应式的图形建模。这是通过指定具有至少一个子系统块（S1）的计算机实现的方法来实现的，其中所述子系统块（S1）位于第一层级上，并且另外的子系统块是（S1.1、S1.2、S1.3…），其中所述子系统块（S1）由位于下级层级的子系统块组成，所述子模块（S1）是可参数化的并且具有至少一个回调函数，其中回调函数定义响应于特定建模活动而执行的命令，其中，为了降低子系统块（S1）的层级（S1.1、S1.2、S1.3…），为子系统块创建至少一个层级黑盒（B1.1、B1.2、B1.3…），由此从最低层级向上进行缩减，其中该程序包括以下步骤：自动创建子系统块（S1.1、S1.2、S1.3…）的分层黑盒（B1.1、B1.2、B1.3…），其中，黑盒（B1.1、B1.2、B1.3…）以这样的方式设置，即用户选择相应的层次级别，从而通过回调功能进行自动创建。
",G06F30/30,实质审查,集群化测试技术,2.0,IPC分类号匹配: G06F30/30,"包括, 方法, 实现, 具有, 设置, 模型, 系统, 计算",通用
US20230177241A1,US18075791,"一种用于提供用于基于传感器数据的数据集的场景数据来确定类似场景的机器学习算法的计算机实现的方法，其中将优化算法应用于由第一机器学习算法输出的传感器数据的数据集的第一次增加的特征表示，其中优化算法对由第二机器学习算法输出的传感器数据的数据集的第二次增加的特征表示进行近似。本发明还涉及一种用于基于传感器数据的数据集的场景数据来确定类似场景的方法以及一种训练控制器。
",G06F30/27,公开,集群化测试技术,2.0,IPC分类号匹配: G06F30/27,"提供, 方法, 实现, 算法, 控制, 计算","传感器, 控制"
US20230131446A1,US17996950,"本发明涉及一种用于产生真实像素前照灯的控制的方法，借助该控制可以基于场景的可被照亮的区域的不同区域的特征来控制场景的可被像素前照灯照亮的区域的照明强度的二维分布。这提供了这样的方法，该方法允许依赖于光功能的物理选择区域的自动捕获以及对物理选择区域影响的像素的光强的自动改变。
",G06F30/27,暂缺,集群化测试技术,2.0,IPC分类号匹配: G06F30/27,"提供, 方法, 控制",控制
US20230124300A1,US17908250,"本发明涉及一种用于生成可执行模拟程序并且模拟待测试的控制设备(10)与另外的控制设备(20、30、40)之间的控制设备通信(50)的方法，所述待测试的控制设备(10)具有其发送接口和接收接口的描述，并且所述方法包括以下步骤：将发送接口和接收接口的描述传送到数据库；借助于数据库从描述中识别待测试的控制设备(10)的接口；针对待测试的控制设备的每个识别出的发送接口生成作为所述另外的控制设备的接收接口的接收接口元件；针对待测试的控制设备的每个接收接口生成作为所述另外的控制设备的发送接口的发送接口元件；将所生成的接口元件(24、26、28、34、48)存储在数据库中；借助于数据库提供用于模拟控制设备通信(50)的配置，所述配置包括所生成的接口元件(24、26、28、34、48)；使用所述配置生成用于控制设备通信(50)的可执行模拟程序。
",G05B23/02,授权,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 设备, 包括, 方法, 控制, 具有, 配置, 测试, 生成, 模拟","通信, 控制, 测试"
EP4148613A1,EP22194640.3,"本发明的目的是一种用于将具有可在FPGA上执行的操作的另一电路组件（1）添加到FPGA配置（3）的方法，其中，所述FPGA配置（3）已经具有至少一个本地分布在所述FPGA配置（3）中的现有电路组件（2），所述现有电路组件具有可在FPGA上执行的操作，所述步骤包括：合成所述另一电路组件（1）以获得另一网表，以及考虑FPGA配置（3）中的至少一个现有电路组件（2）的进一步网表的分布式布置。
",G06F30/34,实质审查,集群化测试技术,2.0,IPC分类号匹配: G06F30/34,"包括, 方法, 具有, 配置, 电路",通用
US20220414302A1,US17358028,"用于使用计算机模拟真实氢燃料电池系统的气体流动动力学的系统和方法，其中所述真实氢燃料燃料电池系统包括气体容器体积网络，所述气体容器体积通过气体输送线互连。该方法包括定义体积元素和流道类别，为每个流道实例定义多个体积实例和多个流道实例，为流道实例创建定义源容器体积和目的容器体积的第一互连表示，其中所述第一互连表示模拟真实氢燃料电池系统的气体容器体积网络的一部分，所述热力学状态表示真实氢燃料电池系统的气体容器体积网络的每个容器体积中的热力学参数。
",G06F30/28,授权,集群化测试技术,2.0,IPC分类号匹配: G06F30/28,"包括, 方法, 系统, 模拟, 计算",网络
US20220358032A1,US17735219,"一种用于为测试过程自动提供建议的计算机实现的方法，其中，建议由至少一个建议生成器确定，并且用于测试和/或模拟的建议生成器被手动和/或自动选择，其中，建议生成器监视至少两个测试运行，使得在测试运行中检测至少一个事件，并且导出至少一个建议，其中，建议生成器在测试运行期间由建议生成器机构自动执行，并且由建议生成器确定的建议被提供给测试系统和/或用户。
",G06F11/36,暂缺,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G06F11/36,"提供, 检测, 方法, 模拟, 实现, 测试, 生成, 系统, 计算","检测, 测试"
EP4086773A1,EP22163414.0,"本发明涉及一种用于自动提供用于测试过程的提示的计算机实现的方法，其中，所述提示由至少一个提示提供者确定，并且其中，所述用于测试和/或模拟的提示由手动和/或自动选择，其中，所述提示提供者观察至少两个测试执行，使得在所述测试执行中识别至少一个事件并且推导出至少一个提示，其中，所述提示提供者在所述测试执行期间由提示提供者机制自动执行，并且其中，所述提示由所述提示提供者确定为所述测试系统和/或被提供给用户。
",G06F11/36,实质审查,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G06F11/36,"提供, 方法, 模拟, 实现, 测试, 系统, 计算",测试
EP4060377A1,EP22159475.7,"本发明涉及一种用于隔离用于LiDAR传感器(12)的测试系统(1)的触发信号的装置(10)和方法，其中在设置在光学元件(20a，20b)之前的触发信号(TS)的信号路径(SP)，该光学元件(20a，20b)被设计为允许触发信号(TS)通过，并且被反射尤其是在表面(22)上的光学元件(20a，20b)的背反射(RTS)被通过的触发信号(TS)至少部分地吸收。本发明还涉及一种用于LiDAR传感器(12)的测试系统。
",G01S7/497,实质审查,集群化测试技术,1.0,关键词匹配: 测试系统,"方法, 装置, 设置, 测试, 系统","传感器, 测试"
US20220180032A1,US17535743,"一种用于电动机的实时模拟的方法包括：通过计算机系统在包括通过信号连接的至少两个块的图形模型中对电动机进行建模。该图形模型包括至少两个矩阵运算，该至少两个矩阵运算从至少一个多维输入信号产生多维输出信号。计算机系统包括操作者计算机和连接到操作者计算机的实时计算机，其中操作者计算机执行图形建模环境，并且实时计算机具有至少一个可编程逻辑设备。
",G06F30/331,暂缺,集群化测试技术,2.0,IPC分类号匹配: G06F30/331,"设备, 包括, 方法, 模拟, 具有, 模型, 系统, 计算",通用
EP3869380A1,EP20157861.4,"本发明涉及一种用于为不同类别的逻辑元件（12）规划分区设计（14，16）的过程，该逻辑元件（12）的预定位置包括可编程网格布置（10）和多个程序例程，包括至少一个初始程序例行程序和至少一个进一步的程序例行程序。规定了以下程序步骤：将可编程网格布置（10）的第一分区（14）分配给第一程序例程，并将可编程网格布置的至少一个分区（16）分配给至少一个程序例程（S1），确定每一类逻辑元件（12）（S2）对第一程序例程的需求，将该需求与对应类（S3）逻辑元件（12）的第一分区（14）的资源进行比较；以及通过改变这些分区（14，16）之间的分区边界（18）的路线，从下一个分区（16）或其他分区中的至少一个分区到第一个分区（14）的至少一个逻辑元素（12）对应类的传输，如果对该类逻辑元件的第一程序例程的需求超过第一分区（14）（S4）的相应资源，其中分区边界（18）在至少一个部分中改变其路线之前和/或之后是圆形的。
",G06F30/337,实质审查,集群化测试技术,2.0,IPC分类号匹配: G06F30/337,包括,通用
EP3244326B1,EP16168899.9,"一种用于创建从FPGA源代码和至少一个影子寄存器生成的FPGA网表的方法。FPGA源代码定义至少一个功能和至少一个信号。影子寄存器被分配给至少一个信号，并且被布置和提供为在运行时存储所分配的信号的值。提供了用于在运行时读出所存储的信号值的选项。在FPGA源代码中定义的功能不被影子寄存器改变。FPGA执行FPGA源代码描述的功能，并且提供影子寄存器与FPGA源代码中描述的功能解耦。经由该去耦合，当FPGA源代码中描述的功能正在执行时，影子寄存器保持在去耦合时存储的信号值。
",G06F30/331,授权,集群化测试技术,2.0,IPC分类号匹配: G06F30/331,"生成, 提供, 方法",通用
US20200311330A1,US16827796,"一种使用计算机的动态系统的基于计算机的仿真或控制的方法，包括：由可编程逻辑器件循环地接收至少一个输入信号；由可编程逻辑器件计算至少一个矩阵乘法；以及由可编程逻辑器件输出至少一个输出信号。可编程逻辑器件的配置包括：矩阵的至少两个元素的块与向量的至少一个输入信号相关元素的并行乘法，以及用于乘法结果的加法器树。矩阵的连续块被临时存储在管线中并被顺序处理。根据至少一个系统方程的参数的数量和/或值来确定块的目标数量和目标加法级。基于目标块数和达到的目标加法器级，终止当前周期的块处理。
",G06F30/343,授权,集群化测试技术,2.0,IPC分类号匹配: G06F30/343,"仿真, 包括, 方法, 控制, 处理, 配置, 系统, 计算",控制
US20190196925A1,US16226730,"用于配置一适合于测试一电子控制单元的测试系统的一组态系统，其中一结构示图具有多个等级制度元素，一等级制度元素也具有一等级制度元素或多种等级制度元素或没有等级制度元素。等级制度元素具有一标识符，其中一等级制度元素具有端口或没有端口，其中至少一个等级制度元素被分配给测试系统的一功能性质被配置，其中在一扩充视图模式中，等级制度元素最少地可被显示在部分地嵌套的和端口，并且在另一个下面显示一个标识符，其中在一至少部分地叠放视图模式中，显示第一组等级制度元素，使得并排地显示标识符，其中端口和标识符保持可见的，并且等级制度元素的等级关系保持所显示的。
",G06F11/26,申请终止,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G06F11/26,"具有, 配置, 系统, 测试, 控制","控制, 测试"
CN104142678B,CN201410180526.2,"本发明涉及一种用于在模拟器中利用模拟环境(3)测试虚拟控制仪(2)的至少一部分的测试装置(1)，其具有虚拟控制仪和模拟环境(3)，其中，所述虚拟控制仪包括至少一个带有至少一个外部数据接口(7)的软件组件(4、5、6)，其中，所述模拟环境包括至少一个数据接口(8)以用于与所述虚拟控制仪(2)至少间接地进行数据交换；特别是利用虚拟控制仪能简单地进行电气错误模拟，减少的相关性由如下方式达到：在虚拟控制仪和模拟环境之间设有虚拟控制仪插脚单元(9)和虚拟影响单元(13)，它们通过虚拟控制仪插脚单元的虚拟控制仪插脚(12)传输至少一个虚拟的物理控制仪信号，所述虚拟影响单元输出受影响的虚拟的物理控制仪信号。
",G05B23/02,授权,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"包括, 控制, 装置, 具有, 测试, 模拟","控制, 测试"
CN107037803A,CN201710027205.2,"本发明描述和阐述了一种用于仿真残余总线控制仪组合的计算机实现的方法，其中，所述残余总线控制仪组合包括至少两个经由总线系统相连接的残余总线控制仪，并且所述残余总线控制仪组合与至少一个另外的控制仪经由总线系统相连接，其中，描述残余总线控制仪的通信关系，基于所述通信关系生成用于仿真残余总线控制仪的程序代码，并且借助于程序代码的可执行的文本在仿真计算机上对残余总线控制仪组合进行仿真。通过如下方式能实现对残余总线控制仪组合的简化和灵活的仿真，即，为残余总线控制仪生成唯一的共同的残余总线控制仪模型作为用于仿真残余总线控制仪的程序代码。此外，本发明涉及一种用于仿真残余总线控制仪组合的设备。
",G05B23/02,申请终止,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"设备, 仿真, 包括, 方法, 控制, 实现, 生成, 模型, 系统, 计算","通信, 控制"
CN106997198A,CN201710013965.8,"本发明涉及一种用于自动配置为测试控制器而设置的测试设备的方法，在测试设备中执行技术系统的第一模型和第二模型，测试设备包括用于执行第一或第二模型的FPGA和用于执行第一或第二模型的CPU，要测试的控制器在执行模型时连接到测试设备上并且实现在所述控制器和/或第一模型和/或第二模型之间的数据交换。所述方法具有下列步骤：为第一模型分配个性化的第一采样率并且为第二模型分配个性化的第二采样率；配设第一模型以用于在CPU或者FPGA上执行并且配设第二模型以用于在CPU或FPGA上执行；以及自动配置测试设备，以用于在FPGA或CPU上以第一采样率执行第一模型并且在FPGA或CPU上以第二采样率执行第二模型。本发明还涉及一种为测试控制器而设置的测试设备。
",G05B23/02,申请终止,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"设备, 包括, 方法, 控制, 实现, 具有, 配置, 设置, 测试, 模型, 系统","控制, 测试"
EP2866111A1,EP13190448.4,"本发明的目的是通过测试环境（2）测试控制装置（1）的装置，基于计算机的综合测试管理工具（5），其中，测试管理工具（5）被设计用于基于模型的开发和/或管理至少一个被设计为用于测试控制装置（1）的数据结构（3）的测试计划，并且测试计划（3）具有用于触发测试计划（3）的执行的至少一个测试（6）和一个启动条件（8），基于计算机的测试执行控制工具（4），其中，测试执行控制工具（4）设计用于在满足启动条件（8）和基于计算机的数据库（9）时，在测试环境（2）上启动测试计划（3）的执行，其中，数据库（9）被设计为将测试计划存储为数据结构（3），并通过测试管理工具（5）和测试执行控制工具（4）共享对测试计划（3）的同时访问。
",G05B23/02,授权,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"装置, 具有, 测试, 模型, 工具, 控制, 计算","控制, 测试"
EP2770434A1,EP13000867.5,"在由控制设备测试系统通过模型模拟的环境中，利用控制设备测试系统测试控制设备(1) 。针对部分硬件部件或全部因模拟而异的硬件部件，读出唯一地和数字地识别硬件部件(1，4，8)的部件信息。存储所读出的识别部件信息。通过测试系统的计算机(5.1，5.2)或与测试系统连接的控制计算机来进行比较。
",G06F11/22,授权,集群化测试技术,3.0,关键词匹配: 测试系统; IPC分类号匹配: G06F11/22,"设备, 模拟, 系统, 测试, 模型, 控制, 计算","控制, 测试"
CN101441473B,CN200810175669.9,"本发明涉及一种测试至少一个电子控制系统(1)的测试设备(2)和用于操作测试设备的相应方法，其中具有一个可编址的物理存储器，测试设备(2)适合且被设置为通过数据通道与待测试的控制系统(1)建立连接。测试设备还适合且被设置为计算至少一个环境模型(21)和执行至少一个测试模型(22)。其中环境模型(21)可以通过环境模型变量来描述，环境模型变量按固定的物理地址存储在存储器的存储位置处。测试设备(2)还包括一个对应单元(23)，在该单元中以可读取的方式存储了所有或部分环境模型变量与存储器的相应物理地址之间的对应关系。
",G05B23/02,未缴年费,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"设备, 包括, 方法, 控制, 具有, 设置, 测试, 模型, 系统, 计算","控制, 测试"
CN202041846U,CN201120010554.1,"本实用新型涉及一种用于仿真系统经由不同总线与电子控制单元通信的设备，包括至少一个控制器、多个收发器和用于总线线路的接头，其特征在于，具有用于经由所述接头的通信的多个收发器，并且所述多个收发器能够分别与所述总线线路连接。本实用新型的一个实施例解决的一个问题是减少不必要的硬件和空间消耗。根据本实用新型的一个实施例的一个用途是用同一仿真系统对经由不同总线系统与其他控制装置通信的不同控制装置进行测试。
",G05B23/02,期限届满,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"设备, 仿真, 包括, 控制, 装置, 具有, 测试, 系统","通信, 控制, 测试"
CN201837890U,CN201020552641.5,"本公开涉及一种用于测试电气元件的设备和仿真器，该设备包括：就至少一种电气特性而言相互不同的至少两个连接装置(3，4，5)，用于选择连接装置(3，4，5)的选择装置，和能借助于选择装置与至少一个连接装置(3，4，5)导电地连接的用于生成仿真信号的仿真装置(1)和用于连接电气元件的测试装置(2)。本公开的一个实施例解决的一个问题是构建一种能提供更好测试结果的电气元件测试设备，依据该项公开所实现的电气元件测试设备能够以简单的方式将由于连接装置(3，4，5)的寄生特性而形成的信号失真情况最小化，从而提高测试的精确性。根据本公开的一个实施例的一个用途是对诸如汽车或者自动化装置的控制系统进行检测。
",G05B23/02,期限届满,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"提供, 设备, 仿真, 包括, 检测, 控制, 装置, 实现, 测试, 生成, 系统","检测, 汽车, 控制, 测试"
EP2083339A1,EP08100972.2,"本发明涉及一种方法，所述方法包括使用两个双向交换系统(1a，1b)来设计测试模型(2a)和/或特定测试部分(3a，3b)，其中所述系统之一是设置用于设计环境模型的仿真处理器。电子控制系统(5)使用数据信道(6)与所述仿真处理器连接。所述测试模型被设计在所述系统(1a)中。部分测试模型被设计在所述系统(1b)中和/或通过与所述系统(1b)的通信而被传输到所述系统(1b) 。对于测试装置还包括独立的权利要求，所述测试装置包括用于测试电子控制系统的两个双向交换系统。
",G05B23/02,撤回,集群化测试技术,2.0,IPC分类号匹配: G05B23/02,"仿真, 包括, 方法, 控制, 装置, 处理, 设置, 测试, 模型, 系统","通信, 控制, 测试"
