# dSPACE HIL相关技术专利详细分析报告

## 概述

本报告对dSPACE公司的432项专利进行了技术分类分析，特别关注HIL（硬件在环）相关技术的专利布局情况。

## HIL相关技术专利总览

HIL相关技术专利总数: 298 条 (69.0%)

## HIL硬件在环仿真技术

**专利数量**: 62 条

**技术描述**: HIL硬件在环仿真相关技术，包括实时仿真、硬件模拟器等

**法律状态分布**:
- 授权: 21 条
- 暂缺: 15 条
- 实质审查: 8 条
- 撤回: 6 条
- 公开: 5 条
- 期限届满: 2 条
- 申请终止: 2 条
- 驳回: 2 条
- 未缴年费: 1 条

**重点专利**:

### US12216967B2
- **申请号**: US17239068
- **IPC分类号**: G06F30/20
- **法律状态**: 授权
- **分类理由**: 关键词匹配: 模拟器, 实时仿真; IPC分类号匹配: G06F30/20
- **技术手段**: 提供, 仿真, 包括, 方法, 实现, 生成, 模型, 模拟, 计算
- **应用场景**: 通用
- **技术摘要**: 一种计算机实现的方法，用于通过模拟器算术单元实时模拟特定电动机的操作，该模拟器算术单元包括在其上实现通用电动机模型的可编程逻辑器件。该方法包括：提供对应于通用电动机模型的通用方程组；接收与待模拟的特定电机对应的用于通用方程组的特定信息，并将该信息输入到通用方程组中；生成包含用于计算特定电机的操作的矩阵操作所需的算术运算中的至少一些的特定库；在通用电机模型中执行对实时仿真特定电机的操作所需的特定库的...

### EP4369240A1
- **申请号**: EP22206970.0
- **IPC分类号**: G06F30/20
- **法律状态**: 撤回
- **分类理由**: 关键词匹配: 模拟器, 仿真器; IPC分类号匹配: G06F30/20
- **技术手段**: 设备, 仿真, 检测, 方法, 控制, 模拟
- **应用场景**: 检测, 数据传输, 传感器, 车辆, 控制
- **技术摘要**: 提出了一种用于将车辆的虚拟环境（EGO）的图像数据（BD）输出到控制单元（SG）的模拟设备（SV）和方法，其被设计为根据图像数据（BD.）执行至少一个车辆功能。它规定：环境模拟器（US），其被设计成模拟所述车辆（EGO）的环境并将关于所述环境的数据（Da）传输到传感器模拟器（SeSi），所述传感器模拟器被设计成根据关于所述周围的数据（Da）来模拟所述车辆（EGO）的至少一个传感器（SE1、SE2、...

### US20230359785A1
- **申请号**: US18143672
- **IPC分类号**: G06F30/20
- **法律状态**: 暂缺
- **分类理由**: 关键词匹配: 模拟器, 环路; IPC分类号匹配: G06F30/20
- **技术手段**: 方法, 处理, 实现, 具有, 电路, 模型, 模拟, 计算
- **应用场景**: 通用
- **技术摘要**: 一种用于借助于环路硬件模拟器的至少一个处理单元来模拟电驱动器的计算机实现的方法。所述电驱动器的模型具有：逆变器，所述逆变器由具有至少一个半桥的直流电压源供电，所述半桥具有至少两个半导体开关；以及电动机，所述电动机具有电绕组电阻和绕组电感。具有所述半桥的中心抽头电压的中心抽头借助于具有供给线路电流的供给线路连接至所述电动机的电动机连接端。通过操控所述半导体开关，所述电动机连接端在所述逆变器的导电状态...

### CN112166428B
- **申请号**: CN201980032587.5
- **IPC分类号**: G06F30/20
- **法律状态**: 授权
- **分类理由**: 关键词匹配: 模拟器; IPC分类号匹配: G06F30/20
- **技术手段**: 包括, 方法, 模拟, 具有, 配置, 系统, 计算
- **应用场景**: 通用
- **技术摘要**: 用于系统的基于事件的模拟的方法，所述模拟在包括第一运算单元和第二运算单元的计算机系统上执行，第一运算单元具有模拟时间；第二运算单元具有系统时间；第二运算单元执行模拟器应用，在其上执行模拟对象；以及第一运算单元管理事件等待队列，对每个模拟步骤列出事件并该事件配置要执行的进程和设定用于执行进程的模拟时刻；第二运算单元具有虚拟时钟发生器，对于每个模拟步骤：通过第一运算单元将开始信号传送给虚拟时钟发生器以...

### EP4409367A1
- **申请号**: EP22786818.9
- **IPC分类号**: G05B17/02
- **法律状态**: 期限届满
- **分类理由**: 关键词匹配: 模拟器; IPC分类号匹配: G05B17/02
- **技术手段**: 提供, 方法, 控制, 测试, 模拟
- **应用场景**: 通信, 网络, 控制, 测试
- **技术摘要**: 本发明涉及一种用于测试至少一个控制器的方法。在模拟器上提供至少两个单独的网络，其中，待测试的控制器经由第一网络和第一控制器接口连接至模拟器，并且待测试的控制器被设计为与至少一个第一附加控制器通信。第一附加控制器的通信至少部分地以记录消息的形式提供，其中用于播放记录消息的再现单元通过至少一个再现接口连接到模拟器上的第二网络或第三网络，再现单元通过再现接口连接到模拟器。


---

## 模拟数字转换板卡技术

**专利数量**: 16 条

**技术描述**: 模拟数字转换相关技术，包括ADC、DAC等转换器技术

**法律状态分布**:
- 授权: 7 条
- 公开: 5 条
- 权利终止: 2 条
- 实质审查: 1 条
- 暂缺: 1 条

**重点专利**:

### US11940556B2
- **申请号**: US17469921
- **IPC分类号**: G01S7/40
- **法律状态**: 授权
- **分类理由**: 关键词匹配: 模数转换, 数模转换, 转换器, 采样, 信号转换
- **技术手段**: 包括, 装置, 处理, 配置, 测试, 模拟
- **应用场景**: 传感器, 测试
- **技术摘要**: 一种用于测试距离传感器的测试装置包括：接收器，用于接收电磁自由空间波作为接收信号；模数转换器，被配置为在模拟模式中将接收信号转换为采样信号；信号处理单元，被配置为：延迟采样信号或调制采样信号以形成延迟采样信号或调制延迟采样信号；以及在采样信号上或在延迟采样信号上调制作为要被模拟的反射对象的特性运动轮廓的可预定的多普勒签名以形成调制采样信号或调制延迟采样信号；数模转换器，被配置为将调制或调制延迟采样...

### US20230076301A1
- **申请号**: US17469921
- **IPC分类号**: G01S7/40
- **法律状态**: 授权
- **分类理由**: 关键词匹配: 模数转换, 数模转换, 转换器, 采样, 信号转换
- **技术手段**: 包括, 装置, 处理, 配置, 测试, 模拟
- **应用场景**: 传感器, 测试
- **技术摘要**: 一种用于测试距离传感器的测试装置，包括：接收器，用于接收电磁自由空间波作为接收信号；模数转换器，被配置为在模拟模式下将所述接收信号转换为采样信号；信号处理单元，被配置为：延迟所述采样信号或调制采样信号以形成延迟采样信号或调制延迟采样信号；以及基于采样信号或基于延迟采样信号调制作为要被模拟的反射对象的特征运动轮廓的可预定多普勒特征，以形成调制采样信号或调制延迟采样信号；数模转换器，被配置为将调制的或...

### JP2025092461A
- **申请号**: JP2024210783
- **IPC分类号**: H03K17/08
- **法律状态**: 公开
- **分类理由**: IPC分类号匹配: H03K17/08
- **技术手段**: 提供, 检测, 装置, 具有, 电路, 生成, 控制
- **应用场景**: 检测, 控制
- **技术摘要**: 【课题】提供一种具有简单且高效的半导体开关1以及测量电路2和控制装置3的装置，用于半导体开关的过载保护。【解决手段】在缺陷生成单元中，测量电路2能够检测到过载检测器发现负载路径6出现过载，并且当发生过载时，通过过载信号输出部分13向控制装置3输出过载信号。控制装置连接至半导体开关1的开关信号输入部分7，通过开关信号线路14连接，当接收到过载信号时，向半导体开关提供用于打开半导体开关的开关信号，当过...

### EP4513204A1
- **申请号**: EP23192609.8
- **IPC分类号**: G01R31/28
- **法律状态**: 实质审查
- **分类理由**: IPC分类号匹配: G01R31/28
- **技术手段**: 提供, 设备, 包括, 控制, 电路, 设置, 模拟
- **应用场景**: 控制
- **技术摘要**: 电池模拟器，包括提供第一个电池电压以模拟第一个电池电压的第一个电路和提供第二个电池电压以模拟第二个电池电压的第二个电路。第一个电池电压放在本地电路质量上，第二个电池电压放在同一本地电路质量上，并以这种方式连接到第一个电池电压，本地电路块形成第一电池电压和第二电池电压的极点。设置控制电子设备以控制第一电池电压和第二电池电压。通过单个控制电子设备控制两个电池电压，可以节省组件。


### US12119812B2
- **申请号**: US18065637
- **IPC分类号**: H03K17/14
- **法律状态**: 授权
- **分类理由**: IPC分类号匹配: H03K17/14
- **技术手段**: 电路, 控制, 包括
- **应用场景**: 控制
- **技术摘要**: 一种用于电流均匀分配的电子电路，包括：第一MOSFET和第二MOSFET，其中第一MOSFET和第二MOSFET并联连接以便分配施加到输入端的电流，该电流流向电子电路的输出端，其中输入端分别连接到第一MOSFET的漏端和第二MOSFET的漏端；以及用于控制电压的端子，其中控制电压被施加到第一MOSFET的栅极端子和第二MOSFET的栅极端子。第一MOSFET包括在第一MOSFET的栅极端子处的第一...

---

## 故障注入板卡技术

**专利数量**: 50 条

**技术描述**: 故障注入相关技术，用于测试系统的故障处理能力

**法律状态分布**:
- 授权: 15 条
- 实质审查: 14 条
- 暂缺: 6 条
- 驳回: 4 条
- 撤回: 3 条
- 公开: 2 条
- 未缴年费: 2 条
- 申请终止: 2 条
- 权利终止: 1 条
- 期限届满: 1 条

**重点专利**:

### JP2025080780A
- **申请号**: JP2024198852
- **IPC分类号**: G01R31/00
- **法律状态**: 暂缺
- **分类理由**: 关键词匹配: 故障插入; IPC分类号匹配: G01R31/00
- **技术手段**: 包括, 控制, 平台, 装置, 配置, 测试, 模拟
- **应用场景**: 控制, 测试
- **技术摘要**: 【课题】用于使被测体面临模拟电气错误的测试平台。【解决方案】该测试平台包括：一个用于汇总模拟电气错误中央错误控制单元；以及至少一个作为故障插入单元（FIU）构成的本地节点，该节点配备有用于在测试平台中篡改所选电流的开关装置。错误控制单元被配置为创建指定至少一个连接到电线路的电气错误的抽象错误描述，并将其传输到本地节点。本地节点被配置为从抽象错误描述中导出关于开关装置的驱动控制规则，以便将电气错误连...

### EP4556917A1
- **申请号**: EP24187394.2
- **IPC分类号**: G01R31/28
- **法律状态**: 实质审查
- **分类理由**: 关键词匹配: 故障插入; IPC分类号匹配: G01R31/28
- **技术手段**: 包括, 模拟, 设置, 测试, 控制
- **应用场景**: 控制, 测试
- **技术摘要**: 测试台 （2） 用来对抗试样 （4） 的模拟电气故障。试验台包括一个用于编排模拟电气故障的中央故障控制单元（10），以及至少一个作为故障插入单元（FIU）设置的本地节点（8a），其中有一个用于伪造试验台中选定电流的开关安排（16a）。故障控制单元设置为创建一个抽象的故障描述，该描述指定至少一个电气故障，以便在电线（6a）上开启，并将其发送到本地节点。本地节点已设置，从抽象故障描述中得出一个适合将电...

### EP4343552B1
- **申请号**: EP23191816.0
- **IPC分类号**: G01R31/327
- **法律状态**: 授权
- **分类理由**: 关键词匹配: 故障插入; IPC分类号匹配: G01R31/327
- **技术手段**: 提供, 具有, 电路, 系统
- **应用场景**: 网络
- **技术摘要**: 一种用于连接到与总线或网络接口连接的被测对象的故障插入单元，其中，所述被测对象可以借助于所述故障插入单元经受大于所述总线或网络接口被设计用于的最大电压的故障电压，所述故障插入单元具有熔丝电路，所述熔丝电路保护连接到所述故障插入单元的总线或网络接口免受大于所述总线或网络接口被设计用于的最大电压的电压的影响。这提供了一种即使在以高带宽总线工作的系统中也能够使用故障插入单元而不存在由于过电压而损坏总线或...

### EP4553662A1
- **申请号**: EP24206600.9
- **IPC分类号**: G06F11/3668
- **法律状态**: 实质审查
- **分类理由**: IPC分类号匹配: G06F11/3668
- **技术手段**: 包括, 方法, 实现, 配置, 设置, 测试, 算法, 系统, 计算
- **应用场景**: 驾驶, 车辆, 测试
- **技术摘要**: 本发明涉及一种计算机实现的方法，用于基于标准创建一个虚拟测试（VT）的配置（10），以测试车辆的自动驾驶功能，其中包括定义配置参数（12a，12b）对虚拟测试（VT）的目标参数（14）的依赖性（S1），确定 （S2） 测试算法 （A） 和/或参数间隔 （16） 用于各个目标数量 （14）；并设置（S3）配置参数（12a，12b）的应用条件（18），参数间隔（16）和/或测试算法（A）。此外，本发明...

### US12278074B2
- **申请号**: US18064348
- **IPC分类号**: H02H9/00
- **法律状态**: 暂缺
- **分类理由**: IPC分类号匹配: H02H9/00
- **技术手段**: 包括, 检测, 装置, 具有, 配置, 电路, 模拟
- **应用场景**: 检测
- **技术摘要**: 一种用于具有包括输出电压的输出的模拟电池单元的保护电路，包括：至少一个MOSFET，其连接到模拟电池单元的输出，用于短路模拟电池单元的输出；电容器，连接到至少一个MOSFET的栅电极；过电压检测装置，其被配置为基于超过所述输出电压的过电压限制，利用所述输出电压对所述电容器充电；以及阈值电压检测装置，其被配置为基于未达到电容器处的电压的阈值来释放栅电极。


---

## 频率可调脉冲输出板卡技术

**专利数量**: 2 条

**技术描述**: 频率可调的脉冲输出技术，用于信号生成和控制

**法律状态分布**:
- 授权: 1 条
- 暂缺: 1 条

**重点专利**:

### US20230198532A1
- **申请号**: US18082345
- **IPC分类号**: H03L7/107
- **法律状态**: 暂缺
- **分类理由**: IPC分类号匹配: H03L7/107
- **技术手段**: 方法, 具有, 配置
- **应用场景**: 通用
- **技术摘要**: 一种用于改变FPGA配置的位宽的方法，FPGA配置具有多个至少2×<Sup>n </Sup>位的包含n ε的数据信号≥ 3，并且该方法具有以下步骤：当超过FPGA的电流消耗和/或温度的阈值和/或存在替换信号时，在每种情况下用零替换数据信号的k个最低有效位，其中k为ε3538.在FPGA上执行FPGA配置期间的≥ 2。


### EP4060376B1
- **申请号**: EP22159467.4
- **IPC分类号**: G01S7/497
- **法律状态**: 授权
- **分类理由**: 关键词匹配: 信号发生
- **技术手段**: 提供, 包括, 检测, 方法, 具有, 配置, 测试, 系统
- **应用场景**: 检测, 传感器, 测试
- **技术摘要**: 一种用于LiDAR传感器的测试系统，其包括触发检测器和连接到触发检测器的信号发生器，信号发生单元包括具有预定义数量的像素的显示面板，并且信号发生器被配置成将相同强度的像素聚集到集群中。还提供了一种用于测试LiDAR传感器的方法。


---

## 车载以太网板卡技术

**专利数量**: 12 条

**技术描述**: 车载以太网通信技术，包括车载网络协议和通信接口

**法律状态分布**:
- 授权: 6 条
- 暂缺: 1 条
- 实质审查: 1 条
- 公开: 1 条
- 申请终止: 1 条
- 未缴年费: 1 条
- 撤回: 1 条

**重点专利**:

### US20250080610A1
- **申请号**: US18728472
- **IPC分类号**: H04L67/12
- **法律状态**: 暂缺
- **分类理由**: IPC分类号匹配: H04L67/12
- **技术手段**: 方法, 计算, 实现, 包括
- **应用场景**: 传感器, 网络
- **技术摘要**: 一种用于自动注释传感器数据的计算机实现的方法，包括：接收多个传感器数据帧；使用至少一个神经网络注释所述多个传感器数据帧，其中所述注释包括向每个传感器数据帧分配至少一个数据点，并且向每个数据点分配至少一个状态属性；基于所述至少一个状态属性对所述数据点进行分组，其中第一组包括所述至少一个状态属性在定义的值范围内的数据点；从第一组中选择一个或多个数据点的第一样本；以及确定第一样本中的一个或多个数据点的质...

### EP4507253A1
- **申请号**: EP23190448.3
- **IPC分类号**: H04L12/46
- **法律状态**: 实质审查
- **分类理由**: IPC分类号匹配: H04L12/46
- **技术手段**: 包括, 方法, 实现, 系统, 计算
- **应用场景**: 通信, 数据传输, 网络
- **技术摘要**: 本发明涉及在网络系统（10）中进行数据传输的计算机实现的方法，该网络系统包括一个外部网络（12）和一个内部网络（14），其中，所述外部网络（12）包括至少一个第一网络元件（18）和一个外部网络通信元件（22）；其中，所述内部网络（14）至少包括第二网络元件（20）和内部网络通信元件（24），其中，所述第二网络元件（20）被设计为仅在所述内部网络（14）内通信，其中该过程包括以下步骤：-对要发送的数...

### US20240267429A1
- **申请号**: US18434055
- **IPC分类号**: H04L67/12
- **法律状态**: 公开
- **分类理由**: IPC分类号匹配: H04L67/12
- **技术手段**: 提供, 方法, 配置, 测试, 系统
- **应用场景**: 通信, 网络, 测试
- **技术摘要**: 一种用于借助于第一程序来变换所记录的通信数据的方法，其中，所述通信数据由来自网络或总线通信的消息给出，其中，所记录的通信基于第一通信矩阵来执行，其中，所记录的通信数据被提供以借助于测试系统被发送到被测ECU，其中，所述测试系统经由第一通信链路被连接到被测ECU，其中，所述被测ECU基于第二通信矩阵来配置。


### EP4096170B1
- **申请号**: EP22169648.7
- **IPC分类号**: H04L12/46
- **法律状态**: 授权
- **分类理由**: IPC分类号匹配: H04L12/46
- **技术手段**: 控制, 方法, 具有, 系统
- **应用场景**: 网络, 控制
- **技术摘要**: 一种用于在网络系统中传输数据的方法和一种网络系统，该网络系统具有处于混杂模式的第四网络元件的网络控制器的操作，以及在第一网络和第二网络之间产生IP隧道，第三网络元件和第四网络元件是经由接入元件引导的IP隧道的特定端点。


### EP2221697B1
- **申请号**: EP09002479.5
- **IPC分类号**: H04L12/26
- **法律状态**: 授权
- **分类理由**: IPC分类号匹配: H04L12/26
- **技术手段**: 设备, 包括, 检测, 方法, 装置, 具有, 测试, 生成, 模型, 控制, 计算
- **应用场景**: 检测, 控制, 测试
- **技术摘要**: 方法包括获取以形式语言作为要求的包含时间依赖性的目标功能，其中功能包括动作和预期反应。根据要求生成要求模型。通过执行在考虑功能的时间依赖性的同时在模型中执行的不可逆性分析来生成测试情况。将情况变换成信号模式。通过测试设备经由信号接口将模式应用于装置。利用测试设备检测装置的相关状态变量。还包括对于以下的独立权利要求：(1)具有用于执行利用测试设备测试控制装置的方法的程序代码的计算机程序；(2)用于测...

---

## 多通道视频注入同步技术

**专利数量**: 2 条

**技术描述**: 多通道视频注入和同步技术，用于视频信号的处理和同步

**法律状态分布**:
- 授权: 1 条
- 申请终止: 1 条

**重点专利**:

### US12250365B2
- **申请号**: US18169893
- **IPC分类号**: H04N17/00
- **法律状态**: 授权
- **分类理由**: IPC分类号匹配: H04N17/00
- **技术手段**: 包括, 检测, 装置, 配置, 测试, 生成, 计算
- **应用场景**: 检测, 车辆, 摄像, 测试
- **技术摘要**: 一种用于测试车辆的立体摄像机的测试装置，包括：计算单元；以及自动立体屏幕。计算单元被配置为生成合成图像数据并且在自动立体屏幕上输出合成图像数据，使得图像数据可被立体相机检测为立体图像。


### US20230199168A1
- **申请号**: US18068535
- **IPC分类号**: H04N17/00
- **法律状态**: 申请终止
- **分类理由**: IPC分类号匹配: H04N17/00
- **技术手段**: 包括, 检测, 模拟, 装置, 处理, 配置, 系统, 测试, 控制
- **应用场景**: 检测, 控制, 测试
- **技术摘要**: 一种系统，包括：相机单元；控制单元；以及用于测试控制单元的测试装置。测试装置包括处理器和图像输出单元。处理器被配置为操纵图像数据以模拟相机单元的误差，并且在图像输出单元上输出经操纵的图像数据，所述经操纵的图像数据呈经由相机光学器件可检测的图像的形式。相机单元被配置为经由相机光学器件检测所输出的模拟相机单元的误差的图像数据。控制单元被配置为从相机单元接收模拟相机单元的误差的相机图像数据。


---

## 数据注入类型支持技术

**专利数量**: 54 条

**技术描述**: 不同数据注入类型的支持技术，包括数据格式转换和处理

**法律状态分布**:
- 授权: 32 条
- 实质审查: 9 条
- 暂缺: 3 条
- 申请终止: 3 条
- 公开: 1 条
- 权利终止: 1 条
- 撤回: 1 条
- 驳回: 1 条
- 全部撤销: 1 条

**重点专利**:

### EP4567585A1
- **申请号**: EP23214679.5
- **IPC分类号**: G06F8/41
- **法律状态**: 实质审查
- **分类理由**: 关键词匹配: 数据处理; IPC分类号匹配: G06F8/41
- **技术手段**: 包括, 方法, 平台, 装置, 实现, 处理, 配置, 生成, 模型, 控制, 计算
- **应用场景**: 控制
- **技术摘要**: 该发明涉及一种计算机实现的方法用于从开发平台的图形控制模型生成目标平台的控制程序该开发平台包括用于存储有关图形控制模型的信息的定义数据库并被设计为支持矩阵运算由此特征在于在定义数据库中可以存储关于图形控制模型中使用的矩阵结构的注释信息，并且在为目标平台生成控制程序时，应考虑存储的注释信息，使得a）在执行至少涉及一个矩阵的编辑任务的目标平台上，计算速度有所提高和/或b）在目标平台保存矩阵时所需存储空...

### EP4521233A1
- **申请号**: EP24191642.8
- **IPC分类号**: G06F8/34
- **法律状态**: 暂缺
- **分类理由**: 关键词匹配: 数据处理; IPC分类号匹配: G06F8/34
- **技术手段**: 包括, 方法, 平台, 装置, 实现, 处理, 生成, 模型, 控制, 计算
- **应用场景**: 控制
- **技术摘要**: 本发明涉及一种用于从开发平台的图形控制模型（12）生成用于目标平台的控制程序的计算机实现的方法，其中，图形控制模型，第一块（14a）的输出驱动第二块（14b）的输入，以及（i） 第一块（14a）和第二块（14b）被设计为使得第一块（14a）的输入信号（Ii）对应于第二块的输出信号（O2）；或（ii）第一块（14a）和第二块。
此外，本发明涉及数据处理装置（10），其中包括执行上述方法的手段、计算机...

### EP3413204A1
- **申请号**: EP17175193.6
- **IPC分类号**: G06F13/12
- **法律状态**: 授权
- **分类理由**: 关键词匹配: 数据传输; IPC分类号匹配: G06F13/12
- **技术手段**: 设备, 包括, 处理, 系统, 计算
- **应用场景**: 数据传输
- **技术摘要**: 一个计算机系统的接口单元的至少一个第一和一个第二schnittstellenprozessor schnittstellenprozessor包括一个至少有两个softwareroutinen综合数量softwareroutinen转发输入的数据从一个处理器的计算机系统的外围设备，在一个应用程序，程序的编制。第一子集的数据传输是一个softwareroutinen tasksynchrone规定的...

### EP2950175A1
- **申请号**: EP14169948.8
- **IPC分类号**: G05B23/02
- **法律状态**: 授权
- **分类理由**: 关键词匹配: 数据处理, 数据传输, 数据转换
- **技术手段**: 设备, 方法, 控制, 装置, 处理, 测试, 模拟, 计算
- **应用场景**: 数据传输, 传感器, 网络, 测试, 控制
- **技术摘要**: 本发明涉及一种方法和一种装置，用于测试一个开口（8），其中在一个真正的或模拟的控制单元（8）通过网络连接（7）的传感器的数据传输，通过一个数据处理设备（1）是要通过模拟计算恩，其中，模拟传感器的数据至少部分至少有一个图形处理器，至少有一个grafikprozessoreinheit（2）数据处理设备（1）进行的模拟传感器的数据和图像数据编码中的一个visualisierungsschnittste...

### EP4597303A1
- **申请号**: EP24155284.3
- **IPC分类号**: G06F8/30
- **法律状态**: nan
- **分类理由**: IPC分类号匹配: G06F8/30
- **技术手段**: 提供, 包括, 方法, 控制, 实现, 测试, 生成, 算法, 系统, 计算
- **应用场景**: 驾驶, 验证, 测试, 车辆, 控制
- **技术摘要**: 该发明涉及一种计算机实现的方法和系统（1），用于识别至少一个用于生成虚拟构件（10）的代码更改（14），特别是用于测试和/或验证车辆的预定功能，尤其是自动化驾驶功能的虚拟控制器，包括以下步骤：提供（S1）至少一个源代码段（12）以测试和/或验证车辆的预定功能；应用（S2）机器学习算法（A）到所述至少一个源代码段（12）以识别至少一个所需的代码更改（14）；以及输出（S3）一个数据集（16），包含生...

---

## 多实时机级联技术

**专利数量**: 42 条

**技术描述**: 多实时机级联技术，用于分布式实时计算和处理

**法律状态分布**:
- 授权: 30 条
- 撤回: 4 条
- 公开: 2 条
- 暂缺: 2 条
- 驳回: 2 条
- 申请终止: 1 条
- 未缴年费: 1 条

**重点专利**:

### CN113796047B
- **申请号**: CN202080033771.4
- **IPC分类号**: H04L41/14
- **法律状态**: 授权
- **分类理由**: 关键词匹配: 分布式; IPC分类号匹配: H04L41/14
- **技术手段**: 仿真, 检测, 方法, 实现, 具有, 计算
- **应用场景**: 检测, 通信, 网络
- **技术摘要**: 本发明示出且说明了一种用于重构(U)预定的分布式实时仿真网络(2)的计算机实现的方法(1)，其中，仿真网络(2)具有多个网络节点(4、RK、R、IO)和多个数据连接(DV)，其中，每个网络节点(4、RK、R、IO)具有至少一个数据连接接口以连接数据连接(DV)，其中，网络节点(4、RK、R、IO)通过数据连接(DV)至少部分处于通信连接(KV)中，并且其中，在仿真网络(2)运行中在至少一个网络节点...

### US20230333892A1
- **申请号**: US18134231
- **IPC分类号**: G06F9/48
- **法律状态**: 暂缺
- **分类理由**: 关键词匹配: 实时系统; IPC分类号匹配: G06F9/48
- **技术手段**: 包括, 方法, 处理, 系统, 计算
- **应用场景**: 通用
- **技术摘要**: 一种用于对在处理器的计算机核上执行的实时系统的计算步骤进行文档记录的方法，其中在计算机核上执行任务，该任务包括一个或多个子任务，并且其中在每一种情况下在计算步骤期间执行任务的子任务。在计算步骤开始时记录第一处理器时间，并且在计算步骤结束时记录第二处理器时间，并且将取决于第一和第二处理器时间的时间信息存储在存储器中。以能够将时间信息分配给子任务和在计算步骤期间执行的任务的方式将该时间信息存储在存储器...

### EP2645200B1
- **申请号**: EP12162111.4
- **IPC分类号**: G06F9/48
- **法律状态**: 授权
- **分类理由**: 关键词匹配: 实时系统; IPC分类号匹配: G06F9/48
- **技术手段**: 提供, 包括, 方法, 处理, 系统, 计算
- **应用场景**: 通用
- **技术摘要**: 方法涉及提供用于系统时间的CPU计数器(100)和CPU (110)中的同步计数器，其中同步计数器由CPU的时钟信号驱动。 CPU计数器被读取(120)以由实时应用提供系统时间。在应用中查询(130)同步计数器。当同步计数器输出对应于从CPU计数器与系统时间的上一次同步起的超过预定时间段的值时，CPU计数器与应用中的系统时间同步(150) 。对于以下内容还包括独立权利要求：(1)数据处理系统(2)...

### EP1997005A1
- **申请号**: EP07722970.6
- **IPC分类号**: G06F9/50
- **法律状态**: 驳回
- **分类理由**: 关键词匹配: 分布式; IPC分类号匹配: G06F9/50
- **技术手段**: 方法, 具有, 系统, 控制, 计算
- **应用场景**: 控制
- **技术摘要**: 描述了一种用于创建优化的流程图以借助于时间控制的分布式计算机系统执行功能的方法，其中分布式计算机系统和功能具有至少一个元素类的(尤其是结构和功能的)元素的集合，并且元素至少部分地依赖于该依赖性。在此，求解任务的根据本发明的方法最初地并且基本上以如下事实为特征，即识别元素之间的依赖性，分类并且将元素分配给相应的依赖性类，并且通过协调至少一个依赖性类的元素来进行对流程图的优化。


### CN113009992B
- **申请号**: CN202011507562.7
- **IPC分类号**: G06F1/20
- **法律状态**: 授权
- **分类理由**: IPC分类号匹配: G06F1/20
- **技术手段**: 实现, 电路, 包括
- **应用场景**: 通用
- **技术摘要**: 本发明涉及一种冷却体，其用于面状地安置在装备有电子部件的电路板上，所述冷却体包括第一部分表面和第二部分表面。在第一部分表面与第二部分表面之间延伸有隔热部，并且跨越隔热部的刚性的机械连接部将第一部分表面与第二部分表面相连接。所述冷却体由此能实现将第一部分表面和第二部分表面分配给电路板上的电子部件并且有助于电路板的机械稳定性。


---

## 集群化测试技术

**专利数量**: 33 条

**技术描述**: 集群化测试技术，用于大规模并行测试和验证

**法律状态分布**:
- 授权: 9 条
- 实质审查: 8 条
- 暂缺: 7 条
- 申请终止: 3 条
- 期限届满: 2 条
- 权利终止: 1 条
- 公开: 1 条
- 未缴年费: 1 条
- 撤回: 1 条

**重点专利**:

### US20240385953A1
- **申请号**: US18667582
- **IPC分类号**: G06F11/36
- **法律状态**: 暂缺
- **分类理由**: 关键词匹配: 测试系统; IPC分类号匹配: G06F11/36
- **技术手段**: 提供, 方法, 配置, 测试, 系统
- **应用场景**: 测试
- **技术摘要**: 一种测试系统，用于应用场景的资源优化参数变化的方法，以测试至少一个被测真实和/或虚拟系统和/或显示测试结果。提供至少一种场景，其中场景由至少一个参数定义，并且其中参数由至少一个数值和/或文本值配置，其中数值和/或文本值可由附加的数值和/或文本值配置。执行至少一个基于场景的测试，其中场景由配置的参数定义并随配置的参数而变化。输出测试结果。


### EP4307121A1
- **申请号**: EP23176378.0
- **IPC分类号**: G06F11/36
- **法律状态**: 权利终止
- **分类理由**: 关键词匹配: 测试系统; IPC分类号匹配: G06F11/36
- **技术手段**: 提供, 方法, 实现, 具有, 配置, 测试, 算法, 系统, 计算
- **应用场景**: 机动车, 车辆, 测试
- **技术摘要**: 本发明涉及一种用于配置用于测试机动车辆的车辆功能的虚拟测试系统的计算机实现的方法，-根据第一条件（B1）对具有最高置信度值（K）的至少一个待测试的附加伪像（16）的输出端口（14）的分配（S3a），根据第二条件（B2）创建（S3b）最高置信度（K）输出端口（14）的列表，或者根据第三条件（B3）不分配（S3c）输出端口n待测试的至少一个附加工件（16）的输出端口（14）。本发明还涉及一种计算机实现...

### EP4202691A1
- **申请号**: EP22207368.6
- **IPC分类号**: G06F11/36
- **法律状态**: 实质审查
- **分类理由**: 关键词匹配: 测试系统; IPC分类号匹配: G06F11/36
- **技术手段**: 提供, 设备, 包括, 方法, 实现, 具有, 测试, 算法, 系统, 计算
- **应用场景**: 机动车, 驾驶, 车辆, 测试
- **技术摘要**: 本发明涉及一种用于确定系统参数（12、14、24、26）的兼容性的计算机实现的方法和系统，所述系统参数用于对用于机动车辆的至少部分自主引导的设备（8）的虚拟测试的测试执行，包括基于规则的算法（a）对至少一个第一系统参数（12）的应用（S2），以确定具有待测试系统（10a）的组中的至少一个第二系统参数（14）与至少一个所述第一系统参数的兼容性，测试环境（10b），测试系统（10c），测试场景（10d...

### US20220358032A1
- **申请号**: US17735219
- **IPC分类号**: G06F11/36
- **法律状态**: 暂缺
- **分类理由**: 关键词匹配: 测试系统; IPC分类号匹配: G06F11/36
- **技术手段**: 提供, 检测, 方法, 模拟, 实现, 测试, 生成, 系统, 计算
- **应用场景**: 检测, 测试
- **技术摘要**: 一种用于为测试过程自动提供建议的计算机实现的方法，其中，建议由至少一个建议生成器确定，并且用于测试和/或模拟的建议生成器被手动和/或自动选择，其中，建议生成器监视至少两个测试运行，使得在测试运行中检测至少一个事件，并且导出至少一个建议，其中，建议生成器在测试运行期间由建议生成器机构自动执行，并且由建议生成器确定的建议被提供给测试系统和/或用户。


### EP4086773A1
- **申请号**: EP22163414.0
- **IPC分类号**: G06F11/36
- **法律状态**: 实质审查
- **分类理由**: 关键词匹配: 测试系统; IPC分类号匹配: G06F11/36
- **技术手段**: 提供, 方法, 模拟, 实现, 测试, 系统, 计算
- **应用场景**: 测试
- **技术摘要**: 本发明涉及一种用于自动提供用于测试过程的提示的计算机实现的方法，其中，所述提示由至少一个提示提供者确定，并且其中，所述用于测试和/或模拟的提示由手动和/或自动选择，其中，所述提示提供者观察至少两个测试执行，使得在所述测试执行中识别至少一个事件并且推导出至少一个提示，其中，所述提示提供者在所述测试执行期间由提示提供者机制自动执行，并且其中，所述提示由所述提示提供者确定为所述测试系统和/或被提供给用户...

---

## 虚拟仿真软件技术

**专利数量**: 25 条

**技术描述**: 虚拟仿真软件相关技术，包括仿真建模、虚拟环境等

**法律状态分布**:
- 实质审查: 9 条
- 暂缺: 6 条
- 授权: 4 条
- 公开: 2 条
- 撤回: 2 条
- 申请终止: 1 条

**重点专利**:

### EP3783452B1
- **申请号**: EP19192743.3
- **IPC分类号**: G06N3/084
- **法律状态**: nan
- **分类理由**: 关键词匹配: 虚拟测试; IPC分类号匹配: G06N3/084
- **技术手段**: 提供, 设备, 方法, 实现, 测试, 计算
- **应用场景**: 机动车, 网络, 测试
- **技术摘要**: 本发明涉及一种用于近似设备的虚拟测试的测试结果以至少部分自主引导机动车的计算机实现的方法。本发明还涉及一种用于提供经训练的人造神经网络的计算机实现的方法、一种测试单元(1)、一种计算机程序以及一种计算机可读的数据载体。


### US12265771B2
- **申请号**: US17697095
- **IPC分类号**: G06F30/34
- **法律状态**: 授权
- **分类理由**: 关键词匹配: 仿真模型; IPC分类号匹配: G06F30/34
- **技术手段**: 仿真, 包括, 方法, 处理, 具有, 配置, 生成, 模型
- **应用场景**: 通用
- **技术摘要**: 一种用于将图形仿真模型划分为第一子模型和第二子模型的方法，包括：基于采样时间和/或资源分配，将至少一个第一块识别为属于第一子模型，并且将至少一个第二块识别为属于第二子模型；搜索块的循环组，其中，块全部具有相同采样时间的循环组被认为是原子的；识别块的非循环组；将来自所述循环块组和所述非循环块组的单独块分配给所述第一子模型或所述第二子模型，其中原子循环组的所有块被分配给相同子模型；从第一子模型生成用于...

### EP4390747A1
- **申请号**: EP23211097.3
- **IPC分类号**: G06F30/20
- **法律状态**: 实质审查
- **分类理由**: 关键词匹配: 仿真模型; IPC分类号匹配: G06F30/20
- **技术手段**: 提供, 仿真, 包括, 方法, 具有, 模型, 模拟
- **应用场景**: 通信
- **技术摘要**: 本发明涉及一种用于创建具有多个具有通信接口（2）的仿真模块（1）的协同仿真（100）的方法，其中具有仿真代理（4）的仿真模型（1）是可执行的，包括以下步骤：S1）创建仿真组件（6），所述仿真组件是用于一个或多个仿真模块的容器；S2）将模拟模块（1）分类为模拟组件（6），S3）为每个模拟组件（6）提供运行时环境信息模块（8），S4）为每个模拟组件（6）提供包含对所述模拟模块（1）的通信接口（2）的描...

### US20240160806A1
- **申请号**: US18406790
- **IPC分类号**: G06F30/20
- **法律状态**: 公开
- **分类理由**: 关键词匹配: 虚拟测试; IPC分类号匹配: G06F30/20
- **技术手段**: 控制, 模拟, 系统, 测试
- **应用场景**: 驾驶, 控制, 测试
- **技术摘要**: 一种用于驾驶员辅助系统的虚拟测试环境，其中虚拟道路用户基于游戏理论被模拟。虚拟测试环境被设计为将虚拟测试环境中的至少一个预定交通状况识别为游戏状况，其中涉及第一路径用户和第二路径用户，以将第一路径用户和第二路径用户指定为第一玩家和第二玩家。分配给游戏情形的回报矩阵被存储在虚拟测试环境中。虚拟测试环境被设计成将策略从策略选择分配给游戏情形中的两个玩家中的每一个，这取决于他们各自的点数帐户的余额，并在...

### EP4202753A1
- **申请号**: EP21216219.2
- **IPC分类号**: G06F30/15
- **法律状态**: 撤回
- **分类理由**: 关键词匹配: 虚拟仿真; IPC分类号匹配: G06F30/15
- **技术手段**: 提供, 仿真, 包括, 检测, 方法, 控制, 模拟, 实现, 具有, 测试, 生成, 系统, 计算
- **应用场景**: 驾驶, 检测, 传感器, 测试, 机动车, 车辆, 控制
- **技术摘要**: 本发明涉及一种用于生成测试数据的计算机实现的方法，该测试数据用于测试评估传感器数据流的机动车辆的控制系统，具有以下过程步骤：通过指定虚拟车辆（1）在虚拟仿真环境中的平移运动，在虚拟传感器（2）承载虚拟车辆（2）的情况下模拟驾驶通过虚拟仿真环境的至少一部分，通过捕捉虚拟仿真环境，通过在虚拟传感器（2）的视场中检测虚拟车辆（1）经过的虚拟模拟环境并提供合成传感器数据作为测试数据以测试评估传感器数据流的...

---

## 技术发展趋势分析

### 核心技术布局

1. **HIL硬件在环仿真技术** (62条, 14.4%): dSPACE在HIL技术方面拥有最多专利，体现了其在硬件在环仿真领域的技术领先地位。

2. **故障注入板卡技术** (50条, 11.6%): 故障注入技术是HIL测试的重要组成部分，用于验证系统的故障处理能力。

3. **数据注入类型支持技术** (54条, 12.5%): 支持多种数据类型的注入，提高了测试系统的灵活性和适用性。

### 技术特点

- **实时性**: 大量专利涉及实时仿真和实时数据处理
- **模块化**: 采用模块化设计，支持不同类型的板卡和接口
- **标准化**: 支持多种工业标准和通信协议
- **智能化**: 集成机器学习和人工智能技术

### 应用领域

- **汽车电子**: 车载网络、自动驾驶、电动汽车控制系统测试
- **航空航天**: 飞行控制系统、导航系统测试
- **工业自动化**: 工业控制系统、机器人控制测试
- **新能源**: 电池管理系统、电力电子设备测试

