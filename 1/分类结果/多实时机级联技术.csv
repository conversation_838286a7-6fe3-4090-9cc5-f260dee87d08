﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,当前法律状态,技术领域,分类得分,分类理由,技术手段,应用场景
CN113796047B,CN202080033771.4,"本发明示出且说明了一种用于重构(U)预定的分布式实时仿真网络(2)的计算机实现的方法(1)，其中，仿真网络(2)具有多个网络节点(4、RK、R、IO)和多个数据连接(DV)，其中，每个网络节点(4、RK、R、IO)具有至少一个数据连接接口以连接数据连接(DV)，其中，网络节点(4、RK、R、IO)通过数据连接(DV)至少部分处于通信连接(KV)中，并且其中，在仿真网络(2)运行中在至少一个网络节点(4、RK、R、IO)上实施仿真应用(5)。用所述方法可以自动找到实时仿真网络(2)的一种结构，在该结构中，以如下方式减少并且尽可能避免处于临界状态的通信连接(KV)，即，检测仿真网络(2)的拓扑，使得存在有关网络节点(4、RK、R、IO)和在网络节点(4、RK、R、IO)之间的数据连接(DV)的拓扑信息，特别是为仿真网络(2)的网络节点(4、RK、R、IO)确定节点数据率的预期值(E‑KDR)和/或节点延时的预期值(E‑KL)。
",H04L41/14,授权,多实时机级联技术,3.0,关键词匹配: 分布式; IPC分类号匹配: H04L41/14,"仿真, 检测, 方法, 实现, 具有, 计算","检测, 通信, 网络"
CN113009992B,CN202011507562.7,"本发明涉及一种冷却体，其用于面状地安置在装备有电子部件的电路板上，所述冷却体包括第一部分表面和第二部分表面。在第一部分表面与第二部分表面之间延伸有隔热部，并且跨越隔热部的刚性的机械连接部将第一部分表面与第二部分表面相连接。所述冷却体由此能实现将第一部分表面和第二部分表面分配给电路板上的电子部件并且有助于电路板的机械稳定性。
",G06F1/20,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F1/20,"实现, 电路, 包括",通用
US20240126608A1,US18380378,"一种用于执行用于区分验证任务优先级的方法的验证系统，其中，所述验证任务由所述验证系统的执行单元执行，其中，所述执行单元被划分为至少两个组，并且每个组由所述验证系统和/或所述验证系统的用户分配能力，使得相应组的执行单元具有所述组的能力，并且当所述验证任务由所述验证系统和/或所述用户自动执行时，指定和要求所述相应验证任务对所述执行单元的能力的要求和执行优先级，考虑优先级和能力执行单元，确定执行顺序，并由能力执行单元根据执行顺序执行验证任务。
",G06F9/50,公开,多实时机级联技术,2.0,IPC分类号匹配: G06F9/50,"方法, 具有, 系统",验证
US20230418324A1,US18209558,"一种对FPGA编程的方法，其中提供具有基本操作的库和针对库的每个基本操作的相应延迟表。定义数据路径。针对彼此不同的多个时钟速率记录延迟，并且针对每个时钟速率添加这些延迟，使得数据路径的总延迟针对该多个不同的时钟速率产生。确定在相应时钟速率下最低总延迟和总延迟之间的比率。识别针对每个时钟速率的FPGA的利用率。确定在相应时钟速率下FPGA的最低利用率和FPGA的利用率之间的比率。在考虑总延迟和FPGA的利用率的同时确定针对每个时钟速率的质量因数。
",G06F1/10,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F1/10,"提供, 方法, 具有",通用
US11822466B2,US17557060,"一种方法提供用于操纵运行时环境的变量以便在测试环境中测试待测试的控制设备程序组件的干预点。根据AUTOSAR标准，控制设备程序组件的软件被分为三层，其中三层包括程序组件层，运行时间环境层，以及设备相关基本程序层。程序组件层具有用于提供输入值的测试场景程序组件和用于接收输出值并显示测试结果的程序组件。该方法包括：为待测试的控制设备程序组件提供分别按照AUTOSAR标准定义的接口；以及创建待测试的控制设备程序组件和测试场景程序组件的可执行程序。
",G06F9/44,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"提供, 设备, 包括, 方法, 具有, 测试, 控制","控制, 测试"
US20230333892A1,US18134231,"一种用于对在处理器的计算机核上执行的实时系统的计算步骤进行文档记录的方法，其中在计算机核上执行任务，该任务包括一个或多个子任务，并且其中在每一种情况下在计算步骤期间执行任务的子任务。在计算步骤开始时记录第一处理器时间，并且在计算步骤结束时记录第二处理器时间，并且将取决于第一和第二处理器时间的时间信息存储在存储器中。以能够将时间信息分配给子任务和在计算步骤期间执行的任务的方式将该时间信息存储在存储器中。
",G06F9/48,暂缺,多实时机级联技术,3.0,关键词匹配: 实时系统; IPC分类号匹配: G06F9/48,"包括, 方法, 处理, 系统, 计算",通用
US20230131079A1,US17913454,"一种用于在电子电路的输入和/或输出通道组上以时间同步的方式输入和/或输出具有可选采样速率的信号的方法，包括：在标准采样周期配置组的每个通道；在标准采样周期同步地启动组的所有通道；检测在修改的采样周期T <Sub>Period of </Sub> A组的第一通道的条目；检测当前计数器值T <Sub>Counter</Sub>；在修改的采样周期配置第一通道；根据T <Sub>Waiting</Sub> = T <Sub>Period</Sub> − mod (T <Sub>Counter</Sub>，T <Sub>Period</Sub>)建立T <Sub>Waiting </Sub>时钟的等待时间，其中mod (T <Sub>Counter</Sub>，T <Sub>Period</Sub>)表示从当前计数器值T <Sub>Counter </Sub>和修改的采样周期T <Sub>Period</Sub>的除法余数；以及在等待时间T <Sub>Waiting</Sub>之后启动第一通道。
",G06F1/12,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F1/12,"包括, 检测, 方法, 具有, 配置, 电路",检测
US11558493B2,US17338003,"一种用于监视在至少两个控制单元之间交换的消息分组的方法。消息分组被串接在数据流中，并且每个消息分组具有由预定义字大小的数据项描述的有效载荷的长度规范、有效载荷和标识符。至少两个控制单元通过分发器连接。分发器通过第一分发器端口被连接到至少两个控制单元中的第一控制单元，通过第二分发器端口被连接到至少两个控制单元中的第二控制单元，并且通过第三分发器端口被连接到计算机系统。数据流流经第一和分发器端口以用于第一节点和第二节点之间的通信。计算机系统具有存储器，并且关于消息分组的相应标识符的信息被存储在存储器中。
",G06F15/16,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F15/16,"方法, 具有, 系统, 控制, 计算","通信, 控制"
US11392353B2,US17016499,"框图包括与外部服务交换数据的至少第一块。第一块引用数据定义集合中的发现对象。发现对象定义选择准则。一种用于从框图的一个或多个块生成源代码的方法包括：由计算机系统打开包括模型编辑器中的第一块的框图；由计算机系统从数据定义集合读出所引用的发现对象；由计算机系统至少基于第一块生成应用源代码；以及由计算机系统至少基于所引用的发现对象生成服务发现源代码。
",G06F9/44,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"包括, 方法, 生成, 模型, 系统, 计算",通用
US20210132651A1,US16784291,"一种模块化计算机系统，包括：壳体；至少一个容器；数据连接；以及外壳盖。处于降低状态的至少一个集装箱被配置为经由安装在至少一个集装箱的外部上的至少一个数据连接而连接到计算机系统的主板。数据连接用于在之间交换数据当所述至少一个容器处于所述降低状态时，所述计算机系统的所述至少一个容器和所述母板中的可插拔电路板。壳体盖被构造成固定在壳体上。容器保持装置安装在壳体盖上其中，所述容器保持装置构造成在所述壳体盖已经固定在所述壳体上之后在所述至少一个容器上施加力，以将所述至少一个容器以抗振的方式固定在所述壳体中。
",G06F1/16,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F1/16,"包括, 装置, 配置, 电路, 系统, 计算",通用
US10860298B2,US16181781,"一种用于编辑技术计算环境的框图中的一个或多个模型元素的一个或多个属性的计算机实现的方法。模型元素包括块和块中的变量，其中一个或多个属性被分配给每个模型元素。技术计算环境具有模型编辑器、数据定义工具和代码生成器。主机的处理器打开模型编辑器中的框图，显示存在于框图中的模型元素的列表，接收对一个或多个模型元素的选择，突出显示所选择的模型元素，接收编辑命令以设置用于所选择的模型元素的所选择的属性的新值，并且将所选择的属性设置为新值。还提供了一种非暂时性计算机可读介质和计算机系统。
",G06F9/44,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"提供, 包括, 方法, 处理, 实现, 具有, 设置, 生成, 模型, 工具, 系统, 计算",通用
US10678537B1,US15817856,"一种用于生成程序的文档的方法，所述程序是从技术计算环境中的框图的一个或多个块生成的，所述程序的所述一个或多个块具有至少一个分层块，所述分层块的功能由所述框图的下级分层层级中的多个块定义。该方法由具有至少一个处理器的计算机系统执行，处理器在技术计算环境的模型编辑器中的顶层层次级别打开框图，并验证对于框图的当前层次级别是否满足文档条件。当满足文档编制条件时，处理器生成当前层次级别中的块的文档编制文本。
",G06F9/44,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"方法, 处理, 具有, 生成, 模型, 系统, 计算",验证
US20200174860A1,US16697214,"一种通信系统的消息显示方法，包括：经由所述通信系统在待测试的控制单元与测试环境之间交换消息；由所述测试环境的至少一个处理单元创建、接收和处理消息，以用于与待测试的所述控制单元的所述消息交换以及用于由所述至少一个处理单元的查看器进行的命令处理；由所述测试环境的显示设备在第一视图中的第一显示元件中显示所有接收到的消息；由所述查看者根据过滤指令检查每个接收的消息；以及响应于针对相应消息的检查是肯定的，在第二视图中的第二显示元素中附加地显示相应消息。
",G06F9/54,暂缺,多实时机级联技术,2.0,IPC分类号匹配: G06F9/54,"设备, 包括, 方法, 控制, 处理, 测试, 系统","通信, 控制, 测试"
US10521332B1,US16145208,"一种用于参数化仿真模型的方法，包括：基于基本块和基本块之间的线连接器的布置来构成仿真模型；将包含第一数字标识符的第一标记块添加到所述仿真模型中的第一子系统；将包含第二数字标识符的第二标记块添加到仿真模型中的第二子系统；分析仿真模型；在分层树中列出所述仿真模型的参数，并且在屏幕上显示所述分层树，以便于经由所述分层树来更改所述仿真模型的所述参数；以及基于第一数字标识符和第二数字标识符是否相同，确定是将第一子系统和第二子系统列出在分层树的公共节点中还是列出在分层树的单独节点中。
",G06F9/44,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"仿真, 包括, 方法, 模型, 系统",通用
EP3575976A1,EP18174804.7,"确定物理连接拓扑的过程涉及设计用于开发控制装置（2）的实时测试装置，在测试装置（2）具有多种数据处理单元（16）的情况下，每个数据处理单元（16）具有用于数据处理单元（16）之间的通信的指定数量的物理接口，其中大量的仿真模型（18）与各种数据处理单元相关联，其中，各种仿真模型（18）包括要控制的技术系统的至少一个模型和/或技术系统的至少一个控制模型和/或至少一个技术环境模型。程序规定了以下步骤：确定仿真模型之间的逻辑通信链路（20），其中，每个逻辑通信链路表示仿真模型的多重性中的两个之间的数据链路，并且其中数据处理单元的多重性中的至少一个的指定物理接口数量小于与该数据处理单元相关联的数量；逻辑通信链路；以及通过在数据处理单元之间建立直接物理通信链路（24）来自动确定物理连接停止学，同时考虑到物理接口的各自数量，其中，对每个逻辑通信链路（20）的直接物理通信链路（24）的确定确定与逻辑通信链路（20）相对应的直接物理通信链路是否是物理连接拓扑（24）的一部分。
",G06F15/173,撤回,多实时机级联技术,2.0,IPC分类号匹配: G06F15/173,"仿真, 包括, 控制, 装置, 处理, 具有, 测试, 模型, 系统","通信, 控制, 测试"
US10423571B2,US15730155,"一种用于配置真实或虚拟电子控制单元的方法，其中在控制单元上执行控制单元软件，并且控制单元软件包括基本软件层，基本软件层通过设置参数值由模块配置文件配置，可配置参数的范围在第一模块定义文件中定义，第一模块定义文件包含可配置参数的标识符。第一模块定义文件被第二模块定义文件所取代，并且发生第一模块配置文件到第二模块配置文件的转换。
",G06F15/78,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F15/78,"包括, 方法, 配置, 设置, 控制",控制
US10310822B1,US15827196,"一种用于作为在一技术的计算环境中的一框图的一个或多个块模拟一程序模型的方法。一框图开放在一型号编辑器。对于框图的一个或多个块使用代码生成程序产生源代码。程序使用一预定义的编辑由源码构造，为了生成一二进制可执行文件，程序被模拟，其包括运行在辅助文件中的至少一个功能，为了确定一对应于在二进制可执行文件中的可变的列举的基本数据类型的至少宽度，为了记录仿真测试结果在确定的字节宽度上分配一个或多个可变基础。
",G06F9/44,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"仿真, 包括, 方法, 测试, 生成, 模型, 模拟, 计算",测试
US20190138310A1,US16182637,"在运行时间的来自一现场可编程门阵列（FPGA）的一种用于读取变数的方法包括：计算，在FPGA 中，首变量操作，其中首变量操作与第一阴影寄存器和第二阴影寄存器有关，其中首变量操作与第一测量栅有关和具有第二测量栅；同步地储存，在第一时间点，所有的变量与在阴影寄存器与各自的变量相关联的中的第一测量栅相关联的；同步地储存，在第二时间点，所有的变量与在阴影寄存器与各自的变量相关联的中的第二测量栅相关联的；互相之间独立读出阴影寄存器。
",G06F9/30,申请终止,多实时机级联技术,2.0,IPC分类号匹配: G06F9/30,"计算, 方法, 具有, 包括",通用
EP3479237A1,EP17733359.8,"本发明涉及一种用于使检验设备(10)同步的方法，其中，所述检验设备(10)被构造用于检验至少一个第一控制设备，并且所述检验设备(10)至少具有：一个第一计算单元(Cn1)用于执行模型代码，其中，借助所述模型代码能够提供模拟的受控系统信号用于激励所述控制设备，并且能够处理所述控制设备的执行器信号，其中，第一时间差(T1D)反映全局开始时间(TGO)与第一局部开始时间(TLO)的时间距离，并且随后提供所述第一时间差(T1D)用于在所述检验设备(10)中的附加应用和/或用于在附加的检验设备中的应用。本发明还涉及一种相应的检验设备(10)和一种复合系统，其中，所述复合系统具有至少一个检验设备(10)和与检验设备(10)同样工作的附加检验设备。
",G06F9/52,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/52,"提供, 设备, 方法, 控制, 模拟, 处理, 具有, 模型, 系统, 计算",控制
EP3438817A1,EP17183987.1,"本发明涉及一种方法生成的源代码从一个或多个块，一个拉丁方这是一个动态的系统模型至少包括一个连接块之间，其中该框图可以执行以动态系统仿真框图，并在至少一个地区的定义是在一个或多个块设置。该系统包括一个第一块和第二块，其中第一块第二块的第一和第二blockvariable blockvariable）每个标识符是一个blockvariable的拉丁方。blockvariable标识符标识符是与第一和第二blockvariable比较，这是检查是否第一个块和第二块是在同一地区。第一和第二blockvariable blockvariable作为唯一的变量在源代码中被实现，如果标识符匹配的块和在一个地区的谎言。如果标识符是不同的块和\/或不在同一地区，第一和第二的blockvariable blockvariable为两个独立的变量在源代码中的实现。本发明还涉及一个方法来配置一个控制装置和一个计算机系统，一个computerprogrammprodukt。
",G06F9/45,撤回,多实时机级联技术,2.0,IPC分类号匹配: G06F9/45,"仿真, 包括, 方法, 控制, 装置, 实现, 配置, 设置, 生成, 模型, 系统, 计算",控制
EP3396549A1,EP17168710.6,"一个适配器用于连接一个嵌入式系统，包括一个bedienrechner标准接口的网络接口，特别是一，第一和第二teilschaltung teilschaltung，其中第一teilschaltung为此建立的标准接口，如以太网，特别是在一个标准的或iocnet，最好是通过一个standardprotokolls，bedienrechner XCP与沟通。这是第一teilschaltung建立在一个标准的标准接口的要求，从大量的protokollfunktionalität protokollfunktionalitäten呼吁支持在一个或多个元素arfunktionen总量由初等函数定义的翻译。一个内部接口的第一和第二teilschaltung teilschaltung相连，其中一个可编程的第二teilschaltung rechenbaustein）它被配置为从至少一个初等函数elementarfunktion总量分配什么样的内部通过一个调用接口调用。第一teilschaltung是建立一个或多个内部的一个接口接收到的值的standardprotokolls协议格式翻译其中，第二teilschaltung individualschnittstelle嵌入式系统与一个可连接其中第一和第二teilschaltung teilschaltung实施和定制设计。本发明还涉及一个计算机系统和方法定制一个适配器。
",G06F9/54,公开,多实时机级联技术,2.0,IPC分类号匹配: G06F9/54,"包括, 方法, 配置, 系统, 计算",网络
US10078500B2,US15273992,"提供一种用于从在一主机上的一框图产生生产代码的方法。在框图中的一块具有用于接收信号的一些输入口和用于发送信号的一些输出端口。处理器在框图中标识第一块。输入信号追溯到第二块上游的第一块。服从一最优条件被检查，最优条件，其被实现，当一组临近块具有当留下不变的复合变量的至少一个元素时，其影响输入信号的一个或多个元素的赋值运算时。当最优条件被实现时，对临近块小组来说产生一合作生产代码，以便合作生产代码包括用于受赋值运算影响的复合变量的那些元素的写入指令。
",G06F9/44,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"提供, 包括, 方法, 处理, 实现, 具有",通用
EP2881857B1,EP13005730.0,"本发明涉及一种用于改变电子控制装置的存储器中的软件的方法，其中，可以将来自覆盖存储器的每个存储器地址通过分配信息分配给只读存储器的存储器地址。在控制装置的运行时间期间，将至少部分地替换原始程序例程的旁路例程的至少功能性部分存储在覆盖存储器的地址范围中，或者将转移指令作为旁路例程的第一部分存储在覆盖存储器中，该第一部分引用存储在处理器可访问的地址范围中的旁路例程的第二部分。为了激活覆盖功能性，将覆盖存储器的地址和/或地址范围分配给要替换的程序例程的地址或地址范围。
",G06F9/445,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"装置, 处理, 方法, 控制",控制
US20180101501A1,US15730155,"配置方法真实或虚拟电子数控制单元，其中控制单元软件运行在控制单元，和控制单元软件包括基础软件层，对基本软件层配置，通过模块配置文件通过设定值的参数，范围的可配置参数，限定在第一模块定义文件包含这些标识符的可配置参数。第一模块定义文件被第二模块定义文件，和转换第一模块到第二模块的配置文件，配置文件中发生。
",G06F15/78,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F15/78,"方法, 配置, 控制, 包括",控制
US09841954B1,US15233347,"用于发电生产代码从方框图在技术计算环境主机。第一接收第一输入信号具有多个元件。一种尺寸的第一所需信号外部函数的尺寸被确定并且与第一输入信号。当该大小第一所需信号的大小对应的元素在第一输入信号产生代码产生封闭调用外部函数，通过环路连续地寻址多个元件中的每个的第一输入信号。当该大小第一所需信号的大小对应的第一输入讯号产生代码打电话外部函数的产生没有封闭的环路元件上的第一输入信号。
",G06F9/44,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"具有, 计算",通用
US09811361B2,US14055497,"用于生成软件的硬件部件的测量、控制或调节系统具有处理器，FPGA，多个输入输出通道。该I/O通道连接到FPGA的FPGA连接到处理器通过通信接口。该方法包括步骤：选择第一子集的输入输出通道操作，通过该FPGA，产生第一应用执行的FPGA，选择第二子组的输入输出通道的操作由处理器，和生成第二执行申请的处理器。产生步骤包括连接第一代码的第二子集的输入输出通道到通信接口。本发明另外还涉及一种用于操作硬件组件。
",G06F9/44,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"包括, 方法, 控制, 处理, 具有, 生成, 系统","通信, 控制"
US20170206097A1,US14996281,"输入/输出接口的测试设备的构造，其中输入/输出接口是开发用于硬件单元至行为模型存在于试验装置。该方法包括步骤：显示图形表示之间的信号路径的输入/输出接口作为硬件端口连接的硬件和至少一个模型端口，用于将行为模型经由可选输入/输出功能；接收到第一信号通路；接收一测试值可预定义的，即在硬件端口或模型端口的信号路径，但是，例如，还通过可预定义的图形表示硬件端口或端口；模型传送测试信号与测试值沿信号路径，根据第一配置信号路径，和显示传播对测试信号的图形表示模型端口或硬件端口。
",G06F9/445,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"设备, 包括, 方法, 装置, 配置, 测试, 模型",测试
EP2645200B1,EP12162111.4,"方法涉及提供用于系统时间的CPU计数器(100)和CPU (110)中的同步计数器，其中同步计数器由CPU的时钟信号驱动。 CPU计数器被读取(120)以由实时应用提供系统时间。在应用中查询(130)同步计数器。当同步计数器输出对应于从CPU计数器与系统时间的上一次同步起的超过预定时间段的值时，CPU计数器与应用中的系统时间同步(150) 。对于以下内容还包括独立权利要求：(1)数据处理系统(2)包括用于执行用于在实时系统中提供时间戳的方法的指令集的计算机程序产品(3)包括用于执行用于在实时系统中提供时间戳的方法的指令集的数字存储介质。
",G06F9/48,授权,多实时机级联技术,3.0,关键词匹配: 实时系统; IPC分类号匹配: G06F9/48,"提供, 包括, 方法, 处理, 系统, 计算",通用
CN103116488B,CN201210281762.4,"本发明涉及用于在影响装置中处理数据的方法，影响装置与汽车控制装置和与数据处理装置连接。如果影响装置收到来自汽车控制装置、数据处理装置或影响装置的第一触发，则检测第一触发与函数的有效对应。当存在有效对应时开始所对应的函数。函数具有第一地址。借助第一地址从影响装置和/或汽车控制装置的存储器读出数值。函数还具有第一子函数和/或第二子函数。检测第一地址和/或该数值与第一子函数或第二子函数的有效对应。当存在有效对应时调用所对应的子函数。根据所调用的子函数检查和/或操作该数值并且根据检测的结果将经检测的数值和/或经操作的数值由影响装置发送到汽车控制装置和/或数据处理装置和/或存储到影响装置的存储器。
",G06F9/44,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"检测, 方法, 装置, 处理, 具有, 控制","检测, 汽车, 控制"
US20170010887A1,US14794331,"一种计算机实现的方法，用于编辑数据对象变量被描述和至少一个软件工具，由此数据对象变体至少具有一个共同的软件/硬件属性和各构型的属性。可以针对改变构型的硬件属性不同的数据对象变形以改变配组，而且因此在编辑数据对象变量，因为至少一个属性匹配构造属性在不同的数据对象变体被捕获和那个属性信息配组的数据对象被存储在变体具有匹配构造属性。
",G06F9/44,未缴年费,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"方法, 实现, 具有, 工具, 计算",通用
US09360853B2,US13621773,"提供了一种用于创建电子控制单元软件的计算机项目管理系统和方法。该系统具有配置成设计用于机动车辆的电子控制单元的图形模型的软件架构工具。行为模型工具将图形模型转换成计算机可读生产代码。软件容器具有基于电子控制单元生成的文件。容器管理器在向软件架构工具或行为模型工具输入或输出软件容器期间将软件容器与之前的软件容器进行比较，并且生成指示是否对电子控制单元进行了接口修改的比较列表。然后在显示屏幕上向用户显示比较列表。
",G06F9/44,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"提供, 方法, 控制, 具有, 配置, 生成, 模型, 工具, 系统, 计算","机动车, 控制"
EP3015995A1,EP15193577.2,"一个计算机系统的方法来配置一个接口单元包括第一处理器和第二处理器上沉积的一个接口单元，其中一个连接之间的第一个处理器和第二处理器和接口设置一个单位和至少一个eingangsdatenkanal ausgangsdatenkanal）。一个计算机系统的外围设备设置为输入数据，输入数据eingangsdatenkanal存放在从第二ausgangsdatenkanal处理器读取和设置为输入数据从数据读取和在从eingangsdatenkanalgangsdatenkanal存款。一个序列的第二个prozessorbefehlen处理器编写的，编写了大量的subsequenzen，最初的一个例行的subsequenz读取和处理至少在第一和第二eingangsdatenkanal沉积数据的分析和处理subsequenz一例行的至少第二个数据或eingangsdatenkanal沉积一个常规的处理通过第一个处理器生成的数据和存放。处理后的数据在第一ausgangsdatenkanal R是通过拼接序列和序列的subsequenzen创建在第二个处理器加载。
",G06F15/78,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F15/78,"设备, 包括, 方法, 处理, 配置, 设置, 生成, 系统, 计算",通用
EP2881858B1,EP13196209.4,"本发明涉及一种用于改变电子控制单元的存储器中的软件的方法。在电子控制单元的工作存储器中存储旁路例程，并且在表中存储旁路函数的地址。服务函数从表中读取地址并且调用旁路例程。在电子控制单元的运行时间，通过擦除表条目可以替换旁路例程。服务函数的调用通过覆盖存储器、存储器管理单元或借助于手表集成到电子控制单元的程序代码中。
",G06F9/445,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/445,"方法, 控制",控制
EP2990941A1,EP15172489.5,"描述并描绘了用于生成控制装置程序代码（2）的计算机实现的过程（1）。其中，在使用至少一个第一软件工具（4）从至少一个第一数据对象（3）产生控制装置程序代码（2）的过程中生成控制装置程序代码（2）或中间表示，其中，第一软件工具（4）产生用于产生控制装置程序代码（2）或中间表示的至少一条消息（M1、Mn、Mv），并且计算机实现的消息管理环境（5）记录由软件工具（M1、Mn、Mv）发出的消息。
通过从消息管理环境（5）到记录的消息（M1、Mn、Mv）的限定（q1、qn），对软件工具在生产过程中发出的消息的评估更加有效，qv）被确认为至少“开放”或“签署”，并且资格要求（p1、pn、pv）也被记录为来自报告管理环境（5）的“签署”消息（M1、Mn、Mv）。
",G06F9/44,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"装置, 实现, 生成, 工具, 控制, 计算",控制
EP2963541A1,EP14174904.4,"本发明涉及一种方法创建一个FPGA代码（7）基于FPGA的一个模型（10）至少有一个signalwert（16）为常数，建立全面的步骤，插入一个常数（16）与一个预定的signalwert在FPGA（10）模型schaltvariablen（22）设置在FPGA（10）模式之间切换正常模式和一个kalibriermodus为FPGA代码（7）生成的代码（7）对FPGA的FPGA（10）全面实施模型常数（16）在FPGA代码（7）在实施schaltvariable常数（16）（22）在一个正常模式的实现为常数（16）为固定值，在FPGA（7）包括编码的实现，和常数（16）的槽（22）在schaltvariable为实现这kalibriermodus更多的工作（16）常数比（24）signalwert FPGA代码（7）包括。本发明还涉及一个校准模型的方法在FPGA（10）。
",G06F9/44,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"包括, 方法, 实现, 设置, 生成, 模型",通用
EP2874060A1,EP14153375.2,"本发明涉及一种用于配置模型的发展的一个技术系统来显示波形，特别是在一个计算机系统有一个显示模式，其中至少有两个波形的显示和在一个系统和技术ngsdarstellung所有输入信号。和所有的处理单元的输出信号的形式在一个blockelementen映射图，它的特点是发展的训练后，选择一个任意的或无效的ausgangssignals模型表示在signalver运行选定的信号，以减少只有相关的处理单元的显示或强调。
",G06F9/44,撤回,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"处理, 配置, 模型, 系统, 计算",通用
JP2015088188A,JP2014218868,"要解决的问题：に允许控制程序，而不需要修改的源代码或描述控制程序，即使对于信息缺失的控制程序。
SOLUTION：响应于函数调用，程序代码控制程序检查来检测跳转目的地址涉及函数调用和地址的返回指令，存储器容量所占用的面积相应的程序代码，并检测标识符包括尺寸和地址的存储区分配给各个第一功能显示在显示单元上。气密娇小者第一函数中选择将被删除，且尺寸和地址的选择的第一功能存储在信息结构，并调用选择的第一功能是停用和/或选择的第一功能被第二个功能，与程序代码的选择的第一功能被重写该程序代码的第二功能。
",G06F9/44,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"检测, 控制, 包括","检测, 控制"
EP2530584A1,EP11004526.7,"装置具有用于在库字段中显示图形化的库功能元件(5)的显示装置。通过放置功能元件的实例在配置字段中产生测试序列。功能元件设有功能占位符(8) 。在实例中为功能占位符提供实例功能。实例的参考保留在功能元件上。在配置字段中产生的测试序列或部分测试序列被定义为功能元件。
",G06F9/44,驳回,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"提供, 装置, 具有, 配置, 测试",测试
EP2302516B1,EP10015461.6,"一种方法包括将单个任务的计算时段的信息存储在数据处理单元中。以根据所存储的信息的计算时段开始任务。以任务的开始开始来开始计时器，其中任务管理器根据循环持续时间和计时器的时间值的差来终止所开始的任务。
",G06F9/48,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/48,"处理, 方法, 计算, 包括",通用
EP1901146B1,EP07016974.3,"设备具有显示设备(2)，其中模型配置字段(4)中的输入/输出(I/O)接入点(3)和/或功能配置字段(6)中的硬件功能(5)可由显示设备呈现。接口配置字段(8)中的I/O接入点、硬件功能和硬件接口(7)通过具有输入和/或输出端口(10a、10b)的块来呈现。 I/O接入点、硬件功能和硬件接口通过图形指派单元(例如信号线(11))来彼此指派。针对以下内容还包括了独立权利要求：(1)包括指令的计算机程序，以执行用于生成用于控制控制系统的控制程序的方法；(2)用于生成用于控制控制系统的控制程序的方法。
",G06F9/44,授权,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"设备, 包括, 方法, 控制, 具有, 配置, 生成, 模型, 系统, 计算",控制
EP1997005A1,EP07722970.6,"描述了一种用于创建优化的流程图以借助于时间控制的分布式计算机系统执行功能的方法，其中分布式计算机系统和功能具有至少一个元素类的(尤其是结构和功能的)元素的集合，并且元素至少部分地依赖于该依赖性。在此，求解任务的根据本发明的方法最初地并且基本上以如下事实为特征，即识别元素之间的依赖性，分类并且将元素分配给相应的依赖性类，并且通过协调至少一个依赖性类的元素来进行对流程图的优化。
",G06F9/50,驳回,多实时机级联技术,3.0,关键词匹配: 分布式; IPC分类号匹配: G06F9/50,"方法, 具有, 系统, 控制, 计算",控制
JP2008171391A,JP2007277493,"要解决的问题：提供以下方法建立嵌入式系统的需求描述方法，即使任何用户独特地认识到什么是正在处理的混凝土要求按照需求描述和用于创建需求描述适于自动地创建唯一的测试嵌入式系统从所测试的要求。
SOLUTION：在数据处理系统中，词汇是由可选择的自然语言文本段中，存储了至少一个自然语言语句包括英语和由结合自然语言文本段创建机器可读需求描述。气密娇小者自然语言的语句不只限于英语。
",G06F9/44,撤回,多实时机级联技术,2.0,IPC分类号匹配: G06F9/44,"提供, 包括, 方法, 处理, 测试, 系统",测试
