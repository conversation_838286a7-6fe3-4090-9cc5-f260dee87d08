﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,当前法律状态,技术领域,分类得分,分类理由,技术手段,应用场景
JP2025080780A,JP2024198852,"【课题】用于使被测体面临模拟电气错误的测试平台。【解决方案】该测试平台包括：一个用于汇总模拟电气错误中央错误控制单元；以及至少一个作为故障插入单元（FIU）构成的本地节点，该节点配备有用于在测试平台中篡改所选电流的开关装置。错误控制单元被配置为创建指定至少一个连接到电线路的电气错误的抽象错误描述，并将其传输到本地节点。本地节点被配置为从抽象错误描述中导出关于开关装置的驱动控制规则，以便将电气错误连接到电线路，并通过根据驱动控制规则驱动控制开关装置，将电气错误连接到电线路。【选择图】图6
",G01R31/00,暂缺,故障注入板卡技术,3.0,关键词匹配: 故障插入; IPC分类号匹配: G01R31/00,"包括, 控制, 平台, 装置, 配置, 测试, 模拟","控制, 测试"
EP4556917A1,EP24187394.2,"测试台 （2） 用来对抗试样 （4） 的模拟电气故障。试验台包括一个用于编排模拟电气故障的中央故障控制单元（10），以及至少一个作为故障插入单元（FIU）设置的本地节点（8a），其中有一个用于伪造试验台中选定电流的开关安排（16a）。故障控制单元设置为创建一个抽象的故障描述，该描述指定至少一个电气故障，以便在电线（6a）上开启，并将其发送到本地节点。本地节点已设置，从抽象故障描述中得出一个适合将电故障插入电线的开关安排的控制规则，并通过按照控制规则控制开关安排将电故障插入电线。
",G01R31/28,实质审查,故障注入板卡技术,3.0,关键词匹配: 故障插入; IPC分类号匹配: G01R31/28,"包括, 模拟, 设置, 测试, 控制","控制, 测试"
EP4553662A1,EP24206600.9,"本发明涉及一种计算机实现的方法，用于基于标准创建一个虚拟测试（VT）的配置（10），以测试车辆的自动驾驶功能，其中包括定义配置参数（12a，12b）对虚拟测试（VT）的目标参数（14）的依赖性（S1），确定 （S2） 测试算法 （A） 和/或参数间隔 （16） 用于各个目标数量 （14）；并设置（S3）配置参数（12a，12b）的应用条件（18），参数间隔（16）和/或测试算法（A）。此外，本发明还涉及基于标准创建用于测试车辆自动驾驶功能的虚拟测试（VT）的配置（10）的系统。
",G06F11/3668,实质审查,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/3668,"包括, 方法, 实现, 配置, 设置, 测试, 算法, 系统, 计算","驾驶, 车辆, 测试"
US12278074B2,US18064348,"一种用于具有包括输出电压的输出的模拟电池单元的保护电路，包括：至少一个MOSFET，其连接到模拟电池单元的输出，用于短路模拟电池单元的输出；电容器，连接到至少一个MOSFET的栅电极；过电压检测装置，其被配置为基于超过所述输出电压的过电压限制，利用所述输出电压对所述电容器充电；以及阈值电压检测装置，其被配置为基于未达到电容器处的电压的阈值来释放栅电极。
",H02H9/00,暂缺,故障注入板卡技术,2.0,IPC分类号匹配: H02H9/00,"包括, 检测, 装置, 具有, 配置, 电路, 模拟",检测
EP4506824A8,EP23189912.1,"描述和示出了一种计算机实现的方法（1），用于借助于模拟器（2）上的模拟环境（4）的至少一个计算单元（5）来验证ECU的至少一种ECU功能（f1，f2，f3）的执行，其中，在面向事件的离散仿真中，在连续仿真步骤之间的离散渐进仿真时间（tsim）的零时间假设下，在模拟器（2）上执行ECU功能（f1，f2，f3）。
通过基于模拟器（2）的观测服务（6）比较离散模拟时间（tsim）的进度和模拟器实时（treal）的进度，如果模拟器实时（treal）的进度超过离散模拟时间的进度，可以有效地确定模拟中堵塞情况的原因它（tsim）至少间接地超过预定的极限值（td）多个堆轨道（7），所述堆轨道创建至少一个计算单元（5）（8）其在相应堆轨迹（7）的创建（8）时最频繁地执行了至少一个计算单元（5），并且显示和/或进一步处理所确定的最常见的ECU功能（fmax）。
",G06F11/36,实质审查,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"仿真, 方法, 处理, 实现, 模拟, 计算",验证
EP4407466B1,EP22197283.9,"一种用于在模拟器上测试作为虚拟控制装置的至少一个电子控制装置的方法，该模拟器包括至少一个模拟器计算单元。电子控制装置具有硬件配置，该硬件配置具有至少一个计算单元(和用于交换数据的外部接口，软件配置被分配给硬件配置，电子控制装置被映射到虚拟控制装置，其中电子控制装置的软件配置的内部功能被接管作为虚拟控制装置的软件配置的内部功能，电子控制装置的外部接口功能被模拟器计算单元的软件配置的数据传输功能替代，虚拟控制装置的软件配置被转换成用于模拟器计算单元的可执行代码并在模拟器上执行。
",G06F11/26,权利终止,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/26,"包括, 方法, 控制, 装置, 具有, 配置, 测试, 模拟, 计算","数据传输, 控制, 测试"
JP2025031511A,JP2024074257,"减少对形成电池模拟器的电构成元件的需求。第一电路，其提供用于模拟第一电池单元的电压的第一单元电压；以及第二电路，其提供用于模拟第二电池单元的电压的第二单元电压。第一单元电压连接到局部电路接地，第二单元电压连接到相同的局部电路接地，并且与第一单元电压串联连接，使得局部电路接地形成第一单元电压和第二单元电压的极。控制电子装置被配置为对第一单元电压和第二单元电压进行闭环控制。通过通过一个控制电子设备驱动控制两个单元电压，可以省略构成元件。图2是示出图2
",G01R31/367,暂缺,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/367,"提供, 设备, 控制, 装置, 配置, 电路, 模拟",控制
EP4455891A3,EP24159546.1,"本发明涉及一种计算机实现的方法和系统，用于基于标准创建一个包含虚拟车辆环境的场景库（10），用于测试机动车辆的高度自动化驾驶功能，具有：使用预定标准，由传感器数据（12）包含的第一个场景数据集（16），代表感兴趣的驾驶情况（14）；比较（S3）代表感兴趣的驾驶情况（14）的第一个场景数据集（16）与由场景库（10）包含的第二个场景数据集（18）。本发明还涉及一种计算机实现的方法，用于验证模拟模型的模型质量，以执行机动车辆的高度自动化驾驶功能。
",G06F11/36,实质审查,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"方法, 模拟, 实现, 具有, 测试, 模型, 系统, 计算","驾驶, 传感器, 验证, 测试, 机动车, 车辆"
EP4343552B1,EP23191816.0,"一种用于连接到与总线或网络接口连接的被测对象的故障插入单元，其中，所述被测对象可以借助于所述故障插入单元经受大于所述总线或网络接口被设计用于的最大电压的故障电压，所述故障插入单元具有熔丝电路，所述熔丝电路保护连接到所述故障插入单元的总线或网络接口免受大于所述总线或网络接口被设计用于的最大电压的电压的影响。这提供了一种即使在以高带宽总线工作的系统中也能够使用故障插入单元而不存在由于过电压而损坏总线或网络接口的风险的方式。
",G01R31/327,授权,故障注入板卡技术,3.0,关键词匹配: 故障插入; IPC分类号匹配: G01R31/327,"提供, 具有, 电路, 系统",网络
US20240337695A1,US18619303,"一种用于使用消息来测试控制设备的测试组件，包括：接收器，其被配置为以片段形式接收所述消息，其中每个片段具有第一标识符和第二标识符，所述第一标识符表示所述消息中的相应片段的时间顺序；缓冲存储器，被配置为基于第一和第二标识符存储片段，其中第二标识符使得消息的所有片段被及时记录在缓冲存储器中，以允许在消息的第一片段被发送到控制设备之前操纵消息；机械手，被配置为基于预定数据修改消息；以及发送器，其被配置为将修改后的消息发送到控制设备。
",G01R31/319,暂缺,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/319,"设备, 包括, 具有, 配置, 测试, 控制","控制, 测试"
EP4390699A1,EP23193448.0,"本发明涉及一种计算机实现的方法和系统，用于确定车辆设备和/或车辆功能的虚拟测试和/或模拟的无错误执行所需的相互兼容的系统元件（10a、10b、10c、10d），通过以下步骤：提供（S1）预先记录的选择或选择执行车辆设备和/或车辆功能的虚拟测试和/或模拟所需的第一系统元件（10a）中的至少一个；确定（S2）支持第二系统元件（10b）的第一系统元件（10）的兼容性要求中的至少一个，以及输出（S3）指定的第二系统元件（10b），其支持正确执行车辆设备和/或功能的虚拟测试和/或模拟所需的第一系统元件（10）的兼容性要求。
",G06F11/263,实质审查,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/263,"提供, 设备, 方法, 模拟, 实现, 测试, 系统, 计算","车辆, 测试"
EP4390700A1,EP23195862.0,"本发明涉及一种计算机实现的方法和系统，用于确定执行虚拟测试和/或模拟（a）的算法的特定版本，特别是测试和/或者模拟算法（a）所需的、车辆设备和/或者车辆功能的虚拟测试和／或模拟的无错误执行，兼容测试和/或模拟数据（24a、24b、24c、24d；124a、124b、124c、124d）的配置，包括测试和/或模拟数据（24a、24b、24c、24d；124a、124b、124c、124d）的第一配置（10）和/或第二配置（12）的兼容性要求（14a、14b）之一的确定（S2），以及指定数据集（D1，D2）的输出（S3），其支持测试和/或模拟数据（24a，24b，24c，24d；124a，124b，124c，124d）的第一配置（10）和/或第二配置（12）的兼容性要求（14a，14b）。
",G06F11/263,实质审查,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/263,"设备, 包括, 方法, 模拟, 实现, 配置, 测试, 算法, 系统, 计算","车辆, 测试"
EP4325366A1,EP22193650.3,"本发明涉及一种用于执行用于机动车辆的至少部分自主引导的设备（10）的虚拟测试的方法和系统（1），包括使用驾驶状况参数的至少一组参数（P）通过算法（a）执行（S2）虚拟测试执行的虚拟测试模拟至少一组驾驶状况参数（P）；并且如果满足给定和/或算法（a）条件（B），在虚拟测试的运行时间修改（S4）由至少一个车辆传感器（12a）检测到的至少一个第一参数（P1）和/或与车辆致动器（12b）相关的第三参数（P3）。
",G06F11/36,实质审查,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"设备, 包括, 检测, 方法, 模拟, 测试, 算法, 系统","驾驶, 检测, 传感器, 测试, 机动车, 车辆"
EP4318245A1,EP22196810.0,"本发明涉及一种用于分析用于机动车辆的至少部分自主引导的设备（10a）和/或功能（10b）的测试工具的方法，其中，针对多个测试用例（T1）中的每一个计算的第一特征值与表示元测试用例（T2）的第二特征值的聚合（S4）；以及使用预定的第二标准（18）并且通过将大多数测试用例（T1）的第一评估结果（14a）与元测试用例（T2）的第二评估结果（18a）逻辑连接来对第二特性（16）进行评估（S5）。此外，本发明涉及一种用于分析用于机动车辆的至少部分自主引导的装置（10a）和/或功能（10b）的测试执行的系统（1）。
",G06F11/36,实质审查,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"设备, 方法, 装置, 测试, 工具, 系统, 计算","机动车, 测试"
US20240028457A1,US18224778,"在FPGA中，通过以下步骤检测FPGA内的错误：在可配置逻辑块中提供至少一个计算操作，添加奇偶不变的附加结果，通过选取全加法器的XOR操作的XOR位来提供奇偶不变的附加结果，该XOR操作包括至少两个输入信号；借助下式形成输入信号的XOR操作的奇偶性，并提供奇偶性信号：奇偶性(XOR (x1，x2))；借助用于检验奇偶性的设备，使用XOR (奇偶性(x1)，奇偶性(x2))来计算输入信号的携带奇偶性(奇偶性(x1)，奇偶性(x2))的XOR操作；使用下式的真实性的检验来检验奇偶性：XOR (奇偶性(x1)，奇偶性(x2)) = =奇偶性(XOR (x1，x2))；在前述步骤的公式的非正确陈述的存在下检测FPGA内路线/计算中的错误。
",G06F11/14,公开,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/14,"提供, 设备, 包括, 检测, 配置, 计算",检测
EP4300316A1,EP22182519.3,"本发明涉及一种用于基于标准创建具有虚拟车辆环境的场景库（10）的计算机实现的方法和系统（1），该场景库用于测试机动车辆的高度自动化驾驶功能，该方法和系统包括多个场景元素（14）中的一个的比较（S2），具有所述数量的测试场景数据集（12）和用于创建所述场景库（10）的所述需求简档（16）的至少一个集群的进一步的测试场景数据库（18），其中所述比较包括用于将场景元素（14）聚类到由所述场景库（10）和所述另一测试场景记录（18）覆盖的测试场景记录的数量（12）的算法（A）的应用（S3）。
",G06F11/36,实质审查,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"包括, 方法, 实现, 具有, 测试, 算法, 系统, 计算","机动车, 驾驶, 车辆, 测试"
US20230401145A1,US18208969,"一种使用至少一个测试和/或仿真的存储的规范部件的计算机实现的方法，包括以下步骤：提供待规范的所述至少一个测试和/或用于测试车辆的行驶功能的所述一个仿真，并且通过至少一个参数值和/或设置值来确定所述至少一个测试和/或所述至少一个仿真；以及执行所述至少一个测试和/或所述至少一个仿真的规范，其中，所述规范包括至少一个规范部件，其中，选择所述参数值和/或设置值，并且为所述测试和/或仿真分配由所述规范确定的参数值和/或设置值，其中，人工地或自动地选择所述参数值和/或设置值，并且其中，人工地和/或自动地选择已经存储的规范部件并且将其集成到所述规范中。
",G06F11/36,暂缺,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"提供, 仿真, 包括, 方法, 实现, 设置, 测试, 计算","车辆, 测试"
US20230341496A1,US18187701,"一种高电压电子设备具有串联连接在高电压电子设备的端子之间的多个电子模块。一种用于校准高电压电子设备的系统包括：多个可控模块开关，其中，对于多个电子模块中的每个相应的电子模块，相应的可控模块开关被配置为通过相应的电子模块的闭合来分流相应的电子模块。多个可控模块开关被配置为被致动，使得：在多个可控模块开关的第一状态下，没有电子模块连接到端子；在多个可控模块开关的第二状态下，一个电子模块连接到端子；以及在多个可控模块开关的第三状态下，多个电子模块连接到端子。
",G01R31/3842,暂缺,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/3842,"设备, 包括, 具有, 配置, 系统",通用
EP4202452A1,EP21216287.9,"这是一个装置。提出了一种校准电池模拟器的方法。电池模拟器模拟串联切换的多个电池单元，其中每个模拟的电池单元具有抽头，通过该抽头可以抽头至少一个模拟量，其中该设备具有切换设备，校准标准可以通过该切换设备与不同的抽头可切换地连接。
",G01R31/00,实质审查,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/00,"设备, 方法, 装置, 具有, 模拟",通用
EP4195435A1,EP21213957.0,"本发明的目的是一种用于模拟电池单元（1）的保护电路，连接到模拟电池单元（1）的用于短路的输出端（6）的电容器（3），与至少一个MOSFET（2）的栅极电极（7）连接的过电压检测装置（4），其被配置为当超过输出电压过电压极限时用输出电压对电容器（3）充电，被布置为在所述电容器（3）处的电压的阈值下释放所述栅电极（7）。
",H02H9/04,实质审查,故障注入板卡技术,2.0,IPC分类号匹配: H02H9/04,"检测, 装置, 配置, 电路, 模拟",检测
EP4191265A1,EP21211759.2,"示出并描述了一种用于测试功率电子控制单元(2)的测试装置(1)，控制单元(2)具有用于提供能量的供电端子(3)和用于控制电负载的负载端子(4)，测试装置具有：多个功率电子模块(5)，每个功率电子模块(5)提供用于提供能量的供电端子(6)；至少一个用于提供至少一个电连接尺寸的负载端子(7)；用于提供电控制电压的供电电路；选择电路(10)，其具有用于将控制电压中的一个连接在功率电子模块(5)的负载端子(7)上的功率开关(11)和用于控制功率开关(11)的接口(12)，其中，在测试装置(1)的运行状态中，控制单元(2)的供电端子(3)和/或负载端子(4)分别与功率电子模块(5)的负载端子(7)连接，以便在控制单元(2)的供电端子(3)和/或负载端子(4)上提供期望的电连接尺寸，并且功率电子模块(5)的供电端子(6)经由电中间网络(13)彼此连接。通过如下方式简化了将AC供电部件集成到测试装置中：功率电子模块的供电端子(6)构造为AC供电端子(6 ~)，并且电中间网络(13)构造为AC中间网络(13 ~)，并且供电电路构造为多相电路(8)，以便在多个相导体(9)上提供多个相电压，选择电路(10)利用功率开关(11)构造为用于将相导体(9)连接到功率电子模块(5)的负载端子(7) 。
",G01R31/42,实质审查,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/42,"提供, 装置, 具有, 电路, 测试, 控制","网络, 控制, 测试"
EP4155943A1,EP22163418.1,"一种用于自动测试功能，特别是安全功能的计算机实现方法，该方法集成到从车辆中的数据收集到更新车辆中的驾驶功能和/或待测试的至少部分自动驾驶功能的虚拟认证的端到端过程中。
",G06F11/36,实质审查,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"方法, 计算, 实现, 测试","驾驶, 车辆, 测试"
US20230025895A1,US17857192,"一种用于经由模拟测试控制单元的系统，包括：模拟器；主机；以及用于通信系统的至少一个连接。至少一个通信工具存储在系统上。真实控制单元可经由通信系统连接至系统。在系统上提供至少一个控制器，用于至通信系统的连接。在系统上存储用于至少一个控制器的驱动器软件。至少一个通信工具被配置为生成用于模拟控制单元和/或真实控制单元之间的通信的通信代码，其中，通信代码被配置为与驱动器软件交互，并且将来自真实控制单元和模拟控制单元的信号和/或消息中继至驱动器软件，并且从驱动器软件接收信号和/或消息。提供用于驱动器软件的循环模式。
",G06F11/36,授权,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"提供, 包括, 控制, 模拟, 配置, 测试, 生成, 工具, 系统","通信, 控制, 测试"
EP4027245A1,EP21204716.1,"一种用于通过确定交通场景中的道路用户的运动简档来确定交通场景的相似性值的计算机实现的方法，具有以下步骤：利用运动元素中的至少一个运动元素创建道路用户的至少一个运动简档，横向运动、纵向运动、位置和/或距离参数，其中运动简档中的新分段以至少一个运动元素中的每个变化开始；创建交通场景的本地车辆和/或同地车辆的至少一个运动简档的序列，其中针对至少一个运动元素的分段中的变化使用序列变化；通过比较从运动简档创建的序列，基于来自至少两个交通场景的运动简档来确定相似性度量；提供相似性度量。
",G06F11/36,实质审查,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"提供, 方法, 实现, 具有, 计算",车辆
US11255909B2,US16229696,"本发明涉及一种用于使检验装置同步的方法，其中检验装置被构造用于测试至少一个第一电子调节单元。此外，本发明还涉及一种可转移到同步状态的检验装置。此外，本发明还涉及一种复合系统，其包括至少两个检验装置。此外，本发明还涉及一种用于测试至少一个第一调节单元的检验装置以及一种具有至少一个检验装置和另一检验装置的组合系统，其中，所述另一检验装置被设计用于具有与所述第一检验装置相同的作用。
",G06F11/00,授权,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/00,"包括, 方法, 装置, 具有, 测试, 系统",测试
EP3739479B1,EP19174383.0,"一种用于对计算机系统的编程逻辑进行故障检修的方法。在计算机系统的第一可编程门阵列上对第一逻辑电路和与其通信地隔离的第一监视电路进行编程。在计算机系统的第二可编程门阵列上对第二逻辑电路和与其通信地隔离的第二监视电路进行编程。在计算机系统的编程逻辑中已经检测到错误之后，在第一可编程门阵列中对第一信号线进行编程而不改变第一逻辑电路，所述第一信号线将来自第一逻辑电路的信号施加到第一监视电路的第一信号输入端，并且在第二可编程门阵列中对第二信号线进行编程而不改变第二逻辑电路，所述第二信号线将来自第二逻辑电路的信号施加。
",G06F11/36,授权,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"检测, 方法, 电路, 系统, 计算","检测, 通信"
US10747649B2,US15996850,"一种用于在检验装置中传输计量地采集的和数字化的测量数据的方法和装置。测量数据对应于程序任务，并且测量数据从测试设备的测量数据发送器的传输方向经由数据信道被提供给测试设备的测量数据接收器。测量数据发送器具有信号预处理器、任务监视处理器和数据信道仲裁器。经由该任务监控处理器，在该程式任务的执行开始或在该程式任务的执行结束时产生一任务ID资料封包，将所述任务ID数据包发送给所述数据通道仲裁器。经由数据通道仲裁器，测量数据和任务ID数据包经由数据通道作为数据流被连续转发到测量数据接收器。
",G06F11/36,授权,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"提供, 设备, 方法, 装置, 处理, 具有, 测试",测试
EP3647801A1,EP18203310.0,"第一FPGA程序（1）的错误检测程序，其中，FPGA程序（1）在FPGA（2）上执行，FPGA（2）连接到处理器（3），监视程序（4）在处理器（3）上执行，信号值 （5、6、7、8）从FPGA程序（1）中读出，并馈送到监视程序（4），监视程序（4）的信号值（5、6、7、8）带有 比较其他来源的参考值（9）。
",G01R31/3185,撤回,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/3185,"检测, 处理",检测
EP2685382B1,EP13166958.2,"该方法包括创建控制单元程序组件和测试场景程序组件的可执行程序。创建运行时环境。提供一种元件测试服务，其提供一介面至依据自动开放系统架构(AUTOSAR)标准之运行时间环境，作为一介入点，用以操控运行数量IME环境。测试场景程序组件通过用于运行时环境的适当代码生成来允许对该数量的运行时环境的访问。本发明涉及一种开发设备，用于提供干预点，以操纵用于在测试环境中测试控制单元程序组件的运行时间环境的量。
",G06F11/36,授权,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"提供, 设备, 包括, 方法, 控制, 测试, 生成, 系统","控制, 测试"
US20190065356A1,US16119718,"用于一试验设备可测试性设计的一组态系统一电子控制单元。试验设备是一台硬件回路模拟器或一台快速的样机控制系统模拟器，其中一技术系统的一软件模型在试验设备上被执行，并且软件模型电子地经由具有一种系统的试验设备的一输入输出接口传送被测试那连接到试验设备。仿真数据电子地是由通信传输，组态系统被连接到一建模系统并在建模系统中是其特征在于横向地和纵向地连接的处理功能块的软件模型。组态系统通过互连的配置条目配置试验设备，使得配置条目确定输入输出接口的物理特性和/或与软件模型的输入输出接口的连接。
",G06F11/36,未缴年费,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"设备, 仿真, 控制, 模拟, 处理, 具有, 配置, 测试, 模型, 系统","通信, 控制, 测试"
CN105426280B,CN201510680947.6,"本发明涉及一种用于部分释放第一可编程的硬件构件的调试接口的设备，其中，在配置存储器上可存储用于所述可编程的硬件构件的第一逻辑，并且配置装置构造用于，借助所述可编程的硬件构件的配置接口按照第一逻辑对所述可编程的硬件构件进行编程.所述配置装置另外构造用于，记录借助调试接口按照第二逻辑进行的对所述可编程的硬件构件的编程过程并且在借助调试接口进行的编程过程结束后按照第一逻辑重编程所述可编程的硬件构件。
",G06F11/26,授权,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/26,"装置, 设备, 配置",通用
JP2018041450A,JP2017158131,"“课题”提供能够模拟技术系统的操作软件和其硬件依赖性的软件部分的简单且低成本的手段。“解决手段”是用于打开循环控制或控制设备的控制单元的操作软件20，以硬件依赖性的软件来代替硬件依赖性的软件部分。用等价函数23模拟亚部分的功能行为。为了改变所需的变更，自动与硬件依存性的软件部分进行同定，自动生成等价函数。改变的操作软件在适当的模拟环境200上执行时，不依赖技术系统的实际软件模拟技术系统。【选择图】图3
",G06F11/34,授权,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/34,"提供, 设备, 模拟, 系统, 生成, 控制",控制
EP3232327B1,EP16165503.0,"用于监视差错的方法当测试在一模拟环境（40）中的一控制设备的一控制程序（10）时，被在一台计算机上的一仿真程序执行的控制程序（10），仿真程序，其分配项目（50 ，60 ，70）的一扩充域到控制程序（10）的程序变量，一变量值（52 ，62 ，72）分配给储存在项目（50 ，60 ，70）的扩充域的一程序变量的，作为错误的或非错误的，对一第一纲（K1）和错误的程序变量到一第二类（K2），在具有一数据区（64）的第二类（K2）中的每一程序变量的项目（60）的扩充域，或以存储在项目（70）的扩充域中的一误差场（76），被分配给被分配给一错误的程序变量的误差场（76），每一程序变量的项目（70）的扩充域的一非错误的程序变量和一误差值的误差场（76）的一有效性值为基础进行的标记以非错误的程序变量的一个分配为基础进行的标记的仿真程序标注程序变量，其具有一数据区（74）。
",G06F11/26,授权,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/26,"设备, 仿真, 方法, 控制, 具有, 测试, 模拟, 计算","控制, 测试"
EP2363809B1,EP11153945.8,"本发明涉及用于执行机构的控制程序的优化方法。在该方法中，将分配给参数(P1)的值(W1)存储在分配给一个模型单元的存储区域中。通过代码发生器(CG)利用分配给函数(F1)的程序线路来产生一个优化的控制程序(OCC) 。通过代码发生器的优化单元(OE)从所分配的存储区域中读取分配给参数的值。根据由优化单元进行的比较来确定一个结果。
",G06F11/36,授权,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"模型, 方法, 控制",控制
US20180039566A1,US15229231,"计算机实施的方法，用于控制程序，即模型化为一个或更多个块的计算环境的框图。第一用户接口提供用于选择模拟模式操作的框图，和第二用户提供用于选择编译器意欲产生代码编译。当确认在环仿真模式已经被选择的第一用户界面，这块的框图转换成生产代码和编译成可执行的使用编译器选择在第二用户界面。通过运行该电脑主机可执行而记录一个或多个数据点基于输入/输出信号和/或评估遵守一个或多个数据指向一个或多个标准，控制程序对应于所述一个或多个块的测试。
",G06F11/36,授权,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"提供, 仿真, 方法, 模拟, 测试, 模型, 控制, 计算","控制, 测试"
EP3203376A1,EP16154002.6,"描述并描述了用于模拟剩余总线控制单元连接（2）的计算机实现的过程（1）。如果剩余总线控制单元组合（2）包括至少两个连接到总线系统（3）（2a，2b）的剩余总线控制单元，并且剩余总线控制单元组合（2）经由总线系统（3）连接到至少一个附加控制单元（4），则剩余总线控制装置（2a，在通信关系的基础上，创建程序代码（5，5a，5b）以模拟剩余总线控制器（2a，2b），并且剩余总线控制器化合物（2）通过程序代码（5）的可执行版本在模拟计算机（6，6a，6b）上模拟。
通过为剩余总线控制装置（2a，2b）生成单个公共剩余总线控制装置模型作为程序代码（5）来模拟剩余总线控制装置（2a，2b），可以简化和灵活地模拟剩余总线控制单元组合（2）。
",G06F11/26,驳回,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/26,"包括, 控制, 模拟, 装置, 实现, 生成, 模型, 系统, 计算","通信, 控制"
US20170168920A1,US15367216,"传送有效载荷数据从缓冲区到目的数据存储器被提供，使得这些数据可以在那里进行处理，由计算机辅助开发环境。为此，数据管理环境提供了缓冲和目的地存储。具有有效载荷数据的数据记录和语义数据彼此关联，带有有效载荷数据提供缓冲器，和一个数据对象与处理特定的对象语义的数据提供存储。对象实例化与有效载荷数据通过语义数据那个有效载荷数据放置在数据对象作为该对象的功能语义数据对象以开发环境可以基于对象语义的有效载荷数据。
",G06F11/36,申请终止,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"处理, 提供, 具有, 计算",通用
EP3104278A1,EP15171801.2,"本发明涉及一种用于确定实时系统中的控制单元的控制程序的功能所需的运行时间的过程和设备，所述实时系统是在循环处理器（PIL）仿真中的目标处理器上执行的，在创建控制单元的图形开发模型或将其加载到开发环境中的过程中，图形开发模型通过图形开发模型中的功能块来显示控制单元的功能；在图形开发模型中至少选择一个功能块，特别是通过图形用户界面选择功能块，根据图形开发模型自动生成控制程序在目标处理器上运行，控制程序包含该功能，它表示所选功能块的功能。此外，第一运行点与控制程序中的函数的开始相关联，第二运行点与函数的结束相关联，在控制程序中，在函数的第一运行点之前，所使用的目标处理器的高速缓存处于预定状态，并且在目标处理器上的控制程序的第一和第二运行点处测量运行值，从中确定到期日。
",G06F11/30,撤回,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/30,"设备, 仿真, 处理, 系统, 生成, 模型, 控制",控制
EP2942851B1,EP14167629.6,"一种用于监测具有电容负载的电力消耗装置的功耗的方法，所述可控电路元件和所述装置串联。检测流经所述装置的电流的幅值、所述装置上的电压降、以及所述装置上的电压降的随时间变化。根据所述装置上的电压降和预定功率来计算允许的工作电流幅值。根据所述装置上的电压降的随时间变化来计算所述电容负载的充电电流幅值。计算允许的瞬时电流幅值。将所述允许的瞬时电流幅值与流经所述装置的电流的幅值进行比较，并且如果流经所述装置的电流的幅值大于所述允许的瞬时电流幅值，则增大所述电路元件的电阻。
",H02H9/02,授权,故障注入板卡技术,2.0,IPC分类号匹配: H02H9/02,"检测, 方法, 装置, 具有, 电路, 计算",检测
EP3001318A1,EP14186124.5,"本发明涉及一种自动测定方法modellsignalen FPGA的FPGA方案的生成（4）和一个FPGA回读（8）从提取的步骤，产生一个综合的FPGA模型（2），并生成一个代码（3）FPGA的FPGA模型（2）但过程结束之前的步骤，生成的代码（3）FPGA的FPGA模型（2）额外的步骤，自动检测的analysierens从FPGA（8）包括一个auslesbaren读回信号的步骤和程序，ausgebens金的FPGA（8）包括一个auslesbaren读回信号。本发明还涉及一种datenverarbeitungseinrichtung（1）执行上述程序。
",G06F11/36,驳回,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"包括, 检测, 方法, 生成, 模型",检测
EP2977905A1,EP15177297.7,"本发明的目的是防止第一可编程硬件组件（1）的配置的布置，使用第一可编程硬件组件（1），第二可编程硬件组件（2）和开关元件（3），其中，第一可编程硬件组件（1）是设计用于配置第一可编程硬件组件（1）的逻辑（4）的配置接口（5），数据接口（7），设计用于将逻辑（4）与第二可编程硬件组件（2）通信，调试接口（8），设计用于调试和配置逻辑（4）和配置监控接口（9），设计用于向逻辑（4）的配置过程发送信号，其中开关元件（3）连接到调试接口（8），并且设计为在配置过程中可中断对调试接口（8）的访问。
",G06F11/36,撤回,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,配置,通信
JP2015191671A,JP2015065615,"要解决的问题：提供交互方法对控制程序的控制装置，能够扩展目前
SOLUTION：程序代码的控制程序( STP )检查，转移地址与函数调用包括程序代码的控制程序和变量与各第一功能( F1A，F1B、F1C、体量，确定相应的存储器地址F1N )基于各个变量。确定第一函数和变量分别对应于第一功能存储在第一分配表( ZUORD 1 )与相关联的存储器地址它们。基于比较在第一分配表并设定了第二分配表( ZUORD 2 )，至少一些函数名分配给多个第一功能，第一值的至少一个变量被第二值。
",G06F11/00,未缴年费,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/00,"提供, 包括, 方法, 装置, 控制",控制
CN204405746U,CN201390000553.6,"本实用新型涉及一种用于测试电气部件的设备(1)，所述设备具有一个用于生成模拟信号的模拟装置(2)、多个测试装置(4)以及至少一个电气连接装置(5)，其中，所述模拟装置(2)和多个测试装置(4)通过所述至少一个连接装置(5)导电地连接或能连接，并且所述至少一个连接装置(5)具有至少一个电气开关装置(9)，所述开关装置这样设置，以便断开或接通在多个测试装置(4)之间的电气连接。
",G01R31/00,期限届满,故障注入板卡技术,2.0,IPC分类号匹配: G01R31/00,"设备, 装置, 具有, 设置, 测试, 生成, 模拟",测试
US20140215270A1,US14167154,"一种操纵存储器操作程序存储器的控制单元上的虚拟或真实电子控制单元( ECU )，例如用于车辆中，例如。操纵该存储操作的完成是通过存储器组件处理程序，通过这种一组操纵功能，提供从所述至少一个操纵功能被选择，以使得该功能，通过激活存储器组件处理程序，改变了存储器访问由控制单元根据所选择的处理函数程序执行期间的控制单元的程序。
",G06F11/26,授权,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/26,"处理, 提供, 控制","车辆, 控制"
EP2759939A1,EP13152993.5,"方法涉及提供一组操纵功能，以及选择操纵功能。激活存储器操纵程序组件(100)，以便基于所选择的功能改变由控制单元程序发起的存储器访问，其中该组件形成离散的程序组件。该组件在定义的接口上与存储器操纵接口程序组件(30)、存储器操纵程序组件(7)和/或存储器操纵程序组件连接。通过代码生成来控制存储器操纵程序组件。
",G06F11/36,授权,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"生成, 提供, 方法, 控制",控制
US20130268919A1,US13911311,"检测内存泄漏产生的本发明涉及的一种方法用于由运行于计算机上的程序，其中在程序运行期间，每个分配存储区域有关的至少一个信息项所进行过的分配进入分配和检查列表是否在分配列表包括所存储的参考图案的信息的一个或更多个内存泄漏是典型的，并在找到所存储的参考图案运行的暂停，或存储器泄漏，因此检测与区域比赛中没有赢的希望的程序谁的处理导致中最近的条目分配列表。本发明还涉及一种计算机程序产品，当所述计算机程序进行了执行该方法的计算机程序。
",G06F11/36,公开,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"包括, 检测, 方法, 处理, 计算",检测
CN102016805B,CN200980116525.9,"本发明涉及校正至少一个信源(1)传送给至少一个信宿(2)的至少一个数字信息的方法，信源借助数据传输媒介(4)与信宿、也与校正设备(3)连接，信源为信宿提供的信息包括第一变量名，具有以下步骤：a)在校正设备(3)的第二存储区(32)中提供列出的变量名，b)信源发送包含第一变量名的信息，c)从传输的信息中提取第一变量名并存储在第一存储区(31)中，d)比较第一变量名与列出的一个变量名并基于该比较确定决定标准(EK)，e)基于决定标准(EK)决定第一变量名是保持不变还是在步骤c)中用列出的这个变量名替换，或者是否利用列出的另一变量名并借助确定另一决定标准而重复步骤d)和e)。本发明还涉及校正设备。
",G06F11/00,授权,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/00,"提供, 设备, 包括, 方法, 具有",数据传输
CN102495794A,CN201110376528.5,"本公开涉及一种用于在计算机支持下以图形方框的形式设置测试过程的设备，所述设备包括用于配置所述图形方框的装置和用于将所述图形方框相互嵌套的装置，所述设备包括用于解释所述图形方框的装置。本公开的一个实施例解决的一个问题是将图形的测试过程转换为机器可读的代码。根据本公开的一个实施例的一个用途是图形地开发自动测试。
",G06F11/36,申请终止,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"设备, 包括, 装置, 配置, 设置, 测试, 计算",测试
JP2008287709A,JP2008102563,"要解决的问题：に构成装置，能进行影响或传输存储内容的存储器属于控制装置在不同工作状态控制装置，并提供相应的方法。
SOLUTION：适配单元( 6 )用于执行选择性构型之间的数据连接的控制装置( 2 )和至少一个影响装置( 4 )来影响控制装置( 2 )包括存储器( 32 ) 。存储器( 32 )存储至少一部分装置之间传送的数据的影响( 4 )与控制装置( 2 )，即数据读取和/或写入由微控制器的接通的控制装置( 2 ) 。
COPYRIGHT：( C ) 2009、JPO&INPIT
",G06F11/28,驳回,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/28,"提供, 包括, 方法, 装置, 控制",控制
EP1947568A1,EP07024784.6,"该方法涉及提供具有跟踪功能的调试接口以监视跟踪地址域中的跟踪地址范围内的监视地址(8A-8c) 。在控制器中提供监视服务以供通过微控制器执行。地址域中的跟踪地址范围被分配有预设地址(11) 。地址域中的监视地址被分布或传送到服务。监视地址的地址内容被顺序地复制在跟踪地址范围的预设地址中。
",G06F11/36,驳回,故障注入板卡技术,2.0,IPC分类号匹配: G06F11/36,"提供, 方法, 具有, 控制",控制
