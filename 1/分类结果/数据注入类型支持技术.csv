﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,当前法律状态,技术领域,分类得分,分类理由,技术手段,应用场景
EP4597303A1,EP24155284.3,"该发明涉及一种计算机实现的方法和系统（1），用于识别至少一个用于生成虚拟构件（10）的代码更改（14），特别是用于测试和/或验证车辆的预定功能，尤其是自动化驾驶功能的虚拟控制器，包括以下步骤：提供（S1）至少一个源代码段（12）以测试和/或验证车辆的预定功能；应用（S2）机器学习算法（A）到所述至少一个源代码段（12）以识别至少一个所需的代码更改（14）；以及输出（S3）一个数据集（16），包含生成虚拟构件（10）以测试和/或验证车辆的预定功能所需的代码更改（14）的信息。此外，本发明还涉及一种计算机实现的方法，用于提供机器学习算法（A）。
",G06F8/30,,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/30,"提供, 包括, 方法, 控制, 实现, 测试, 生成, 算法, 系统, 计算","驾驶, 验证, 测试, 车辆, 控制"
EP3812885B1,EP19205062.3,"根据本发明，从模型生成模拟代码和生产代码作为源代码。所述模型包括一个或多个块，所述块规定了程序、尤其是控制程序的期望行为。所述块中的至少一个用模拟代码属性来标记。针对包含模拟代码属性的那些块生成模拟代码。针对所有其他的块生成生产代码。所生成的源代码包括模拟代码部分和生产代码部分两者。模拟代码部分被包含在源代码中，以使得它们可以与生产代码部分分离。
",G06F8/35,,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/35,"包括, 模拟, 生成, 模型, 控制",控制
EP4567585A1,EP23214679.5,"该发明涉及一种计算机实现的方法用于从开发平台的图形控制模型生成目标平台的控制程序该开发平台包括用于存储有关图形控制模型的信息的定义数据库并被设计为支持矩阵运算由此特征在于在定义数据库中可以存储关于图形控制模型中使用的矩阵结构的注释信息，并且在为目标平台生成控制程序时，应考虑存储的注释信息，使得a）在执行至少涉及一个矩阵的编辑任务的目标平台上，计算速度有所提高和/或b）在目标平台保存矩阵时所需存储空间减少。
此外本发明涉及一种用于配置作为控制单元（ES）设计的目标平台的方法。
此外本发明涉及一种数据处理装置(10)包括执行上述方法的手段计算机程序产品和相应的计算机可读存储介质
",G06F8/41,实质审查,数据注入类型支持技术,3.0,关键词匹配: 数据处理; IPC分类号匹配: G06F8/41,"包括, 方法, 平台, 装置, 实现, 处理, 配置, 生成, 模型, 控制, 计算",控制
EP4557078A1,EP24157503.4,"本发明涉及从块图的一个或多个块生成源代码的方法，该块图包括至少两个块和块之间至少一个信号连接。生成源代码包括将块图转换为临时视图，连续优化临时视图，并将优化临时视图转换为源代码。本发明的优化包括识别至少两个并行的控制流分支，其中一个是读取或写入的，识别访问该变量的操作，检查成本标准，并在满足成本标准时在至少两个并行的控制流分支中复制操作。此外，本发明还涉及配置控制装置、计算机程序产品和计算机系统的方法。
",G06F8/34,实质审查,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/34,"包括, 方法, 控制, 装置, 配置, 生成, 系统, 计算",控制
US20250138784A1,US18495784,"一种用于执行框图的图形建模环境的矩阵计算块。框图的框具有输入端口和/或输出端口，并且可通过相应端口由信号线连接以用于数据传输。矩阵计算块包括：至少一个输入端口，其被配置为接收输入矩阵信号；具有至少一个错误条件检查的计算定义；至少一个输出端口，其被配置用于发射输出信号，所述输出信号是向量信号或矩阵信号；以及被配置用于发射误差信号的误差端口。误差信号的值指示多个预定义误差状态中的误差状态，其包括无误差状态。误差信号的值取决于是否满足至少一个误差条件检查。
",G06F7/57,授权,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"计算, 具有, 配置, 包括",数据传输
EP4521233A1,EP24191642.8,"本发明涉及一种用于从开发平台的图形控制模型（12）生成用于目标平台的控制程序的计算机实现的方法，其中，图形控制模型，第一块（14a）的输出驱动第二块（14b）的输入，以及（i） 第一块（14a）和第二块（14b）被设计为使得第一块（14a）的输入信号（Ii）对应于第二块的输出信号（O2）；或（ii）第一块（14a）和第二块。
此外，本发明涉及数据处理装置（10），其中包括执行上述方法的手段、计算机程序产品和相应的计算机可读数据载体。
",G06F8/34,暂缺,数据注入类型支持技术,3.0,关键词匹配: 数据处理; IPC分类号匹配: G06F8/34,"包括, 方法, 平台, 装置, 实现, 处理, 生成, 模型, 控制, 计算",控制
US12248420B2,US18129438,"本发明涉及一种计算机系统和方法，其具有至少一个外围设备、用于控制外围设备的控制模块和经由第一数据链路连接至控制模块的连接模块。连接模块具有用于与外围设备交换信息的I/O数据端口，并且外围设备经由第二数据链路连接到I/O数据端口中的一个或多个。第一数据链路中的通道数小于连接模块的I/O数据端口数。控制模块包括第一网关，其接收与连接模块的I/O数据端口有关的多条设定点状态信息。
",G06F13/40,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F13/40,"设备, 包括, 方法, 控制, 具有, 系统, 计算",控制
JP2025025408A,JP2023130144,"本发明提供一种避免当乘法余数的数值变大时发生的位数溢出现象（溢出）的加密控制程序和利用该加密控制程序的加密控制系统。包括对指令输入进行加密第1加密部101的输入装置具有秘密计算部201控制装置20在由具备解密部301及对反馈信号进行加密第二加密部302的工厂控制装置30构成的加密控制系统中使用来自输入装置加密的指令输入和来自工厂控制装置的加密的反馈信号（来自控制对象30a的传感器信号）将存储在规定数据类型中的值为了不在乘法时引起位数溢出分割成更小的值进行计算。图5是示出图5
",G09C1/00,公开,数据注入类型支持技术,1.0,关键词匹配: 数据类型,"提供, 包括, 控制, 装置, 具有, 系统, 计算","传感器, 控制"
US12223301B2,US18185413,"一种生成源代码的方法，包括：将框图变换成中间表示，其中将框图变换成中间表示包括变换至少两个块，其中至少一个循环由变换操作块产生；识别中间表示中的至少一个候选循环，其中候选循环的循环主体包括访问数组变量的至少一个指令；从所述至少一个候选循环中识别至少一个可并行化循环；确定所述至少一个可并行化循环的构建选项和所述数组变量；基于所确定的造型选项将造型标记插入中间表示中；以及将中间表示转换成源代码。
",G06F8/41,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/41,"生成, 方法, 包括",通用
EP4492226A1,EP23185063.7,"用于无故障地运行基于 FPGA 的应用程序的多个计算机控制的构建和/或初始化步骤的计算机系统，该计算机系统配置为在构建步骤中生成 FPGA 程序代码，将其转移到受控制的 FPGA，并在初始化步骤中为运行做好准备，该计算机系统进一步配置为此，将构建和/或初始化步骤的结果集中存储和/或控制在可写内存中，并在构建和/或初始化步骤和/或初始化的 FPGA 程序代码到期之前通过读取可写内存来检查是否已完成至少一个以前的构建和/或初始化步骤。这提供了一个计算机系统，以避免现有技术的缺点。
",G06F8/71,实质审查,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/71,"提供, 配置, 系统, 生成, 控制, 计算",控制
EP4432075A1,EP24162626.6,"一种用于从框图的一个或多个块生成源代码的方法，所述框图包括至少两个块，源块发射与阵列变量相对应的矩阵信号，以及操作块接收所述矩阵信号并且基于施加于所述矩阵信号的操作来发射输出信号。该方法包括将框图变换成中间表示，标识中间表示中的至少一个候选循环，其中候选循环的循环主体包括访问数组变量的指令，从至少一个候选循环中标识至少一个可并行化循环，确定所述至少一个可并行化循环的构建选项和所述数组变量，基于所确定的构建选项将构建标记插入所述中间表示中，以及将所述中间表示转换成源代码。
",G06F8/30,实质审查,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/30,"生成, 方法, 包括",通用
EP4428821A1,EP24157920.0,"本发明涉及一种计算机实现的方法，用于确定移动系统的自我姿态，并通过因子图（10）表示的优化问题创建基于3D椭球体的环境冲浪图，其中通过将冲浪数据与冲浪图进行比较来识别新的冲浪者（16）和已知的冲浪者，并将冲浪因子（10）添加到先前识别的冲浪者的因子图（14a，14a'）和/或添加到因子图（十）的冲浪节点（12b）和新识别的冲浪器（16）的冲浪因子（14a）。
此外，本发明涉及一种用于数据处理的设备，包括用于执行上述方法的装置和计算机程序产品，该计算机程序产品包括在计算机执行程序时使上述方法执行的命令。
此外，本发明涉及一种存储上述计算机程序产品的计算机可读数据载体。
",G06T7/579,实质审查,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"设备, 包括, 方法, 装置, 处理, 实现, 系统, 计算",通用
US12026485B2,US17940036,"一种用于从框图的一个或多个块生成源代码的方法，所述框图包括至少两个非虚拟块和两个非虚拟块之间的至少一个信号链路，所述方法包括：将所述框图变换为中间表示，其中将所述框图变换为所述中间表示包括变换可访问多分量变量的第一块；连续优化所述中间表示；以及将优化的中间表示转换成源代码。变换第一块包括：测试由第一块和相邻块组成的块对是否包括相等分配；以及移除其中在两侧上存在对相同变量的引用的任何分配。
",G06F8/41,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/41,"生成, 方法, 测试, 包括",测试
US20240201965A1,US18067738,"一种框图，包括至少三个块，包括：发射第一复合信号的源块；接收第一复合信号并发射具有相同尺寸的第二复合信号的通过块；以及接收第二复合信号并丢弃第二复合信号的至少一个分量的滤波器块。一种用于从框图的一个或多个块生成源代码的方法，包括：将所述框图变换成中间表示，包括变换所述至少三个块；将至少一个优化应用于中间表示，包括确定是否满足存储器条件；基于满足的存储条件，用较小维的辅助变量替换经过的块的输出变量；以及将优化的中间表示转换成源代码。
",G06F8/40,暂缺,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/40,"生成, 方法, 具有, 包括",通用
US20240184538A1,US18074802,"一种用于在基于模型的测试环境中配置系统模型的模型组件的方法，其中所述测试环境包括模型编辑器，其中所述测试环境在具有处理器和显示器的计算机系统上执行，并且其中所述模型组件具有多个端口，所述方法包括以下步骤：S1)将系统模型加载到模型编辑器中，S2)接收对至少包括图形端口标识符的第一端口的用户选择，S3)将所选择的图形端口标识符分配给列表，S4)在测试环境中显示列表，S5)接收对来自列表的第一端口标识符的用户选择，S6)接收对第二端口的用户选择，S7)通过信号连接将第一端口连接到第二端口。
",G06F8/34,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/34,"包括, 方法, 处理, 具有, 配置, 测试, 模型, 系统, 计算",测试
EP4332753A2,EP23188126.9,"在一种用于管理用于处理来自多个传感器的信号的平台中的图形图的组件的方法中，至少一个组件可以处于第一模式，例如B.快速控制原型设计，存在并且处于第二模式，例如B.源代码生成缺失。另外，在第二模式中还可以删除仅由缺失的组件使用的组件。关于这些组件的信息可以被保存在配置简档中。
",G06F8/35,实质审查,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/35,"方法, 平台, 处理, 配置, 生成, 控制","传感器, 控制"
EP4276600A1,EP23172265.3,"本发明涉及一种用于从框图的一个或多个块生成源代码的方法，其中存储接收到的对框图的模型元素的改变。创建源代码包括将框图转换为中间视图，依次优化中间视图，并将优化后的中间视图转换为源代码。针对修改的模型元素检查至少一个合理性规则。如果不符合合理性规则，则会发出警告。用户可以批准该改变，以避免重复显示警告。此外，本发明涉及一种用于配置ECU的方法、计算机程序产品和计算机系统。
",G06F8/33,实质审查,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/33,"包括, 方法, 配置, 生成, 模型, 系统, 计算",通用
EP4270176A1,EP22169925.9,"本发明涉及一种用于从框图的至少一个分层块生成源代码的方法，其中分层块包括至少一个通信块，该通信块与分层块之外的块交换数据，其中向至少一个通讯块提供对数据对象的引用。该方法包括读取框图，用引用的数据对象替换引用，基于所选择的数据对象确定至少一个代码生成设置，生成中间表示，对所述中间表示执行至少一个优化，并将所述中间表达翻译成源代码。此外，本发明涉及一种用于配置ECU的方法、计算机程序产品和计算机系统。
",G06F8/34,权利终止,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/34,"提供, 包括, 方法, 配置, 设置, 生成, 系统, 计算",通信
US20230342115A1,US18191905,"一种用于从框图的至少一个分级块生成源代码的方法包括：在框图中读取；用参考数据对象替换参考；基于参考数据对象确定至少一个代码生成设置；生成中间表示；对中间表示执行至少一个优化；以及将中间表示翻译成源代码。
",G06F8/30,暂缺,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/30,"生成, 方法, 设置, 包括",通用
US20230195661A1,US18082288,"提供了一种用于FPGA的至少一个子区域与另一区域之间的数据通信的方法，其中数据通信是资源高效的。这通过以下事实来实现，即，经由FPGA的内部配置接口的命令序列来进行数据通信，即，从一个位置到FPGA的任何块RAM或从任何块RAM到任何块RAM的读和写。
",G06F13/16,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F13/16,"提供, 方法, 配置, 实现",通信
US20230195430A1,US18066308,"一种用于在图形开发环境中创建层次块图的计算机实现的方法，包括：自动创建用于相应的另外的子系统块的层次黑盒。所述层次黑盒被配置为当被用户选择时解析对应的层次级别。经由回调函数执行自动创建。所述层次黑盒具有以下特征：在其层次级别中的相应的另外的子系统块的输入/输出的至少一个接口描述和/或图形表示，所述相应的另外的子系统块先前也被占用；对用于相应的另外的子系统块的模型内容的至少一个引用，包括用于从属层次级别中的另外的子系统块的另外的黑盒；相应的另外的子系统块先前也具有的参数；以及生成的相应的另外的子系统块的源代码。
",G06F8/34,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/34,"包括, 方法, 实现, 具有, 配置, 生成, 模型, 系统, 计算",通用
EP4198750A1,EP21215621.0,"在用于FPGA（1）的至少一个子集与另一范围之间的数据通信的方法中，数据通信应当是资源有效的。这是通过以下事实实现的：数据通信，经由FPGA（1）的内部配置接口（3）的命令序列，从一个位置向FPGA（1。
",G06F13/14,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F13/14,"方法, 配置, 实现",通信
EP4198482A1,EP21215409.0,"本发明涉及一种将来自真实世界的原始数据处理为用于刺激ECU的测试数据的方法，该方法包括以下过程步骤：提供来自真实世界记录的原始数据，从原始数据中检测传感器检测到的真实物体，并创建路线数据集，每个描述具有这些真实对象在连续时间点的图像的场景，提供合成对象库，将合成对象分配给真实对象的捕获图像，用合成物体替换捕获的图像，用补充数据集补充第一路线数据集之前的按时间顺序连续的路线数据集，使得传感器在引入时段的时间零点具有零的绝对速度，并且按时间顺序顺序连续的补充数据集具有合成物体，其示出了准静态时间运动序列，该准静态时间移动序列在记录持续时间的时间零点准瞬时地引导到第一路线数据集的合成对象，并且通过将由补充数据集补充的路线数据集转换成原始数据来生成测试数据，因为它们在真实世界中的引入时段和记录时段期间将由传感器记录，如果传感器将合成物体检测为真实物体。这样，就提供了一种生成此类测试数据的方法，该方法没有不可信之处。
",G01M17/06,授权,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"提供, 包括, 检测, 方法, 处理, 具有, 测试, 生成","检测, 传感器, 测试"
EP4174641A1,EP22201338.5,"本发明涉及一种用于产生程序代码(1)的计算机实施的方法，所述方法包括：产生(S1)第一占位符(P1)，所述第一占位符代表在所述程序代码(1)的预定的片段(10)中使用的变量；产生(S2)第二占位符(P2)，所述第二占位符在使用所述程序代码(1)的预定的片段(10)的情况下被置入在所述第一占位符(P1)的开始之前；以及产生(S3)第三占位符(P3)，所述第三占位符在使用所述第一占位符(P1)的情况下在所述程序代码(1)的预定的片段(10)的结束之后。本发明还涉及一种用于配置控制单元的方法。本发明还涉及一种计算机程序、一种计算机可读的数据载体和一种计算机系统。
",G06F8/34,实质审查,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/34,"包括, 方法, 控制, 配置, 系统, 计算",控制
US11593076B2,US17356562,"一种用于合并在架构定义工具和行为建模工具之间交换的架构数据的计算机实现方法，包括：在比较工具中打开具有第一架构数据的第一文件和具有第二架构数据的第二文件；将第一架构数据与第二架构数据进行比较以获得第一差异列表；检索至少一个组合规则，其中所述至少一个组合规则包括用于识别差异的识别规则和要应用于所识别的差异的改变；确定满足识别规则并从第一列表中移除第二列表的差异的第二差异列表；以及将在至少一个组合规则中定义的改变应用于第二列表中的每个差异。
",G06F8/41,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/41,"包括, 方法, 实现, 具有, 工具, 计算",通用
US20220019414A1,US17356562,"一种用于合并架构定义工具和行为建模工具之间交换的架构数据的计算机实现方法包括：在比较工具中打开具有第一架构数据的第一文件和具有第二架构数据的第二文件；将所述第一架构数据与所述第二架构数据进行比较以获得差异的第一列表；检索至少一个组合规则，其中所述至少一个组合规则包括用于识别差异的识别规则和要应用于所识别差异的变更；确定满足识别规则的差异的第二列表，并从第一列表中移除第二列表的差异；以及将在所述至少一个组合规则中定义的改变应用于所述第二列表中的每个差异。
",G06F8/41,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/41,"包括, 方法, 实现, 具有, 工具, 计算",通用
CN113448565A,CN202110293727.3,"本发明涉及一种对分布式计算机系统中的第一可编程门装置、例如第一FPGA进行编程的方法，为了进行所述编程而规定：在第一可编程门装置上设立第一配置逻辑。第一配置逻辑被设立为从配置软件接收用于在第一可编程门装置上设立第一用户逻辑的第一用户比特流并且将第一用户比特流存储在第一可编程门装置的只读存储器上，以便紧接着按照第一用户比特流中的规定在第一可编程门装置上设立第一用户逻辑。在本发明的扩展阶段，还规定在第一可编程门装置上设立编程逻辑，用于对第二可编程门装置进行编程，该第二可编程门装置至少与第一可编程门装置电路连接成菊花链。
",G06F8/34,实质审查,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/34,"方法, 装置, 配置, 电路, 系统, 计算",通用
EP3812885A1,EP19205062.3,"根据本发明，从模型生成模拟代码和生产代码作为源代码。所述模型包括一个或多个块，所述块规定了程序、尤其是控制程序的期望行为。所述块中的至少一个用模拟代码属性来标记。针对包含模拟代码属性的那些块生成模拟代码。针对所有其他的块生成生产代码。所生成的源代码包括模拟代码部分和生产代码部分两者。模拟代码部分被包含在源代码中，以使得它们可以与生产代码部分分离。
",G06F8/35,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/35,"包括, 模拟, 生成, 模型, 控制",控制
US20210096830A1,US17023418,"源代码从框图的一个或多个块生成。该框图是分层的，并且至少包括被配置用于增量代码生成的第一子系统。该框图引用包括第一对象的定义数据库。所述源代码生成方法包括：由计算机系统在模型编辑器中打开包括所述第一子系统的所述框图；由所述计算机系统生成用于所述第一子系统的源代码，其中生成用于所述第一子系统的所述源代码包括确定关于所述第一子系统的信息；由所述计算机系统存储，关于第一对象中的第一子系统的信息；以及由计算机系统生成用于框图的源代码，其中第一对象被读出以影响用于生成用于框图的源代码的至少一个参数。
",G06F8/35,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/35,"包括, 方法, 配置, 生成, 模型, 系统, 计算",通用
US20210034337A1,US16983462,"一种用于准备具有一个或多个块的框图以便在计算环境中生成代码的方法，所述计算环境包括模型编辑器、数据定义工具和代码生成器。该方块图系开放於该模型编辑器中，其中第一区块系为一阶层区块，其包含复数个从属区块、至少一输入埠及至少一输出埠，以讯号连接。接收输入和输出端口的最小值和最大值，基于所接收的最小值和最大值确定输入和输出端口的缩放参数。为第一块中的每个从属块确定比例因数，其中至少一个从属块的比例因数是基于至少一个输出端口的比例因数来确定的。此外，提供了一种用于生成程序代码的方法、非暂时性计算机可读介质和计算机系统。
",G06F8/35,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/35,"提供, 包括, 方法, 具有, 生成, 模型, 工具, 系统, 计算",通用
EP3736688A1,EP19210226.7,"本发明涉及一种根据自动配置总成标准的虚拟控制器（10），其具有服务层（12），ECU抽象层（14）和微控制器抽象层（16）。根据本发明，虚拟控制器（10）另外配备有硬件层（18），所述硬件层（18）被设置为用于模拟至少一个硬件组件（20）。这提供了一种虚拟控制装置（10），该装置允许容易地使用环境模型进行HiL测试和软件测试以及快速模拟。
",G06F8/10,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/10,"提供, 模拟, 装置, 具有, 配置, 设置, 测试, 模型, 控制","控制, 测试"
US10761814B2,US16190729,"一种用于在模型管理环境中可视化系统模型的方法，包括以下步骤：在模型编辑器中打开系统模型，接收用于重新缩放块的用户输入，确定块中的每个端口的相对水平位置和相对垂直位置，基于块的相对水平位置和相对垂直位置以及为块预设的新的大小来计算块中的每个端口的新的绝对水平位置和绝对垂直位置，以及显示块和块中的每个端口。
",G06F8/35,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/35,"包括, 方法, 模型, 系统, 计算",通用
US20200201608A1,US16230312,"一种用于基于框图的一个或多个块生成程序代码的计算机实现的方法，至少一个块包括块变量。该方法包括在模型编辑器中打开框图，从数据定义工具检索用于块变量的生成设置，该生成设置包括变量的范围，确定在生成设置中引用了修改规则，以及从数据定义工具检索所引用的修改规则，其中修改规则包括过滤条件和一个或多个代码改变。处理器基于框图和生成设置生成程序代码，并将所引用的修改规则应用于所生成的代码中的块变量，这包括验证对于块变量满足了过滤条件，并将代码改变应用于代码中变量的每次出现。
",G06F8/35,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/35,"包括, 方法, 处理, 实现, 设置, 生成, 模型, 工具, 计算",验证
EP3629151A1,EP19196334.7,"本发明涉及一种用于在包括与信号相连的块的周期性计算模型的框图之间传输变化的过程，其中基本模型包括一个或多个基本块，一个或多个基本块具有一个或多个基本参数，其中，扩展模型包括一个或多个基本块和至少一个具有至少一个附加扩展参数的扩展块。第一框图包括第一版本中的基本模型，第二框图包括从第一版本的基本模型导出的扩展模型，第三个框图包括第二个版本中的基本模型，第一个版本比第二个版本旧。根据本发明，对第一框图应用各种变换规则以获得第一中间模型、第二框图与第一中间模型的比较、从比较中确定至少一个配置规则，将各种转换规则应用于第三框图以获得第二中间模型，并将至少一个配置规则应用于第二中间模型，以获得第四个框图。此外，本发明涉及用于配置控制器、计算机程序产品和计算机系统的过程。
",G06F8/71,撤回,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/71,"包括, 控制, 具有, 配置, 模型, 系统, 计算",控制
US10585650B1,US16230468,"一种用于在包括模型编辑器和代码生成器的技术计算环境中基于框图的一个或多个块生成程序代码的计算机实现的方法。该方法包括在模型编辑器中打开框图，该框图包括延迟块，该延迟块在由输入端口接收的信号在输出端口被发射之前将该信号延迟多个周期，确定复合信号被连接到输入端口，以及生成变量的定义，该变量包括状态缓冲器、指针和索引。该方法进一步包括生成循环代码，循环代码包括用于将指针设置到状态缓冲器中具有索引偏移的位置的指令、用于从状态缓冲器输出元素的指令、用于将复合信号输入到状态缓冲器的指令、以及用于调整索引的指令。
",G06F8/35,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/35,"包括, 方法, 实现, 具有, 设置, 生成, 模型, 计算",通用
US20190369585A1,US16420203,"一种用于确定具有实时能力的测试设备的物理连接拓扑的方法，所述测试设备被设立用于控制设备开发，所述方法包括：确定多个仿真模型之间的逻辑通信链路；以及通过基于所述多个数据处理单元的物理接口的各自指定量指定所述多个数据处理单元之间的直接物理通信链路来自动确定所述物理连接拓扑。指定直接物理通信链路包括，对于每个逻辑通信链路，对应于逻辑通信链路的直接物理通信链路是否形成物理连接拓扑的一部分。
",G05B19/05,申请终止,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"设备, 仿真, 包括, 方法, 处理, 具有, 测试, 模型, 控制","通信, 控制, 测试"
US20190258460A1,US15901933,"一种用于对于通过在一计算机系统上的一处理器的一电子控制单元产生一软件成分的方法，在计算机系统包括一架构定义工具和一个行为-建模工具上的软件，适于限定软件成分的一个架构的架构定义工具，结构信息包括一个或多个亚成分和一个或多个接口的声明，另外适于出口和进口结构信息的架构定义工具，对于基于包括多个互连的块的一个功能模型的软件成分适于生成源码的行为模型化工具，另外适于进出口结构信息的行为-建模工具，其中行为-建模工具创建或更新用于基于结构信息的功能模型的一航模骨架。
",G06F8/35,申请终止,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/35,"包括, 方法, 控制, 处理, 生成, 模型, 工具, 系统, 计算",控制
EP2942678B1,EP14167501.7,"提供了一种用于将数据处理系统的多个数据结构与人机接口(MMI)的多个元素相链接的方法和数据处理系统。该方法包括步骤：提供具有多个元素的MMI，在列表中排列多个数据结构，由用户选择MMI的元素，将列表中的第一数据结构与MMI的所选元素自动链接，以及将列表的开始设置为在列表中跟随先前链接的数据结构的数据结构。重复执行以下步骤：由用户选择MMI的元素，将列表中的第一数据结构与MMI的所选元素自动链接，以及将列表的开始设置为数据结构。
",G05B19/042,授权,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"提供, 包括, 方法, 处理, 具有, 设置, 系统",通用
US20190163452A1,US15827234,"一种用于在一技术的计算环境中的一个框图的一个或多个块上的产生程序编码基数的方法，一标识符被分配给至少一个，优选地各自，框图的一个或多个块。一处理器在型号编辑器中打开框图，使用代码生成程序把框图到一中间表示，如果一替换情况用于在框图中的一当前像块被实现，其中转换包括检查。检查替换情况包括验证一预定义的操作码单元被分配给当前像块的标识符，在这种情况下改变块到占位符包含输入/输出-定义但是没有功能。处理器接着把中间表示到程序代码，转换包括从数据定义工具至对应于占位符块的定义码添加一预定义的操作码单元。
",G06F8/35,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/35,"包括, 方法, 处理, 实现, 生成, 工具, 计算",验证
US20190102149A1,US16146018,"一种用于提供基于多个模拟模型的一个一体化控制装置开发过程的方法，具有至少一个第一模拟模型和第二模拟模型，其中综合过程模拟一控制单元的一控制单元或一环境并在一模拟设备上是可执行的。方法包括以下步骤：外部地隔离第一模拟模型的可见的第一通信参数和第二个模拟模型的孤立的外部地可见的第二通信参数；比较第一通信参数和第二通信参数并标识同名的通信参数；对于第一模拟模型和第二模拟模型之一至少修改同名的通信参数，使得综合过程在一单核心处理器上是可执行的。
",G06F8/35,申请终止,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/35,"提供, 设备, 包括, 方法, 控制, 装置, 处理, 具有, 模型, 模拟","通信, 控制"
US10180917B2,US15151767,"在一计算机系统和一外部环境的一第一处理器之间的用于数据交换的一接口装置。接口装置具有用于从外部环境和一个首次访问管理单元接收输入数据的一些输入数据通道。存取管理单元被配置为接收对提供输入数据的要求，存储在输入数据通道的数中，从存储在接口装置中的第一界面处理器和从存储在接口装置中的和提供或不提供输入数据第二界面处理器，存储在输入数据通道的数中，对第一界面处理器和第二界面处理器。一当务之急和一第二优先级可贮存在第一调整孔管理单元。
",G06F13/10,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F13/10,"提供, 装置, 处理, 具有, 配置, 系统, 计算",通用
EP3413204A1,EP17175193.6,"一个计算机系统的接口单元的至少一个第一和一个第二schnittstellenprozessor schnittstellenprozessor包括一个至少有两个softwareroutinen综合数量softwareroutinen转发输入的数据从一个处理器的计算机系统的外围设备，在一个应用程序，程序的编制。第一子集的数据传输是一个softwareroutinen tasksynchrone规定的第一类，第二子集和一对softwareroutinen将连续数据传输规定的第二类。第一schnittstellenprozessor将与第一子集和第二子集的第二schnittstellenprozessor编程。执行启动的软件，在软件执行的第一子集的第一与第一zyklusrate schnittstellenprozessor循环。由第二和第二子集使用第二zyklusrate schnittstellenprozessor循环结束。
",G06F13/12,授权,数据注入类型支持技术,3.0,关键词匹配: 数据传输; IPC分类号匹配: G06F13/12,"设备, 包括, 处理, 系统, 计算",数据传输
EP3399425A1,EP17169690.9,"检测方法之间的一个verdrahtungstopologie FPGA。第一FPGA包括一个多元化的接口引脚，特别是一个接口引脚。一个包括多个接口的FPGA引脚接口，特别是第二针。一个管道连接的一部分第一FPGA的接口引脚与引脚的第二部分接口的FPGA实现。第一个驱动程序是在第一个接口引脚的存在。第一empfangsregister销将第二接口的实现。第一个驱动程序是在第一senderegister实现。从senderegister将发行从第一驱动信号的定义。第一个驱动程序是在第一aktivierungssignal激活。第一个驱动程序发送一个第一信号。通过将读出的第一empfangsregisters是否第一信号接收的第二接口引脚。第二个接口引脚第一组接口引脚，当信号的第一个驱动程序接收的第二接口引脚。
",G06F13/40,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F13/40,"检测, 方法, 实现, 包括",检测
EP2977894B1,EP14177800.1,"本发明涉及一种方法来创建一个FPGA的FPGA代码模型（1）的步骤，全面的模型设计的FPGA的FPGA（1）作为程序的至少两个部件（3）和至少一个signalpfad（5）之间的至少两个部件（3），设置一个马rkierung（7）中的至少一个signalpfad（5）FPGA的程序生成的模型作为FPGA的FPGA（1）的至少一部分的代码的基础上的模型，其中的步骤的模型制造，使用FPGA的FPGA（1）作为代码自动插入一个beeinflussungsstruktur（11）上的标记（7）（5）和杆状signalpfad FPGA产生的M（1）odells与beeinflussungsstruktur（11）包括。的beeinflussungsstruktur（11）允许在任何一个时间，执行的过程模型，基于FPGA的signalwert signalpfad（5）完全取代或改变。本发明还涉及一datenverarbeitungseinrichtung computerprogrammprodukt数字存储设备，以及一个电子信号线auslesbaren执行的程序。
",G06F8/41,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F8/41,"设备, 包括, 方法, 设置, 生成, 模型",通用
US09940297B2,US13900008,"用于操纵总线通信的电子控制装置，其总线包括总线独立于硬件的第一连通层，以及总线与硬件有关第二通信层。第一通信层编码至少一个信息在第一数据单元和将它发送到第二通信层和/或第一通信层接收第一协议数据单元从第二通信层和解码第一信息从第一协议数据单元。第二通信层生成总线与硬件有关总线信息原来协议数据单元或从附加协议数据单元从第一协议数据单元经由总线和/或第二通信层产生第一协议数据单元或额外的协议数据单元，从第一协议数据单元可被导出。
",G06F13/42,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F13/42,"装置, 生成, 控制, 包括","通信, 控制"
CN102955875B,CN201210281759.2,"根据本发明的主题提出一种用于在影响装置中处理数据的方法，其中影响装置与汽车控制装置并与数据处理装置连接。汽车控制装置和影响装置设置在汽车中或例如被设置在实验室的测试台中。第一程序在汽车控制装置上运行。根据XCP协议或CCP协议在数据处理装置与影响装置之间交换数据。根据本发明，影响装置具有第二执行单元，其中第二执行单元比第一执行单元更快地执行预定的数据步骤。按照根据本发明的方法，按照预定的标准检验借助于XCP协议或CCP协议交换的数据，并且根据检验结果或者在第一执行单元中或者在第二执行单元中执行数据处理。
",G06F17/50,授权,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"方法, 装置, 处理, 具有, 设置, 测试, 控制","汽车, 控制, 测试"
EP2950175A1,EP14169948.8,"本发明涉及一种方法和一种装置，用于测试一个开口（8），其中在一个真正的或模拟的控制单元（8）通过网络连接（7）的传感器的数据传输，通过一个数据处理设备（1）是要通过模拟计算恩，其中，模拟传感器的数据至少部分至少有一个图形处理器，至少有一个grafikprozessoreinheit（2）数据处理设备（1）进行的模拟传感器的数据和图像数据编码中的一个visualisierungsschnittstelle（9）快递在一个egeben datenwandlereinheit（6），特别是一个在visualisierungsschnittstelle（9）连接，通过模拟visualisierungseinheit datenwandlereinheit（6）接收的图像数据转换成paketdaten paketbasiert含有的传感器，特别是根据TCP \/ IP或UDP \/ IP协议通过网络连接到控制单元（7）（8）转让。本发明还涉及一种datenwandlereinheit（6）全面empfangsschnittstelle（6A）连接到一个指定的visualisierungsschnittstelle grafikprozessoreinheit（2）（9）和一个数据处理设备（1）sendeschnittstelle（6B），以输出一个数据包在基于分组的网络协议。（6）建立，datenwandlereinheit在图像数据编码的用户数据，empfangsschnittstelle（6a）的接收，转换数据的网络数据包中含有的versendbar sendeschnittstelle（6B）。
",G05B23/02,授权,数据注入类型支持技术,3.0,"关键词匹配: 数据处理, 数据传输, 数据转换","设备, 方法, 控制, 装置, 处理, 测试, 模拟, 计算","数据传输, 传感器, 网络, 测试, 控制"
JP2014220813A,JP2014095807,"要解决的问题：提供一种能够在FPGA控制系统可变数据传输发送器和接收器之间的侧具有期望的数据速率和等待时间与提供高数据速率和低，实现其成本低。
SOLUTION：最大寄存器9传输的配置每个FPGA应用8 。共享，固定寄存器宽度被配置用于所有寄存器9 。使能信号EN设置在发送方3的寄存器9传输超出最大数量的寄存器9发送，使能信号EN被从发送方到接收方侧3 4，并且寄存器9在使能信号EN已被设置了被从发送方到接收方的3 4 。
COPYRIGHT：( C ) 2015、JPO&INPIT
",H03K19/173,授权,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"提供, 控制, 实现, 具有, 配置, 设置, 系统","数据传输, 控制"
EP2801915A1,EP13167208.1,"本发明涉及一种方法执行一个自适应接口（7）之间的至少一个（2）至少有一个FPGA的FPGA（8）和至少一个应用程序的I \/ O模块（5）连接到FPGA（2），（3）分别为相应的变速器（4）接受培训，其中至少有一个FPGA之间（2）和至少一个I \/ O模块（5），一个串行接口（6）综合训练的步骤，配置的最大数量的传输寄存器（9）每个FPGA的应用（8）一个共同的固定配置，registerbreite所有寄存器（9）设置一个使能信号（s）在变速器（3）和寄存器（9）的最大数量的传输寄存器（9），使传输的信号（s）从发送端到接收端（3）（4）和转移登记册（9），这是enable信号（S）是设置，从发送端到接收端（3）（4）。本发明还涉及一种基于FPGA的控制系统（1）至少有一个FPGA的FPGA（2）至少有一个应用程序（8）和至少一个I／O模块（5）连接到FPGA（2），（3）分别为相应的变速器（4）接受培训，其中至少有一个FPGA之间（2）和至少一个I \/ O模块（5），一个串行接口（6）进行训练，在FPGA的控制系统（1），以实现一个自适应接口（7）之间（2）和至少一个现场可编程门阵列（FPGA）至少有一个I／O模块（5）按照上面的方法进行训练。
",G06F13/42,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F13/42,"方法, 控制, 实现, 配置, 设置, 系统",控制
EP2557506B1,EP11176910.5,"方法在一次触发器的存在期间涉及在一个存储区的中断记忆，测试具有触发器的一触发器类。在存储区的存储器在触发器类的存在期间被中断。在存储区的数据储存的（12）的值是读取自存储区。其他值横向到达，在来自一数据通信机的触发器在另一存储区被储存（13）之后。测试通过数据通信机执行（14）以检查是否出现另一触发器，其中数据通信机包括二数据检测单元。
",G06F13/42,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F13/42,"包括, 检测, 方法, 具有, 测试","检测, 通信, 测试"
US20130326098A1,US13900008,"提供一种用于操纵一电子控制器装置的总线通信方法的方法，其中总线通信方法包括一条总线独立于硬件的第一层次通信层和一总线与硬件有关第二通信层。第一层次通信层在第一协议数据单位中编码至少一个信息并将其传输至第二通信层和/或第一层次通信层从第二通信层接收第一协议数据单位并从第一协议数据单位译码第一信息。第二通信层从第一协议数据单位生成总线与硬件有关公共汽车信息或从用于经由总线和/或第二通信层的传输的来源于第一协议数据单位的附加议定书数据单元生成第一协议数据单位或一附加议定书数据单元，从哪个第一协议数据单位可以被导出。
",G06F13/42,授权,数据注入类型支持技术,2.0,IPC分类号匹配: G06F13/42,"提供, 包括, 方法, 装置, 生成, 控制","汽车, 通信, 控制"
EP2557462A1,EP11176934.5,"本发明涉及基于确定的地址从影响装置(10)的存储器和从机动车控制装置(11)的存储器(13)中获得值。检查地址或所选值与子函数的关联。基于有效关联的存在，调用所述确定的子函数。基于所调用的子函数，检查或操纵所选值。基于所检查的结果，将所述值传输给机动车控制装置或数据处理装置(12) 。
",G05B19/042,授权,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"装置, 处理, 控制","机动车, 控制"
EP2557463A1,EP11176936.0,"方法涉及根据一份扩展校准协议（XCP）或一控制器区域网络校准协议（CCP）在一数据处理设备（14）和一影响的装置（11）之间交换数据，其中影响的装置与一机动车操控装置（12）连接。数据基于一预设标准被检查。在以一考试结果为基础的第一及第二执行单元（17 ，18）之一中，执行数据处理，其中控制和影响的装置被设置在一辆机动车或一测试台上。第一执行部件是一处理器单元，第二执行部件是一现场可编程门阵列单元。
",G05B19/042,驳回,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"设备, 方法, 装置, 处理, 设置, 测试, 控制","机动车, 网络, 控制, 测试"
EP1522910B1,EP04024001.2,"程序生成方法具有用于控制系统(2)的功能控制程序(9)，该控制系统(2)借助于通过图形建模环境(5)提供的框图(6)而生成，该功能控制程序(9)具有用于功能块(7)之间的信号传输的信号线(8) 。至少一个信号线代表用于控制系统的输入/输出设备(4)的输入/输出接入点(10)，其中配置环境(12)生成与功能控制程序相组合的输入/输出控制程序(13)，以提供总控制程序(1) 。还包括对以下独立权利要求：(A)在配置环境中使用的计算机程序，用于生成用于控制系统的总控制程序；(B)配置设备，用于生成用于控制系统的输入/输出控制程序；(C)具有由数据处理器执行的配置设备程序代码的计算机程序；(D)具有存储在机器可读的数据载体上的配置设备程序代码的计算机程序。
",G05B19/042,全部撤销,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"提供, 设备, 包括, 方法, 控制, 处理, 具有, 配置, 生成, 系统, 计算",控制
