﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,当前法律状态,技术领域,分类得分,分类理由,技术手段,应用场景
EP3783452B1,EP19192743.3,"本发明涉及一种用于近似设备的虚拟测试的测试结果以至少部分自主引导机动车的计算机实现的方法。本发明还涉及一种用于提供经训练的人造神经网络的计算机实现的方法、一种测试单元(1)、一种计算机程序以及一种计算机可读的数据载体。
",G06N3/084,,虚拟仿真软件技术,3.0,关键词匹配: 虚拟测试; IPC分类号匹配: G06N3/084,"提供, 设备, 方法, 实现, 测试, 计算","机动车, 网络, 测试"
US12265771B2,US17697095,"一种用于将图形仿真模型划分为第一子模型和第二子模型的方法，包括：基于采样时间和/或资源分配，将至少一个第一块识别为属于第一子模型，并且将至少一个第二块识别为属于第二子模型；搜索块的循环组，其中，块全部具有相同采样时间的循环组被认为是原子的；识别块的非循环组；将来自所述循环块组和所述非循环块组的单独块分配给所述第一子模型或所述第二子模型，其中原子循环组的所有块被分配给相同子模型；从第一子模型生成用于处理器的程序代码；以及从第二子模型生成用于可编程逻辑模块的配置比特流。
",G06F30/34,授权,虚拟仿真软件技术,3.0,关键词匹配: 仿真模型; IPC分类号匹配: G06F30/34,"仿真, 包括, 方法, 处理, 具有, 配置, 生成, 模型",通用
EP4501730A1,EP23188955.1,"本发明涉及一种计算机实现的方法，用于提供用于在虚拟测试（12）中生成关键交通场景（10）的机器学习算法（a），以验证机动车辆的自动驾驶功能，包括接收（S3）由奖励功能（18）奖励（20）和交通流模拟（14）的当前状态（22）生成的奖励，以响应（16）至少一个道路使用者（15）的执行动作（16）；并提供（S4）训练过的机器学习算法（A），如果一个代表虚拟测试（12）的交通场景（10）的关键性的参数（24）位于预定范围内。本发明涉及提供机器学习算法（A）的系统（1）。
",B60W40/00,实质审查,虚拟仿真软件技术,1.0,关键词匹配: 虚拟测试,"提供, 包括, 方法, 模拟, 实现, 测试, 生成, 算法, 系统, 计算","机动车, 验证, 驾驶, 测试"
EP4485003A1,EP23199431.0,"一种用于实时模拟激光束扫描小物体复合体（如粮田）的设备，包括具有小物体复合体的单片3D模型的虚拟环境，以及用于模拟传感器以通过激光束进行距离测量的传感器模拟。传感器模拟包括模拟激光束的光线投射程序和噪声程序。噪声例程被设计为从包括各种强度分布的数据库中确定激光束在3D模型表面上的入射角，选择与所确定的入射角相关联的强度分布。根据激光束对小物体复合体的穿透深度，可以从每个强度分布中读出激光束反射的强度部分。噪声程序旨在计算噪声距离测量，同时考虑所选的强度分布，以模拟激光束在各种小物体上的各种反射，如粮田的耳朵。
",G01S7/497,实质审查,虚拟仿真软件技术,1.0,关键词匹配: 虚拟环境,"设备, 包括, 具有, 模型, 模拟, 计算","激光, 传感器"
US20240330674A1,US18190187,"本发明涉及一种用于在虚拟训练环境中训练用于致动技术设备的神经网络的方法。该方法包括在神经网络和技术设备的第一模拟之间建立第一数据链路，然后通过致动第一模拟来训练神经网络。一旦已经实现了第一训练目标，第一数据链路被断开，并且在神经网络和技术设备的第二仿真之间建立第二数据链路，以便通过启动第二仿真来训练神经网络。第二模拟被配置为比第一模拟更逼真，并且由于其更高的真实程度，需要更多的数学运算来进行模拟周期。
",G06N3/08,暂缺,虚拟仿真软件技术,2.0,IPC分类号匹配: G06N3/08,"设备, 仿真, 包括, 方法, 实现, 配置, 模拟",网络
EP4390747A1,EP23211097.3,"本发明涉及一种用于创建具有多个具有通信接口（2）的仿真模块（1）的协同仿真（100）的方法，其中具有仿真代理（4）的仿真模型（1）是可执行的，包括以下步骤：S1）创建仿真组件（6），所述仿真组件是用于一个或多个仿真模块的容器；S2）将模拟模块（1）分类为模拟组件（6），S3）为每个模拟组件（6）提供运行时环境信息模块（8），S4）为每个模拟组件（6）提供包含对所述模拟模块（1）的通信接口（2）的描述的通信单元（10），使得与其他模拟组件（5）的通信成为可能；S5）将模拟组件（6）的通信单元（10）连接到其他模拟组件（六）的一个或多个通信单元（十），S6）创建协同仿真应用程序（18），其中仿真组件级别（12）的仿真组件（6）被分配给具有仿真组件（12）运行时环境信息模块（8）的仿真代理类型（16）。以这种方式，提供了一种用于创建通用协同仿真（100）的时间和存储器高效的方法。
",G06F30/20,实质审查,虚拟仿真软件技术,3.0,关键词匹配: 仿真模型; IPC分类号匹配: G06F30/20,"提供, 仿真, 包括, 方法, 具有, 模型, 模拟",通信
US20240160806A1,US18406790,"一种用于驾驶员辅助系统的虚拟测试环境，其中虚拟道路用户基于游戏理论被模拟。虚拟测试环境被设计为将虚拟测试环境中的至少一个预定交通状况识别为游戏状况，其中涉及第一路径用户和第二路径用户，以将第一路径用户和第二路径用户指定为第一玩家和第二玩家。分配给游戏情形的回报矩阵被存储在虚拟测试环境中。虚拟测试环境被设计成将策略从策略选择分配给游戏情形中的两个玩家中的每一个，这取决于他们各自的点数帐户的余额，并在游戏情形中以他们根据他们各自的分配策略来行动的方式来控制两个玩家中的每一个。
",G06F30/20,公开,虚拟仿真软件技术,3.0,关键词匹配: 虚拟测试; IPC分类号匹配: G06F30/20,"控制, 模拟, 系统, 测试","驾驶, 控制, 测试"
EP4366849A1,EP21743142.8,"一种用于驾驶辅助系统的虚拟测试环境，其中基于游戏理论模拟虚拟道路用户。每个道路用户被分配点数余额。虚拟测试环境被设计成在虚拟测试环境中识别第一和第二道路用户被涉及作为游戏情况的至少一个预定义交通情况，将第一路路用户指定为第一玩家并且将第二玩家指定为第二玩家。分配给游戏情形的回报矩阵被存储在虚拟测试环境中。虚拟测试环境被设计为向两个玩家分配来自选择的一个策略游戏情形中的策略取决于其相应点数的状态而平衡，并控制游戏情形中的两个玩家，使得他们根据其相应分配的策略来行动。虚拟测试环境被设计成读取相应的支付值根据游戏情况的进程，从第一玩家和第二玩家的支付矩阵中，将其相对于各个玩家的点数余额进行补偿。虚拟测试环境包括虚拟测试车辆和用于使用驾驶辅助系统控制虚拟测试车辆的逻辑接口。虚拟测试环境还包括编程接口，用于改变支付矩阵或用于改变点数余额的至少一个偏移值，该偏移值影响策略向第一玩家和第二玩家的分配。该编程接口使得有可能，通过影响策略的分配，影响驾驶辅助系统的虚拟测试环境的难度。
",A63F13/803,撤回,虚拟仿真软件技术,1.0,关键词匹配: 虚拟测试,"包括, 模拟, 系统, 测试, 控制","驾驶, 车辆, 控制, 测试"
US20230394742A1,US18236037,"一种用于参数化用于图像合成的程序逻辑以使由程序逻辑合成的图像适应于相机模型的方法。三维场景的数字照片由神经网络处理，并且从神经网络的层的选择中提取照片的abicile第一表示。根据输出参数的初始集合参数化程序逻辑，以便合成从场景的三维模型重建照片的图像。由相同的神经网络处理合成图像，从层的相同选择中提取合成图像的abicile第二表示，并且基于考虑第一表示和第二表示的度量来计算合成图像与照片之间的距离。
",G06T15/20,暂缺,虚拟仿真软件技术,2.0,IPC分类号匹配: G06T15/20,"处理, 模型, 方法, 计算",网络
EP4202760A1,EP21216228.3,"本发明涉及一种用于创建三维仿真环境的计算机实现方法，包括以下过程步骤：提供具有三维虚拟对象的基本库、检测地理区域的输入、获取特征信息、，表征所述地理区域的不同区域的特征，基于所述特征信息导出所述地理区的不同区域中的附加信息，导出土地利用信息作为附加信息，如果特征信息不包括土地使用信息，则基于特征信息和/或附加信息从基本图书馆识别出现在地理区域中的对象，并将这些对象放置在区域图书馆中，根据土地利用信息将地理区域细分为土地利用平等的部门，为每个部门从区域图书馆中识别与该部门的土地利用相匹配的对象，并用选定的对象填充每个部门，这提供了以简单可靠的方式创建逼真的三维虚拟环境的机会。
",G06K9/62,实质审查,虚拟仿真软件技术,1.0,关键词匹配: 虚拟环境,"提供, 仿真, 包括, 检测, 方法, 实现, 具有, 计算",检测
EP4202753A1,EP21216219.2,"本发明涉及一种用于生成测试数据的计算机实现的方法，该测试数据用于测试评估传感器数据流的机动车辆的控制系统，具有以下过程步骤：通过指定虚拟车辆（1）在虚拟仿真环境中的平移运动，在虚拟传感器（2）承载虚拟车辆（2）的情况下模拟驾驶通过虚拟仿真环境的至少一部分，通过捕捉虚拟仿真环境，通过在虚拟传感器（2）的视场中检测虚拟车辆（1）经过的虚拟模拟环境并提供合成传感器数据作为测试数据以测试评估传感器数据流的机动车辆的控制系统，利用虚拟传感器（1）生成合成传感器数据，其中确定通过所述虚拟仿真环境的至少一部分的步骤，其中所述虚拟传感器（2）承载由所述虚拟车辆（1）的平移运动引起的至少一个车辆动态运动，其中车辆动态运动包括虚拟车辆（1）的点头和/或哈欠和/或摆动和/或垂直运动；并且在利用虚拟传感器（2）生成合成传感器数据的步骤中考虑由虚拟车辆（1）的模拟车辆动态运动引起的虚拟传感器（2中）的视场（3）的变化，其中，所述虚拟传感器（1）检测所述虚拟车辆（1）所经过的虚拟模拟环境。这样，提供了在模拟的情况下获得这样的测试数据的可能性，所述测试数据应用于测试ECU，该测试数据与通过实际系统获得的数据的偏差尽可能小。
",G06F30/15,撤回,虚拟仿真软件技术,3.0,关键词匹配: 虚拟仿真; IPC分类号匹配: G06F30/15,"提供, 仿真, 包括, 检测, 方法, 控制, 模拟, 实现, 具有, 测试, 生成, 系统, 计算","驾驶, 检测, 传感器, 测试, 机动车, 车辆, 控制"
EP4202779A1,EP21216234.1,"本发明涉及一种用于对虚拟测试（T）的场景进行分类的计算机实现的方法和系统，提供（S1）由多个车载环境传感器（10a、10b、10c）检测到的自我车辆的行程的传感器数据的第一数据集（DS1），通过第一算法（A1）对传感器数据的第一数据集（DS1）的变换（S2），特别地，一种多元数据分析方法，在传感器数据的数据缩减的第二数据集（DS2）中，将机器学习的第二算法（A2）应用于传感器数据的用于对第二数据集中所涵盖的场景进行分类的数据精简的第二数据集（DS2）（S3），表示车辆动作第三记录（DS3）的多个类别（K）。本发明还涉及一种计算机实现的方法，用于提供用于对虚拟测试（T）的场景进行分类的经过训练的第二算法（A2）机器学习。
",G06N3/08,实质审查,虚拟仿真软件技术,3.0,关键词匹配: 虚拟测试; IPC分类号匹配: G06N3/08,"提供, 检测, 方法, 实现, 测试, 算法, 系统, 计算","检测, 传感器, 车辆, 测试"
EP4202790A1,EP21216241.6,"本发明涉及一种用于对虚拟测试的交通状况（VS）进行分类的计算机实现的方法和系统。该方法包括连接（S3）自我车辆（12）的多个指定数据段（14）的横向和纵向行为，用于识别车辆动作（16）和分类（S4）交通状况n（VS），通过将自身车辆（12）的横向和纵向行为的指定数据段（14）的子集与所识别的车辆动作（16）相链接。本发明还包括一种计算机实现的方法，用于提供用于虚拟测试的交通状况（VS）分类的经过训练的机器学习算法。
",G06N5/04,实质审查,虚拟仿真软件技术,1.0,关键词匹配: 虚拟测试,"提供, 包括, 方法, 实现, 测试, 算法, 系统, 计算","车辆, 测试"
US20230195977A1,US18082756,"一种用于对虚拟测试的场景分类的计算机实现的方法和系统，包括提供由多个车辆侧周围环境检测传感器捕获的本车辆的行驶的传感器数据的第一数据集；通过第一算法将传感器数据的第一数据集变换为传感器数据的数据减少的第二数据集，特别是多变量数据分析方法；将第二机器学习算法应用于传感器数据的数据减少的第二数据集，以用于对由第二数据集包括的场景分类；以及具有表示车辆动作的多个类的第三数据集的输出。还提供了一种用于提供训练的第二机器学习算法以用于对虚拟测试的场景分类的计算机实现的方法。
",G06F30/27,公开,虚拟仿真软件技术,3.0,关键词匹配: 虚拟测试; IPC分类号匹配: G06F30/27,"提供, 包括, 检测, 方法, 实现, 具有, 测试, 算法, 系统, 计算","检测, 传感器, 车辆, 测试"
US20230192148A1,US18084633,"一种用于对虚拟测试的交通状况进行分类的计算机实现的方法和系统。该方法包括：连接本车辆的横向和纵向行为的多个所确定的数据段以识别车辆动作；以及通过将本车辆的横向和纵向行为的所确定的数据段的子集与所识别的车辆动作链接来对交通状况进行分类。本发明进一步包括一种用于提供训练的机器学习算法以对虚拟测试的交通状况进行分类的计算机实现的方法。
",B60W60/00,暂缺,虚拟仿真软件技术,1.0,关键词匹配: 虚拟测试,"提供, 包括, 方法, 实现, 测试, 算法, 系统, 计算","车辆, 测试"
EP4199553A1,EP21214247.5,"本发明涉及一种计算机实现的方法和测试单元（1），用于最小化用于机动车辆的至少部分自主引导的装置的多个虚拟测试（T1-Tn）的测试执行的计算工作量，包括执行（S3）所述第一虚拟测试（T1）和所述第二虚拟测试（T2），其中以这种方式考虑所述驾驶状况参数（FP）的所述第2虚拟测试（T2）与所述第1虚拟测试（T1）的相同份额（An1）和/或差分份额（An2）和/或者至少一个参数的变化时间点（Z1），测试执行的计算工作量被最小化。
",H04W4/40,实质审查,虚拟仿真软件技术,1.0,关键词匹配: 虚拟测试,"包括, 方法, 装置, 实现, 测试, 计算","机动车, 驾驶, 测试"
US20230185989A1,US18061489,"一种用于使用于执行多个虚拟测试的计算工作量最小化的方法，包括：由测试单元提供第一虚拟测试的参数集和第二虚拟测试关于运行状况参数和实现第一虚拟测试和第二虚拟测试的算法的配置数据的参数集；由测试单元确定第二虚拟测试关于运行状况参数和/或第二虚拟测试的至少一个参数与第一虚拟测试相比变化的时间点相对于第一虚拟测试的相同分量和/或差异分量；以及由测试单元在考虑相同分量和/或差异分量的同时执行第一虚拟测试和第二虚拟测试，以便使用于测试执行的计算工作量最小化。
",G06F30/20,暂缺,虚拟仿真软件技术,3.0,关键词匹配: 虚拟测试; IPC分类号匹配: G06F30/20,"提供, 包括, 方法, 实现, 配置, 测试, 算法, 计算",测试
US20230174083A1,US17680387,"一种用于确定至少部分自主地驱动机动车辆的装置的虚拟测试的计算努力的计算机实现的方法包括：提供执行虚拟测试的第一算法的配置数据和驱动状况参数的至少一个参数集，其中由第一算法执行的虚拟测试模拟驱动状况参数的至少一个参数集，并且其中模拟的结果用于确定在后续迭代中模拟的驱动状况参数的至少一个另外的参数集；将第二算法应用于驱动状况参数的至少一个参数集和第一算法的配置数据；以及输出表示虚拟测试的计算努力的至少一个数值。
",B60W50/06,暂缺,虚拟仿真软件技术,1.0,关键词匹配: 虚拟测试,"提供, 包括, 方法, 装置, 实现, 配置, 测试, 算法, 模拟, 计算","机动车, 测试"
EP4191479A1,EP21212420.0,"本发明涉及一种用于生成传感器数据的数据集的复杂度降低的逻辑表示（10）的方法和系统（1），以及第三数据集（DS3）的输出（S4），其表示第二数据集（DS2）的复杂性降低的逻辑场景（L）。本发明还涉及一种用于提供用于生成传感器数据的数据集的复杂度降低的表示的训练算法（a）机器学习的方法。
",G06N3/08,实质审查,虚拟仿真软件技术,2.0,IPC分类号匹配: G06N3/08,"提供, 方法, 生成, 算法, 系统",传感器
EP4002217A1,EP21202565.4,"根据用于使用系统减少训练数据的方法，所述系统包括编码器，所述训练数据的至少一部分形成时间序列并且被组合在第一训练数据集中，并且所述编码器输入数据是原型特征向量集合中的原型特征向量的数据，a)接收来自所述第一训练数据集合的第一输入数据，b)所述第一输入数据由所述编码器传播，其中所述输入数据由所述编码器分配有一个或多个特征向量，并且根据所分配的特征向量，确定特定的原型特征向量集合并且将其分配给所述第一输入数据，c)针对所述第一输入数据创建聚合向量，d)利用来自所述第一训练数据集合的第二输入数据执行步骤a)至c)，并且针对所述第二输入数据创建第二聚合向量，e)将至少所述第一和第二聚合向量进行比较并且确定所述聚合向量的相似性度量，并且f)如果所确定的相似性度量超过阈值，则标记所述第一输入数据或者从所述第一训练数据集合中移除所述第一输入数据，其中所述标记或者移除导致来自所述第一训练数据集合的所述第一输入数据不被用于第一训练。
",G06N3/04,实质审查,虚拟仿真软件技术,2.0,IPC分类号匹配: G06N3/04,"方法, 系统, 包括",通用
EP3876157A1,EP20160370.1,"本发明涉及一种计算机实现的过程以及一种用于利用环境传感器（尤其是激光雷达传感器）、车辆的合成传感器数据（SSD）生成叠加的距离和强度信息（11，12）的系统。本发明还涉及一种计算机实现的程序，该程序提供一种经过训练的机器学习算法，用于利用环境传感器（尤其是激光雷达传感器）的合成传感器数据（SSD）生成叠加的距离和强度信息（I1，I2），车辆。本发明还涉及计算机程序和计算机可读介质。
",G06N3/04,授权,虚拟仿真软件技术,2.0,IPC分类号匹配: G06N3/04,"提供, 实现, 生成, 算法, 系统, 计算","激光, 雷达, 传感器, 车辆"
US20210056432A1,US16999533,"一种计算机实现的方法，用于训练生成的对手网络的人工神经生成器网络，以便近似来自用于机动车辆的至少部分自主引导的设备的虚拟测试的第一测试结果的所识别的子集的第二测试结果。本发明还涉及一种用于训练人工神经鉴别器网络的计算机实现的方法、测试单元、计算机程序和计算机可读数据载体。
",G06N3/08,暂缺,虚拟仿真软件技术,3.0,关键词匹配: 虚拟测试; IPC分类号匹配: G06N3/08,"设备, 方法, 实现, 测试, 生成, 计算","机动车, 网络, 测试"
EP3783452A1,EP19192743.3,"本发明涉及一种用于近似设备的虚拟测试的测试结果以至少部分自主引导机动车的计算机实现的方法。本发明还涉及一种用于提供经训练的人造神经网络的计算机实现的方法、一种测试单元(1)、一种计算机程序以及一种计算机可读的数据载体。
",G05D1/02,授权,虚拟仿真软件技术,1.0,关键词匹配: 虚拟测试,"提供, 设备, 方法, 实现, 测试, 计算","机动车, 网络, 测试"
EP3783446A1,EP19192741.7,"本发明涉及一种计算机实现的程序，用于近似用于车辆的至少部分自主驾驶的设备的虚拟测试的测试结果的子集。本发明还涉及一篇论文单元（1），用于从用于车辆至少部分自主驾驶的装置的虚拟试验得到的试验结果子集的近似值。本发明还涉及计算机程序和计算机可读介质。
",G05B13/02,授权,虚拟仿真软件技术,1.0,关键词匹配: 虚拟测试,"设备, 装置, 实现, 测试, 计算","驾驶, 车辆, 测试"
US20190152486A1,US15818787,"用于一图象处理装置的一张试验台包括：设置在试验台中的第一计算部件，其中第一计算部件对于一环境模型经配置以执行仿真软件，仿真软件，其经配置以计算一第一位置x （t）和一初速向量v （t）并分配第一位置x （t）和初速向量v （t）到在环境模型中的第一虚物；第二计算部件设置在试验台中的，其中第二计算部件经配置以在环境模型中的第一虚物的一位置循环地读取并计算，基于至少记录位置，表示一个二维的第一图像数据，环境模型的第一图形投射；设置在试验台中的过渡舱。
",B60W40/04,申请终止,虚拟仿真软件技术,1.0,关键词匹配: 仿真软件,"仿真, 包括, 装置, 处理, 配置, 设置, 模型, 计算",通用
