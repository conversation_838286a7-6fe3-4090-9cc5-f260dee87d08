﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,当前法律状态,技术领域,分类得分,分类理由,技术手段,应用场景
EP4592886A1,EP24221364.3,"该发明涉及一种计算机实现的方法用于将模拟与测试同步该模拟根据输入数据生成输出数据包括以下步骤：S1）将输入数据从测试传输到仿真，以便仿真确定输出数据，同时启动测试时间和仿真时间，以便可以获取经过的测试时间和经过的仿真时间，并将确定的输出数据从仿真传输到测试，S2）获取测试时间以及获取仿真时间并比较仿真时间与测试时间，S3a）当比较已经证明模拟时间等于或超过测试时间时：从预定的输入数据集向模拟传输另一个输入数据，以便模拟根据该额外输入数据确定另一个输出数据，或者如果所有输入数据已经传输到模拟，则结束过程，并S3b)当比较已经证明模拟时间小于测试时间时：暂停测试一段时间，在这段时间内停止测试时间的流逝，并在暂停时间后重新执行步骤S2以及随后的步骤S3a或S3b。
这样就提供了一种可以实现精确同步的方法。
",G06F30/20,暂缺,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"提供, 仿真, 包括, 方法, 实现, 测试, 生成, 模拟, 计算",测试
EP4105811B1,EP22163416.5,"一种用于在基于场景的测试和/或通过关键性能指标(KPI)对要测试的至少部分自主驾驶功能的同源性进行评估的计算机实现的方法，其中KPI由KPI插件映射，并且KPI插件被动态地选择并且可重用于模拟和/或测试情况，并且其中至少一个KPI插件由KPI插件机制选择以用于模拟和/或测试定义的目的，并且在由KPI插件机制执行时自动地执行。
",G06F30/15,授权,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/15,"方法, 实现, 测试, 模拟, 计算","驾驶, 测试"
EP4575875A1,EP23217568.7,"该发明涉及一种计算机实现的方法用于生成针对至少一个模拟运行（12）的更改建议（10），所述模拟运行（12）是包含多个模拟运行（12）的模拟（14），包括以下步骤- 接收一个或多个仿真运行结果(16)的一个或多个已结束的仿真运行(12)的仿真(14)- 预测至少一个尚未完成的模拟运行(12)的模拟运行结果(20)在考虑接收到的模拟运行结果(16)的情况下并且生成变更建议(10)用于尚未结束的模拟运行(12)的模拟(14)并考虑至少一个预测的模拟运行结果(20)。
此外本发明涉及一种数据处理装置包括用于执行上述方法的手段计算机程序产品和相应的计算机可读存储介质
",G06F30/20,实质审查,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"仿真, 包括, 方法, 装置, 处理, 实现, 生成, 模拟, 计算",通用
EP4571331A1,EP24217495.1,"展示和描述了一种计算机实现的方法（1）用于通过硬件在环模拟器（2）对具有励磁绕组的多相电动机进行仿真（3）以测试功率电子控制装置（4）其中包含逆变器（4）。模拟器（2）根据电动机的数学模型（7）计算电动机各绕组中的绕组电流（i_m）以及由电动机反作用产生的无电位绕组连接的电气反作用电势（u_emf_w），其中无电位绕组连接通过电压模拟器（9）被施加到反作用电势上。
该方法通过在每个支路(8)中分别用虚拟开关(11)来补充电动机的数学模型(7)从而减少不准确计算的反向电势对仿真结果的影响其中虚拟开关(11)在打开状态下会减小相应测量的支路电压(u_m)对相应支路(8)中计算出的电流分布(i_m)的影响而虚拟开关(11)则由模拟器(2)的开关逻辑(12)通过评估至少一个支路电压(u_m)和/或一个支路电流(i_m)来在电动机的支路(8)中打开和/或关闭
",G01R31/28,实质审查,HIL硬件在环仿真技术,2.0,"关键词匹配: 硬件在环, 模拟器","仿真, 方法, 控制, 装置, 实现, 具有, 测试, 模型, 模拟, 计算","控制, 测试"
EP4538877A1,EP24198573.8,"根据本发明，提供了一种用于在第一处理器类型的真实处理器上执行仿真的方法，其中该仿真具有为第一处理器类型编译的至少一个仿真模型，其过程步骤如下：在第一处理器类型的真实处理器上运行为第一处理器类型编译的仿真模型，作为第一处理器类型处理器的仿真。
这提供了在选择模型以及选择仿真目标平台方面实现极大灵活性的可能性。
",G06F9/455,实质审查,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F9/455,"提供, 仿真, 方法, 平台, 处理, 实现, 具有, 模型",通用
US12216967B2,US17239068,"一种计算机实现的方法，用于通过模拟器算术单元实时模拟特定电动机的操作，该模拟器算术单元包括在其上实现通用电动机模型的可编程逻辑器件。该方法包括：提供对应于通用电动机模型的通用方程组；接收与待模拟的特定电机对应的用于通用方程组的特定信息，并将该信息输入到通用方程组中；生成包含用于计算特定电机的操作的矩阵操作所需的算术运算中的至少一些的特定库；在通用电机模型中执行对实时仿真特定电机的操作所需的特定库的算术操作的引用；以及通过在模拟器运算单元上运行通用电动机模型来模拟特定电动机的操作。
",G06F30/20,授权,HIL硬件在环仿真技术,4.0,"关键词匹配: 模拟器, 实时仿真; IPC分类号匹配: G06F30/20","提供, 仿真, 包括, 方法, 实现, 生成, 模型, 模拟, 计算",通用
EP4485095A1,EP24181414.4,"描述和示出了一种用于用模拟器（3）测试电子控制单元（2）的计算机实现的方法（1），其中所述模拟器（3）在计算单元上以数字方式计算数学环境模型（4），其中所述环境模型（3）至少部分地模拟所述控制单元（2）的环境，其中，所述控制装置（2）和所述模拟器）经由对应的I/O接口（5，6）彼此耦合和相互作用，其中，在所述仿真器（3）上的所述环境模式（4）的所述数字计算中，执行矩阵向量乘法，其中，矩阵（M）乘以向量（v）得到结果向量（r），其中，将所述矩阵向量乘法分解为两个被加数的总和（b）的序列，其中每个求和是两个因子的乘积，一个是矩阵（M）的元素（ki），另一个是向量（v）的元素。
矩阵向量乘法的优化规划和执行是通过以下方式实现的：在序列确定步骤（8）中，求和（b）被确定为序列中的下一个求和，其求和取决于来自矩阵（M）的最高占用行的矩阵元素（ki），其中矩阵元素（ki）是从具有最高求和可用性（P）的最高占据行中选择的，因此序列确定步骤，其中，通过将矩阵（M）中的所有矩阵元素（Mij）设置为零，残差矩阵（M’）从矩阵（M”）中出现，这些元素涉及在执行计划序列的求和形成（b）仓的序列（b）中已经计划的求和构成，其中，用于计算求和的乘积构成是在序列中执行的，因为求和是执行求和形成的必要条件，并且在最终求和步骤（9）中，通过连续的对加法计算结果向量（r）每行中的前一个求和（b）得到的和。
",G05B17/00,实质审查,HIL硬件在环仿真技术,2.0,"关键词匹配: 模拟器, 仿真器","仿真, 方法, 控制, 装置, 实现, 具有, 设置, 测试, 模型, 模拟, 计算","控制, 测试"
CN112166428B,CN201980032587.5,"用于系统的基于事件的模拟的方法，所述模拟在包括第一运算单元和第二运算单元的计算机系统上执行，第一运算单元具有模拟时间；第二运算单元具有系统时间；第二运算单元执行模拟器应用，在其上执行模拟对象；以及第一运算单元管理事件等待队列，对每个模拟步骤列出事件并该事件配置要执行的进程和设定用于执行进程的模拟时刻；第二运算单元具有虚拟时钟发生器，对于每个模拟步骤：通过第一运算单元将开始信号传送给虚拟时钟发生器以用于在第二运算单元中执行新的模拟步骤；以及基于在之前的模拟步骤的模拟时间与新的模拟步骤的模拟时间之间的时间差增加第二运算单元的系统时间，以及在配置给所等候的进程的模拟时刻执行该进程。
",G06F30/20,授权,HIL硬件在环仿真技术,3.0,关键词匹配: 模拟器; IPC分类号匹配: G06F30/20,"包括, 方法, 模拟, 具有, 配置, 系统, 计算",通用
EP4471650A1,EP23176385.5,"根据本发明，提供了一种用于为包括多个子模型（11、12、13、21、22）的仿真系统创建残差模型（3）的方法，该方法具有以下过程步骤：选择至少一个子模型（11、12、13、21、22）作为不应包含在剩余模型（3）中的目标模型（21，22），捕获必须连接到目标模型（21、22）的端口的仿真系统（1）的端口，在残差模型（3）中创建具有相同属性的相应端口的检测端口，捕获必须连接到目标模型（21、22）的仿真系统现场总线（1）；以及创建适用于残差模型中捕获的现场总线的现场总线控制器（3）。
",G06F30/20,实质审查,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"提供, 仿真, 包括, 检测, 方法, 控制, 具有, 模型, 系统","检测, 控制"
US20240385290A1,US18664932,"一种用于测试环境传感器的测试装置。测试装置包括用于环境传感器的容器，其被配置为发射环境信号。第一物体模拟器被配置为接收环境信号作为第一接收信号。第二物体模拟器被配置为接收环境信号作为第二接收信号。处理器被配置为从第一和第二接收信号中确定信号差，特别是相位差和/或频率差，并且根据信号差来确定环境传感器、第一物体模拟器和第二物体模拟器之间的空间关系，和/或第二物体模拟器。
",G01S7/40,公开,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"包括, 装置, 处理, 配置, 测试, 模拟","传感器, 测试"
US20240329201A1,US18621356,"一种用于模拟传感器(雷达，LIDAR)的距离的模拟器。模拟器包括接收器，其被设置为从传感器(雷达、LIDAR)接收第一传感器信号并将其转换为工作信号。具有多条延迟线的延迟部分被施加到至少一个衬底。第一电开关装置，其被设置为根据第一选择信号以使得工作信号的信号路径包括第一选择的方式来切换延迟线的第一选择。发射器被设置成在通过信号路径之后将工作信号转换成第二传感器信号并将其发送到传感器(雷达，激光雷达)。还提供了一种用于操作模拟器的方法和用于模拟器的延迟部分。
",G01S7/40,公开,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"提供, 包括, 方法, 装置, 具有, 设置, 模拟","激光, 雷达, 传感器"
US20240296112A1,US18593244,"一种计算机实现的方法，用于经由模拟器上的模拟环境的至少一个计算单元来测试控制单元的至少一个控制单元功能的执行。控制单元功能是在连续地提前两个仿真时间之间的零时间假设下执行的在模拟器上的面向事件的离散模拟中的连续模拟步骤。模拟中死锁情况的原因被识别，因为在模拟器上操作的观察服务将离散模拟时间的提前与模拟器的提前实时地进行比较，并且如果仿真器的超过离散仿真时间的提前的实时提前超过预定极限值，则至少间接地创建至少一个计算单元的堆栈跟踪。
",G06F11/36,公开,HIL硬件在环仿真技术,2.0,"关键词匹配: 模拟器, 仿真器","仿真, 方法, 控制, 实现, 测试, 模拟, 计算","控制, 测试"
US20240289418A1,US18572221,"一种模拟器包括被配置为模拟技术系统的运算单元。模拟器被配置为使用矩阵向量乘法基于输入向量来确定用于输出模拟器的输出信号的输出向量。矩阵代表技术性SY柄。算术单元具有至少一个乘法器和至少一个加法器，用于矩阵向量乘法。所述至少一个乘法器和所述至少一个加法器每个都被配置用于时分复用功能，以执行它们各自的任务。提供一种调度器其被配置为将所述至少一个乘法器的输出值分配给所述至少一个加法器。
",G06F17/16,暂缺,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"提供, 包括, 模拟, 具有, 配置, 系统",通用
EP4409367A1,EP22786818.9,"本发明涉及一种用于测试至少一个控制器的方法。在模拟器上提供至少两个单独的网络，其中，待测试的控制器经由第一网络和第一控制器接口连接至模拟器，并且待测试的控制器被设计为与至少一个第一附加控制器通信。第一附加控制器的通信至少部分地以记录消息的形式提供，其中用于播放记录消息的再现单元通过至少一个再现接口连接到模拟器上的第二网络或第三网络，再现单元通过再现接口连接到模拟器。
",G05B17/02,期限届满,HIL硬件在环仿真技术,3.0,关键词匹配: 模拟器; IPC分类号匹配: G05B17/02,"提供, 方法, 控制, 测试, 模拟","通信, 网络, 控制, 测试"
US20240202396A1,US18540443,"一种用于仿真电动机的相电流以测试功率电子控制单元的测试组件和方法，该功率电子控制单元被设计成控制电动机并且可以连接到测试组件。该测试组件具有电感器仿真器，该电感器仿真器使用功率电子电路来模拟作为控制单元的电负载的电动机，其中电感器仿真器充当电源。此外，测试组件具有测试装置，该测试装置被设计为根据对依赖于控制单元的输出电压的变量的分析来将电感器仿真器切换到另一操作状态。
",G06F30/20,暂缺,HIL硬件在环仿真技术,3.0,关键词匹配: 仿真器; IPC分类号匹配: G06F30/20,"仿真, 方法, 模拟, 装置, 具有, 电路, 测试, 控制","控制, 测试"
US20240202390A1,US18173814,"通过模拟开发和测试多部件系统和/或其相互作用部件。该模拟包括至少两个彼此交换状态数据的实体。一种用于这种开发和测试的方法，包括：请求加入模拟的实体；以及请求实体以这样的方式加入仿真，即，由请求实体进行的状态数据的传输与由先前加入仿真的实体进行的状态数据的传输基本上同时发生。
",G06F30/20,暂缺,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"仿真, 包括, 方法, 系统, 测试, 模拟",测试
US20240176926A1,US18070968,"一种用于模拟在计算环境中被建模为框图的一个或多个块的程序的计算机实现的方法和系统。所述一个或多个块具有至少一个信号，所述计算环境包括模型编辑器、数据定义工具和代码生成器。该方法包括：在模拟环境中模拟第一时间跨度的框图，显示作为模拟时间的函数的信号值，在特定模拟时间接收暂停命令和新的参数值，在模拟环境中模拟第二时间跨度的框图，以及显示来自第一时间跨度的信号的值和来自第二时间跨度的信号的值两者。来自第二时间跨度的值与来自第一时间跨度的值图形地不同地显示。
",G06F30/20,暂缺,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"包括, 方法, 模拟, 实现, 具有, 生成, 模型, 工具, 系统, 计算",通用
US20240157974A1,US18127993,"本发明涉及一种用于将图像数据从车辆的虚拟环境输出到控制单元的模拟装置和方法，其被设计为根据图像数据执行至少一个车辆功能。环境模拟器被配置为模拟车辆的环境，并将关于环境的数据传输到传感器模拟器，其被配置为根据关于环境的数据模拟车辆的至少一个传感器，以检测环境，并且根据其模拟输出图像数据。环境模拟器被设计为确定关于模拟时钟时间处的环境的数据，并且根据模拟时钟时间将这些数据传输到传感器模拟器，并且其中传感器模拟器被配置为在传感器时钟时间处输出图像数据。
",B60W60/00,暂缺,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"检测, 方法, 控制, 装置, 配置, 模拟","检测, 数据传输, 传感器, 车辆, 控制"
EP4369240A1,EP22206970.0,"提出了一种用于将车辆的虚拟环境（EGO）的图像数据（BD）输出到控制单元（SG）的模拟设备（SV）和方法，其被设计为根据图像数据（BD.）执行至少一个车辆功能。它规定：环境模拟器（US），其被设计成模拟所述车辆（EGO）的环境并将关于所述环境的数据（Da）传输到传感器模拟器（SeSi），所述传感器模拟器被设计成根据关于所述周围的数据（Da）来模拟所述车辆（EGO）的至少一个传感器（SE1、SE2、SE3、SE4），用于检测所述环境并根据其模拟输出所述图像数据（BD），其中，所述环境模拟器（US）被设计成在模拟时间点处确定关于所述环境的所述数据（Da），并根据所述模拟时间点将所述数据传输到所述传感器模拟器（SeSi），并且其中，所说传感器模拟器（Semi）被设计为在传感器时钟时间处输出所述图像数据（BD），其中，关于所述传感器时钟时间的信息被存储在所述环境仿真器（US）中，其中所说环境模拟器（美国）被设计成对于所说传感器时钟时间将所说数据（Da。
",G06F30/20,撤回,HIL硬件在环仿真技术,4.0,"关键词匹配: 模拟器, 仿真器; IPC分类号匹配: G06F30/20","设备, 仿真, 检测, 方法, 控制, 模拟","检测, 数据传输, 传感器, 车辆, 控制"
US20240103508A1,US18535538,"一种用于测试电子控制单元的装置，具有环路中硬件模拟器以模拟至少一个传感器信号，并且配备有经由其输出传感器信号的双线电缆，并且具有经由双线电缆连接到环路中硬件模拟器的切换装置，使得可从切换装置接收传感器信号。切换装置中的双线电缆被布线到切换装置的两极接口，电子控制单元可以连接到两极接口以接收传感器信号，并且切换装置具有两个电压源，两个电压源中的每一个被分配给双线电缆的导线并且可以经由可控开关连接到相应导线。以这种方式，在诊断期间可以经由两个可切换电压源继续向传感器供应电能，从而避免诊断错误。
",G05B23/02,暂缺,HIL硬件在环仿真技术,2.0,"关键词匹配: 模拟器, 环路","控制, 装置, 具有, 测试, 模拟","诊断, 传感器, 控制, 测试"
US20240094339A1,US17948287,"一种用于有源环境感测系统的雷达传感器的测试系统，包括：第一目标模拟器，其包括第一电子控制单元、连接至所述第一电子控制单元用于接收雷达传感器信号的第一接收天线、以及连接至所述第一电子控制单元用于发射由所述第一电子控制单元生成的第一雷达回波的第一发射天线；以及第二目标模拟器，其包括第二电子控制单元、连接至所述第二电子控制单元用于接收雷达传感器信号的第二接收天线、以及连接至所述第二电子控制单元用于发射由所述第二电子控制单元生成的第二雷达回波的第二发射天线。
",G01S7/40,暂缺,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"包括, 模拟, 系统, 测试, 生成, 控制","传感器, 雷达, 控制, 测试"
US20240054258A1,US17886479,"一种用于实时模拟的系统，包括：实时计算设备，其被配置为执行所述实时模拟；以及主机计算设备，其与所述实时计算设备通信。所述主机计算设备被配置为：配置用于执行所述实时模拟的实时模拟应用；以及将所述实时模拟应用发送到所述实时计算设备。所述实时计算设备还被配置为作为执行所述实时模拟的一部分：接收与到所述实时计算设备的缓冲器的数据阵列相对应的消息；将所述缓冲器中的所述消息拆包；确定与所述数据阵列相对应的拆包触发器已被激活；以及响应于确定与所述数据阵列相对应的所述拆包触发器已被激活，将所述缓冲器中的所述消息拆包。
",G06F30/20,暂缺,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"设备, 包括, 配置, 系统, 模拟, 计算",通信
US20230408648A1,US18211836,"一种用于确定空中测试室的部件的布置的计算机实现的方法和系统，包括确定空中测试室中的部件相对于彼此的优化布置的位置数据和/或空中测试室中的优化布置的位置数据的分组，以及第二数据集合的输出，第二数据集合包括DUT的优化布置的位置，特别是雷达传感器、反射器和目标模拟器或目标模拟器的发射/接收设备在空中测试室中的位置，和/或在空中测试室中的优化布置的分组。
",G01S7/40,公开,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"设备, 包括, 方法, 模拟, 实现, 测试, 系统, 计算","雷达, 传感器, 测试"
US20230359785A1,US18143672,"一种用于借助于环路硬件模拟器的至少一个处理单元来模拟电驱动器的计算机实现的方法。所述电驱动器的模型具有：逆变器，所述逆变器由具有至少一个半桥的直流电压源供电，所述半桥具有至少两个半导体开关；以及电动机，所述电动机具有电绕组电阻和绕组电感。具有所述半桥的中心抽头电压的中心抽头借助于具有供给线路电流的供给线路连接至所述电动机的电动机连接端。通过操控所述半导体开关，所述电动机连接端在所述逆变器的导电状态中能够或者在所述逆变器的打开状态中在所述半导体开关打开的情况下连接至所述直流电压源的电势，或者在所述逆变器的打开状态中在所述半导体开关打开的情况下能够在所述电路上对所述电动机连接端解锁。
",G06F30/20,暂缺,HIL硬件在环仿真技术,4.0,"关键词匹配: 模拟器, 环路; IPC分类号匹配: G06F30/20","方法, 处理, 实现, 具有, 电路, 模型, 模拟, 计算",通用
US20230359780A1,US18040269,"一种用于提供对要测试的交通场景的测试过程的计算机实现的方法，具有以下步骤：训练和使用相似性分类模块以用于基于特定场景的参数集的子集对两个场景之间的相似性进行分类的目的；生成包括多个场景的图表表示，其中节点各自表示场景的参数集的子集并且两个节点之间的边用指示相应节点的两个场景之间的相似性的相似性分类值来加权；借助于用户界面和/或自动系统输入接收用户输入，其中输入包括用于覆盖场景的所需值和/或要测试的场景的所需数目，使得确定场景的选择和执行序列。
",G06F30/15,暂缺,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/15,"提供, 包括, 方法, 实现, 具有, 测试, 生成, 系统, 计算",测试
EP4273638A1,EP23158920.1,"本申请涉及一种用于配置用于模拟（12）至少一个传感器的模块的方法，其中该模块（12）具有传感器模拟（16）和第一类型的第一数据接口（14），通过第一类型的第一数据接口（14）向用于执行控制和/或控制任务的设备（30）提供合成传感器数据。该程序包括以下步骤：a） 接收真实传感器（22）与所述设备（30）的数据交换的记录（20），其中所述数据交换经由所述第一类型的第二数据接口（24）进行，和/或接收关于所述真实传感器（二十二）和/或所述第一型型型的第二数字接口（二十四）的至少一个特性的信息，b） 读出所述记录（20）和/或关于至少一个特性的信息；c） 确定真实传感器（22）和/或第一类型的第二数据接口（24）的至少一个特性（Ch），d） 使用至少一个特性（Ch）配置传感器模拟（16）和/或第一类型的第一数据接口（14），使得合成传感器数据模拟真实传感器（22）的传感器数据。
本申请还涉及一种用于配置用于模拟的模块的设备。
",G05B17/02,实质审查,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"提供, 设备, 包括, 方法, 控制, 具有, 配置, 模拟","传感器, 控制"
EP3196714B2,EP16171914.1,"提供一种用于模拟可以连接到控制装置的外围电路布置的方法。模拟装置电连接到控制装置并且具有第一控制元件，通过该第一控制元件可以影响可以从控制装置的第一负载端子传递到第一控制元件的第一控制元件输出的第一模拟电流。第一控制元件包括第一多级转换器，并且模拟装置还包括第一半导体开关控制器和执行模型代码的计算单元。计算第一开关控制信号并且提供该第一开关控制信号以用于转发到具有至少一个第一比较器的第一半导体开关控制器。生成经脉宽调制的第一栅极-源极电压并且将其施加到第一控制端子，并且第一模拟电流受第一栅极-源极电压影响。
",G05B17/02,授权,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"提供, 包括, 方法, 控制, 装置, 具有, 电路, 生成, 模型, 模拟, 计算",控制
US11693998B2,US16113560,"一种提供用于控制单元开发的能够实时地进行的模拟的方法，其中，所述能够实时地进行的模拟对控制单元或者控制单元的环境或者控制单元与控制单元的环境的组合进行模拟，其中，所述能够实时地进行的模拟具有能够实时地进行的子模拟和与所述能够实时地进行的子模拟相互作用的、不能够实时地进行的子模拟的共模拟，其中，所述能够实时地进行的子模拟和所述不能够实时地进行的子模拟被设计用于传递模拟数据，其中，所述能够实时地进行的子模拟具有与实时地相对应的第一模拟时间，并且所述不能够实时地进行的子模拟具有虚拟的、第二模拟时间，所述第二模拟时间与所述第一模拟时间耦合并且在开始所述能够实时地进行的模拟时与所述第一模拟时间相匹配。
",G06F30/20,授权,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"提供, 方法, 控制, 具有, 模拟",控制
US20230194643A1,US18082058,"提出了一种用于校准电池仿真器的装置或方法。电池仿真器仿真多个串联连接的单元，其中每个仿真单元具有抽头，通过这些抽头分接至少一个仿真量，其中该装置包括开关装置，校准标准件可通过该开关装置与不同的抽头可切换地连接。
",G01R35/00,公开,HIL硬件在环仿真技术,1.0,关键词匹配: 仿真器,"仿真, 包括, 方法, 装置, 具有",通用
EP4198722A1,EP21215811.7,"根据本发明，提供了一种用于配置ECU在计算机上运行的SIL仿真的方法，其中对于ECU在具有多个任务的ECU的计算机软件模型上的SIL仿真，在各个时钟时间之间具有预定周期的预定时钟中对其进行处理，并且计算机具有多个处理器核，多个虚拟机在其上运行，每个处理器核处理预定任务，具有以下处理步骤：a） 确定软件模型的偶发模型份额和周期模型份额，b） 从软件模型的周期性模型部分创建一组可并行化的任务，c） 确定可并行任务的各个循环时间，直到可并行任务循环时间之间的所有周期的最小公倍数，d） 确定各个任务在各自循环时间的计算时间，以及e） 基于可并行任务的时钟时间之间的所有周期的所确定的最低公倍数和单个任务在它们各自的时钟时间的所确定计算时间，以这样的方式确定任务到各个虚拟机的分配和虚拟机到各个处理器核的分配，SIL模拟的计算时间是最小的。这提供了一种保持SIL测试的计算时间尽可能低的方法。
",G06F9/455,撤回,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F9/455,"提供, 仿真, 方法, 处理, 具有, 配置, 测试, 模型, 模拟, 计算",测试
US20230176882A1,US18076977,"一种模拟基于AUTOSAR标准的控制单元的方法，在连续的时间步骤中处理不同的任务，在时间步骤中执行所有任务所需的执行时间被假定为零，该方法包括：测量任务的执行时间，在超过提供AUTOSAR函数调用的AUTOSAR参数时，为特定任务的执行时间定义上阈值；和/或测量任务的激活时间，为特定任务的激活时间定义上阈值，该上阈值是在AUTOSAR参数的帮助下实现的；和/或在至少一个时间步骤中改变AUTOSAR参数，以使得它大于测量的实际激活时间。
",G06F9/455,暂缺,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F9/455,"提供, 包括, 方法, 控制, 处理, 实现, 模拟",控制
US20230168342A1,US17990274,"一种用于生成模拟雷达回波信号的方法和雷达目标模拟器。从待测试的雷达传感器以已知带宽发送雷达信号。在雷达目标模拟器中接收雷达信号。通过具有已知滤波器曲线的低通滤波器对雷达信号进行滤波。确定低通滤波器的全部带宽上的经滤波的雷达信号的频谱。计算校正频谱和与校正频谱对应的雷达信号的功率。根据经滤波的雷达信号和作为经缩放的雷达信号的反射的雷达回波信号来计算经缩放的雷达信号。从雷达目标模拟器的发射天线向待测试的雷达传感器发送雷达回波信号。
",G01S7/40,授权,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"方法, 具有, 测试, 生成, 模拟, 计算","雷达, 传感器, 测试"
US20230113864A1,US17954063,"一种用于在使用制动试验台的模拟计算机中模拟可旋转体的运动的方法和装置，该制动试验台具有发动机、表示所模拟的可旋转体的真实的可旋转体、以及制动器。该方法包括以下方法步骤：指定目标速度，将该目标速度施加到发动机，旋转真实的可旋转体，指定制动值，基于所指定的制动值控制制动器，测量真实的可旋转体的实际扭矩和实际速度，确定实际速度是否超过预定极限速度，以及基于所模拟的可旋转体的扭矩来模拟可旋转体的运动。以这种方式，提供了用于模拟可旋转体的运动的可能性，即使对于可旋转体的低速，该可能性也提供至少近似正确的结果。
",G06F30/15,暂缺,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/15,"提供, 包括, 方法, 控制, 装置, 具有, 模拟, 计算",控制
EP4124915A1,EP21209965.9,"一种计算机实现的方法（1），用于产生适用于数值模拟软件图像（2）的真实ECU（3）的至少一部分，其中真实ECU（4）在操作中将ECU输入（4a）的输入矢量映射到ECU输出（4b）的输出矢量。
软件映像的生成基于：所述软件图像（2）由人工神经网络（5）或支持向量机形成，所述支持向量机具有具有感兴趣的ECU输入（7a）的图像输入（6a）的输入向量和具有感兴趣ECU输出（7b）的输出向量，通过监督学习或通过强化学习（8）对软件图像（2）进行训练，所述软件图像（8）利用感兴趣的ECU输入（7a）的多个训练输入向量（9a）进行训练，其中训练输入向量（9a）和相应的训练输出向量（9b）是从实际ECU（3）的操作、即从ECU输入变量（4a）和ECU输出变量（4b）获得的。
",G05B17/02,实质审查,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"方法, 实现, 具有, 生成, 模拟, 计算",网络
US20220222394A1,US17553838,"一种用于提供技术系统的模拟的方法包括：通过不具有实时能力的部分模拟来生成ping消息；通过所述不具有实时能力的部分模拟来向具有实时能力的部分模拟发送ping消息；以及通过所述不具有实时能力的部分模拟来将从所述具有实时能力的部分模拟接收到的乒乓消息存储在日志文件中，或者在没有从所述具有实时能力的部分模拟接收到乒乓消息的情况下，通过所述不具有实时能力的部分模拟来将所述ping消息存储在所述日志文件中。
",G06F30/20,暂缺,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"提供, 包括, 方法, 模拟, 具有, 生成, 系统",通用
US20220146627A1,US17095813,"一种用于校准用于主动环境检测系统的目标模拟器的方法，包括：通过确定至少一个信号参数的第一值与所述至少一个信号参数的第一参考值的第一偏差来校准包括第一信号路径和第二信号路径的完整信号路径；通过确定所述至少一个信号参数的第二值与所述至少一个信号参数的第二参考值的第二偏差来校准所述第一信号路径和所述第二信号路径中的一个；以及通过将第一偏差偏移第二偏差来校准第一信号路径和第二信号路径中的另一个。
",G01S7/40,授权,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"包括, 检测, 方法, 系统, 模拟",检测
US20220043411A1,US17394437,"一种用于低等待时间生成传感器数据并将其输入控制单元或控制单元网络的方法包括：由模拟器系统或设备使用虚拟环境场景的环境模型以模拟频率计算环境数据集，其中每个环境数据集与在总体模拟时间中的相应时间点相关联，在该时间点相应的环境数据集可用于进一步处理；以及由所述模拟器系统或设备或由至少一个附加系统或设备执行至少一个传感器模拟模型，其中所述至少一个传感器模拟模型基于所述环境数据集以采样频率生成传感器数据集，以用于由至少一个控制单元进一步处理，其中每个传感器数据集与在所述总体模拟时间中的相应开始时间和相应完成时间相关联。
",G05B17/02,暂缺,HIL硬件在环仿真技术,3.0,关键词匹配: 模拟器; IPC分类号匹配: G05B17/02,"设备, 包括, 方法, 控制, 模拟, 处理, 生成, 模型, 系统, 计算","传感器, 网络, 控制"
EP3832517A1,EP19212861.9,"本发明涉及一种计算机实现的过程，用于将至少一个信号值集成到具有应用层、运行时环境（RTE）的虚拟控制单元中，基本软件层和微控制器抽象层。虚拟控制器通过个人计算机在模拟平台上操作，并且需要至少一个输入信号，其中至少一个信号值可归因于输入信号。模拟功能应向虚拟控制装置提供至少一个第一模拟信号值。微控制器抽象层具有用于将第一模拟信号值和第二外部信号值集成到虚拟控制设备中的软件组件，其中，第二外部信号值由外部外围设备提供。用户通过用于输入信号的用户接口选择软件组件应使用第一模拟信号值还是第二外部信号值。在另一处理步骤中，当运行虚拟控制器时，根据用户的选择，将第一模拟信号值或第二外部信号值提供给虚拟控制器作为输入信号的信号值。
",G06F30/20,实质审查,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/20,"提供, 设备, 模拟, 平台, 装置, 实现, 具有, 处理, 控制, 计算",控制
CN106575106B,CN201580042555.5,"本发明涉及一种用于仿真可连接到调节装置(DUT)上的外围电路设备的仿真装置(100)，其中，所述仿真装置(100)与调节装置电气连接或者可电气连接，并且所述仿真装置(100)具有用于影响第一负载电流(IL1)的第一电流调节器(150)以及用于影响第一源电流(IS1)的第二电流调节器(160)，其中，所述仿真装置(100)此外包括计算单元(CU)和能在所述计算单元(CU)上执行的模型代码，并且借助于能由模型代码控制的第一电流调节器(150)能调节第一负载电流(IL1)，并且所述第一负载电流(IL1)被引导到调节装置(DUT)的第一负载连接端(W1)上，并且借助于能由模型代码控制的第二电流调节器(160)能调节第一源电流(IS1)，并且第一源电流(IS1)被引导到调节装置(DUT)的第一电源连接端(C1)上，并且设定和设置所述模型代码，以便通过模型代码对第一电流调节器(150)和第二电流调节器(160)施加影响使得所述第一负载电流(IL1)能至少部分地从第一源电流(IS1)回收，和/或所述第一源电流(IS1)能至少部分地从第一负载电流(IL1)回收。此外，本发明涉及一种用于仿真的方法。
",G05B17/02,授权,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"设备, 仿真, 包括, 方法, 装置, 具有, 电路, 设置, 模型, 控制, 计算",控制
EP3082000B1,EP15163609.9,"一种用于通过测试环境与真实和/或虚拟部分交互来测试真实和/或虚拟汽车系统的计算机实现的方法，所述真实和/或虚拟部分包括具有针对在测试配置中指定的不同执行条件的测试的不同测试例的测试序列。根据对相应测试中的系统的功能的评估，向测试例和测试配置的每个组合分配来自预定义测试状态值的组的测试状态值。为了进一步规划、执行和/或评估所述测试序列，至少一次在得到的测试例配置矩阵中确定所述状态值中的至少一个的相对测试覆盖，和/或确定提高所述状态值中的至少一个关于测试例和/或关于配置的测试覆盖的相对潜在性。
",G06F30/15,授权,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F30/15,"包括, 方法, 实现, 具有, 配置, 测试, 系统, 计算","汽车, 测试"
US10488835B2,US15220433,"一种用于配置被配备用于测试电子控制单元的测试器的方法，其中技术系统的软件模型在测试器上执行，并且通过测试器的输入/输出接口与连接到测试器的设备电子地通信。配置系统耦合到建模系统，并且在建模系统中存在由彼此连接的功能块表征的软件模型。测试器在配置系统中由互连的配置元件配置，使得输入/输出接口和/或CONN的物理特性通过配置元件定义输入/输出接口与软件模型的连接。配置系统耦合到建模系统，使得软件模型在建模系统的运行时经由耦合接口被提供给配置系统。
",G05B17/02,授权,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"提供, 设备, 方法, 控制, 配置, 测试, 模型, 系统","通信, 控制, 测试"
US10386806B2,US14842152,"一种用于连接技术系统的模型的方法，其在为具有第一技术系统的一第一个模型的连接到第二技术系统的一次第二种模型的控制单元发展装备的一测试装置中。第一个模型和第二种模型包括一控制单元的一模型，一技术系统的一模型被控制，或与控制单元相互作用的一环境的一模型或在技术系统被控制的情况下。第一个模型具有第一数据界面，并且第二种模型具有第二数据界面。方法具有提供一第一个模型层级构造和提供一第二种模型层级构造。方法具有相容的连接的自动配置，以便通过相容的连接的存在于具有存在于测试装置中的第二种模型的测试装置数据交换中的第一个模型。
",G05B17/02,授权,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"提供, 包括, 方法, 控制, 装置, 具有, 配置, 测试, 模型, 系统","控制, 测试"
US10353363B1,US15339968,"由一种功能组成的系统接收块，并且一功能为一图解的发送块，用于具有一个框图的技术的和关系式的图形模型化的基于块的模型环境。框图的块具有输入口和/或输出端口，其中块通过用于数据传输的检测信号线可以通过它们的端口连接。实现的一个功能的柔性管理特别达成在于功能接收块具有一功能接收贯穿其中功能接收块的端口可以被赋予一功能可分配的功能的接口仅仅在功能里指定以功能的输出量的输入和/或数的数的形式接收块。功能发送块具有一功能使贯穿其中一功能被发送出至一个联想机能的端口接收块。
",G05B17/02,授权,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"检测, 实现, 具有, 模型, 系统","检测, 数据传输"
CN109932926A,CN201711370654.3,"本发明涉及一种用于图像处理系统的试验台，其具有：第一计算单元用于运行具有虚拟物体的环境模型；第二计算单元用于基于虚拟物体的位置计算环境模型的图像投影；以及适配器模块用于处理图像数据并且将其馈入图像处理系统。第一计算单元设置用于从图像处理系统读入基于图像数据计算的控制数据并且在考虑控制数据的情况下将新速度向量配置给虚拟物体。试验台设置用于测量从通过第二计算单元计算图像数据开始直到适配器模块对图像数据的处理结束所经过的时间间隔的长度。第一计算单元设置用于借助该长度估计图像数据的延迟时间、借助该延迟时间求取虚拟物体的外推位置并且将其传输给第二计算单元。本发明还涉及一种用于测试图像处理系统的方法。
",G05B17/02,申请终止,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"方法, 控制, 处理, 具有, 配置, 设置, 测试, 模型, 系统, 计算","控制, 测试"
US10275542B2,US13957463,"一配置工具包括一有形的，非暂时性计算机可读介质，其具有计算器可执行指令，其用于配置一技术系统的一模型并展示对连接到一台计算机的显示器的模型。模型至少包括二模型组成部分。每一模型组成部分具有至少一个端口。每一模型组成部分在显示器上的一展开的分量表示中是可显示。每一模型组成部分的至少一个端口用端口的结合线可连通到另一模型组成部分的至少一个端口。每一模型组成部分在随着每一模型组成部分的至少一个端口和端口的结合线的显示器上的一展开的划线表示中是可显示。至少为一选择模型组件连接到选择模型组件的端口的端口的结合线可被选出显示在一减少的划线表示中。
",G06F9/455,授权,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F9/455,"包括, 具有, 配置, 模型, 工具, 系统, 计算",通用
EP3454234A1,EP17189524.6,"为控制装置的开发提供实时模拟（6）的程序，其中，实时模拟（6）模拟控件或控件的环境或控件与控件环境的组合；其中，实时仿真（6）是实时局部仿真（10）的联合仿真，以及与实时局部仿真交互的联合仿真，非实时子仿真（12），其中实时子仿真（10）和非实时子仿真（12）用于仿真数据通信（SE0、SN0、SE1、SN1、SE2、SN2、SE3、SN3、SE4、SN4、SE5、SN5）经过培训，其中实时部分仿真（10）具有与实时（TE）对应的第一仿真时间，而非实时部分仿真（12）具有与第一仿真时间G对应的第一仿真时间。耦合虚拟二次仿真时间（TV）对应于实时仿真开始时的第一次仿真时间；如果程序有以下步骤：（a）在实时局部模拟的基础上进行验证（10），非实时子仿真（12）是否为即将到来的宏仿真步骤提供了计算的仿真数据；（b）非实时子仿真（12）已经为即将到来的宏模拟步骤完成了计算的模拟数据（sn2），并提供了实时局部模拟（10），在实时子仿真（10）的下一个宏仿真步骤中使用计算的仿真数据（sn2）；（c）如果非实时子仿真（12）没有e已经完成了即将进行的宏观模拟步骤的计算模拟数据，并提供了实时局部模拟（10）。为下一个宏模拟步骤创建估计的模拟数据（SG3）；（d）完成读数后，将第二个模拟时间调整为第一个模拟时间。未及时提供仿真数据的计算（sn3）。
",G06F17/50,撤回,HIL硬件在环仿真技术,1.0,关键词匹配: 实时仿真,"提供, 仿真, 控制, 装置, 具有, 模拟, 计算","验证, 通信, 控制"
US20190065644A1,US16119780,"一试验设备可测试性设计的一组态系统一电子控制单元。试验设备是一台硬件回路模拟器或一台快速的样机控制系统模拟器。一技术系统的一软件模型在试验设备上被执行，经由具有连接到试验设备的设备的试验设备的一输入输出接口，软件模型连通。数据电子地是由通信传输，其中组态系统具有多个配置条目。使用技术的功能性质，配置条目在所连接设备和软件模型之间被赋予试验设备和试验设备和/或通信的技术的功能性质被配置。配置条目被赋予一功能类别并在组态系统中的功能性板材中被建造。
",G06F17/50,申请终止,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"设备, 控制, 模拟, 具有, 配置, 测试, 模型, 系统","通信, 控制, 测试"
EP3352028A1,EP17152600.7,"一个模拟器（5）和一个测试程序（1）在一个函数（f））的一个控制面板（2）的车辆（3A）。该车辆包括umgebungssensoren，例如雷达（6A，6B），照相机（6C条）和接收器（6D），作为输入的steuergerätefunktion（f））。服务控制单元（2），其中一款型号为（12A条），和一个umgebungsmodell sensormodelle（13）（14）在一个多recheneinheiten（4A，4B条，4C）和存储器（5）和分布式模拟器进行模拟。仿真steuergerätefunktion（ECU）和模拟作为其输入（第12A），辅助车辆umgebungsmodell sensormodelle（13）和（14）被同步到recheneinheiten（4a，4b，4c）的启动，其中对内存进行数据交换。通过对多recheneinheiten（4A，4B，4C）及数据交换的分布式仿真是一种高simulationsgeschwindigkeit内存了。控制单元（2）不一定需要模拟真实的存在。通过参与，例如数据从其他车辆（3B）（8）建筑物，道路，交通标志（9），（10）或（11）交通标志，可以模拟。
",G05B17/02,撤回,HIL硬件在环仿真技术,3.0,关键词匹配: 模拟器; IPC分类号匹配: G05B17/02,"仿真, 包括, 模拟, 测试, 控制","雷达, 车辆, 控制, 测试"
EP3316477A1,EP16195987.9,"提出并描述了用负载模拟器（1）再现三相电动机的过程。其中负载模拟器（1）通过其负载终端（2）分三个阶段连接到发动机控制装置（4）的馈送终端（3），负载模拟器（1）具有模拟器电力电子装置（5）和模拟器控制装置（6）以控制模拟器电力电子装置（5），其中仿真器控制装置（6）确定由发动机控制装置（4）控制的电源端子（3）和非电源端子（3），以及由仿真器控制装置（6）控制的仿真器电力电子装置（5），由发动机控制装置（4）计算的相电流从仿真器控制装置（6）根据发动机模型（8）和不受发动机控制装置（3）控制的馈电线连接（4）流入馈电线终端（3）仿真器控制装置根据发动机模型（8）计算的相电流放弃相电压仿真。
通过负载模拟器（1）包括开关（15），可以实现考虑三相电动机超出正常电动机或发电机运行的特殊运行情况的可能性，其中，仿真器电力电子器件（5）可以大容量地从馈电电路（3）断开，仿真器电力电子器件（5）可以低电平地接通馈电电路（3），并且馈电电路（3）通过高电平电阻网络（16）连接，为了模拟电动机的选定运行模式，带有开关（15）的仿真器电力电子器件（5）应与高电平（3）的馈电电路分开，或者仿真器电力电子器件已被开关（15）从低电平到电源（3）。
",H02P27/06,授权,HIL硬件在环仿真技术,2.0,"关键词匹配: 模拟器, 仿真器","仿真, 包括, 控制, 装置, 实现, 具有, 电路, 模型, 模拟, 计算","网络, 控制"
EP3285165A1,EP16184654.8,"根据本发明，修改了技术系统的操作软件10，特别是用于控制或控制至少一个技术设备的控制器，通过不执行在技术系统10上直接可执行的硬件相关软件部件，而是通过替换功能23再现硬件相关软件部件的功能行为。自动识别硬件相关的软件组件（步骤S1），以及替换功能23自动检测或生成。当在适当的模拟环境200上运行时，经修改的操作软件20独立于其实际硬件100来模拟技术系统（步骤S3）。
",G06F9/455,撤回,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F9/455,"设备, 检测, 模拟, 系统, 生成, 控制","检测, 控制"
CN103033364B,CN201210322295.5,"本发明涉及用于借助模拟器实时测试内燃机控制设备的方法，其中模拟器包括第一模拟器计算单元和第一模拟器I/O接口，控制设备包括控制设备计算单元和控制设备I/O接口且控制设备和模拟器通过其I/O接口借助第一数据通道相互连接并且控制设备通过第一数据通道传送内燃机控制数据至模拟器，模拟器借助内燃机控制数据及内燃机整体模型在模拟器的第一模拟器计算单元上以第一采样步长计算内燃机状态参数且以第一传输步长传输至少一部分内燃机状态参数至控制设备。模拟器利用内燃机局部模型以不同于第一采样步长的第二采样步长计算至少一个特定的内燃机状态参数，这样所选出的内燃机状态参数可以以另一个尤其是比通过第一采样步长所能实现的更高的频率被提供。
",G01M15/00,授权,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"提供, 设备, 包括, 方法, 控制, 实现, 测试, 模型, 模拟, 计算","控制, 测试"
CN102467091B,CN201110043394.5,"一种电池模拟设备(5)，具有控制单元(7)和用于根据由控制单元(7)经电隔离的接口(14)预给定的额定值在电池控制装置(3)的端子(10d)上模拟电池单元电压的至少一个模拟通道(8)，其中模拟通道(8)具有电压源(15)、放大器单元(13)、用于连接模拟通道(8)的连接线路(11a，11b，11c)、及测量线路(9a，9b)和用于对故障状态、尤其是对电缆断裂进行仿真的装置(12)。
",G05B17/02,授权,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"设备, 仿真, 模拟, 装置, 具有, 控制",控制
EP3001313A1,EP14185873.8,"方法执行一个应用程序的第一个开口的第一个计算机上使用的应用程序，其中第一个功能来控制执行器和传感器的功能和\/或处理和\/或提供的致动器和\/或数据传感器执行其中第一控制装置控制设备的硬件的一个具有至少一个第一类型的第一rechenkern rechenkern和计算机硬件，计算机（1）和至少一个第二和一个第二rechenkern rechenkern类型，其中第一类型和rechenkern至少在第二rechenkern型使用的指令集识别一个操作系统的一个源代码控制设备和控制设备的操作系统，其中第一个通过一个接口控制单元之间的硬件和应用程序的第一个开口的第一个训练，应用程序的源代码是可用的，源代码和源代码控制设备操作系统的可执行应用程序的第一，第二rechenkern型编译的编译通过，其中一第一操作系统的虚拟设备（6）和（8第一虚拟应用程序计算机生成的），并显示一个仿真环境（7）同时通过仿真环境simulationsumgebungsschnittstelle（9）的一个移交的日期和\/或一个虚拟应用程序事件的第一（8）和\/或操作系统的虚拟设备（6）提供的，和一个计算机操作系统（5））通过这一之间的第二界面的计算机操作系统的计算机硬件和软件仿真环境（1）（7）培训和管理程序（2），其中一个第一个虚拟机管理程序的计算机提供的（4）通过第一个虚拟机（4）计算机硬件（1）部分或完全的形式提供虚拟硬件的虚拟硬件，包括至少一个第一虚拟rechenkern虚拟操作系统，和控制设备（6）在第一个虚拟机执行，（4）仿真环境（7）一个虚拟应用程序执行的第一个控制设备（8）内的操作系统（6）在第一个虚拟机（4）的simulationsumgebungsschnittstelle发起和控制（9）其中税款移交数据和\/或事件在虚拟应用程序（8）和\/或虚拟设备的操作系统（6）包括第一虚拟应用程序，通过控制设备（8）（6）虚拟操作系统直接访问虚拟的硬件提供的第一个虚拟机（4）提供。
",G06F9/455,撤回,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G06F9/455,"提供, 设备, 仿真, 包括, 方法, 控制, 装置, 处理, 具有, 生成, 系统, 计算","传感器, 控制"
EP2990892A1,EP14183024.0,"一个程序，一个连接的输入\/输出接口（4），一个控制面板提供用于测试一个testgeräts（2）与一个在测试设备（2）提供的现有技术系统的模型，的输入\/输出接口连接器的测试或控制面板（20）连接到一个控制系统和技术训练与输入\/输出接口（4）连接到一个模型的模型（8）从屏幕或一个技术系统这个模型的控制面板，其中测试装置（2）仍然是与模型相关的输入\/输出功能（6））。这提供了一个接口的方法是提供一个hierarchiestruktur hierarchiestruktur以及功能。一个自动配置程序继续指出兼容接口之间的联系和功能上的hierarchiestruktur hierarchiestruktur，这样在测试装置（2）对现有的模型，至少部分兼容与碲的化合物控制单元（20）和stenden该系统可以从屏幕的技术交流。
",G05B17/02,授权,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"提供, 设备, 方法, 控制, 装置, 配置, 测试, 模型, 系统","控制, 测试"
EP2940857A1,EP14166171.0,"本发明公开了一种用于用一负载仿真程序（1）模拟一三相无刷直流电动机的方法，其中对一个电动机控制器（4）的馈供终端（3）的通过其加载口（2）的负载仿真程序（1）三相连接和用于控制仿真程序动力电子设备（5）的负载仿真程序一仿真程序动力电子设备（5）和一台仿真程序控制器（6），其中仿真程序控制器（6）由发动机调节器（4）馈供终端（3）驱动的和无驱动供给口（3）确定和仿真程序动力电子设备（5）（通过仿真程序控制器6）被驱动那样在（通过电动机控制器4）馈供终端（3）（仿真程序控制器6）基于一电动机模型（8）驱动计算的相电流i 以一电动机模型（8）为基础仿真仿真器控制的流动和（通过发动机操纵4）无驱动馈送端（3）计算的相电压v 仿真被废弃。
可靠的交换被保证那检测对来自在从动状态S PH （j ）=0 中的非驱动状态S PH （j ）=1 的发动机操纵（4）的一馈送端j 的过渡如果微分电压v 困难（j）的量值在之间到馈供终端j 到电动机控制器（4）测得的输出电压V inv （j）和计算的相电压v 对于一预定的第一次周期t 1 仿真（j）大于一预定阈值电压值V th ，那在发动机控制的馈送端j 的状态转换的检测之后，其来自在从动状态S PH （j ）=0 中的非驱动状态S PH （j ）=1 ，仿真程序控制器（6），仿真程序动力电子设备（5）在这种通过现在通过仿真程序控制器（6）的发动机操纵（4）被驱动的馈送端j 计算的相电流i 仿真（j）流动
",H02P7/06,授权,HIL硬件在环仿真技术,1.0,关键词匹配: 仿真器,"设备, 仿真, 检测, 方法, 控制, 模型, 模拟, 计算","检测, 控制"
EP2933695A1,EP14165123.2,"描述并示出了一种用于利用模拟器(2)对控制单元(1)进行实时测试的计算机实现的方法，其中，模拟器(2)包括模拟器I/O接口(3)，其中，控制单元(1)是控制单元I和O接口(4)，并且其中，控制单元(1)和模拟器(2)经由它们的I/O接口(3、4)借助于至少一个数据通道(5)而互连，并且控制单元(1)经由数据通道(5)使转换器控制数据(6)并且借助于电负载模型(7)而在不考虑由功率转换器(8)引起的电流间隙(11)的情况下发送到模拟器(2)的转换器控制数据(6)来计算负载电流(ix)和负载电压(ux)，并且将负载状态变量的至少一部分发送到控制单元(1) 。通过在模拟器(2)上附加地执行控制技术的观察器(9)来以更高的精度进行实时测试，其中，观察器(9)在考虑转换器控制数据(6)的情况下并且利用观察器负载模型(10)至少检测来自所计算的负载电流(ix)的负载电流(ix)，观察器(9)检测负载电流(ix)的过零和由此引起的电流间隙(11)，并且在检测到电流间隙(11)时观察器(9)检测电补偿变量(11) 。以如下方式计算u comp)：使得当负载模型(7)中的电负载附加地负载有补偿变量(u comp)时，利用负载模型(7)以对于现有电流间隙(11)减小的误差来计算负载电流(ix) 。
",G05B17/02,授权,HIL硬件在环仿真技术,3.0,关键词匹配: 模拟器; IPC分类号匹配: G05B17/02,"包括, 检测, 方法, 控制, 实现, 测试, 模型, 模拟, 计算","检测, 控制, 测试"
EP2876512A1,EP13194228.6,"方法用于自动连接的组件模型的一个技术系统的计算机上显示一个模式，其中第一和第二modellkomponente modellkomponente）。第一和第二modellkomponente modellkomponente hierarchieelement分别显示在至少一个，其中一个或一个hierarchieelement hierarchieelement hierarchieelement hierarchieelemente包含一个或多个端口或没有。hierarchieelement指出一个或多个端口E的其中一个端口，一个端口标识符和一个hierarchieelement特性。两个端口之间的连接提供了一个技术系统的映射。通过一个图形用户交互的数量和\/或hierarchieelementen第一端口和第二端口的数量和\/或hierarchieelementen选择一个端口，其中第一和第二港口第一数量的第二数量的分配，我们考虑一个潜在的D.一个可能的映射方法，给出了在第一端口和第二端口，以及相对应的等级上升的第一端口和第二端口的标识符hierarchieelemente的第一端口和第二端口的标准，每一个从端口到一个相同的层次水平vorgebbaren数量根据预定的规则是一致的或一致的评价。在有可能成为第一个端口分配第一数量自动与第二第二连接端口的数量。
",G05B17/02,驳回,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"提供, 方法, 模型, 系统, 计算",通用
EP2851815A1,EP13184920.0,"显示和描述用于实时测试具有控制装置代码（EC）的虚拟控制装置（3）的至少一部分的测试装置（1）；其中，测试设备（1）具有至少一个具有第一组指令（IS1）的第一类型计算器（4）和至少一个用于实时模拟虚拟控制设备环境（3）的模拟环境（5），并且其中，模拟环境（5）和控制设备代码（EC）具有至少一个计算器（4类型已计算。
使用控制装置代码（EC）对虚拟控制装置（3）进行试验，它能够在具有第二指令集（IS2）的计算器（6）第二类型上运行，由此，计算器核心（6）第二类型的第二指令集（IS2）与计算器核心（4）第一类型的第一指令集（IS1）不同，第一类计算器（4）执行仿真器（7）以模拟第二类计算器核心（6），仿真器（7）执行控制设备代码（EC），仿真器（7）具有模拟环境接口（8），以与模拟环境（5）交换数据和/或事件。
",G06F17/50,驳回,HIL硬件在环仿真技术,1.0,关键词匹配: 仿真器,"设备, 仿真, 模拟, 装置, 具有, 测试, 控制, 计算","控制, 测试"
EP2801872A1,EP13166604.2,"描述的测试是一个测试的一部分，至少一个虚拟仿真环境的控制面板（2）（3）定义在一个模拟器，虚拟仿真环境的控制装置（2）和（3），其中至少有一个虚拟控制装置（2）的软件组件（4,5,6）与至少一个外部主机接口（7）包括在仿真环境（3）至少有一个主机接口（8）至少间接的数据交换与虚拟控制装置（2）包括。一个链接的anpassungsaufwand减少从而实现了一个虚拟设备针单元（9）的至少一个虚拟steuergeräteschnittstelle（10）是利用虚拟steuergeräteschnittstelle（10）至少有datenschnittste外（7）所有构件（4）的虚拟控制面板（2）连接。
一个测试之间的依赖关系，在虚拟仿真环境，控制和减少，从而实现了一个虚拟设备针单元（9）的至少一个虚拟steuergeräteschnittstelle（10）是利用虚拟steuergeräteschnittstelle（10）至少与外部主机接口（7）软件组件（4）连接的虚拟控制面板（2）IST控制单元的虚拟针单元（9）至少有一个simulationsumgebungsschnittstelle（11）通过simulationsumgebungsschnittstelle）和主机接口（8）与环境（3）连接到控制单元和虚拟单元（9）至少销一个虚拟设备的引脚（12），和物理接口有一个引脚对应的模拟真实的控制，同时通过虚拟设备的引脚（12）是一个虚拟的物理steuergerätesignal转让。
",G05B17/02,授权,HIL硬件在环仿真技术,3.0,关键词匹配: 模拟器; IPC分类号匹配: G05B17/02,"设备, 仿真, 包括, 模拟, 装置, 实现, 测试, 控制","控制, 测试"
CN102099755B,CN200980127353.5,"这里通过以下方式可以对具有高功率转换的大负载进行仿真，即在桥对角线支路(12)中设置可控电压源(13)和该桥对角线支路中的有效电感(14)，实际电流(i<Sub>ist</Sub>)借助于作用于可控电压源(13)的电流控制单元(15)能调节为预给定的额定电流(i<Sub>soll</Sub>)的值。
",G05B17/02,未缴年费,HIL硬件在环仿真技术,2.0,IPC分类号匹配: G05B17/02,"控制, 具有, 设置, 仿真",控制
EP2579115A1,EP11184086.4,"本发明涉及将具有计算单元(3)和模拟器-输入/输出(I/O)接口(4)的模拟器(2)与具有控制器-I/O接口(5)的控制单元(1)连接。在控制器和模拟器之间形成数据通道(6)，模拟器通过该数据通道接收电动机控制数据。基于电动机控制数据的接收，通过模拟器计算单元的全电动机模型(7)计算发动机状态参数和发动机状态变量。在模拟器电动机模型(8)中扫描计算的电动机结果。为模拟器包括独立权利要求。
",G05B17/02,授权,HIL硬件在环仿真技术,3.0,关键词匹配: 模拟器; IPC分类号匹配: G05B17/02,"包括, 控制, 具有, 模型, 模拟, 计算",控制
CN202183045U,CN201120043267.0,"本公开涉及一种用来测试至少一个控制单元的装置，具有用来执行至少一个环境模型的至少一个计算单元、用来为电子控制单元提供至少一个信号的至少一个信号生成卡、及用来在计算单元与信号生成卡之间双向传输数据的至少一个串行总线，且不仅计算单元而且信号生成卡均具有用来接收和/或发送时间同步消息及角度同步消息的至少一个与串行总线的接口。本公开一实施例解决的一个问题是克服用于测试至少一个电子控制单元的传统装置和传统的HIL仿真器在执行相对复杂的模型、尤其是复杂环境模型时存在性能瓶颈，或者提高在硬件扩展或者硬件空间分布方面的灵活性。根据本公开的一个实施例的一个用途是对汽车中的控制单元进行测试。
",G05B23/02,期限届满,HIL硬件在环仿真技术,2.0,"关键词匹配: HIL, 仿真器","提供, 仿真, 装置, 具有, 测试, 生成, 模型, 控制, 计算","汽车, 控制, 测试"
