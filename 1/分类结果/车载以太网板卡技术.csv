﻿公开(公告)号,申请号,摘要(中文),IPC主分类号,当前法律状态,技术领域,分类得分,分类理由,技术手段,应用场景
US20250080610A1,US18728472,"一种用于自动注释传感器数据的计算机实现的方法，包括：接收多个传感器数据帧；使用至少一个神经网络注释所述多个传感器数据帧，其中所述注释包括向每个传感器数据帧分配至少一个数据点，并且向每个数据点分配至少一个状态属性；基于所述至少一个状态属性对所述数据点进行分组，其中第一组包括所述至少一个状态属性在定义的值范围内的数据点；从第一组中选择一个或多个数据点的第一样本；以及确定第一样本中的一个或多个数据点的质量度量。
",H04L67/12,暂缺,车载以太网板卡技术,2.0,IPC分类号匹配: H04L67/12,"方法, 计算, 实现, 包括","传感器, 网络"
EP4507253A1,EP23190448.3,"本发明涉及在网络系统（10）中进行数据传输的计算机实现的方法，该网络系统包括一个外部网络（12）和一个内部网络（14），其中，所述外部网络（12）包括至少一个第一网络元件（18）和一个外部网络通信元件（22）；其中，所述内部网络（14）至少包括第二网络元件（20）和内部网络通信元件（24），其中，所述第二网络元件（20）被设计为仅在所述内部网络（14）内通信，其中该过程包括以下步骤：-对要发送的数据进行编码（S10，34）；将编码数据（S12）传输到外部网络通信元件（22）；通过IP通信通道将数据（S14）传输到内部网络通信元件（24）并对数据进行解码（36）；通过内部网络通信元件（24）将数据（S16）重新编码到应用层；将重新编码的数据（S18）传输到第二个网络元件（20）。
",H04L12/46,实质审查,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/46,"包括, 方法, 实现, 系统, 计算","通信, 数据传输, 网络"
US20240267429A1,US18434055,"一种用于借助于第一程序来变换所记录的通信数据的方法，其中，所述通信数据由来自网络或总线通信的消息给出，其中，所记录的通信基于第一通信矩阵来执行，其中，所记录的通信数据被提供以借助于测试系统被发送到被测ECU，其中，所述测试系统经由第一通信链路被连接到被测ECU，其中，所述被测ECU基于第二通信矩阵来配置。
",H04L67/12,公开,车载以太网板卡技术,2.0,IPC分类号匹配: H04L67/12,"提供, 方法, 配置, 测试, 系统","通信, 网络, 测试"
EP4096170B1,EP22169648.7,"一种用于在网络系统中传输数据的方法和一种网络系统，该网络系统具有处于混杂模式的第四网络元件的网络控制器的操作，以及在第一网络和第二网络之间产生IP隧道，第三网络元件和第四网络元件是经由接入元件引导的IP隧道的特定端点。
",H04L12/46,授权,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/46,"控制, 方法, 具有, 系统","网络, 控制"
EP2221697B1,EP09002479.5,"方法包括获取以形式语言作为要求的包含时间依赖性的目标功能，其中功能包括动作和预期反应。根据要求生成要求模型。通过执行在考虑功能的时间依赖性的同时在模型中执行的不可逆性分析来生成测试情况。将情况变换成信号模式。通过测试设备经由信号接口将模式应用于装置。利用测试设备检测装置的相关状态变量。还包括对于以下的独立权利要求：(1)具有用于执行利用测试设备测试控制装置的方法的程序代码的计算机程序；(2)用于测试控制装置的测试设备。
",H04L12/26,授权,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/26,"设备, 包括, 检测, 方法, 装置, 具有, 测试, 生成, 模型, 控制, 计算","检测, 控制, 测试"
US20190165996A1,US15822521,"一种用于操作具有用于计算一个模拟模型的多种网络节点的一真实的时间有能力的模拟网络的方法。网络节点经由一串行数据总线彼此连接，网络节点交换数据通过数据总线信息。模拟模型的至少一个事件驱动任务实施于一第一网络节点，一非定常的触发事件被一第二网络节点检测。第二网络节点传送检测到的触发事件到第一网络节点，并且第一网络节点计算事件驱动任务。一快速响应时间通过一检测信号是从以对模拟网络的多种网络节点或到在串行数据总线上的模拟网络的所有的网络节点的一条播数据总线消息或一条广播数据总线消息的形式的第二网络节点传送的手段实现。
",H04L12/24,申请终止,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/24,"检测, 方法, 实现, 具有, 模型, 模拟, 计算","检测, 网络"
CN103944794B,CN201410016033.5,"本发明涉及一种用于将总线用户连接到至少一个总线上的电路装置，其具有用于将总线用户与电路装置连接的接口、第一总线输入端和第一总线输出端。电路装置包括第二总线输入端和第二总线输出端以用于环形拓扑地将总线连接到电路装置上，使第一总线输出端至少间接地经由该总线而与第二总线输入端连接并且第二总线输出端至少间接地经由总线而与第一总线输入端连接，其中总线在电路装置中能拆散以用于获得线性拓扑，并且电路装置在其中一个总线输入端或总线输出端处能配置成使总线终止。此外本发明包括用于在仿真环境中对总线上的总线用户进行功能检查的系统，该系统包括仿真器单元和多个所述电路装置。
",H04L12/40,授权,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/40,"仿真, 包括, 装置, 具有, 配置, 电路, 系统",通用
CN103475552B,CN201310122307.4,"本发明阐述并描写了一种用于干扰控制装置的总线通信的方法，其中，总线通信包括至少一个不依赖于总线硬件的第一通信层以及至少一个依赖于总线硬件的第二通信层，第一通信层将至少一条信息编码为第一协议数据单元并将其传输到第二通信层，和/或第一通信层从第二通信层得到第一协议数据单元并从第一协议数据单元解码第一条信息，并且，第二通信层由第一协议数据单元或者由一个从第一协议数据单元推导出的另一个协议数据单元产生一条依赖于总线硬件的总线信息用于通过总线传输，和/或第二通信层至少由一条依赖于总线硬件的总线信息产生第一协议数据单元或者产生能够推导出第一协议数据单元的另一个协议数据单元。
",H04L12/40,授权,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/40,"装置, 方法, 控制, 包括","通信, 控制"
CN102132528B,CN200880130860.X,"介绍展示了通信系统，具有至少两个外围装置(2a，2b，2c，2d)，每个外围装置(2a，2b，2c，2d)有至少一个I/O接口(3a，3b，3c，3d)，外围装置(2a，2b，2c，2d)经至少一个数据总线(4)相互连接且通过通信关系(KV)经数据总线(4)交换数据，本发明的任务是提供一种通信系统，利用其能至少部分解决现有技术中的问题。该任务根据本发明通过以下方式实现：设置具有外围装置接口(6a，6b，6c)和数据总线接口(7a，7b，7c)的至少一个接口装置(5a，5b，5c)，接口装置(5a，5b，5c)经其外围装置接口(6a，6b，6c)与外围装置(2a，2b，2c)之一经该外围装置的I/O接口(3a，3b，3c)相连且接口装置(5a，5b，5c)经其数据总线接口(7a，7b，7c)与数据总线(4)相连且通信关系(KV)能预给定给接口装置(5a，5b，5c)。
",H04L12/40,未缴年费,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/40,"提供, 装置, 实现, 具有, 设置, 系统",通信
EP2978168A1,EP14177808.4,"一种用于模拟通过时间的方法，其中第一网络订户经由第一网络线路连接到网络网关，并且第二网络订户经由第二网络线路连接到网络网关，第一网络订户具有经由第一网络线路发送的寻址到第二网络订户的第一消息，其中网络网关经由第一网络线路接收第一消息，并且经由第二网络线路发送到第二网络订户，第二网络订户经由第二网络线路接收第一消息，借助于网络网关延迟第一消息，使得第一消息从第一网络订户到第二网络订户的第一通过时间基本上对应于第一消息通过分段网络的第二通过时间，其中分段网络至少包括第一分段耦合元件和第二分段耦合元件以及连接到第一分段耦合元件和第二分段耦合元件的第三网络线路。
",H04L12/40,撤回,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/40,"方法, 具有, 模拟, 包括",网络
EP2672660A1,EP12170769.9,"本方法包括操纵从协议数据单元中导出的协议数据单元(5)或附加协议数据单元(6) 。被操纵的协议数据单元和附加协议数据单元被提供给通信层用于处理，其中接收操纵层(9)操纵附加协议数据单元或从附加协议数据单元中导出的协议数据单元，并将获得的被操纵的协议数据单元或被操纵的附加协议数据单元提供给通信层用于处理。
",H04L12/26,授权,车载以太网板卡技术,2.0,IPC分类号匹配: H04L12/26,"处理, 提供, 方法, 包括",通信
JP2010173633A,JP2010015277,"要解决的问题：提供一种能够有利地调节控制装置。
SOLUTION：第一预定时间t1由调试接口TRDE当处理程序代码。触发时间与处理例程RU1由操作单元IN通过信息被发送到操作单元IN在第一时刻t1从调试接口TRDE 。第二值V1Y写入第一存储单元的第一存储器SP1在第二时间t2，通过调试界面TRDE IN从操作单元由处理例程RU1 。然后，第一存储单元的第一存储器SP1读出在第三时间由ECU t3控制。
COPYRIGHT：( C ) 2010、JPO&INPIT
",B60R16/02,授权,车载以太网板卡技术,2.0,IPC分类号匹配: B60R16/02,"装置, 提供, 处理, 控制",控制
