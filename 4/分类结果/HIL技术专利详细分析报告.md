# dSPACE HIL相关技术专利详细分析报告

## 概述

本报告对dSPACE公司的51项专利进行了技术分类分析，特别关注HIL（硬件在环）相关技术的专利布局情况。

## HIL相关技术专利总览

HIL相关技术专利总数: 35 条 (68.6%)

## HIL硬件在环仿真技术

**专利数量**: 7 条

**技术描述**: HIL硬件在环仿真相关技术，包括实时仿真、硬件模拟器等

**主要申请人**:
- 上海北汇信息科技有限公司: 7 条

**重点专利**:

### CN120454904A
- **申请号**: CN202510580989.6
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 硬件在环, HIL
- **技术手段**: 实现, 包括, 模型, 装置, 测试, 方法, 设备, 模拟
- **应用场景**: 通信, 测试
- **技术摘要**: 本申请公开了一种车联网硬件在环测试方法及装置，涉及车联网技术领域。该方法包括：基于设定的测试需求，构建对应的虚拟道路场景；发射多个射线，并使多个射线在对应的虚拟道路场景中传输，以得到虚拟道路场景对应的信道参数；根据信道参数，构建与虚拟道路场景对应的信道模型；基于虚拟道路场景、信道模型以及射频仪表，对车联网设备进行车联网硬件在环测试。在本申请中，构建与虚拟道路场景的信道模型，并将信道模型与V2X‑H...

### CN119414349A
- **申请号**: CN202411868431.X
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 硬件在环, 模拟器
- **技术手段**: 包括, 测试, 方法, 处理, 控制, 系统, 模拟
- **应用场景**: 雷达, 测试, 控制
- **技术摘要**: 本申请公开了一种毫米波雷达的硬件在环测试系统及方法，系统包括第一上位机、第二上位机、视频暗箱、硬件在环测试机柜、雷达回波模拟器系统和控制器，硬件在环测试机柜分别与第一上位机、第二上位机、雷达回波模拟器系统和控制器相连，视频暗箱与第一上位机相连，雷达回波模拟器系统分别与第二上位机和控制器相连。毫米波雷达的硬件在环测试系统用于获取测试场景，识别测试场景，得到场景信息，运行自动化测试软件，以获取雷达感知...

### CN120295932A
- **申请号**: CN202510444031.4
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: HIL
- **技术手段**: 提供, 包括, 测试, 方法, 控制, 系统
- **应用场景**: 测试, 控制
- **技术摘要**: 本申请提供了一种测试系统、车载控制器的冒烟测试方法，该方法可应用于测试管理系统，包括：判断车载控制器是否完成软件新版本的开发释放；若是，则创建车载控制器的软件刷写任务，并根据软件刷写任务对车载控制器进行软件刷写；在车载控制器完成软件刷写之后，确定车载控制器的冒烟测试任务，并根据冒烟测试任务对车载控制器进行冒烟测试，能够利用测试管理系统对车载控制器自动进行冒烟测试，解决了现有冒烟测试需要在实车或者传...

### CN119620738A
- **申请号**: CN202411857426.9
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 模拟器
- **技术手段**: 提供, 测试, 仿真, 方法, 控制, 系统, 检测, 模拟
- **应用场景**: 驾驶, 测试, 控制, 车辆, 检测
- **技术摘要**: 本申请提供一种基于驾驶员在环的V2X功能测试方法及系统，当检测到仿真测试开始时，通过上位机运行测试执行及管理软件调用场景仿真软件，在场景显示大屏上显示由测试场景构成的场景界面；通过测试执行及管理软件响应真实驾驶员根据仿真交通环境操作驾驶模拟器发出的控制指令，向场景仿真软件发送控制信号，场景仿真软件基于控制信号控制仿真测试车辆行驶；在行驶的过程中，通过测试执行及管理软件将仿真测试车辆的实时位置信息和...

### CN117950980A
- **申请号**: CN202211291641.8
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 模拟器
- **技术手段**: 模拟, 测试, 装置, 方法, 工具, 配置
- **应用场景**: 测试
- **技术摘要**: 本申请公开了一种测试车载嵌入式软件的方法及装置。根据不同的芯片架构和不同的编译工具链对模拟器进行配置，利用模拟器代替实际目标板进行测试。本申请所述方法减小了白盒测试的单元测试阶段对硬件的集成度、降低了成本，并且通过模拟器进行测试时无需切换目标版和清理冗余资源，大大提高了测试效率。


---

## 模拟数字转换板卡技术

**专利数量**: 1 条

**技术描述**: 模拟数字转换相关技术，包括ADC、DAC等转换器技术

**主要申请人**:
- 上海北汇信息科技有限公司: 1 条

**重点专利**:

### CN210518375U
- **申请号**: CN202020023371.2
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 信号转换
- **技术手段**: 实现, 包括, 测试, 仿真, 设置, 设备, 控制, 系统, 检测, 配置
- **应用场景**: 检测, 汽车, 测试, 控制
- **技术摘要**: 本申请公开了一种汽车总线的自动化检测系统，包括至少一个远端工控机、外部交换机、服务器和至少一个测试机柜，所述测试机柜内设置有本地工控机、内部交换机、程控示波器、程控万用表、故障注入板、总线接口卡、电源仿真模块、信号转换模块、环境配置板卡、设备路由板卡和IO板卡。在进行测试时，测试人员在远端工控机上进行登录，并将测试工程上传到服务器，监控测试执行状态与结果。测试机柜使用设备路由板卡控制程控示波器、程...

---

## 故障注入板卡技术

**专利数量**: 8 条

**技术描述**: 故障注入相关技术，用于测试系统的故障处理能力

**主要申请人**:
- 上海北汇信息科技有限公司: 8 条

**重点专利**:

### CN120491511A
- **申请号**: CN202510573286.0
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 故障注入
- **技术手段**: 模拟, 包括, 测试, 具有, 仿真, 方法, 生成, 控制, 检测, 电路, 配置
- **应用场景**: 检测, 测试, 控制
- **技术摘要**: 本申请公开了一种爆炸熔丝仿真测试方法，涉及熔丝仿真的技术领域，其包括构建包含四态开关组的仿真电路，其中S1和S4构成主回路常闭通道，S2和S3分别作为过压和过流故障注入通道；获取真实熔丝的阻抗特性，建立等效阻抗特性；配置动态参数控制模块，动态参数控制模块实时接收上位机下发的电压和/或电流触发阈值以及故障模式参数；在检测到主回路参数达到预先设定的触发阈值时，触发S4断开形成模拟熔断状态；生成具有时序...

### CN119224391A
- **申请号**: CN202411464500.0
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 故障注入
- **技术手段**: 提供, 测试, 包括, 装置, 控制
- **应用场景**: 测试, 控制
- **技术摘要**: 本申请提供了一种测试辅助装置，包括：控制单元和模式切换单元；模式切换单元与控制单元相连，模式切换单元的运行模式切换受控于控制单元，模式切换单元的运行模式包括：正向模式、反向模式、开路模式和正负极短接模式中的至少一种，能够在执行自动化测试过程中辅助程控电源完成极性反转功能和故障注入等功能，解决了市场上大部分程控电源厂家的电源极性翻转功能为选配功能需要特别定制，并且程控电源无法支持开路、短路等故障功能...

### CN119127774A
- **申请号**: CN202411436168.7
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 故障注入
- **技术手段**: 实现, 测试, 装置, 方法, 设置, 设备, 控制
- **应用场景**: 测试, 控制
- **技术摘要**: 本申请公开了一种基于RS232串口通讯的设备的故障注入方法及相关装置，涉及故障注入技术领域，在基于RS232串口通讯的上位机与被控设备间的通讯线上设置了一个故障注入模块，该故障注入模块连接上位机上的第一测试节点和被控设备上的第二测试节点，故障注入模块接收上位机发送的故障注入请求，解析故障注入请求并按照解析得到对应的故障注入方式完成故障注入。本申请中采用外接的故障注入模块即可通过上位机自行控制实现数...

### CN118449624A
- **申请号**: CN202310068911.7
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 故障注入
- **技术手段**: 提供, 测试, 仿真, 方法, 生成, 控制, 系统
- **应用场景**: 测试, 控制
- **技术摘要**: 本申请公开了一种自动化测试方法及系统，当服务器接收到测试任务时，将控制指令发送至客户端工控机，触发客户端工控机获取测试台架上的被测对象的测试数据，测试台架为提供测试所需的车内仿真环境、人机交互环境、故障注入环境和测试接口的测试台架，通过客户端工控机以节点仿真方式对测试数据进行测试，生成测试结果，测试结果表征逆向测试和部件测试的测试结果，服务器接收客户端工控机发送的测试结果进行分析，得到分析结果并展...

### CN220650781U
- **申请号**: CN202321425632.3
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 故障注入
- **技术手段**: 提供, 实现, 包括, 测试, 装置, 仿真, 设备
- **应用场景**: 测试, 汽车
- **技术摘要**: 本申请提供了一种电动汽车的高压回路故障注入仿真装置，包括：继电器故障仿真单元、第一绝缘电阻仿真单元、第二绝缘电阻仿真单元以及电容仿真单元；其中，继电器故障仿真单元分别与第一绝缘电阻仿真单元、第二绝缘电阻仿真单元以及电容仿真单元连接，第一绝缘电阻仿真单元、电容仿真单元以及继电器故障仿真单元共用第一高压源和被测设备，能够覆盖电动汽车的直流充电、交流充电、电池包以及电机的高压回路故障注入，解决了现有高压...

---

## 车载以太网板卡技术

**专利数量**: 7 条

**技术描述**: 车载以太网通信技术，包括车载网络协议和通信接口

**主要申请人**:
- 上海北汇信息科技有限公司: 7 条

**重点专利**:

### CN119629046A
- **申请号**: CN202411740636.X
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 车载以太网, 以太网
- **技术手段**: 包括, 测试, 具有, 方法, 设置, 生成, 系统
- **应用场景**: 通信, 测试, 网络
- **技术摘要**: 本申请公开了一种车载以太网集中式数据管理系统及方法，涉及基于时间敏感网络的车载以太网通信的技术领域，其包括ECU识别模块：用于提取ECU单元；当前参数分析模块：用于根据ECU单元，得到每个基础参数的提及率以及重要程度，得到第一参数概率；历史参数分析模块：用于提取历史参数的查询率和修改率，得到第二参数概率；参数筛选模块：用于根据第一参数概率以及第二参数概率，得到目标基础参数；快捷入口模块：用于根据参...

### CN119561883A
- **申请号**: CN202411740639.3
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 车载以太网, 以太网
- **技术手段**: 包括, 具有, 仿真, 生成, 方法, 设置, 检测, 计算
- **应用场景**: 检测, 通信, 网络
- **技术摘要**: 本申请公开了基于TSN的车载以太网时间敏感流量规划方法，包括基于用户场景和数据流信息创建网络拓扑图；基于数据流信息分析各数据流的优先级得到数据流优先级结果，基于数据流优先级结果和数据流信息确定数据流的转发路径得到转发路径匹配信息，确定各数据流的转发表条目信息得到转发表条目数据集，基于转发路径匹配信息和转发表条目数据对各数据流的门控列表设置得到门控列表生成信息；重复以上步骤以获取多个数据流样本进行仿...

### CN119583390A
- **申请号**: CN202411839637.X
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: CAN
- **技术手段**: 提供, 包括, 测试, 方法, 生成, 设备, 系统, 工具
- **应用场景**: 测试, 验证
- **技术摘要**: 本发明提供一种CAN同步测试方法和系统、电子设备、存储介质，该方法包括：获取CAN同步系统中主时钟节点发送至从时钟节点的时间同步报文；对时间同步报文按照报文标准规范进行格式检查，得到格式检查结果；对主时钟节点和从时钟节点进行同步精度验证，得到精度验证结果；将格式检查结果和精度验证结果，作为同步测试结果；也即，本申请基于CAN时间同步进制开发了自动化的测试工程，显著降低了测试难度，大大缩短了测试的时...

### CN119483826A
- **申请号**: CN202411586488.0
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: CAN
- **技术手段**: 实现, 包括, 方法, 设置, 配置
- **应用场景**: 通用
- **技术摘要**: 本发明公开了一种实现CAN报文小周期发送的优化方法，包括：为CAN报文中的每一帧报文分别设置对应的配置报文；其中，CAN报文中的每一帧报文对应的配置报文的数据场的值不同；每次进行CAN报文发送时，根据输入的配置报文的数据场的值判断CAN报文中的每一帧的报文是否发送；其中，如果输入的配置报文的数据场的值与CAN报文中的某一帧报文对应的配置报文的数据场的值相同，则按照设定周期发送该帧的报文；否则，不发...

### CN117007054A
- **申请号**: CN202311036056.8
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: CAN
- **技术手段**: 提供, 测试, 装置, 方法, 处理, 系统, 计算
- **应用场景**: 车辆, 测试
- **技术摘要**: 本申请公开了一种CANoe工程中车辆当前位置的获取方法及相关装置。在执行本申请实施例提供的方法时，首先可以利获取CANoe工程中目标车辆的测量点变量，并在测量点变量为第一预设值时，记录当前坐标值作为第一坐标值，同时获取CANoe工程的第二坐标值。对第一坐标值和第二坐标值进行预处理得到第一目标经度和第二目标经度。再将第一目标经度和第二目标经度赋值到相应的偏移系统进行偏移计算得到偏移量，并获取目标车辆...

---

## 数据注入类型支持技术

**专利数量**: 2 条

**技术描述**: 不同数据注入类型的支持技术，包括数据格式转换和处理

**主要申请人**:
- 上海北汇信息科技有限公司: 2 条

**重点专利**:

### CN222271813U
- **申请号**: CN202421220865.4
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 数据传输
- **技术手段**: 提供, 包括, 设备
- **应用场景**: 车辆, 数据传输
- **技术摘要**: 本实用新型提供了一种充电数据采集设备，包括：数据采集单元、数据传输单元、兼容插座接口、枪线插座和多类充电线路；充电线路的一端与兼容插座接口相连接，另一端与枪线插座相连接；兼容插座接口，用于在需要通过目标充电桩为目标车辆充电时，连接目标充电线路对应的充电插座，以连接目标充电桩的充电枪；枪线插座，用于连接目标车辆对应的充电枪线，以连接目标车辆的充电插座；数据采集单元，用于采集充电过程中的充电数据；数据...

### CN116933541A
- **申请号**: CN202310920777.9
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 数据处理
- **技术手段**: 提供, 实现, 装置, 方法, 生成, 处理, 设备, 工具, 模拟
- **应用场景**: 驾驶
- **技术摘要**: 本发明提供一种数据处理方法及装置，执行预先保存在exe文件中的读取指令，从模拟驾驶的游戏设备读取USB格式的第二驾驶数据并转换为符合用户数据报协议的第一驾驶数据，向接收端发送第一驾驶数据，接收端对第一驾驶数据进行拆解，得到各个子设备对应的子设备参数，针对每一子设备参数，将子设备参数与CarMaker工具中相应的变量关联并生成模拟驾驶场景，在本方案中，利用发送端预先保存在exe文件中的读取指令，读取...

---

## 集群化测试技术

**专利数量**: 8 条

**技术描述**: 集群化测试技术，用于大规模并行测试和验证

**主要申请人**:
- 上海北汇信息科技有限公司: 8 条

**重点专利**:

### CN117630553A
- **申请号**: CN202311678594.7
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 并行测试, 测试系统
- **技术手段**: 提供, 模拟, 实现, 包括, 测试, 仿真, 系统, 配置
- **应用场景**: 通信, 测试, 传感器
- **技术摘要**: 本申请提供一种多个储能BMS并行测试系统，其中，所述测试系统包括：电源模块，用于给多个储能BMS提供各自所需的电源；可编程电源测试仪件模块，用于给各所述储能BMS配置所需的模拟传感器信号；通信模块，用于为各所述储能BMS配置所需的通信接口，以使所述测试系统与各所述储能BMS的信息交互；电池组仿真模块，用于为各所述储能BMS模拟出所需的电池组状态；测试模块，用于加载各所述储能BMS对应的测试用例并同...

### CN119472417A
- **申请号**: CN202411587419.1
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 测试系统
- **技术手段**: 实现, 包括, 测试, 方法, 控制, 系统, 检测
- **应用场景**: 检测, 测试, 控制
- **技术摘要**: 本发明公开了一种多测试系统联调急停控制系统，包括：多个电源开关和多组测试模块，用于控制多台测试系统的通断电；多组测试模块均包括检测回路模块和急停控制模块，检测回路模块包括第一信号发出端、第一信号接收端、第二信号发出端和第二信号接收端，急停控制模块包括第一控制开关和第二控制开关；第一信号发出端可选择的与第一信号接收端连接或断开，第一控制开关的一端与第一信号接收端相连接，另一端与其他测试模块的第一信号...

### CN119413468A
- **申请号**: CN202411536643.8
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 测试系统
- **技术手段**: 提供, 测试, 包括, 处理, 控制, 系统, 模拟
- **应用场景**: 测试, 汽车, 控制
- **技术摘要**: 本申请提供了一种新能源电动汽车及其台架测试系统，该系统包括上位机、高压模拟单元、低压模拟单元、原车BMS控制器和原车负载；高压模拟单元分别与上位机、原车BMS控制器和原车负载相连；低压模拟单元分别与上位机和原车BMS控制器相连；高压模拟单元和低压模拟单元受控于上位机，上位机用于控制高压模拟单元对新能源电动汽车的原车电池包的高压部分进行模拟和低压模拟单元对原车电池包的低压部分进行模拟，可以利用上位机...

### CN118012771A
- **申请号**: CN202410230520.5
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 测试平台
- **技术手段**: 提供, 平台, 实现, 包括, 模型, 测试, 算法, 仿真, 方法, 生成, 控制, 系统, 电路, 配置
- **应用场景**: 测试, 控制
- **技术摘要**: 本申请涉及嵌入式软件测试的领域，提供一种基于多测试平台的嵌入式软件测试方法和系统，方法包括：基于预配置的激励参数生成第一测试用例数据和第二测试用例数据，并在仿真软件环境下根据第一测试用例数据和第二测试用例对目标软件进行测试以得到目标软件的期望运行结果；在仿真硬件环境下，将目标软件烧录至目标板，将第一测试用例数据和第二测试用例分别输入至调试电路和板卡硬件电路以生成第一激励信号和第二激励信号且发送至目...

### CN116661425A
- **申请号**: CN202310764184.8
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 测试系统
- **技术手段**: 测试, 模型, 仿真, 方法, 设备, 控制, 系统
- **应用场景**: 通信, 测试, 控制
- **技术摘要**: 本申请公开了一种目标控制器的联合测试方法及相关设备，首先获取基于目标控制器的测试系统发出的联合测试请求，然后根据联合测试请求，确定与目标控制器所对应的联合测试控制器，并根据联合测试请求和联合测试控制器，确定与联合测试请求所对应的预设测试模型。然后通过预设测试模型，建立目标控制器的测试系统与所述测试控制器的测试系统之间的通信连接，最后基于目标控制器的测试系统与测试控制器的测试系统之间的通信连接以及所...

---

## 虚拟仿真软件技术

**专利数量**: 2 条

**技术描述**: 虚拟仿真软件相关技术，包括仿真建模、虚拟环境等

**主要申请人**:
- 上海北汇信息科技有限公司: 2 条

**重点专利**:

### CN119336000A
- **申请号**: CN202411449545.0
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 仿真软件
- **技术手段**: 包括, 测试, 仿真, 方法, 生成, 处理, 设备, 控制, 系统
- **应用场景**: 驾驶, 测试, 摄像, 控制
- **技术摘要**: 本申请公开了一种控制器测试方法及相关系统，所述方法包括：针对外围设备中的每个目标组件，通过仿真软件生成目标组件对应的仿真数据；其中，目标组件包括融合组件、前视摄像头组件及定位组件；融合组件及前视摄像头组件对应的仿真数据由外围设备中的场景仿真软件生成；定位组件对应的仿真数据通过动力学仿真软件生成；融合组件对应的仿真数据包括场景目标信息等；前视摄像头组件仿真数据包括车道线集信息等；定位组件对应的仿真数...

### CN117312130A
- **申请号**: CN202311060721.7
- **申请人**: 上海北汇信息科技有限公司
- **分类理由**: 关键词匹配: 仿真软件
- **技术手段**: 提供, 实现, 包括, 测试, 仿真, 方法, 生成, 控制, 系统, 电路, 配置
- **应用场景**: 测试, 控制
- **技术摘要**: 本申请涉及嵌入式软件测试的领域，提供一种嵌入式软件测试方法和系统，其方法包括基于预配置的激励参数生成测试用例数据，并在仿真软件环境下根据所述测试用例数据对目标软件进行测试，以得到所述目标软件的第一运行结果；在仿真硬件环境下，将所述目标软件烧录至目标板，并将所述测试用例数据输入至调试电路以生成激励信号；将所述激励信号发送至所述目标板，以得到所述目标软件的第二运行结果；比较所述第一运行结果和所述第二运...

---

## 技术发展趋势分析

### 核心技术布局

1. **HIL硬件在环仿真技术** (62条, 14.4%): dSPACE在HIL技术方面拥有最多专利，体现了其在硬件在环仿真领域的技术领先地位。

2. **故障注入板卡技术** (50条, 11.6%): 故障注入技术是HIL测试的重要组成部分，用于验证系统的故障处理能力。

3. **数据注入类型支持技术** (54条, 12.5%): 支持多种数据类型的注入，提高了测试系统的灵活性和适用性。

### 技术特点

- **实时性**: 大量专利涉及实时仿真和实时数据处理
- **模块化**: 采用模块化设计，支持不同类型的板卡和接口
- **标准化**: 支持多种工业标准和通信协议
- **智能化**: 集成机器学习和人工智能技术

### 应用领域

- **汽车电子**: 车载网络、自动驾驶、电动汽车控制系统测试
- **航空航天**: 飞行控制系统、导航系统测试
- **工业自动化**: 工业控制系统、机器人控制测试
- **新能源**: 电池管理系统、电力电子设备测试

