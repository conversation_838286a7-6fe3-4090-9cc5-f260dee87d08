﻿公开(公告)号,申请号,摘要(中文),原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN119336000A,CN202411449545.0,"本申请公开了一种控制器测试方法及相关系统，所述方法包括：针对外围设备中的每个目标组件，通过仿真软件生成目标组件对应的仿真数据；其中，目标组件包括融合组件、前视摄像头组件及定位组件；融合组件及前视摄像头组件对应的仿真数据由外围设备中的场景仿真软件生成；定位组件对应的仿真数据通过动力学仿真软件生成；融合组件对应的仿真数据包括场景目标信息等；前视摄像头组件仿真数据包括车道线集信息等；定位组件对应的仿真数据包括经纬度信息等；将仿真数据发送至目标组件；将仿真数据为Proto数据库信号进行映射赋值，并对赋值后的Proto数据库信号进行序列化处理，最后将序列化数据发送给高级驾驶辅助控制单元中的规控模块。
",上海北汇信息科技有限公司,虚拟仿真软件技术,1.0,关键词匹配: 仿真软件,"包括, 测试, 仿真, 方法, 生成, 处理, 设备, 控制, 系统","驾驶, 测试, 摄像, 控制"
CN117312130A,CN202311060721.7,"本申请涉及嵌入式软件测试的领域，提供一种嵌入式软件测试方法和系统，其方法包括基于预配置的激励参数生成测试用例数据，并在仿真软件环境下根据所述测试用例数据对目标软件进行测试，以得到所述目标软件的第一运行结果；在仿真硬件环境下，将所述目标软件烧录至目标板，并将所述测试用例数据输入至调试电路以生成激励信号；将所述激励信号发送至所述目标板，以得到所述目标软件的第二运行结果；比较所述第一运行结果和所述第二运行结果，并基于比较结果确认所述目标软件是否符合预期要求。本申请给入多种复杂激励参数，通过第一运行结果和第二运行结果的比较，实现控制器在环测试阶段输出的自动化评估以得到测试结果和测试报告。
",上海北汇信息科技有限公司,虚拟仿真软件技术,1.0,关键词匹配: 仿真软件,"提供, 实现, 包括, 测试, 仿真, 方法, 生成, 控制, 系统, 电路, 配置","测试, 控制"
