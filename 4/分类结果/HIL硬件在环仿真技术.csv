﻿公开(公告)号,申请号,摘要(中文),原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN120454904A,CN202510580989.6,"本申请公开了一种车联网硬件在环测试方法及装置，涉及车联网技术领域。该方法包括：基于设定的测试需求，构建对应的虚拟道路场景；发射多个射线，并使多个射线在对应的虚拟道路场景中传输，以得到虚拟道路场景对应的信道参数；根据信道参数，构建与虚拟道路场景对应的信道模型；基于虚拟道路场景、信道模型以及射频仪表，对车联网设备进行车联网硬件在环测试。在本申请中，构建与虚拟道路场景的信道模型，并将信道模型与V2X‑HIL相结合，使得在实验室环境下能够更准确地模拟真实世界的通信环境，从而在实验室环境中实现了对车联网硬件在真实环境下的测试，提高对车联网设备的测试的准确性，从而提高车联网设备的安全性。
",上海北汇信息科技有限公司,HIL硬件在环仿真技术,2.0,"关键词匹配: 硬件在环, HIL","实现, 包括, 模型, 装置, 测试, 方法, 设备, 模拟","通信, 测试"
CN120295932A,CN202510444031.4,"本申请提供了一种测试系统、车载控制器的冒烟测试方法，该方法可应用于测试管理系统，包括：判断车载控制器是否完成软件新版本的开发释放；若是，则创建车载控制器的软件刷写任务，并根据软件刷写任务对车载控制器进行软件刷写；在车载控制器完成软件刷写之后，确定车载控制器的冒烟测试任务，并根据冒烟测试任务对车载控制器进行冒烟测试，能够利用测试管理系统对车载控制器自动进行冒烟测试，解决了现有冒烟测试需要在实车或者传统HiL台架对软件进行，需要安排专门人员部署冒烟测试环境，制定冒烟测试计划，不仅需要投入更多的测试人员，同时频繁的测试环境切换也带来时间成本的增加以及测试计划混乱的问题。
",上海北汇信息科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: HIL,"提供, 包括, 测试, 方法, 控制, 系统","测试, 控制"
CN119620738A,CN202411857426.9,"本申请提供一种基于驾驶员在环的V2X功能测试方法及系统，当检测到仿真测试开始时，通过上位机运行测试执行及管理软件调用场景仿真软件，在场景显示大屏上显示由测试场景构成的场景界面；通过测试执行及管理软件响应真实驾驶员根据仿真交通环境操作驾驶模拟器发出的控制指令，向场景仿真软件发送控制信号，场景仿真软件基于控制信号控制仿真测试车辆行驶；在行驶的过程中，通过测试执行及管理软件将仿真测试车辆的实时位置信息和每个仿真物体的实时位置信息发送给被测件；通过被测件根据仿真测试车辆和每个仿真物体的实时位置测试信息，判断仿真测试车辆是否满足V2X预警功能；若仿真测试车辆满足V2X预警功能，触发相应的V2X预警功能。
",上海北汇信息科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"提供, 测试, 仿真, 方法, 控制, 系统, 检测, 模拟","驾驶, 测试, 控制, 车辆, 检测"
CN119414349A,CN202411868431.X,"本申请公开了一种毫米波雷达的硬件在环测试系统及方法，系统包括第一上位机、第二上位机、视频暗箱、硬件在环测试机柜、雷达回波模拟器系统和控制器，硬件在环测试机柜分别与第一上位机、第二上位机、雷达回波模拟器系统和控制器相连，视频暗箱与第一上位机相连，雷达回波模拟器系统分别与第二上位机和控制器相连。毫米波雷达的硬件在环测试系统用于获取测试场景，识别测试场景，得到场景信息，运行自动化测试软件，以获取雷达感知数据，将雷达感知数据进行逻辑处理，得到处理后的雷达感知数据，对处理后的雷达感知数据进行转换，得到回波数据，将场景信息和回波数据进行感知融合，得到目标数据，以完成毫米波雷达的硬件在环测试的过程。
",上海北汇信息科技有限公司,HIL硬件在环仿真技术,2.0,"关键词匹配: 硬件在环, 模拟器","包括, 测试, 方法, 处理, 控制, 系统, 模拟","雷达, 测试, 控制"
CN117950980A,CN202211291641.8,"本申请公开了一种测试车载嵌入式软件的方法及装置。根据不同的芯片架构和不同的编译工具链对模拟器进行配置，利用模拟器代替实际目标板进行测试。本申请所述方法减小了白盒测试的单元测试阶段对硬件的集成度、降低了成本，并且通过模拟器进行测试时无需切换目标版和清理冗余资源，大大提高了测试效率。
",上海北汇信息科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: 模拟器,"模拟, 测试, 装置, 方法, 工具, 配置",测试
CN117676659A,CN202311673293.5,"一种ECU的在环测试方法、系统、电子设备及介质，涉及汽车技术领域。该方法包括：获取待测试车辆的车辆参数以及对应的交通环境信息；根据所述交通环境信息和所述车辆参数，构建对应的HIL测试环境；确定所述待测试车辆中待测ECU的通信网络参数，根据所述通信网络参数和所述HIL测试环境，生成测试信号；将所述测试信号输入所述待测ECU，得到所述待测ECU基于所述测试信号反馈的测试结果。达到了提高测试效率的效果。
",上海北汇信息科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: HIL,"包括, 测试, 方法, 生成, 设备, 系统","测试, 汽车, 车辆, 通信, 网络"
CN218512576U,CN202222850254.5,"本申请公开了一种HIL台架联合测试系统，包括至少一台上位机和多个机柜，每个机柜内设置有一套HIL台架测试装置，每个HIL台架测试装置包括交换机、工控机和VT System板卡，交换机分别与工控机、VT System板卡连接；上位机用于与任一交换机连接，用于基于用户的调试请求向交换机输出调试控制指令，以使工控机和VT System板卡在调试控制指令的控制下实现调试操作。这样一来，在对单个HIL台架测试装置进行调试时就不会对其他装置产生影响，从而避免了在对HIL台架测试装置在调试时产生冲突。
",上海北汇信息科技有限公司,HIL硬件在环仿真技术,1.0,关键词匹配: HIL,"实现, 包括, 测试, 装置, 设置, 控制, 系统","测试, 控制"
