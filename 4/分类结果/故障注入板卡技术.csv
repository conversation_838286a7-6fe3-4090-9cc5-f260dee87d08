﻿公开(公告)号,申请号,摘要(中文),原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN120491511A,CN202510573286.0,"本申请公开了一种爆炸熔丝仿真测试方法，涉及熔丝仿真的技术领域，其包括构建包含四态开关组的仿真电路，其中S1和S4构成主回路常闭通道，S2和S3分别作为过压和过流故障注入通道；获取真实熔丝的阻抗特性，建立等效阻抗特性；配置动态参数控制模块，动态参数控制模块实时接收上位机下发的电压和/或电流触发阈值以及故障模式参数；在检测到主回路参数达到预先设定的触发阈值时，触发S4断开形成模拟熔断状态；生成具有时序特征的故障波形；将仿真电路的状态参数与BMS控制信号进行闭环反馈，形成包含三种模式的测试矩阵。本申请具有提升熔丝仿真过程中的参数调整灵活性和全面性的效果。
",上海北汇信息科技有限公司,故障注入板卡技术,1.0,关键词匹配: 故障注入,"模拟, 包括, 测试, 具有, 仿真, 方法, 生成, 控制, 检测, 电路, 配置","检测, 测试, 控制"
CN119224391A,CN202411464500.0,"本申请提供了一种测试辅助装置，包括：控制单元和模式切换单元；模式切换单元与控制单元相连，模式切换单元的运行模式切换受控于控制单元，模式切换单元的运行模式包括：正向模式、反向模式、开路模式和正负极短接模式中的至少一种，能够在执行自动化测试过程中辅助程控电源完成极性反转功能和故障注入等功能，解决了市场上大部分程控电源厂家的电源极性翻转功能为选配功能需要特别定制，并且程控电源无法支持开路、短路等故障功能测试的问题。
",上海北汇信息科技有限公司,故障注入板卡技术,1.0,关键词匹配: 故障注入,"提供, 测试, 包括, 装置, 控制","测试, 控制"
CN119127774A,CN202411436168.7,"本申请公开了一种基于RS232串口通讯的设备的故障注入方法及相关装置，涉及故障注入技术领域，在基于RS232串口通讯的上位机与被控设备间的通讯线上设置了一个故障注入模块，该故障注入模块连接上位机上的第一测试节点和被控设备上的第二测试节点，故障注入模块接收上位机发送的故障注入请求，解析故障注入请求并按照解析得到对应的故障注入方式完成故障注入。本申请中采用外接的故障注入模块即可通过上位机自行控制实现数据故障注入或硬件故障注入，人工参与度低且操作简单。
",上海北汇信息科技有限公司,故障注入板卡技术,1.0,关键词匹配: 故障注入,"实现, 测试, 装置, 方法, 设置, 设备, 控制","测试, 控制"
CN118449624A,CN202310068911.7,"本申请公开了一种自动化测试方法及系统，当服务器接收到测试任务时，将控制指令发送至客户端工控机，触发客户端工控机获取测试台架上的被测对象的测试数据，测试台架为提供测试所需的车内仿真环境、人机交互环境、故障注入环境和测试接口的测试台架，通过客户端工控机以节点仿真方式对测试数据进行测试，生成测试结果，测试结果表征逆向测试和部件测试的测试结果，服务器接收客户端工控机发送的测试结果进行分析，得到分析结果并展示。
",上海北汇信息科技有限公司,故障注入板卡技术,1.0,关键词匹配: 故障注入,"提供, 测试, 仿真, 方法, 生成, 控制, 系统","测试, 控制"
CN220650781U,CN202321425632.3,"本申请提供了一种电动汽车的高压回路故障注入仿真装置，包括：继电器故障仿真单元、第一绝缘电阻仿真单元、第二绝缘电阻仿真单元以及电容仿真单元；其中，继电器故障仿真单元分别与第一绝缘电阻仿真单元、第二绝缘电阻仿真单元以及电容仿真单元连接，第一绝缘电阻仿真单元、电容仿真单元以及继电器故障仿真单元共用第一高压源和被测设备，能够覆盖电动汽车的直流充电、交流充电、电池包以及电机的高压回路故障注入，解决了现有高压回路故障注入仿真装置可以实现的故障注入类型较少，无法满足当前国标充电测试要求的问题。
",上海北汇信息科技有限公司,故障注入板卡技术,1.0,关键词匹配: 故障注入,"提供, 实现, 包括, 测试, 装置, 仿真, 设备","测试, 汽车"
CN116794433A,CN202310770273.3,"本申请提供了一种车载充电机故障注入装置、方法及电子设备，在本申请提供的车载充电机故障注入装置中，包括有通讯控制模块以及故障注入模块，具体的，故障注入模块包括有断路故障注入单元、短路故障注入单元、反接故障注入单元以及相序异常故障注入单元。通过上述本申请提供的车载充电机故障注入装置，在对车载充电机进行故障注入测试时，通过下发具体的故障注入指令，确定需要进行故障注入的故障类型，即可自动化实现对车载充电机的故障注入测试，不再需要通过人工手动接线的方式来进行故障注入测试，解决了通过手动接线来进行故障注入测试所带来的安全隐患的问题。
",上海北汇信息科技有限公司,故障注入板卡技术,1.0,关键词匹配: 故障注入,"提供, 实现, 包括, 测试, 装置, 方法, 设备, 控制","测试, 控制"
CN212276236U,CN202020682241.X,"本实用新型提供一种便携式实车网络自动化测试系统，包括：接口面板、示波器、LIN总线故障注入设备、CAN/CANFD总线故障注入设备、FlexRay总线故障注入设备；设备路由板卡，与所述接口面板、所述示波器、所述LIN总线故障注入设备、所述CAN/CANFD总线故障注入设备和所述FlexRay总线故障注入设备相连；设备网关，与所述LIN总线故障注入设备和所述设备路由板卡相连；测试工程独立运行设备，与所述接口面板、所述设备路由板卡、所述设备网关、所述示波器、所述CAN/CANFD总线故障注入设备和所述FlexRay总线故障注入设备相连。本实用新型的便携式实车网络自动化测试系统能够自动化实现实车CAN/CANFD/LIN/FlexRay总线网络的测试，测试速度快，效率高，实用性强。
",上海北汇信息科技有限公司,故障注入板卡技术,1.0,关键词匹配: 故障注入,"提供, 实现, 包括, 测试, 设备, 系统","测试, 网络"
CN207764648U,CN201820158782.5,"本实用新型公开一种程控接线板卡，用于自动配置故障注入模式，该程控接线板卡包括：电源模块、控制模块、驱动模块、状态指示模块、继电器模块，以及供电接口、板卡控制接口、被测接口和外设接口，其中：电源模块分别与控制模块、继电器模块、状态指示模块和供电接口相连；控制模块分别与状态指示模块、驱动模块和调试接口相连；驱动模块与继电器模块相连；继电器分别与被测接口和外设接口相连。本实用新型提供的程控接线板卡采用CAN收发器来获取CAN报文指令，通过控制模块操作各继电器的开关，实现自动配置故障注入模式的需求，操作简单，可靠性高，能够达到自动化测试的目的。
",上海北汇信息科技有限公司,故障注入板卡技术,1.0,关键词匹配: 故障注入,"提供, 实现, 包括, 测试, 控制, 配置","测试, 控制"
