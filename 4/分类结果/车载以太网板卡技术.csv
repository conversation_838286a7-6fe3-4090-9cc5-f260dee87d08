﻿公开(公告)号,申请号,摘要(中文),原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN119629046A,CN202411740636.X,"本申请公开了一种车载以太网集中式数据管理系统及方法，涉及基于时间敏感网络的车载以太网通信的技术领域，其包括ECU识别模块：用于提取ECU单元；当前参数分析模块：用于根据ECU单元，得到每个基础参数的提及率以及重要程度，得到第一参数概率；历史参数分析模块：用于提取历史参数的查询率和修改率，得到第二参数概率；参数筛选模块：用于根据第一参数概率以及第二参数概率，得到目标基础参数；快捷入口模块：用于根据参数条目得到网络条目，根据网络条目设置快捷入口；实体结构生成模块：用于生成分布式数据实体结构。本申请具有提升车载以太网在测试开发过程中参数查询和修改的效率和精确度的效果。
",上海北汇信息科技有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载以太网, 以太网","包括, 测试, 具有, 方法, 设置, 生成, 系统","通信, 测试, 网络"
CN119583390A,CN202411839637.X,"本发明提供一种CAN同步测试方法和系统、电子设备、存储介质，该方法包括：获取CAN同步系统中主时钟节点发送至从时钟节点的时间同步报文；对时间同步报文按照报文标准规范进行格式检查，得到格式检查结果；对主时钟节点和从时钟节点进行同步精度验证，得到精度验证结果；将格式检查结果和精度验证结果，作为同步测试结果；也即，本申请基于CAN时间同步进制开发了自动化的测试工程，显著降低了测试难度，大大缩短了测试的时间成本和人力成本，同时可以满足部件级、系统级测试，也能够在自动化测试系统以及普通供电电源条件的多种测试环境下灵活执行测试。利用代码生成工具，可以批量生成脚本，大幅度提高了自动化测试效率。
",上海北汇信息科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"提供, 包括, 测试, 方法, 生成, 设备, 系统, 工具","测试, 验证"
CN119561883A,CN202411740639.3,"本申请公开了基于TSN的车载以太网时间敏感流量规划方法，包括基于用户场景和数据流信息创建网络拓扑图；基于数据流信息分析各数据流的优先级得到数据流优先级结果，基于数据流优先级结果和数据流信息确定数据流的转发路径得到转发路径匹配信息，确定各数据流的转发表条目信息得到转发表条目数据集，基于转发路径匹配信息和转发表条目数据对各数据流的门控列表设置得到门控列表生成信息；重复以上步骤以获取多个数据流样本进行仿真，检测仿真中各数据流样本的有效仿真信息，基于有效仿真信息计算各数据流样本的适应度f(x)，基于数据流样本的适应度f(x)对数据流样本划分得到数据流样本划分结果。本申请具有提高车载以太网通信数据帧调度精确度的效果。
",上海北汇信息科技有限公司,车载以太网板卡技术,2.0,"关键词匹配: 车载以太网, 以太网","包括, 具有, 仿真, 生成, 方法, 设置, 检测, 计算","检测, 通信, 网络"
CN119483826A,CN202411586488.0,"本发明公开了一种实现CAN报文小周期发送的优化方法，包括：为CAN报文中的每一帧报文分别设置对应的配置报文；其中，CAN报文中的每一帧报文对应的配置报文的数据场的值不同；每次进行CAN报文发送时，根据输入的配置报文的数据场的值判断CAN报文中的每一帧的报文是否发送；其中，如果输入的配置报文的数据场的值与CAN报文中的某一帧报文对应的配置报文的数据场的值相同，则按照设定周期发送该帧的报文；否则，不发送该帧报文。
",上海北汇信息科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"实现, 包括, 方法, 设置, 配置",通用
CN117007054A,CN202311036056.8,"本申请公开了一种CANoe工程中车辆当前位置的获取方法及相关装置。在执行本申请实施例提供的方法时，首先可以利获取CANoe工程中目标车辆的测量点变量，并在测量点变量为第一预设值时，记录当前坐标值作为第一坐标值，同时获取CANoe工程的第二坐标值。对第一坐标值和第二坐标值进行预处理得到第一目标经度和第二目标经度。再将第一目标经度和第二目标经度赋值到相应的偏移系统进行偏移计算得到偏移量，并获取目标车辆的历史停止经度。然后将历史停止经度和偏移量进行相加得到车辆当前经度。本申请联合目标车辆在CANoe工程中的第一坐标值和第二坐标值与历史停止经度进行计算从而得到车辆当前经度，提高了V2X测试系统的测试效率。
",上海北汇信息科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"提供, 测试, 装置, 方法, 处理, 系统, 计算","车辆, 测试"
CN208335011U,CN201821167704.8,"本申请公开了一种测试环境配置板卡，该配置板卡应用于汽车的电子控制器的测试系统，包括主板和子板。所述主板上设置有电源电路、控制电路、驱动电路、状态指示电路、母板配置电路、供电接口、板卡控制接口、被测接口、外设接口和调试接口，所述子板上设置有环境子板电路。本申请的控制模块通过获取CAN报文指令，来控制各电子开关的开断状态，实现对测试环境的自动配置，操作简单，实现了提高测试效率的目的。
",上海北汇信息科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"实现, 包括, 测试, 设置, 控制, 系统, 电路, 配置","测试, 汽车, 控制"
CN207764647U,CN201820154726.4,"本实用新型公开一种设备路由板卡，用于控制电子控制器多路通讯线束与外设设备线束连接关系切换，该设备路由板卡包括：电源模块、控制模块、驱动模块、状态指示模块、继电器模块，以及供电接口、板卡控制接口、被测接口和外设接口，其中：电源模块分别与控制模块、继电器模块、状态指示模块和供电接口相连；控制模块分别与状态指示模块、驱动模块和板卡控制接口相连；驱动模块与继电器模块相连；继电器模块分别与被测接口和外设接口相连。本实用新型提供的设备路由板卡采用CAN收发器来获取CAN报文指令，通过控制模块控制各继电器的开关，实现控制电子控制器多路通讯线束与外设设备线束连接关系切换，操作简单，可靠性高，可实现自动化测试。
",上海北汇信息科技有限公司,车载以太网板卡技术,1.0,关键词匹配: CAN,"提供, 实现, 包括, 测试, 设备, 控制","测试, 控制"
