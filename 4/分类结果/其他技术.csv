﻿公开(公告)号,申请号,摘要(中文),原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN120377427A,CN202510516805.X,"本申请公开了一种高压回路切换系统，涉及高压回路的技术领域，其包括超级电容模块：用于获取电动车的动力系统的电路回路，在电路回路进入动力系统的主回路上建立超级电容模组；电源选择模块：用于进行实时监控，对动力系统的电力来源进行选择；电源切换模块：用于获取当前测试场景，切换动力系统的电力来源；负载调整模块：用于根据实时负载需求，动态调整输出功率；电池充电模块：用于根据实时负载需求，对电池包以及超级电容模组进行充电；异常处理模块：用于获取电动车测试过程中的状态参数，根据异常情况进行异常调整。本申请具有降低电源切换过程中的人力资源的消耗，提升测试效率的效果。
",上海北汇信息科技有限公司,其他技术,0.0,未匹配到特定技术领域,"测试, 包括, 具有, 处理, 系统, 电路",测试
CN120142821A,CN202510516806.4,"本申请公开了一种电动车充电接口温度保护功能测试方法，涉及电动车充电的技术领域，其包括获取电动车充电接口组件的物理结构，得到电流走向以及接口贴合量；获取电动车在充电的过程中的电流大小，得到理论发热量；获取电动车充电接口组件的温度保护功能的触发温度值，对充电接口组件进行加热处理；分别采集加热之前、加热过程以及加热完成后的充电接口的性能数据，得到性能曲线；对充电接口组件进行多次加热，采集每次加热完成后的功能反应数据，得到功能反应曲线；将性能曲线以及功能反应曲线进行曲线拟合，得到功能测试结果。本申请具有提升在升温时对温度控制的精准性，降低测试过程中的繁琐程度的效果。
",上海北汇信息科技有限公司,其他技术,0.0,未匹配到特定技术领域,"包括, 测试, 具有, 方法, 处理, 控制","测试, 控制"
CN119787600A,CN202510032359.5,"本发明提供一种直流高压液冷回路切换系统及其控制方法，该系统包括：开关切换装置的输入端正负极作为高压切换箱的输入端正负极，通过高压线束连接直流源；开关切换装置的第一输出端与第一输出接口连接；开关切换装置的第二输出端与第二输出接口连接；开关切换装置处于第一状态时，高压切换箱采用第一输出接口，输出第一输出接口对应的电流；开关切换装置处于第二状态时，高压切换箱采用第二输出接口，输出第二输出接口对应的电流，且采用液冷单元对第二输出接口对应的线缆冷处理；在不改变的线缆直径的基础上，提高直流高压液冷回路切换系统支持的电流范围，提高直流高压液冷回路切换系统的适用性，降低成本和空间布局方便。
",上海北汇信息科技有限公司,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 装置, 方法, 处理, 控制, 系统",控制
CN222713317U,CN202420349569.8,"本申请公开了一种充放电数据采集设备，包括：供电模块、数据采集模块。供电模块与数据采集模块相连。供电模块用于为数据采集模块供电。数据采集模块用于采集充放电数据，其中，充放电数据为符合欧标充电协议的电动车与充电桩之间进行充电和放电时的数据。本申请的数据采集模块能够准确地采集充放电数据，将这些数据记录下来，为后续分析和研究提供重要的依据。同时，通过供电模块为数据采集模块提供稳定的电力供应，确保数据采集模块能够正常运行和采集充放电数据，避免由于供电问题导致无法充电的故障发生。
",上海北汇信息科技有限公司,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 设备",通用
CN119375673A,CN202411558945.5,"本发明公开了一种电动车高压互锁回路测试设备及测试方法，所述测试设备包括：通讯模块，其连接上位机；控制模块，其与所述通讯模块连接；IN接口，其与待测试的高压互锁回路的输入端连接；OUT接口，其与待测试的高压互锁回路的输出端连接；可调电阻，其连接在所述IN接口和所述OUT接口之间，并且所述可调电阻与所述控制模块电联；信号、波形发生模块，其输入端与所述控制模块连接，输出端连接在所述可调电阻与所述OUT接口之间；第一开关，其连接在IN接口与所述可调电阻之间，所述第一开关为常闭开关；其中，所述第一开关与所述控制模块电联。
",上海北汇信息科技有限公司,其他技术,0.0,未匹配到特定技术领域,"包括, 测试, 方法, 设备, 控制","测试, 控制"
CN119269125A,CN202411398799.4,"本申请公开一种自动化实车测试的方法和装置，方法包括，获得目标测试用例，目标测试用例包括多个测试阶段，每一测试阶段包括多个实车操作步骤；输出当前测试阶段对应的影音信息，以提示被测车辆的驾驶员按当前测试阶段的实车操作步骤操作被测车辆，当前测试阶段为目标测试用例的任一测试阶段；根据被测车辆的报文信号评估当前测试阶段的测试结果；在当前测试阶段的测试结果满足跳转条件的情况下，将未获得测试结果的另一测试阶段确定为当前测试阶段，返回执行输出当前测试阶段对应的影音信息，直至获得目标测试用例中各个测试阶段的测试结果为止。
",上海北汇信息科技有限公司,其他技术,0.0,未匹配到特定技术领域,"方法, 包括, 装置, 测试","车辆, 驾驶, 测试"
CN119197534A,CN202411366716.3,"本申请公开了一种位置信息偏移方法及系统、电子设备、存储介质，所述方法，包括：获取上一场景的结束位置的位置信息及当前场景的起始位置的位置信息；其中，位置信息包括经度和纬度；基于结束位置的位置信息以及起始位置的位置信息，计算得到结束位置与起始位置之间的距离；按照预设最大速度以及预设加速度，计算出结束位置与起始位置之间的距离内，各个行驶阶段的行驶时间以及行驶距离；其中，行驶阶段包括加速阶段、匀速阶段以及减速阶段；将从结束位置至起始位置的时间划分为多个时间段；基于各个行驶阶段的行驶时间以及行驶距离，计算得到各个时间段对应的位置信息；利用各个时间段对应的位置信息，组成结束位置至起始位置的平滑过渡路径。
",上海北汇信息科技有限公司,其他技术,0.0,未匹配到特定技术领域,"包括, 方法, 设备, 系统, 计算",通用
CN118363359A,CN202311360738.4,"本发明涉及一种充电控制器测试方法、系统及电路。其中，方法包括：获取充电控制器的待测试项目；控制通信装置输出符合第一类型充电协议且与待测试项目对应的目标测试信号至待测试充电控制器；接收待测试充电控制器基于目标测试信号反馈的符合第二类型充电协议的测试反馈信号；将目标测试信号与测试反馈信号进行对比，获得待测试充电控制器的项目测试结果。本发明可以检验充电控制器是否满足设计要求。
",上海北汇信息科技有限公司,其他技术,0.0,未匹配到特定技术领域,"包括, 测试, 装置, 方法, 控制, 系统, 电路","通信, 测试, 控制"
CN118090232A,CN202410200445.8,"本申请实施例提供了一种车辆测试方法、装置、设备及存储介质。在执行所述方法时，通过测试管理模块获取需要进行测试的待测试项目后，向代理模块发送测试指令，由代理模块将测试指令转发给测试模块；测试模块接收到测试指令后，基于测试模块对测试指令进行解析，向仿真台架发送仿真指令后，仿真台架可以根据仿真指令进行仿真测试，得到最终的测试结果。通过根据测试指令中的待测试项目向仿真台架发送对应的仿真指令，进而控制仿真台架进行相关的控制，实现对车辆的自动化测试，无须工程师手动控制仿真台架，即可完成车辆测试。降低车辆测试难度，并提高测试效率。
",上海北汇信息科技有限公司,其他技术,0.0,未匹配到特定技术领域,"提供, 实现, 测试, 装置, 仿真, 方法, 设备, 控制","车辆, 测试, 控制"
CN117811970A,CN202311576264.7,"本申请公开了一种时间同步补偿时延测试方法、装置、设备及存储介质，所述方法包括：通过获取节点间链路平均时延及同步误差；在进行Sync消息同步后，将Sync消息的接收时间与发送时间的差值作为Sync时延测试值；根据节点间链路平均时延以及同步误差获取Sync时延真实值；对Sync时延测试值和Sync时延真实值和进行比较，得到测试结果。利用Sync消息同步机制的特点，得到Sync时延测试值，再基于理论研究，通过获取的节点间链路平均时延以及同步误差，得到理论中地Sync时延真实值，将测试值与真实值进行比较得到测试结果，以完成对时间同步补偿时延进行测试。
",上海北汇信息科技有限公司,其他技术,0.0,未匹配到特定技术领域,"包括, 测试, 装置, 方法, 设备",测试
CN117609091A,CN202311673294.X,"本申请涉及嵌入式软件测试的领域，提供一种提取动态测试中函数运行时间的方法和系统，其方法包括：基于预配置的激励参数生成测试用例数据，在仿真环境下，在所述测试用例数据中目标函数的始端和末端分别设置第一断点和第二断点生成断点位置信息；在仿真硬件环境下，根据断点位置信息在目标板中已烧录的目标软件的对应位置设置断点，并将测试用例数据输入至调试电路以生成激励信号；将激励信号发送至所述目标板，以生成对应断点的运行时间；根据运行时间与预设的上限值的比较结果，生成测试报告。本案针对每个工况对应的测试用例数据中目标函数均在测试运行时通过断点来进行记录，准确判定出目标函数的运行时间，无需工作人员持续进行观测。
",上海北汇信息科技有限公司,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 测试, 仿真, 方法, 设置, 生成, 系统, 电路, 配置",测试
CN116990571A,CN202310975335.4,"本发明提供一种电流仿真装置，涉及霍尔电流传感器技术领域，该电流仿真装置包括：电流传感器和四个开关；其中，电流传感器为输出单向信号的霍尔电流传感器；电流传感器的输出端正极，分别连接第一开关的一端和第二开关的一端；电流传感器的输出端负极，分别连接第三开关的一端和第四开关的一端；第一开关的另一端和第三开关的另一端相连，第二开关的另一端和第四开关的另一端相连，两个连接点分别作为电流仿真装置的输出端两端口。本发明能够降低将单向信号输出的霍尔电流传感器修改为双向信号输出的难度。
",上海北汇信息科技有限公司,其他技术,0.0,未匹配到特定技术领域,"仿真, 提供, 包括, 装置",传感器
CN219778215U,CN202321040618.1,"本实用新型提供一种串行接口复用装置及串行接口复用上位机系统，该串行接口复用装置，将N个开关单元的一侧，并联至串行接口复用装置的汇总接口，以连接上位机的通讯接口；并将各开关单元的另一侧，分别与串行接口复用装置中对应的分接接口相连接，以连接各个外围设备的通讯接口；进而可以使上位机通过该串行接口复用装置中不同的开关单元，与不同外围设备实现通讯，且无需为上位机安装多个RS232串口，避免了通讯成本的增加。
",上海北汇信息科技有限公司,其他技术,0.0,未匹配到特定技术领域,"提供, 实现, 装置, 设备, 系统",通用
CN109963377B,CN201810637617.2,"本发明提供一种多色LED灯及其控制方法与装置，对由于工作而导致温度变化的LED，其通过相应AD电压采集模块分别采集对应LED各自的压降，然后由计算模块根据相应LED压降的采集值与其内部预设的标准温度下对应颜色的LED压降之间的偏移量，对照预设的各种颜色的LED的Tj‑ΔV曲线，计算得到处于ON状态的LED的结温；进而使工作中的LED以各自的结温，代替现有技术中统一的控制器温度，进行相应颜色的LED驱动控制的单独计算，进而通过相应PWM驱动模块实现对于相应LED驱动模块的对应控制，完成对于各种颜色的LED各自单独的温度补偿，解决了现有技术中无法对各个LED单独进行温度补偿的问题。
",上海北汇信息科技有限公司,其他技术,0.0,未匹配到特定技术领域,"提供, 实现, 装置, 方法, 控制, 计算",控制
CN109005617B,CN201810735478.7,"本发明提供一种基于车载RGB控制器的无极调光方法和装置。方法包括：将采集到的RGB三色各自LED输入的坐标值分别按百分比转换得到R<Sub>input_ratio</Sub>、G<Sub>input_ratio</Sub>、B<Sub>input_ratio</Sub>，同时依据预设的RGB三色的标准色坐标参数，计算得到混合颜色的三基色刺激值X<Sub>mix</Sub>、Y<Sub>mix</Sub>、Z<Sub>mix</Sub>；依据RGB三色各自LED自身的色坐标参数和X<Sub>mix</Sub>、Y<Sub>mix</Sub>、Z<Sub>mix</Sub>，计算得到RGB三色在混光后分别所占的亮度值；依据得到的亮度值计算RGB三色各自LED的PWM占空比；依据得到的PWM占空比对RGB LED的每个颜色的驱动电流进行调节。本发明保证了RGB颜色及亮度的一致性。
",上海北汇信息科技有限公司,其他技术,0.0,未匹配到特定技术领域,"提供, 包括, 装置, 方法, 控制, 计算",控制
CN304783034S,CN201830061876.6,"1．本外观设计产品的名称：车载RGB控制器。
2．本外观设计产品的用途：本外观设计产品用于汽车内部，用于烘托车内环境氛围，可根据用户需要设定氛围灯颜色、亮度，提升汽车内饰的档次。
3．本外观设计产品的设计要点：产品形状。
4．最能表明本外观设计设计要点的图片或照片：立体图。
",上海北汇信息科技有限公司,其他技术,0.0,未匹配到特定技术领域,控制,"汽车, 控制"
