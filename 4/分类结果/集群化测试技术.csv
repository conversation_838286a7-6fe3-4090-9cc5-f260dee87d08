﻿公开(公告)号,申请号,摘要(中文),原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN119472417A,CN202411587419.1,"本发明公开了一种多测试系统联调急停控制系统，包括：多个电源开关和多组测试模块，用于控制多台测试系统的通断电；多组测试模块均包括检测回路模块和急停控制模块，检测回路模块包括第一信号发出端、第一信号接收端、第二信号发出端和第二信号接收端，急停控制模块包括第一控制开关和第二控制开关；第一信号发出端可选择的与第一信号接收端连接或断开，第一控制开关的一端与第一信号接收端相连接，另一端与其他测试模块的第一信号发出端相连接；第二控制开关的一端与第二信号发出端相连接，另一端与第二信号接收端相连接。本发明还公开了多测试系统联调急停控制系统的控制方法，能够实现单独控制或多台控制。
",上海北汇信息科技有限公司,集群化测试技术,1.0,关键词匹配: 测试系统,"实现, 包括, 测试, 方法, 控制, 系统, 检测","检测, 测试, 控制"
CN119413468A,CN202411536643.8,"本申请提供了一种新能源电动汽车及其台架测试系统，该系统包括上位机、高压模拟单元、低压模拟单元、原车BMS控制器和原车负载；高压模拟单元分别与上位机、原车BMS控制器和原车负载相连；低压模拟单元分别与上位机和原车BMS控制器相连；高压模拟单元和低压模拟单元受控于上位机，上位机用于控制高压模拟单元对新能源电动汽车的原车电池包的高压部分进行模拟和低压模拟单元对原车电池包的低压部分进行模拟，可以利用上位机、高压模拟单元和低压模拟单元对新能源电动汽车的台架测试系统中的原车电池包进行替代，无需新能源电动汽车的原车电池包也可完成测试，解决了现有需要高价采购电池包，且测试后无二次利用价值需进行危废处理的问题。
",上海北汇信息科技有限公司,集群化测试技术,1.0,关键词匹配: 测试系统,"提供, 测试, 包括, 处理, 控制, 系统, 模拟","测试, 汽车, 控制"
CN118012771A,CN202410230520.5,"本申请涉及嵌入式软件测试的领域，提供一种基于多测试平台的嵌入式软件测试方法和系统，方法包括：基于预配置的激励参数生成第一测试用例数据和第二测试用例数据，并在仿真软件环境下根据第一测试用例数据和第二测试用例对目标软件进行测试以得到目标软件的期望运行结果；在仿真硬件环境下，将目标软件烧录至目标板，将第一测试用例数据和第二测试用例分别输入至调试电路和板卡硬件电路以生成第一激励信号和第二激励信号且发送至目标板得到第一运行结果和第二运行结果；比较期望运行结果、第一运行结果和第二运行结果以确认目标软件符合预期要求。常规的白盒测试同时，通过板卡硬件电路实现控制算法和环境模型之间的数据交互测试，完成闭环测试。
",上海北汇信息科技有限公司,集群化测试技术,1.0,关键词匹配: 测试平台,"提供, 平台, 实现, 包括, 模型, 测试, 算法, 仿真, 方法, 生成, 控制, 系统, 电路, 配置","测试, 控制"
CN117630553A,CN202311678594.7,"本申请提供一种多个储能BMS并行测试系统，其中，所述测试系统包括：电源模块，用于给多个储能BMS提供各自所需的电源；可编程电源测试仪件模块，用于给各所述储能BMS配置所需的模拟传感器信号；通信模块，用于为各所述储能BMS配置所需的通信接口，以使所述测试系统与各所述储能BMS的信息交互；电池组仿真模块，用于为各所述储能BMS模拟出所需的电池组状态；测试模块，用于加载各所述储能BMS对应的测试用例并同时执行各所述储能BMS对应的测试用例，以使所述测试系统对各所述储能BMS进行并行测试。采用本申请实施例，同时实现对多个BMS进行测试，提高了测试效率。
",上海北汇信息科技有限公司,集群化测试技术,2.0,"关键词匹配: 并行测试, 测试系统","提供, 模拟, 实现, 包括, 测试, 仿真, 系统, 配置","通信, 测试, 传感器"
CN116661425A,CN202310764184.8,"本申请公开了一种目标控制器的联合测试方法及相关设备，首先获取基于目标控制器的测试系统发出的联合测试请求，然后根据联合测试请求，确定与目标控制器所对应的联合测试控制器，并根据联合测试请求和联合测试控制器，确定与联合测试请求所对应的预设测试模型。然后通过预设测试模型，建立目标控制器的测试系统与所述测试控制器的测试系统之间的通信连接，最后基于目标控制器的测试系统与测试控制器的测试系统之间的通信连接以及所述预设测试模型，对目标控制器进行联合测试。通过此方法，在对目标控制器进行联合测试时，无需仿真目标控制器对应的联合测试控制器的交互功能，即可测试目标控制器与其对应的联合测试控制器之间的匹配协调工作能力。
",上海北汇信息科技有限公司,集群化测试技术,1.0,关键词匹配: 测试系统,"测试, 模型, 仿真, 方法, 设备, 控制, 系统","通信, 测试, 控制"
CN219536083U,CN202321030163.5,"本实用新型提供一种传感器自动化测试系统，传感器自动化测试系统包括：待测试传感器、蓝牙测试链路、LoRa测试链路、上位机；待测试传感器的第一端通过蓝牙测试链路与上位机连接，待测试传感器的第二端通过LoRa测试链路与上位机连接；上位机通过蓝牙测试链路将第一测试指令发送给待测试传感器以进行相应的功能测试，上位机通过LoRa测试链路将第二测试指令发送给待测试传感器以进行相应的功能测试。本方案的上位机可以通过蓝牙测试链路和LoRa测试链路向待测试传感器发送测试指令来进行功能测试，从而能够缩短测试周期和降低测试成本。
",上海北汇信息科技有限公司,集群化测试技术,1.0,关键词匹配: 测试系统,"系统, 提供, 测试, 包括","测试, 传感器"
CN216673307U,CN202220177175.X,"本申请公开了一种实车V2X功能的测试系统，用于对被测车辆上OBU的V2X功能进行测试，其包括与OBU连接的虚拟交通模块。该虚拟交通模块设置在被测车辆的驾驶室内，用于向OBU发送虚拟交通信息，接收OBU根据虚拟交通信息返回的报警信息，还用于向被测车辆上的驾驶员输出虚拟交通信息和报警信息，虚拟交通信息与被测车辆所处的真实交通场景同步。通过报警信息的输出即可实现对OBU的测试，且无需布设路侧基站设备和其他真实车辆，从而降低了测试成本。
",上海北汇信息科技有限公司,集群化测试技术,1.0,关键词匹配: 测试系统,"实现, 包括, 测试, 设置, 设备, 系统","车辆, 驾驶, 通信, 测试"
CN215912101U,CN202122653296.5,"本实用新型提供一种车载单元测试系统，应用于汽车技术领域，该测试系统包括与待测OBU通讯连接、为待测OBU提供模拟卫星信息的环境模拟装置，与待测OBU通讯连接、转发测试信息的信号收发装置，以及与信号收发装置通讯连接并根据测试信息对待测OBU进行联通测试的上位机。本实用新型提供的测试系统，能够对待测OBU进行联通测试，满足实际应用中对于OBU的测试需求。
",上海北汇信息科技有限公司,集群化测试技术,1.0,关键词匹配: 测试系统,"提供, 测试, 包括, 装置, 系统, 模拟","测试, 汽车"
