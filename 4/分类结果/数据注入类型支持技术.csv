﻿公开(公告)号,申请号,摘要(中文),原始申请人,技术领域,分类得分,分类理由,技术手段,应用场景
CN222271813U,CN202421220865.4,"本实用新型提供了一种充电数据采集设备，包括：数据采集单元、数据传输单元、兼容插座接口、枪线插座和多类充电线路；充电线路的一端与兼容插座接口相连接，另一端与枪线插座相连接；兼容插座接口，用于在需要通过目标充电桩为目标车辆充电时，连接目标充电线路对应的充电插座，以连接目标充电桩的充电枪；枪线插座，用于连接目标车辆对应的充电枪线，以连接目标车辆的充电插座；数据采集单元，用于采集充电过程中的充电数据；数据传输单元，用于将充电数据传输至外部设备。本实用新型提供的设备，通过设备连接的充电枪线和充电插座，可对基于不同充电标准的充电过程中的充电数据进行采集，有利于降低设备成本，提高数据采集的便利性。
",上海北汇信息科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据传输,"提供, 包括, 设备","车辆, 数据传输"
CN116933541A,CN202310920777.9,"本发明提供一种数据处理方法及装置，执行预先保存在exe文件中的读取指令，从模拟驾驶的游戏设备读取USB格式的第二驾驶数据并转换为符合用户数据报协议的第一驾驶数据，向接收端发送第一驾驶数据，接收端对第一驾驶数据进行拆解，得到各个子设备对应的子设备参数，针对每一子设备参数，将子设备参数与CarMaker工具中相应的变量关联并生成模拟驾驶场景，在本方案中，利用发送端预先保存在exe文件中的读取指令，读取游戏设备USB格式的数据，并通过用户数据报协议向接收端发送，接收端将接收到的数据处理后与CarMaker工具中相应的变量关联，使得CarMaker工具生成模拟驾驶场景，从而实现了脱离MatLab环境获取USB格式的驾驶数据，并生成模拟驾驶场景的目的。
",上海北汇信息科技有限公司,数据注入类型支持技术,1.0,关键词匹配: 数据处理,"提供, 实现, 装置, 方法, 生成, 处理, 设备, 工具, 模拟",驾驶
