﻿公开(公告)号,申请号,摘要(中文),原始申请人
CN120491511A,CN202510573286.0,"本申请公开了一种爆炸熔丝仿真测试方法，涉及熔丝仿真的技术领域，其包括构建包含四态开关组的仿真电路，其中S1和S4构成主回路常闭通道，S2和S3分别作为过压和过流故障注入通道；获取真实熔丝的阻抗特性，建立等效阻抗特性；配置动态参数控制模块，动态参数控制模块实时接收上位机下发的电压和/或电流触发阈值以及故障模式参数；在检测到主回路参数达到预先设定的触发阈值时，触发S4断开形成模拟熔断状态；生成具有时序特征的故障波形；将仿真电路的状态参数与BMS控制信号进行闭环反馈，形成包含三种模式的测试矩阵。本申请具有提升熔丝仿真过程中的参数调整灵活性和全面性的效果。
",上海北汇信息科技有限公司
CN120454904A,CN202510580989.6,"本申请公开了一种车联网硬件在环测试方法及装置，涉及车联网技术领域。该方法包括：基于设定的测试需求，构建对应的虚拟道路场景；发射多个射线，并使多个射线在对应的虚拟道路场景中传输，以得到虚拟道路场景对应的信道参数；根据信道参数，构建与虚拟道路场景对应的信道模型；基于虚拟道路场景、信道模型以及射频仪表，对车联网设备进行车联网硬件在环测试。在本申请中，构建与虚拟道路场景的信道模型，并将信道模型与V2X‑HIL相结合，使得在实验室环境下能够更准确地模拟真实世界的通信环境，从而在实验室环境中实现了对车联网硬件在真实环境下的测试，提高对车联网设备的测试的准确性，从而提高车联网设备的安全性。
",上海北汇信息科技有限公司
CN120377427A,CN202510516805.X,"本申请公开了一种高压回路切换系统，涉及高压回路的技术领域，其包括超级电容模块：用于获取电动车的动力系统的电路回路，在电路回路进入动力系统的主回路上建立超级电容模组；电源选择模块：用于进行实时监控，对动力系统的电力来源进行选择；电源切换模块：用于获取当前测试场景，切换动力系统的电力来源；负载调整模块：用于根据实时负载需求，动态调整输出功率；电池充电模块：用于根据实时负载需求，对电池包以及超级电容模组进行充电；异常处理模块：用于获取电动车测试过程中的状态参数，根据异常情况进行异常调整。本申请具有降低电源切换过程中的人力资源的消耗，提升测试效率的效果。
",上海北汇信息科技有限公司
CN120295932A,CN202510444031.4,"本申请提供了一种测试系统、车载控制器的冒烟测试方法，该方法可应用于测试管理系统，包括：判断车载控制器是否完成软件新版本的开发释放；若是，则创建车载控制器的软件刷写任务，并根据软件刷写任务对车载控制器进行软件刷写；在车载控制器完成软件刷写之后，确定车载控制器的冒烟测试任务，并根据冒烟测试任务对车载控制器进行冒烟测试，能够利用测试管理系统对车载控制器自动进行冒烟测试，解决了现有冒烟测试需要在实车或者传统HiL台架对软件进行，需要安排专门人员部署冒烟测试环境，制定冒烟测试计划，不仅需要投入更多的测试人员，同时频繁的测试环境切换也带来时间成本的增加以及测试计划混乱的问题。
",上海北汇信息科技有限公司
CN120142821A,CN202510516806.4,"本申请公开了一种电动车充电接口温度保护功能测试方法，涉及电动车充电的技术领域，其包括获取电动车充电接口组件的物理结构，得到电流走向以及接口贴合量；获取电动车在充电的过程中的电流大小，得到理论发热量；获取电动车充电接口组件的温度保护功能的触发温度值，对充电接口组件进行加热处理；分别采集加热之前、加热过程以及加热完成后的充电接口的性能数据，得到性能曲线；对充电接口组件进行多次加热，采集每次加热完成后的功能反应数据，得到功能反应曲线；将性能曲线以及功能反应曲线进行曲线拟合，得到功能测试结果。本申请具有提升在升温时对温度控制的精准性，降低测试过程中的繁琐程度的效果。
",上海北汇信息科技有限公司
CN119787600A,CN202510032359.5,"本发明提供一种直流高压液冷回路切换系统及其控制方法，该系统包括：开关切换装置的输入端正负极作为高压切换箱的输入端正负极，通过高压线束连接直流源；开关切换装置的第一输出端与第一输出接口连接；开关切换装置的第二输出端与第二输出接口连接；开关切换装置处于第一状态时，高压切换箱采用第一输出接口，输出第一输出接口对应的电流；开关切换装置处于第二状态时，高压切换箱采用第二输出接口，输出第二输出接口对应的电流，且采用液冷单元对第二输出接口对应的线缆冷处理；在不改变的线缆直径的基础上，提高直流高压液冷回路切换系统支持的电流范围，提高直流高压液冷回路切换系统的适用性，降低成本和空间布局方便。
",上海北汇信息科技有限公司
CN222713317U,CN202420349569.8,"本申请公开了一种充放电数据采集设备，包括：供电模块、数据采集模块。供电模块与数据采集模块相连。供电模块用于为数据采集模块供电。数据采集模块用于采集充放电数据，其中，充放电数据为符合欧标充电协议的电动车与充电桩之间进行充电和放电时的数据。本申请的数据采集模块能够准确地采集充放电数据，将这些数据记录下来，为后续分析和研究提供重要的依据。同时，通过供电模块为数据采集模块提供稳定的电力供应，确保数据采集模块能够正常运行和采集充放电数据，避免由于供电问题导致无法充电的故障发生。
",上海北汇信息科技有限公司
CN119629046A,CN202411740636.X,"本申请公开了一种车载以太网集中式数据管理系统及方法，涉及基于时间敏感网络的车载以太网通信的技术领域，其包括ECU识别模块：用于提取ECU单元；当前参数分析模块：用于根据ECU单元，得到每个基础参数的提及率以及重要程度，得到第一参数概率；历史参数分析模块：用于提取历史参数的查询率和修改率，得到第二参数概率；参数筛选模块：用于根据第一参数概率以及第二参数概率，得到目标基础参数；快捷入口模块：用于根据参数条目得到网络条目，根据网络条目设置快捷入口；实体结构生成模块：用于生成分布式数据实体结构。本申请具有提升车载以太网在测试开发过程中参数查询和修改的效率和精确度的效果。
",上海北汇信息科技有限公司
CN119620738A,CN202411857426.9,"本申请提供一种基于驾驶员在环的V2X功能测试方法及系统，当检测到仿真测试开始时，通过上位机运行测试执行及管理软件调用场景仿真软件，在场景显示大屏上显示由测试场景构成的场景界面；通过测试执行及管理软件响应真实驾驶员根据仿真交通环境操作驾驶模拟器发出的控制指令，向场景仿真软件发送控制信号，场景仿真软件基于控制信号控制仿真测试车辆行驶；在行驶的过程中，通过测试执行及管理软件将仿真测试车辆的实时位置信息和每个仿真物体的实时位置信息发送给被测件；通过被测件根据仿真测试车辆和每个仿真物体的实时位置测试信息，判断仿真测试车辆是否满足V2X预警功能；若仿真测试车辆满足V2X预警功能，触发相应的V2X预警功能。
",上海北汇信息科技有限公司
CN119583390A,CN202411839637.X,"本发明提供一种CAN同步测试方法和系统、电子设备、存储介质，该方法包括：获取CAN同步系统中主时钟节点发送至从时钟节点的时间同步报文；对时间同步报文按照报文标准规范进行格式检查，得到格式检查结果；对主时钟节点和从时钟节点进行同步精度验证，得到精度验证结果；将格式检查结果和精度验证结果，作为同步测试结果；也即，本申请基于CAN时间同步进制开发了自动化的测试工程，显著降低了测试难度，大大缩短了测试的时间成本和人力成本，同时可以满足部件级、系统级测试，也能够在自动化测试系统以及普通供电电源条件的多种测试环境下灵活执行测试。利用代码生成工具，可以批量生成脚本，大幅度提高了自动化测试效率。
",上海北汇信息科技有限公司
CN119561883A,CN202411740639.3,"本申请公开了基于TSN的车载以太网时间敏感流量规划方法，包括基于用户场景和数据流信息创建网络拓扑图；基于数据流信息分析各数据流的优先级得到数据流优先级结果，基于数据流优先级结果和数据流信息确定数据流的转发路径得到转发路径匹配信息，确定各数据流的转发表条目信息得到转发表条目数据集，基于转发路径匹配信息和转发表条目数据对各数据流的门控列表设置得到门控列表生成信息；重复以上步骤以获取多个数据流样本进行仿真，检测仿真中各数据流样本的有效仿真信息，基于有效仿真信息计算各数据流样本的适应度f(x)，基于数据流样本的适应度f(x)对数据流样本划分得到数据流样本划分结果。本申请具有提高车载以太网通信数据帧调度精确度的效果。
",上海北汇信息科技有限公司
CN119472417A,CN202411587419.1,"本发明公开了一种多测试系统联调急停控制系统，包括：多个电源开关和多组测试模块，用于控制多台测试系统的通断电；多组测试模块均包括检测回路模块和急停控制模块，检测回路模块包括第一信号发出端、第一信号接收端、第二信号发出端和第二信号接收端，急停控制模块包括第一控制开关和第二控制开关；第一信号发出端可选择的与第一信号接收端连接或断开，第一控制开关的一端与第一信号接收端相连接，另一端与其他测试模块的第一信号发出端相连接；第二控制开关的一端与第二信号发出端相连接，另一端与第二信号接收端相连接。本发明还公开了多测试系统联调急停控制系统的控制方法，能够实现单独控制或多台控制。
",上海北汇信息科技有限公司
CN119483826A,CN202411586488.0,"本发明公开了一种实现CAN报文小周期发送的优化方法，包括：为CAN报文中的每一帧报文分别设置对应的配置报文；其中，CAN报文中的每一帧报文对应的配置报文的数据场的值不同；每次进行CAN报文发送时，根据输入的配置报文的数据场的值判断CAN报文中的每一帧的报文是否发送；其中，如果输入的配置报文的数据场的值与CAN报文中的某一帧报文对应的配置报文的数据场的值相同，则按照设定周期发送该帧的报文；否则，不发送该帧报文。
",上海北汇信息科技有限公司
CN119414349A,CN202411868431.X,"本申请公开了一种毫米波雷达的硬件在环测试系统及方法，系统包括第一上位机、第二上位机、视频暗箱、硬件在环测试机柜、雷达回波模拟器系统和控制器，硬件在环测试机柜分别与第一上位机、第二上位机、雷达回波模拟器系统和控制器相连，视频暗箱与第一上位机相连，雷达回波模拟器系统分别与第二上位机和控制器相连。毫米波雷达的硬件在环测试系统用于获取测试场景，识别测试场景，得到场景信息，运行自动化测试软件，以获取雷达感知数据，将雷达感知数据进行逻辑处理，得到处理后的雷达感知数据，对处理后的雷达感知数据进行转换，得到回波数据，将场景信息和回波数据进行感知融合，得到目标数据，以完成毫米波雷达的硬件在环测试的过程。
",上海北汇信息科技有限公司
CN119413468A,CN202411536643.8,"本申请提供了一种新能源电动汽车及其台架测试系统，该系统包括上位机、高压模拟单元、低压模拟单元、原车BMS控制器和原车负载；高压模拟单元分别与上位机、原车BMS控制器和原车负载相连；低压模拟单元分别与上位机和原车BMS控制器相连；高压模拟单元和低压模拟单元受控于上位机，上位机用于控制高压模拟单元对新能源电动汽车的原车电池包的高压部分进行模拟和低压模拟单元对原车电池包的低压部分进行模拟，可以利用上位机、高压模拟单元和低压模拟单元对新能源电动汽车的台架测试系统中的原车电池包进行替代，无需新能源电动汽车的原车电池包也可完成测试，解决了现有需要高价采购电池包，且测试后无二次利用价值需进行危废处理的问题。
",上海北汇信息科技有限公司
CN119375673A,CN202411558945.5,"本发明公开了一种电动车高压互锁回路测试设备及测试方法，所述测试设备包括：通讯模块，其连接上位机；控制模块，其与所述通讯模块连接；IN接口，其与待测试的高压互锁回路的输入端连接；OUT接口，其与待测试的高压互锁回路的输出端连接；可调电阻，其连接在所述IN接口和所述OUT接口之间，并且所述可调电阻与所述控制模块电联；信号、波形发生模块，其输入端与所述控制模块连接，输出端连接在所述可调电阻与所述OUT接口之间；第一开关，其连接在IN接口与所述可调电阻之间，所述第一开关为常闭开关；其中，所述第一开关与所述控制模块电联。
",上海北汇信息科技有限公司
CN119336000A,CN202411449545.0,"本申请公开了一种控制器测试方法及相关系统，所述方法包括：针对外围设备中的每个目标组件，通过仿真软件生成目标组件对应的仿真数据；其中，目标组件包括融合组件、前视摄像头组件及定位组件；融合组件及前视摄像头组件对应的仿真数据由外围设备中的场景仿真软件生成；定位组件对应的仿真数据通过动力学仿真软件生成；融合组件对应的仿真数据包括场景目标信息等；前视摄像头组件仿真数据包括车道线集信息等；定位组件对应的仿真数据包括经纬度信息等；将仿真数据发送至目标组件；将仿真数据为Proto数据库信号进行映射赋值，并对赋值后的Proto数据库信号进行序列化处理，最后将序列化数据发送给高级驾驶辅助控制单元中的规控模块。
",上海北汇信息科技有限公司
CN119269125A,CN202411398799.4,"本申请公开一种自动化实车测试的方法和装置，方法包括，获得目标测试用例，目标测试用例包括多个测试阶段，每一测试阶段包括多个实车操作步骤；输出当前测试阶段对应的影音信息，以提示被测车辆的驾驶员按当前测试阶段的实车操作步骤操作被测车辆，当前测试阶段为目标测试用例的任一测试阶段；根据被测车辆的报文信号评估当前测试阶段的测试结果；在当前测试阶段的测试结果满足跳转条件的情况下，将未获得测试结果的另一测试阶段确定为当前测试阶段，返回执行输出当前测试阶段对应的影音信息，直至获得目标测试用例中各个测试阶段的测试结果为止。
",上海北汇信息科技有限公司
CN119224391A,CN202411464500.0,"本申请提供了一种测试辅助装置，包括：控制单元和模式切换单元；模式切换单元与控制单元相连，模式切换单元的运行模式切换受控于控制单元，模式切换单元的运行模式包括：正向模式、反向模式、开路模式和正负极短接模式中的至少一种，能够在执行自动化测试过程中辅助程控电源完成极性反转功能和故障注入等功能，解决了市场上大部分程控电源厂家的电源极性翻转功能为选配功能需要特别定制，并且程控电源无法支持开路、短路等故障功能测试的问题。
",上海北汇信息科技有限公司
CN222271813U,CN202421220865.4,"本实用新型提供了一种充电数据采集设备，包括：数据采集单元、数据传输单元、兼容插座接口、枪线插座和多类充电线路；充电线路的一端与兼容插座接口相连接，另一端与枪线插座相连接；兼容插座接口，用于在需要通过目标充电桩为目标车辆充电时，连接目标充电线路对应的充电插座，以连接目标充电桩的充电枪；枪线插座，用于连接目标车辆对应的充电枪线，以连接目标车辆的充电插座；数据采集单元，用于采集充电过程中的充电数据；数据传输单元，用于将充电数据传输至外部设备。本实用新型提供的设备，通过设备连接的充电枪线和充电插座，可对基于不同充电标准的充电过程中的充电数据进行采集，有利于降低设备成本，提高数据采集的便利性。
",上海北汇信息科技有限公司
CN119197534A,CN202411366716.3,"本申请公开了一种位置信息偏移方法及系统、电子设备、存储介质，所述方法，包括：获取上一场景的结束位置的位置信息及当前场景的起始位置的位置信息；其中，位置信息包括经度和纬度；基于结束位置的位置信息以及起始位置的位置信息，计算得到结束位置与起始位置之间的距离；按照预设最大速度以及预设加速度，计算出结束位置与起始位置之间的距离内，各个行驶阶段的行驶时间以及行驶距离；其中，行驶阶段包括加速阶段、匀速阶段以及减速阶段；将从结束位置至起始位置的时间划分为多个时间段；基于各个行驶阶段的行驶时间以及行驶距离，计算得到各个时间段对应的位置信息；利用各个时间段对应的位置信息，组成结束位置至起始位置的平滑过渡路径。
",上海北汇信息科技有限公司
CN119127774A,CN202411436168.7,"本申请公开了一种基于RS232串口通讯的设备的故障注入方法及相关装置，涉及故障注入技术领域，在基于RS232串口通讯的上位机与被控设备间的通讯线上设置了一个故障注入模块，该故障注入模块连接上位机上的第一测试节点和被控设备上的第二测试节点，故障注入模块接收上位机发送的故障注入请求，解析故障注入请求并按照解析得到对应的故障注入方式完成故障注入。本申请中采用外接的故障注入模块即可通过上位机自行控制实现数据故障注入或硬件故障注入，人工参与度低且操作简单。
",上海北汇信息科技有限公司
CN118449624A,CN202310068911.7,"本申请公开了一种自动化测试方法及系统，当服务器接收到测试任务时，将控制指令发送至客户端工控机，触发客户端工控机获取测试台架上的被测对象的测试数据，测试台架为提供测试所需的车内仿真环境、人机交互环境、故障注入环境和测试接口的测试台架，通过客户端工控机以节点仿真方式对测试数据进行测试，生成测试结果，测试结果表征逆向测试和部件测试的测试结果，服务器接收客户端工控机发送的测试结果进行分析，得到分析结果并展示。
",上海北汇信息科技有限公司
CN118363359A,CN202311360738.4,"本发明涉及一种充电控制器测试方法、系统及电路。其中，方法包括：获取充电控制器的待测试项目；控制通信装置输出符合第一类型充电协议且与待测试项目对应的目标测试信号至待测试充电控制器；接收待测试充电控制器基于目标测试信号反馈的符合第二类型充电协议的测试反馈信号；将目标测试信号与测试反馈信号进行对比，获得待测试充电控制器的项目测试结果。本发明可以检验充电控制器是否满足设计要求。
",上海北汇信息科技有限公司
CN118090232A,CN202410200445.8,"本申请实施例提供了一种车辆测试方法、装置、设备及存储介质。在执行所述方法时，通过测试管理模块获取需要进行测试的待测试项目后，向代理模块发送测试指令，由代理模块将测试指令转发给测试模块；测试模块接收到测试指令后，基于测试模块对测试指令进行解析，向仿真台架发送仿真指令后，仿真台架可以根据仿真指令进行仿真测试，得到最终的测试结果。通过根据测试指令中的待测试项目向仿真台架发送对应的仿真指令，进而控制仿真台架进行相关的控制，实现对车辆的自动化测试，无须工程师手动控制仿真台架，即可完成车辆测试。降低车辆测试难度，并提高测试效率。
",上海北汇信息科技有限公司
CN118012771A,CN202410230520.5,"本申请涉及嵌入式软件测试的领域，提供一种基于多测试平台的嵌入式软件测试方法和系统，方法包括：基于预配置的激励参数生成第一测试用例数据和第二测试用例数据，并在仿真软件环境下根据第一测试用例数据和第二测试用例对目标软件进行测试以得到目标软件的期望运行结果；在仿真硬件环境下，将目标软件烧录至目标板，将第一测试用例数据和第二测试用例分别输入至调试电路和板卡硬件电路以生成第一激励信号和第二激励信号且发送至目标板得到第一运行结果和第二运行结果；比较期望运行结果、第一运行结果和第二运行结果以确认目标软件符合预期要求。常规的白盒测试同时，通过板卡硬件电路实现控制算法和环境模型之间的数据交互测试，完成闭环测试。
",上海北汇信息科技有限公司
CN117950980A,CN202211291641.8,"本申请公开了一种测试车载嵌入式软件的方法及装置。根据不同的芯片架构和不同的编译工具链对模拟器进行配置，利用模拟器代替实际目标板进行测试。本申请所述方法减小了白盒测试的单元测试阶段对硬件的集成度、降低了成本，并且通过模拟器进行测试时无需切换目标版和清理冗余资源，大大提高了测试效率。
",上海北汇信息科技有限公司
CN117811970A,CN202311576264.7,"本申请公开了一种时间同步补偿时延测试方法、装置、设备及存储介质，所述方法包括：通过获取节点间链路平均时延及同步误差；在进行Sync消息同步后，将Sync消息的接收时间与发送时间的差值作为Sync时延测试值；根据节点间链路平均时延以及同步误差获取Sync时延真实值；对Sync时延测试值和Sync时延真实值和进行比较，得到测试结果。利用Sync消息同步机制的特点，得到Sync时延测试值，再基于理论研究，通过获取的节点间链路平均时延以及同步误差，得到理论中地Sync时延真实值，将测试值与真实值进行比较得到测试结果，以完成对时间同步补偿时延进行测试。
",上海北汇信息科技有限公司
CN220650781U,CN202321425632.3,"本申请提供了一种电动汽车的高压回路故障注入仿真装置，包括：继电器故障仿真单元、第一绝缘电阻仿真单元、第二绝缘电阻仿真单元以及电容仿真单元；其中，继电器故障仿真单元分别与第一绝缘电阻仿真单元、第二绝缘电阻仿真单元以及电容仿真单元连接，第一绝缘电阻仿真单元、电容仿真单元以及继电器故障仿真单元共用第一高压源和被测设备，能够覆盖电动汽车的直流充电、交流充电、电池包以及电机的高压回路故障注入，解决了现有高压回路故障注入仿真装置可以实现的故障注入类型较少，无法满足当前国标充电测试要求的问题。
",上海北汇信息科技有限公司
CN117676659A,CN202311673293.5,"一种ECU的在环测试方法、系统、电子设备及介质，涉及汽车技术领域。该方法包括：获取待测试车辆的车辆参数以及对应的交通环境信息；根据所述交通环境信息和所述车辆参数，构建对应的HIL测试环境；确定所述待测试车辆中待测ECU的通信网络参数，根据所述通信网络参数和所述HIL测试环境，生成测试信号；将所述测试信号输入所述待测ECU，得到所述待测ECU基于所述测试信号反馈的测试结果。达到了提高测试效率的效果。
",上海北汇信息科技有限公司
CN117630553A,CN202311678594.7,"本申请提供一种多个储能BMS并行测试系统，其中，所述测试系统包括：电源模块，用于给多个储能BMS提供各自所需的电源；可编程电源测试仪件模块，用于给各所述储能BMS配置所需的模拟传感器信号；通信模块，用于为各所述储能BMS配置所需的通信接口，以使所述测试系统与各所述储能BMS的信息交互；电池组仿真模块，用于为各所述储能BMS模拟出所需的电池组状态；测试模块，用于加载各所述储能BMS对应的测试用例并同时执行各所述储能BMS对应的测试用例，以使所述测试系统对各所述储能BMS进行并行测试。采用本申请实施例，同时实现对多个BMS进行测试，提高了测试效率。
",上海北汇信息科技有限公司
CN117609091A,CN202311673294.X,"本申请涉及嵌入式软件测试的领域，提供一种提取动态测试中函数运行时间的方法和系统，其方法包括：基于预配置的激励参数生成测试用例数据，在仿真环境下，在所述测试用例数据中目标函数的始端和末端分别设置第一断点和第二断点生成断点位置信息；在仿真硬件环境下，根据断点位置信息在目标板中已烧录的目标软件的对应位置设置断点，并将测试用例数据输入至调试电路以生成激励信号；将激励信号发送至所述目标板，以生成对应断点的运行时间；根据运行时间与预设的上限值的比较结果，生成测试报告。本案针对每个工况对应的测试用例数据中目标函数均在测试运行时通过断点来进行记录，准确判定出目标函数的运行时间，无需工作人员持续进行观测。
",上海北汇信息科技有限公司
CN117312130A,CN202311060721.7,"本申请涉及嵌入式软件测试的领域，提供一种嵌入式软件测试方法和系统，其方法包括基于预配置的激励参数生成测试用例数据，并在仿真软件环境下根据所述测试用例数据对目标软件进行测试，以得到所述目标软件的第一运行结果；在仿真硬件环境下，将所述目标软件烧录至目标板，并将所述测试用例数据输入至调试电路以生成激励信号；将所述激励信号发送至所述目标板，以得到所述目标软件的第二运行结果；比较所述第一运行结果和所述第二运行结果，并基于比较结果确认所述目标软件是否符合预期要求。本申请给入多种复杂激励参数，通过第一运行结果和第二运行结果的比较，实现控制器在环测试阶段输出的自动化评估以得到测试结果和测试报告。
",上海北汇信息科技有限公司
CN117007054A,CN202311036056.8,"本申请公开了一种CANoe工程中车辆当前位置的获取方法及相关装置。在执行本申请实施例提供的方法时，首先可以利获取CANoe工程中目标车辆的测量点变量，并在测量点变量为第一预设值时，记录当前坐标值作为第一坐标值，同时获取CANoe工程的第二坐标值。对第一坐标值和第二坐标值进行预处理得到第一目标经度和第二目标经度。再将第一目标经度和第二目标经度赋值到相应的偏移系统进行偏移计算得到偏移量，并获取目标车辆的历史停止经度。然后将历史停止经度和偏移量进行相加得到车辆当前经度。本申请联合目标车辆在CANoe工程中的第一坐标值和第二坐标值与历史停止经度进行计算从而得到车辆当前经度，提高了V2X测试系统的测试效率。
",上海北汇信息科技有限公司
CN116990571A,CN202310975335.4,"本发明提供一种电流仿真装置，涉及霍尔电流传感器技术领域，该电流仿真装置包括：电流传感器和四个开关；其中，电流传感器为输出单向信号的霍尔电流传感器；电流传感器的输出端正极，分别连接第一开关的一端和第二开关的一端；电流传感器的输出端负极，分别连接第三开关的一端和第四开关的一端；第一开关的另一端和第三开关的另一端相连，第二开关的另一端和第四开关的另一端相连，两个连接点分别作为电流仿真装置的输出端两端口。本发明能够降低将单向信号输出的霍尔电流传感器修改为双向信号输出的难度。
",上海北汇信息科技有限公司
CN116933541A,CN202310920777.9,"本发明提供一种数据处理方法及装置，执行预先保存在exe文件中的读取指令，从模拟驾驶的游戏设备读取USB格式的第二驾驶数据并转换为符合用户数据报协议的第一驾驶数据，向接收端发送第一驾驶数据，接收端对第一驾驶数据进行拆解，得到各个子设备对应的子设备参数，针对每一子设备参数，将子设备参数与CarMaker工具中相应的变量关联并生成模拟驾驶场景，在本方案中，利用发送端预先保存在exe文件中的读取指令，读取游戏设备USB格式的数据，并通过用户数据报协议向接收端发送，接收端将接收到的数据处理后与CarMaker工具中相应的变量关联，使得CarMaker工具生成模拟驾驶场景，从而实现了脱离MatLab环境获取USB格式的驾驶数据，并生成模拟驾驶场景的目的。
",上海北汇信息科技有限公司
CN219778215U,CN202321040618.1,"本实用新型提供一种串行接口复用装置及串行接口复用上位机系统，该串行接口复用装置，将N个开关单元的一侧，并联至串行接口复用装置的汇总接口，以连接上位机的通讯接口；并将各开关单元的另一侧，分别与串行接口复用装置中对应的分接接口相连接，以连接各个外围设备的通讯接口；进而可以使上位机通过该串行接口复用装置中不同的开关单元，与不同外围设备实现通讯，且无需为上位机安装多个RS232串口，避免了通讯成本的增加。
",上海北汇信息科技有限公司
CN116794433A,CN202310770273.3,"本申请提供了一种车载充电机故障注入装置、方法及电子设备，在本申请提供的车载充电机故障注入装置中，包括有通讯控制模块以及故障注入模块，具体的，故障注入模块包括有断路故障注入单元、短路故障注入单元、反接故障注入单元以及相序异常故障注入单元。通过上述本申请提供的车载充电机故障注入装置，在对车载充电机进行故障注入测试时，通过下发具体的故障注入指令，确定需要进行故障注入的故障类型，即可自动化实现对车载充电机的故障注入测试，不再需要通过人工手动接线的方式来进行故障注入测试，解决了通过手动接线来进行故障注入测试所带来的安全隐患的问题。
",上海北汇信息科技有限公司
CN116661425A,CN202310764184.8,"本申请公开了一种目标控制器的联合测试方法及相关设备，首先获取基于目标控制器的测试系统发出的联合测试请求，然后根据联合测试请求，确定与目标控制器所对应的联合测试控制器，并根据联合测试请求和联合测试控制器，确定与联合测试请求所对应的预设测试模型。然后通过预设测试模型，建立目标控制器的测试系统与所述测试控制器的测试系统之间的通信连接，最后基于目标控制器的测试系统与测试控制器的测试系统之间的通信连接以及所述预设测试模型，对目标控制器进行联合测试。通过此方法，在对目标控制器进行联合测试时，无需仿真目标控制器对应的联合测试控制器的交互功能，即可测试目标控制器与其对应的联合测试控制器之间的匹配协调工作能力。
",上海北汇信息科技有限公司
CN219536083U,CN202321030163.5,"本实用新型提供一种传感器自动化测试系统，传感器自动化测试系统包括：待测试传感器、蓝牙测试链路、LoRa测试链路、上位机；待测试传感器的第一端通过蓝牙测试链路与上位机连接，待测试传感器的第二端通过LoRa测试链路与上位机连接；上位机通过蓝牙测试链路将第一测试指令发送给待测试传感器以进行相应的功能测试，上位机通过LoRa测试链路将第二测试指令发送给待测试传感器以进行相应的功能测试。本方案的上位机可以通过蓝牙测试链路和LoRa测试链路向待测试传感器发送测试指令来进行功能测试，从而能够缩短测试周期和降低测试成本。
",上海北汇信息科技有限公司
CN218512576U,CN202222850254.5,"本申请公开了一种HIL台架联合测试系统，包括至少一台上位机和多个机柜，每个机柜内设置有一套HIL台架测试装置，每个HIL台架测试装置包括交换机、工控机和VT System板卡，交换机分别与工控机、VT System板卡连接；上位机用于与任一交换机连接，用于基于用户的调试请求向交换机输出调试控制指令，以使工控机和VT System板卡在调试控制指令的控制下实现调试操作。这样一来，在对单个HIL台架测试装置进行调试时就不会对其他装置产生影响，从而避免了在对HIL台架测试装置在调试时产生冲突。
",上海北汇信息科技有限公司
CN216673307U,CN202220177175.X,"本申请公开了一种实车V2X功能的测试系统，用于对被测车辆上OBU的V2X功能进行测试，其包括与OBU连接的虚拟交通模块。该虚拟交通模块设置在被测车辆的驾驶室内，用于向OBU发送虚拟交通信息，接收OBU根据虚拟交通信息返回的报警信息，还用于向被测车辆上的驾驶员输出虚拟交通信息和报警信息，虚拟交通信息与被测车辆所处的真实交通场景同步。通过报警信息的输出即可实现对OBU的测试，且无需布设路侧基站设备和其他真实车辆，从而降低了测试成本。
",上海北汇信息科技有限公司
CN215912101U,CN202122653296.5,"本实用新型提供一种车载单元测试系统，应用于汽车技术领域，该测试系统包括与待测OBU通讯连接、为待测OBU提供模拟卫星信息的环境模拟装置，与待测OBU通讯连接、转发测试信息的信号收发装置，以及与信号收发装置通讯连接并根据测试信息对待测OBU进行联通测试的上位机。本实用新型提供的测试系统，能够对待测OBU进行联通测试，满足实际应用中对于OBU的测试需求。
",上海北汇信息科技有限公司
CN109963377B,CN201810637617.2,"本发明提供一种多色LED灯及其控制方法与装置，对由于工作而导致温度变化的LED，其通过相应AD电压采集模块分别采集对应LED各自的压降，然后由计算模块根据相应LED压降的采集值与其内部预设的标准温度下对应颜色的LED压降之间的偏移量，对照预设的各种颜色的LED的Tj‑ΔV曲线，计算得到处于ON状态的LED的结温；进而使工作中的LED以各自的结温，代替现有技术中统一的控制器温度，进行相应颜色的LED驱动控制的单独计算，进而通过相应PWM驱动模块实现对于相应LED驱动模块的对应控制，完成对于各种颜色的LED各自单独的温度补偿，解决了现有技术中无法对各个LED单独进行温度补偿的问题。
",上海北汇信息科技有限公司
CN212276236U,CN202020682241.X,"本实用新型提供一种便携式实车网络自动化测试系统，包括：接口面板、示波器、LIN总线故障注入设备、CAN/CANFD总线故障注入设备、FlexRay总线故障注入设备；设备路由板卡，与所述接口面板、所述示波器、所述LIN总线故障注入设备、所述CAN/CANFD总线故障注入设备和所述FlexRay总线故障注入设备相连；设备网关，与所述LIN总线故障注入设备和所述设备路由板卡相连；测试工程独立运行设备，与所述接口面板、所述设备路由板卡、所述设备网关、所述示波器、所述CAN/CANFD总线故障注入设备和所述FlexRay总线故障注入设备相连。本实用新型的便携式实车网络自动化测试系统能够自动化实现实车CAN/CANFD/LIN/FlexRay总线网络的测试，测试速度快，效率高，实用性强。
",上海北汇信息科技有限公司
CN109005617B,CN201810735478.7,"本发明提供一种基于车载RGB控制器的无极调光方法和装置。方法包括：将采集到的RGB三色各自LED输入的坐标值分别按百分比转换得到R<Sub>input_ratio</Sub>、G<Sub>input_ratio</Sub>、B<Sub>input_ratio</Sub>，同时依据预设的RGB三色的标准色坐标参数，计算得到混合颜色的三基色刺激值X<Sub>mix</Sub>、Y<Sub>mix</Sub>、Z<Sub>mix</Sub>；依据RGB三色各自LED自身的色坐标参数和X<Sub>mix</Sub>、Y<Sub>mix</Sub>、Z<Sub>mix</Sub>，计算得到RGB三色在混光后分别所占的亮度值；依据得到的亮度值计算RGB三色各自LED的PWM占空比；依据得到的PWM占空比对RGB LED的每个颜色的驱动电流进行调节。本发明保证了RGB颜色及亮度的一致性。
",上海北汇信息科技有限公司
CN210518375U,CN202020023371.2,"本申请公开了一种汽车总线的自动化检测系统，包括至少一个远端工控机、外部交换机、服务器和至少一个测试机柜，所述测试机柜内设置有本地工控机、内部交换机、程控示波器、程控万用表、故障注入板、总线接口卡、电源仿真模块、信号转换模块、环境配置板卡、设备路由板卡和IO板卡。在进行测试时，测试人员在远端工控机上进行登录，并将测试工程上传到服务器，监控测试执行状态与结果。测试机柜使用设备路由板卡控制程控示波器、程控万用表、故障注入板、总线接口卡、环境配置板卡与被测控制器连接，以实现测试环境的自动化控制。这样一来无需测试人员长时间的繁琐操作即可得到测试结果，从而提高了检测效率。
",上海北汇信息科技有限公司
CN208335011U,CN201821167704.8,"本申请公开了一种测试环境配置板卡，该配置板卡应用于汽车的电子控制器的测试系统，包括主板和子板。所述主板上设置有电源电路、控制电路、驱动电路、状态指示电路、母板配置电路、供电接口、板卡控制接口、被测接口、外设接口和调试接口，所述子板上设置有环境子板电路。本申请的控制模块通过获取CAN报文指令，来控制各电子开关的开断状态，实现对测试环境的自动配置，操作简单，实现了提高测试效率的目的。
",上海北汇信息科技有限公司
CN304783034S,CN201830061876.6,"1．本外观设计产品的名称：车载RGB控制器。
2．本外观设计产品的用途：本外观设计产品用于汽车内部，用于烘托车内环境氛围，可根据用户需要设定氛围灯颜色、亮度，提升汽车内饰的档次。
3．本外观设计产品的设计要点：产品形状。
4．最能表明本外观设计设计要点的图片或照片：立体图。
",上海北汇信息科技有限公司
CN207764648U,CN201820158782.5,"本实用新型公开一种程控接线板卡，用于自动配置故障注入模式，该程控接线板卡包括：电源模块、控制模块、驱动模块、状态指示模块、继电器模块，以及供电接口、板卡控制接口、被测接口和外设接口，其中：电源模块分别与控制模块、继电器模块、状态指示模块和供电接口相连；控制模块分别与状态指示模块、驱动模块和调试接口相连；驱动模块与继电器模块相连；继电器分别与被测接口和外设接口相连。本实用新型提供的程控接线板卡采用CAN收发器来获取CAN报文指令，通过控制模块操作各继电器的开关，实现自动配置故障注入模式的需求，操作简单，可靠性高，能够达到自动化测试的目的。
",上海北汇信息科技有限公司
CN207764647U,CN201820154726.4,"本实用新型公开一种设备路由板卡，用于控制电子控制器多路通讯线束与外设设备线束连接关系切换，该设备路由板卡包括：电源模块、控制模块、驱动模块、状态指示模块、继电器模块，以及供电接口、板卡控制接口、被测接口和外设接口，其中：电源模块分别与控制模块、继电器模块、状态指示模块和供电接口相连；控制模块分别与状态指示模块、驱动模块和板卡控制接口相连；驱动模块与继电器模块相连；继电器模块分别与被测接口和外设接口相连。本实用新型提供的设备路由板卡采用CAN收发器来获取CAN报文指令，通过控制模块控制各继电器的开关，实现控制电子控制器多路通讯线束与外设设备线束连接关系切换，操作简单，可靠性高，可实现自动化测试。
",上海北汇信息科技有限公司